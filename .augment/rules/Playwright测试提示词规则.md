---
type: "agent_requested"
description: "Example description"
---
# Playwright MCP工具测试提示词规则

## 测试执行规则

### 1. 测试前准备
```
在开始测试之前，必须：
1. 获取当前时间：使用 get_current_time_time
2. 确认测试环境：确认目标URL和测试范围
3. 设置浏览器：使用 playwright_navigate_playwright 打开目标页面
4. 完成登录：如果页面需要认证，先完成登录流程
5. 设置API监控：在进入目标页面前设置全面的API监控
```

### 2. API监控规则
```
API监控必须在进入目标页面之前设置：
1. 使用 playwright_evaluate_playwright 设置网络监控脚本
2. 监控所有 /api/ 路径的请求
3. 记录每个API的：URL、方法、响应时间、状态码、数据大小
4. 在测试结束时使用 Performance API 获取完整的网络请求数据
5. 对响应时间超过2秒的API标记为性能问题
```

### 3. 功能测试执行顺序
```
按以下顺序执行功能测试：
1. 页面基础功能测试（加载、导航、用户界面）
2. 交互元素测试（按钮、表单、数据展示）
3. 响应式布局测试（桌面端、平板端、移动端）
4. 错误处理测试（网络异常、边界条件）
5. 性能测试（加载时间、交互响应）
```

### 4. 截图记录规则
```
必须记录的截图：
1. 测试开始状态：页面完整加载后的初始状态
2. 成功操作截图：每个主要功能测试成功后截图
3. 失败操作截图：发现问题时立即截图记录
4. 响应式测试截图：不同屏幕尺寸下的布局截图
5. 错误状态截图：错误提示、异常状态的截图

截图命名规范：
- 成功：[功能名称]_success_[时间戳]
- 失败：[功能名称]_failed_[时间戳]
- 测试：[功能名称]_test_[时间戳]
```

### 5. 错误检测规则
```
必须检测的错误类型：
1. 控制台错误：使用 playwright_console_logs_playwright 检查
2. 网络请求错误：监控API请求失败
3. 页面加载错误：检查页面是否正常渲染
4. 功能操作错误：测试交互功能是否正常工作
5. 性能问题：识别响应时间过长的操作
```

## 测试报告生成规则

### 1. 报告结构要求
```
测试报告必须包含以下部分：
1. 测试环境信息（时间、页面、工具、作者）
2. 测试结果截图展示（成功/失败对比）
3. 完整API请求分析（所有API的详细性能数据）
4. 功能测试结果（详细的测试步骤和结果）
5. 响应式布局测试结果
6. 错误和安全性检查
7. 问题汇总（按优先级分类）
8. 改进建议（具体可执行的建议）
9. 测试总结（统计数据和整体评价）
```

### 2. API分析要求
```
API分析必须包含：
1. 每个API的详细信息：URL、方法、响应时间、数据大小、功能描述
2. API性能统计：最快/最慢响应时间、平均响应时间、总请求数
3. 性能问题识别：标记响应时间超过阈值的API
4. API安全性分析：数据加密、认证机制、权限控制
5. 性能优化建议：针对慢API的具体优化建议
```

### 3. 问题分级规则
```
问题严重程度分级：
- 高优先级：影响核心功能、严重性能问题（>5秒）、安全漏洞
- 中优先级：功能缺陷、中等性能问题（2-5秒）、用户体验问题
- 低优先级：界面优化、轻微性能问题（<2秒）、建议性改进

每个问题必须包含：
- 问题描述、影响范围、重现步骤、截图证据、修复建议
```

## 测试工具使用规范

### 1. Playwright导航工具
```
playwright_navigate_playwright:
- 设置合适的viewport尺寸
- 使用headless=false便于观察测试过程
- 设置合理的timeout时间
```

### 2. Playwright交互工具
```
点击操作：playwright_click_playwright
- 使用精确的CSS选择器
- 失败时尝试不同的选择器策略

填写表单：playwright_fill_playwright
- 确保输入框可见且可编辑
- 验证输入内容是否正确填入

截图工具：playwright_screenshot_playwright
- 使用描述性的文件名
- 设置savePng=true保存到Downloads
- 必要时使用fullPage=true获取完整页面
```

### 3. Playwright检查工具
```
获取页面内容：
- playwright_get_visible_text_playwright：获取可见文本
- playwright_get_visible_html_playwright：获取HTML结构

控制台日志：playwright_console_logs_playwright
- 分别检查error、warning、log类型
- 记录所有错误信息用于问题分析

JavaScript执行：playwright_evaluate_playwright
- 用于性能数据收集
- 用于API监控设置
- 用于页面状态检查
```

## 测试数据收集规范

### 1. 性能数据收集
```
必须收集的性能指标：
1. 页面加载性能：首次绘制、首次内容绘制、DOM加载时间
2. API性能：每个请求的响应时间、数据大小、状态码
3. 交互性能：按钮响应时间、页面切换时间
4. 资源加载：图片、CSS、JS文件加载时间
```

### 2. 功能测试数据
```
记录的测试数据：
1. 测试用例执行结果：通过/失败/跳过
2. 错误信息：详细的错误描述和堆栈信息
3. 用户操作流程：每个操作步骤的结果
4. 页面状态变化：操作前后的页面状态对比
```

### 3. 兼容性测试数据
```
不同环境下的测试结果：
1. 不同屏幕尺寸的布局表现
2. 不同浏览器的功能兼容性
3. 不同网络条件下的性能表现
```

## 测试质量保证

### 1. 测试完整性检查
```
确保测试覆盖：
1. 所有主要功能点都有对应测试
2. 所有API接口都被监控
3. 所有交互元素都被测试
4. 所有错误场景都被验证
```

### 2. 测试结果验证
```
验证测试结果的准确性：
1. 截图与描述一致
2. API数据真实有效
3. 错误信息准确完整
4. 性能数据可信
```

### 3. 报告质量标准
```
高质量测试报告标准：
1. 信息完整：包含所有必要的测试信息
2. 数据准确：所有数据都经过验证
3. 问题明确：问题描述清晰，有具体的重现步骤
4. 建议可行：改进建议具体可执行
5. 格式规范：使用统一的格式和命名规范
```

## 特殊情况处理

### 1. 测试失败处理
```
当测试失败时：
1. 立即截图记录失败状态
2. 记录详细的错误信息
3. 尝试不同的操作方式
4. 如果仍然失败，标记为已知问题
5. 继续执行其他测试项目
```

### 2. 性能问题处理
```
发现性能问题时：
1. 记录具体的性能数据
2. 分析性能瓶颈原因
3. 提供具体的优化建议
4. 评估对用户体验的影响
```

### 3. 安全问题处理
```
发现安全问题时：
1. 详细记录安全漏洞
2. 评估安全风险等级
3. 提供修复建议
4. 标记为高优先级问题
```

## 测试文档规范

### 1. 文件命名规范
```
测试报告文件名：[页面名称]测试报告.md
截图文件名：[功能名称]_[状态]_[时间戳].png
测试用例文件名：[页面名称]测试用例.md
```

### 2. 文档结构规范
```
使用统一的Markdown格式
包含必要的元数据信息
使用清晰的标题层级
包含目录和索引
```

### 3. 版本控制规范
```
每次测试都要记录：
1. 测试时间和版本
2. 测试环境信息
3. 测试人员信息
4. 变更记录
```

## 测试执行提示词模板

### 标准测试执行提示词
```
你好hayden！请使用playwright工具对以下页面进行全面的功能测试和质量检查：

**测试目标页面**: [页面URL]
**测试用例文档**: @[测试用例文件路径]

请按照以下步骤执行测试：

1. **测试准备**
   - 获取当前时间并记录测试开始
   - 打开目标页面并完成登录（如需要）
   - 设置全面的API监控

2. **执行测试用例**
   - 按照测试用例文档中的测试项目逐一执行
   - 对每个主要功能进行测试并截图记录
   - 监控所有API请求的性能数据
   - 测试响应式布局（桌面端、平板端、移动端）

3. **问题检测**
   - 检查控制台错误日志
   - 识别性能瓶颈和安全问题
   - 记录所有发现的问题并截图

4. **生成测试报告**
   - 在docs文件夹下创建详细的测试报告
   - 包含完整的API性能分析
   - 提供成功/失败的直观截图展示
   - 按优先级汇总问题并提供改进建议

请确保测试报告包含所有API的准确响应时间数据，以及直观的成功/失败截图对比。
```

### 快速测试提示词
```
请对页面 [页面URL] 进行快速功能测试，重点关注：
1. 页面基础功能（加载、导航、交互）
2. API性能监控（记录所有API响应时间）
3. 主要功能点测试
4. 生成简化测试报告

测试用例参考：@[测试用例文件路径]
```

### 专项测试提示词
```
请对页面 [页面URL] 进行 [专项名称] 专项测试：

**API性能专项测试**：
- 重点监控所有API请求的响应时间
- 识别性能瓶颈
- 提供优化建议

**响应式布局专项测试**：
- 测试多种屏幕尺寸下的布局表现
- 验证移动端适配效果

**功能完整性专项测试**：
- 测试所有交互功能
- 验证业务流程完整性

测试用例参考：@[测试用例文件路径]
```
