# 会议管理模块 (meet-management) 技术设计文档

## 概述

本设计文档基于需求文档，为会议管理模块(meet)提供详细的技术实现方案。该模块将为组织内部成员提供简洁高效的会议管理功能，采用现代化的架构设计和用户界面。

### 设计原则

1. **架构一致性**: 严格遵循项目现有架构模式，参考investor-management模块
2. **组件复用**: 最大化使用现有UI组件，遵循设计规范
3. **模块化设计**: 前后端分离，API和UI独立开发
4. **安全优先**: 数据隔离、权限控制、加密传输
5. **性能优化**: 响应式设计、无限滚动、缓存策略

## 架构设计

### 整体架构图

```mermaid
graph TB
    A[Web前端 - Next.js] --> B[API层 - Hono]
    B --> C[数据库 - PostgreSQL]
    B --> D[认证中间件]
    B --> E[加密中间件]
    
    subgraph "前端架构"
        F[会议页面路由] --> G[会议管理组件]
        G --> H[会议卡片列表]
        G --> I[会议详情覆盖页面]
        G --> J[会议表单组件]
    end
    
    subgraph "后端架构"
        K[会议路由器] --> L[CRUD路由]
        L --> M[验证器]
        L --> N[数据库操作]
    end
    
    subgraph "数据库层"
        O[会议模型] --> P[用户关联]
        O --> Q[组织关联]
    end
```

### 技术栈选择

- **前端**: Next.js 15.2.3 (App Router) + React 19.0.0
- **UI组件**: shadcn/ui + Radix UI + Tailwind CSS 4.0.17
- **状态管理**: Jotai 2.12.1 + TanStack Query 5.66.9
- **后端**: Hono (API路由) + TypeScript
- **数据库**: PostgreSQL + Prisma ORM
- **验证**: Zod数据验证
- **认证**: Better Auth 1.1.21
- **国际化**: next-intl 3.26.5

## 数据模型设计

### Meet 数据模型

基于现有数据库设计模式，新增Meet模型：

```prisma
// 会议类型枚举
enum MeetType {
  PERFORMANCE_BRIEFING     // 业绩说明会
  STRATEGY_MEETING        // 策略会
  ONE_ON_ONE             // 一对一交流会
  ONE_TO_MANY            // 一对多交流会
  SHAREHOLDER_MEETING     // 股东大会
  CLOSED_MEETING         // 闭门会议
  PRODUCT_LAUNCH         // 产品发布会
  MAJOR_DISCLOSURE       // 重大事项披露会议
  CONVERTIBLE_BOND       // 可转债路演会议
  REVERSE_ROADSHOW       // 反路演会议
  GENERAL               // 常规会议
}

// 会议模型
model Meet {
  id                 String        @id @default(cuid()) // 会议ID，主键，自动生成
  title              String        // 会议标题（必填）
  description        String?       // 会议内容（可选）
  meetingType        MeetType      @default(GENERAL) // 会议类型（必填），默认常规会议
  scheduledStartTime DateTime      // 计划开始时间（必填）
  scheduledEndTime   DateTime      // 计划结束时间（必填）
  location           String?       // 会议地点（可选）
  locationUrl        String?       // 会议链接（可选）
  hostName           String?       // 接待人姓名（可选）
  hostContact        String?       // 接待人联系方式（可选）
  notes              String?       // 会议备注（可选）
  meetingNumber      String        // 会议号（使用数据库ID，唯一）
  
  // 关联关系
  createdBy          String        // 创建人ID，关联User表
  user               User          @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  organizationId     String        // 组织ID，关联Organization表
  organization       Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // 时间戳
  createdAt          DateTime      @default(now()) // 创建时间，默认为当前时间
  updatedAt          DateTime      @updatedAt // 更新时间，自动更新

  // 索引优化
  @@index([createdBy]) // 创建者索引
  @@index([organizationId]) // 组织ID索引
  @@index([scheduledStartTime]) // 计划开始时间索引
  @@index([meetingType]) // 会议类型索引
  @@index([createdAt]) // 创建时间索引
  @@index([organizationId, scheduledStartTime]) // 复合索引，优化列表查询
  @@unique([meetingNumber]) // 会议号唯一索引
  @@map("meet") // 映射到数据库表"meet"
}
```

### 数据关系设计

```mermaid
erDiagram
    User ||--o{ Meet : creates
    Organization ||--o{ Meet : contains
    User ||--o{ Member : has
    Organization ||--o{ Member : has
    
    User {
        string id PK
        string name
        string email
        string image
        datetime createdAt
    }
    
    Organization {
        string id PK
        string name
        string slug
        datetime createdAt
    }
    
    Meet {
        string id PK
        string title
        string description
        enum meetingType
        datetime scheduledStartTime
        datetime scheduledEndTime
        string location
        string locationUrl
        string hostName
        string hostContact
        string notes
        string meetingNumber UK
        string createdBy FK
        string organizationId FK
        datetime createdAt
        datetime updatedAt
    }
    
    Member {
        string id PK
        string userId FK
        string organizationId FK
        string role
    }
```

## API接口设计

### API路由架构

遵循investor-management模块的架构模式：

```
packages/api/src/routes/meet/
├── router.ts               # 主路由文件，聚合所有子路由
├── types.ts               # TypeScript类型定义
├── lib/                   # 工具库目录
│   └── validators.ts      # Zod验证器
├── create.ts              # 创建会议路由
├── list.ts                # 查询会议列表路由
├── detail.ts              # 查询会议详情路由  
├── update.ts              # 更新会议路由
└── delete.ts              # 删除会议路由
```

### API接口规范

#### 1. 创建会议接口

**接口**: `POST /api/meet/create`

**中间件**: `authMiddleware` + `shareholderCryptoMiddleware()`

**请求参数**:
```typescript
interface CreateMeetRequest {
  organizationId: string;     // 组织ID（必填）
  title: string;             // 会议标题（必填）
  description?: string;      // 会议描述（可选）
  meetingType?: MeetType;    // 会议类型（可选）
  scheduledStartTime: string; // 开始时间 ISO 8601（必填）
  scheduledEndTime: string;   // 结束时间 ISO 8601（必填）
  location?: string;         // 会议地点（可选）
  locationUrl?: string;      // 会议链接（可选）
  hostName?: string;         // 接待人姓名（可选）
  hostContact?: string;      // 接待人联系方式（可选）
  notes?: string;           // 会议备注（可选）
}
```

**响应格式**:
```typescript
interface CreateMeetResponse {
  code: number;              // 状态码
  message: string;           // 响应消息
  data: {
    id: string;              // 创建的会议ID
    meetingNumber: string;   // 会议号（使用数据库ID）
  };
}
```

#### 2. 会议列表接口

**接口**: `POST /api/meet/list`

**请求参数**:
```typescript
interface ListMeetRequest {
  organizationId: string;    // 组织ID（必填）
  page?: number;            // 页码（默认1）
  limit?: number;           // 每页数量（默认20，最大100）
  meetingType?: MeetType;   // 类型筛选（可选，对应筛选按钮）
  sortBy?: string;          // 排序字段（默认scheduledStartTime）
  sortOrder?: 'asc' | 'desc'; // 排序方向（默认desc）
}
```

#### 3. 会议详情接口

**接口**: `POST /api/meet/detail`

**请求参数**:
```typescript
interface DetailMeetRequest {
  organizationId: string;    // 组织ID（必填）
  meetingId: string;        // 会议ID（必填）
}
```

#### 4. 更新会议接口

**接口**: `POST /api/meet/update`

**请求参数**: 与创建接口类似，但增加`meetingId`参数，其他字段均为可选

#### 5. 删除会议接口

**接口**: `POST /api/meet/delete`

**请求参数**:
```typescript
interface DeleteMeetRequest {
  organizationId: string;    // 组织ID（必填）
  meetingId: string;        // 会议ID（必填）
}
```

### Zod验证器设计

```typescript
// packages/api/src/routes/meet/lib/validators.ts
import { z } from "zod";

// 会议类型枚举验证
const MeetTypeSchema = z.enum([
  "PERFORMANCE_BRIEFING", "STRATEGY_MEETING", "ONE_ON_ONE", 
  "ONE_TO_MANY", "SHAREHOLDER_MEETING", "CLOSED_MEETING",
  "PRODUCT_LAUNCH", "MAJOR_DISCLOSURE", "CONVERTIBLE_BOND",
  "REVERSE_ROADSHOW", "GENERAL"
], {
  errorMap: () => ({ message: "会议类型必须是有效值" })
});

// 创建会议验证
export const CreateMeetSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  title: z.string().min(1, "会议标题不能为空").max(255, "会议标题不能超过255个字符"),
  description: z.string().optional(),
  meetingType: MeetTypeSchema.default("GENERAL"), // 必填，默认常规会议
  scheduledStartTime: z.string().datetime("开始时间格式无效"),
  scheduledEndTime: z.string().datetime("结束时间格式无效"),
  location: z.string().max(255, "地点不能超过255个字符").optional(),
  locationUrl: z.string().url("会议链接格式无效").optional(),
  hostName: z.string().max(100, "接待人姓名不能超过100个字符").optional(),
  hostContact: z.string().max(100, "接待人联系方式不能超过100个字符").optional(),
  notes: z.string().optional(),
}).refine(data => {
  return new Date(data.scheduledEndTime) >= new Date(data.scheduledStartTime);
}, {
  message: "结束时间不能早于开始时间",
  path: ["scheduledEndTime"]
});

// 查询会议列表验证
export const ListMeetSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(20),
  meetingType: MeetTypeSchema.optional(), // 筛选会议类型
  sortBy: z.string().optional().default("scheduledStartTime"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});
```

## 前端组件设计

### 页面路由设计

```
apps/web/app/(saas)/app/(organizations)/[organizationSlug]/meet/
├── page.tsx                    # 会议管理主页面
├── layout.tsx                  # 会议模块布局
└── loading.tsx                 # 加载状态页面
```

### 组件架构

```
apps/web/modules/saas/meet/
├── components/                 # 组件目录
│   ├── MeetCard.tsx           # 会议卡片组件
│   ├── MeetList.tsx           # 会议单列列表组件
│   ├── MeetDetail.tsx         # 会议详情覆盖页面组件
│   ├── MeetForm.tsx           # 会议表单组件
│   ├── MeetFilter.tsx         # 会议筛选组件
│   └── MeetMenu.tsx           # 会议菜单组件
├── hooks/                     # Hooks目录
│   ├── useMeets.ts            # 会议数据管理Hook
│   ├── useMeetDetail.ts       # 会议详情Hook
│   ├── useMeetForm.ts         # 会议表单Hook
│   └── useMeetFilter.ts       # 会议筛选Hook
├── lib/                       # 工具库
│   ├── api.ts                 # API调用封装
│   ├── types.ts               # TypeScript类型
│   └── utils.ts               # 工具函数
└── constants/                 # 常量定义
    └── meetTypes.ts           # 会议类型常量
```

### 核心组件设计

#### 1. 会议管理主页面 (MeetPage)

```typescript
// apps/web/app/(saas)/app/(organizations)/[organizationSlug]/meet/page.tsx
export default async function MeetPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  
  return (
    <div className="container mx-auto px-6 py-8">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">会议管理</h1>
          <p className="text-muted-foreground mt-2">管理和查看您的会议安排</p>
        </div>
        <div className="flex items-center gap-4">
          <MeetFilter />
          <Button onClick={handleCreateMeet}>
            <Plus className="w-4 h-4 mr-2" />
            预定会议
          </Button>
        </div>
      </div>
      
      {/* 会议列表 */}
      <MeetList organizationSlug={organizationSlug} />
      
      {/* 会议详情覆盖页面 */}
      <MeetDetail />
    </div>
  );
}
```

#### 2. 会议卡片组件 (MeetCard)

```typescript
// apps/web/modules/saas/meet/components/MeetCard.tsx
interface MeetCardProps {
  meet: Meet;
  onClick: (meet: Meet) => void;
}

export function MeetCard({ meet, onClick }: MeetCardProps) {
  const formatDateTime = (dateTime: string) => {
    // 使用统一的时间格式化函数，固定中国时区
    return formatMeetTime(dateTime);
  };

  return (
    <Card 
      className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
      onClick={() => onClick(meet)}
    >
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <CalendarDays className="w-6 h-6 text-primary" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg text-foreground truncate">
              {meet.title}
            </h3>
            
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="w-4 h-4 mr-2" />
                {formatDateTime(meet.scheduledStartTime)} - {formatDateTime(meet.scheduledEndTime)}
              </div>
              
              <div className="flex items-center text-sm text-muted-foreground">
                <User className="w-4 h-4 mr-2" />
                创建者: {meet.user.name}
              </div>
              
              {meet.meetingType && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Tag className="w-4 h-4 mr-2" />
                  {getMeetTypeLabel(meet.meetingType)}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 3. 会议详情覆盖页面 (MeetDetail)

```typescript
// apps/web/modules/saas/meet/components/MeetDetail.tsx
export function MeetDetail({ meetId, onBack }: { meetId: string; onBack: () => void }) {
  const [activeTab, setActiveTab] = useState("info");
  const { data: meet, isLoading } = useMeetDetail(meetId);
  
  if (isLoading) {
    return <div className="animate-pulse">加载中...</div>;
  }
  
  return (
    <div className="fixed inset-0 bg-background z-50 overflow-auto">
      <div className="container mx-auto px-6 py-8">
        {/* 头部区域 */}
        <div className="flex items-center justify-between mb-8 pb-4 border-b">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回
            </Button>
            <h1 className="text-2xl font-semibold">会议详情</h1>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem 
                className="text-destructive"
                onClick={handleDeleteMeet}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                删除会议
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        {/* 基本信息区 */}
        <div className="space-y-4 pb-6 border-b mb-6">
          <MeetBasicInfo meet={meet} onUpdate={handleUpdateMeet} />
        </div>
        
        {/* Tab区域 */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="info">会议信息</TabsTrigger>
            <TabsTrigger value="participants">参会人员</TabsTrigger>
          </TabsList>
          
          <TabsContent value="info" className="mt-6">
            <MeetInfoForm meet={meet} onUpdate={handleUpdateMeet} />
          </TabsContent>
          
          <TabsContent value="participants" className="mt-6">
            <div className="text-center py-12 text-muted-foreground">
              开发中...
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
```

#### 4. 会议单列列表 (MeetList)

```typescript
// apps/web/modules/saas/meet/components/MeetList.tsx
export function MeetList({ organizationSlug }: { organizationSlug: string }) {
  const { 
    data: meets, 
    isLoading, 
    hasNextPage, 
    fetchNextPage,
    isFetchingNextPage 
  } = useMeets(organizationSlug);
  
  // 无限滚动实现
  const { ref: loadMoreRef } = useIntersection({
    root: null,
    rootMargin: '200px',
    threshold: 0,
    onIntersect: () => {
      if (hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    },
  });

  if (isLoading) {
    return <MeetListSkeleton />;
  }

  if (!meets?.length) {
    return (
      <div className="text-center py-12">
        <CalendarDays className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">暂无会议</h3>
        <p className="text-muted-foreground mb-6">点击预定会议开始创建</p>
        <Button onClick={handleCreateMeet}>
          <Plus className="w-4 h-4 mr-2" />
          预定会议
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 单列布局，每行一个会议卡片 */}
      <div className="space-y-4">
        {meets.map((meet) => (
          <MeetCard 
            key={meet.id} 
            meet={meet} 
            onClick={handleMeetClick}
          />
        ))}
      </div>
      
      {/* 加载更多指示器 */}
      {hasNextPage && (
        <div ref={loadMoreRef} className="flex justify-center py-8">
          {isFetchingNextPage ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="w-4 h-4 animate-spin" />
              加载中...
            </div>
          ) : (
            <div className="text-muted-foreground text-sm">
              滚动加载更多
            </div>
          )}
        </div>
      )}
      
      {!hasNextPage && meets.length > 0 && (
        <div className="text-center py-8 text-muted-foreground text-sm">
          没有更多会议了
        </div>
      )}
    </div>
  );
}
```

### 状态管理设计

#### 1. 会议数据管理Hook

```typescript
// apps/web/modules/saas/meet/hooks/useMeets.ts
export function useMeets(organizationSlug: string) {
  const { activeOrganization } = useActiveOrganization();
  
  return useInfiniteQuery({
    queryKey: ['meets', activeOrganization?.id],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await api.meets.list({
        organizationId: activeOrganization!.id,
        page: pageParam,
        limit: 20,
        sortBy: 'scheduledStartTime',
        sortOrder: 'desc'
      });
      
      if (response.code !== 200) {
        throw new Error(response.message);
      }
      
      return response.data;
    },
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext 
        ? lastPage.pagination.page + 1 
        : undefined;
    },
    enabled: !!activeOrganization?.id,
  });
}
```

#### 2. 会议表单Hook

```typescript
// apps/web/modules/saas/meet/hooks/useMeetForm.ts
export function useMeetForm(initialData?: Partial<Meet>) {
  const { activeOrganization } = useActiveOrganization();
  const { user } = useSession();
  
  const createMutation = useMutation({
    mutationFn: api.meets.create,
    onSuccess: () => {
      toast.success('会议创建成功');
      queryClient.invalidateQueries(['meets']);
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    },
  });
  
  const form = useForm<CreateMeetRequest>({
    resolver: zodResolver(CreateMeetSchema),
    defaultValues: {
      organizationId: activeOrganization?.id || '',
      title: initialData?.title || `${user?.name}预定的会议`,
      meetingType: initialData?.meetingType || 'GENERAL',
      scheduledStartTime: getNextAvailableTime(),
      scheduledEndTime: getNextAvailableTime(30), // 30分钟后
      ...initialData,
    },
  });
  
  const handleSubmit = form.handleSubmit((data) => {
    createMutation.mutate(data);
  });
  
  return {
    form,
    handleSubmit,
    isSubmitting: createMutation.isPending,
  };
}
```

## 错误处理设计

### API错误处理

```typescript
// 使用项目现有的错误处理模式
import { HTTPException } from 'hono/http-exception';

// 简化的错误处理，复用现有模式
export function handleMeetAPIError(error: unknown, c: Context) {
  // 使用项目统一的日志系统
  logger.error('Meet API Error', { error: error.message });
  
  // 复用现有的HTTP异常处理
  if (error instanceof HTTPException) {
    throw error; // 让上层中间件处理
  }
  
  // 数据验证错误
  if (error instanceof z.ZodError) {
    throw new HTTPException(400, { 
      message: `数据验证失败: ${error.errors[0]?.message}` 
    });
  }
  
  // 默认服务器错误
  throw new HTTPException(500, { message: '服务器内部错误' });
}
```

### 前端错误处理

```typescript
// 使用项目现有的错误边界组件，无需自定义
import { ErrorBoundary } from '@/modules/shared/components/ErrorBoundary';

// 简化的Hook错误处理
export function useMeetErrorHandler() {
  return useCallback((error: Error) => {
    // 使用项目现有的toast系统
    toast.error(error.message || '操作失败，请稍后重试');
    
    // 统一的错误上报（如果有）
    if (process.env.NODE_ENV === 'production') {
      logger.error('Meet operation error', { error: error.message });
    }
  }, []);
}
```

## 测试策略

### 单元测试

```typescript
// packages/api/src/routes/meet/__tests__/create.test.ts
describe('Meet Create API', () => {
  beforeEach(() => {
    // 设置测试环境
  });
  
  it('should create meet successfully', async () => {
    const meetData = {
      organizationId: 'test-org',
      title: '测试会议',
      scheduledStartTime: '2024-12-01T10:00:00Z',
      scheduledEndTime: '2024-12-01T11:00:00Z',
    };
    
    const response = await request(app)
      .post('/api/meet/create')
      .send(meetData)
      .expect(200);
      
    expect(response.body.code).toBe(200);
    expect(response.body.data.id).toBeDefined();
  });
  
  it('should validate required fields', async () => {
    const response = await request(app)
      .post('/api/meet/create')
      .send({})
      .expect(400);
      
    expect(response.body.message).toContain('组织ID不能为空');
  });
});
```

### 组件测试

```typescript
// apps/web/modules/saas/meet/components/__tests__/MeetCard.test.tsx
describe('MeetCard', () => {
  const mockMeet = {
    id: 'test-id',
    title: '测试会议',
    scheduledStartTime: '2024-12-01T10:00:00Z',
    scheduledEndTime: '2024-12-01T11:00:00Z',
    user: { name: '测试用户' },
    status: 'SCHEDULED',
  };
  
  it('should render meet information correctly', () => {
    render(<MeetCard meet={mockMeet} onClick={jest.fn()} />);
    
    expect(screen.getByText('测试会议')).toBeInTheDocument();
    expect(screen.getByText(/创建者: 测试用户/)).toBeInTheDocument();
  });
  
  it('should call onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<MeetCard meet={mockMeet} onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledWith(mockMeet);
  });
});
```

### 集成测试

```typescript
// apps/web/modules/saas/meet/__tests__/integration.test.tsx
describe('Meet Management Integration', () => {
  it('should create and display new meet', async () => {
    // 模拟API响应
    server.use(
      rest.post('/api/meet/create', (req, res, ctx) => {
        return res(ctx.json({
          code: 200,
          message: '创建成功',
          data: { id: 'new-meet-id' }
        }));
      })
    );
    
    render(<MeetPage organizationSlug="test-org" />);
    
    // 点击创建按钮
    fireEvent.click(screen.getByText('预定会议'));
    
    // 填写表单
    fireEvent.change(screen.getByLabelText('会议标题'), {
      target: { value: '新会议' }
    });
    
    // 提交表单
    fireEvent.click(screen.getByText('创建会议'));
    
    // 验证结果
    await waitFor(() => {
      expect(screen.getByText('会议创建成功')).toBeInTheDocument();
    });
  });
});
```

## 性能优化

### 前端优化策略

1. **组件懒加载**
```typescript
const MeetDetail = lazy(() => import('./components/MeetDetail'));
const MeetForm = lazy(() => import('./components/MeetForm'));
```

2. **响应式设计优化**
```typescript
// 响应式布局断点定义
export const breakpoints = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1199px)',
  desktop: '(min-width: 1200px)'
};

// 移动端触摸优化
export function MeetListMobile({ meets }: { meets: Meet[] }) {
  return (
    <div className="space-y-3 pb-safe">
      {meets.map((meet) => (
        <MeetCard 
          key={meet.id} 
          meet={meet} 
          onClick={handleMeetClick}
          className="touch-manipulation min-h-[44px]" // 触摸优化
        />
      ))}
    </div>
  );
}
```

3. **数据缓存**
```typescript
// React Query配置
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    },
  },
});
```

### 后端优化策略

1. **数据库查询优化**
```typescript
// 优化的列表查询
async function getMeetsList(params: ListMeetRequest) {
  const { organizationId, page, limit, ...filters } = params;
  
  // 构建高效的where条件
  const where: Prisma.MeetWhereInput = {
    organizationId,
    ...(filters.status && { status: filters.status }),
    ...(filters.meetingType && { meetingType: filters.meetingType }),
    ...(filters.search && {
      title: {
        contains: filters.search,
        mode: 'insensitive'
      }
    }),
  };
  
  // 并行查询总数和数据
  const [total, meets] = await Promise.all([
    db.meet.count({ where }),
    db.meet.findMany({
      where,
      orderBy: { [params.sortBy]: params.sortOrder },
      skip: (page - 1) * limit,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          }
        }
      }
    })
  ]);
  
  return { meets, total };
}
```

2. **API响应优化**
```typescript
// 使用React Query内置缓存，无需额外缓存层
export const meetQueryOptions = {
  staleTime: 5 * 60 * 1000, // 5分钟数据保鲜
  cacheTime: 10 * 60 * 1000, // 10分钟缓存
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// API响应优化，只返回必要字段
export function optimizeApiResponse(meets: Meet[]) {
  return meets.map(meet => ({
    ...meet,
    // 只返回必要字段，减少数据传输
    user: {
      id: meet.user.id,
      name: meet.user.name,
      image: meet.user.image
    }
  }));
}
```

## 安全考虑

### 数据访问控制

```typescript
// 组织数据隔离中间件
export const organizationAccessMiddleware = async (c: Context, next: Next) => {
  const requestData = c.get('requestData');
  const user = c.get('user');
  
  if (!requestData?.organizationId) {
    throw new HTTPException(400, { message: '缺少组织ID' });
  }
  
  // 验证用户是否属于该组织
  const membership = await db.member.findFirst({
    where: {
      userId: user.id,
      organizationId: requestData.organizationId,
    }
  });
  
  if (!membership) {
    throw new HTTPException(403, { message: '无权访问该组织数据' });
  }
  
  await next();
};
```

### 输入验证和清理

```typescript
// XSS防护
export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}

// SQL注入防护（通过Prisma自动处理）
// 时间验证
export function validateTimeRange(start: string, end: string): boolean {
  const startTime = new Date(start);
  const endTime = new Date(end);
  
  if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
    throw new Error('无效的时间格式');
  }
  
  if (endTime <= startTime) {
    throw new Error('结束时间必须晚于开始时间');
  }
  
  return true;
}
```

### 敏感数据处理

```typescript
// 数据脱敏
export function sanitizeMeetResponse(meet: Meet): Partial<Meet> {
  const { user, ...sanitizedMeet } = meet;
  
  return {
    ...sanitizedMeet,
    user: {
      id: user.id,
      name: user.name,
      image: user.image,
      // 不返回email等敏感信息
    }
  };
}
```

## 响应式设计详细规范

### 断点和布局策略

```typescript
// 响应式断点定义
export const BREAKPOINTS = {
  mobile: { max: 767 },      // 手机端
  tablet: { min: 768, max: 1199 }, // 平板端  
  desktop: { min: 1200 }     // 桌面端
} as const;

// 响应式布局组件
export function ResponsiveMeetLayout({ children }: { children: ReactNode }) {
  return (
    <div className="
      /* 桌面端 */
      lg:container lg:mx-auto lg:px-6 lg:py-8
      /* 平板端 */
      md:px-4 md:py-6
      /* 手机端 */
      px-3 py-4 pb-safe
    ">
      {children}
    </div>
  );
}
```

### 移动端优化策略

```typescript
// 触摸友好的交互设计
export const TOUCH_TARGET_SIZE = 44; // 最小触摸区域44px

// 移动端手势支持
export function MobileMeetDetail({ meet, onBack }: MobileDetailProps) {
  const { swipeHandlers } = useSwipeGesture({
    onSwipeRight: onBack, // 右滑返回
    threshold: 50
  });

  return (
    <div 
      {...swipeHandlers}
      className="
        /* 全屏覆盖 */
        fixed inset-0 z-50 bg-background
        /* 安全区域适配 */
        pt-safe pb-safe
        /* 滚动优化 */
        overflow-auto scroll-smooth
      "
    >
      {/* 下拉刷新支持 */}
      <PullToRefresh onRefresh={handleRefresh}>
        <MeetDetailContent meet={meet} />
      </PullToRefresh>
    </div>
  );
}
```

### 设备适配规范

| 设备类型 | 屏幕宽度 | 布局特点 | 交互优化 |
|---------|---------|---------|---------|
| **手机端** | < 768px | 单列布局，紧凑间距 | 大触摸区域，手势支持 |
| **平板端** | 768px-1199px | 单列布局，适中间距 | 触摸+鼠标混合 |
| **桌面端** | ≥ 1200px | 单列布局，宽松间距 | 鼠标悬停效果 |

### Tailwind响应式类名规范

```typescript
// 组件样式示例
export const meetCardStyles = {
  base: "w-full rounded-lg border transition-all duration-200",
  responsive: {
    // 手机端
    mobile: "p-4 mb-3 text-sm",
    // 平板端  
    tablet: "md:p-5 md:mb-4 md:text-base",
    // 桌面端
    desktop: "lg:p-6 lg:mb-6 lg:text-base lg:hover:shadow-lg"
  }
};

// 使用示例
<Card className={cn(
  meetCardStyles.base,
  meetCardStyles.responsive.mobile,
  meetCardStyles.responsive.tablet, 
  meetCardStyles.responsive.desktop
)}>
```

## 国际化设计

### 多语言支持

```typescript
// packages/i18n/translations/en.json
{
  "meet": {
    "title": "Meeting Management",
    "create": "Schedule Meeting", 
    "types": {
      "GENERAL": "General Meeting",
      "PERFORMANCE_BRIEFING": "Performance Briefing",
      "STRATEGY_MEETING": "Strategy Meeting"
    },
    "status": {
      "SCHEDULED": "Scheduled",
      "ONGOING": "Ongoing", 
      "COMPLETED": "Completed",
      "CANCELLED": "Cancelled"
    }
  }
}

// packages/i18n/translations/zh.json  
{
  "meet": {
    "title": "会议管理",
    "create": "预定会议",
    "types": {
      "GENERAL": "常规会议",
      "PERFORMANCE_BRIEFING": "业绩说明会",
      "STRATEGY_MEETING": "策略会"
    },
    "status": {
      "SCHEDULED": "已安排", 
      "ONGOING": "进行中",
      "COMPLETED": "已完成",
      "CANCELLED": "已取消"
    }
  }
}
```

### 时区处理

```typescript
// apps/web/modules/saas/meet/lib/time-utils.ts
export function formatMeetTime(dateTime: string): string {
  // 固定使用中国时区(UTC+8)
  return new Date(dateTime).toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  });
}

export function getNextAvailableTime(offsetMinutes: number = 0): string {
  const now = new Date();
  const nextTime = new Date(now.getTime() + offsetMinutes * 60000);
  
  // 调整到下一个30分钟或整点
  const minutes = nextTime.getMinutes();
  if (minutes > 0 && minutes < 30) {
    nextTime.setMinutes(30, 0, 0);
  } else if (minutes > 30) {
    nextTime.setHours(nextTime.getHours() + 1, 0, 0, 0);
  }
  
  return nextTime.toISOString();
}
```

## 部署和运维

### 环境配置

```bash
# .env.example
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/supstar"

# Redis配置 (可选，用于缓存)
REDIS_URL="redis://localhost:6379"

# 会议模块配置
MEET_DEFAULT_DURATION=30  # 默认会议时长(分钟)
MEET_MAX_DURATION=480     # 最大会议时长(分钟)
MEET_CACHE_TTL=300        # 缓存时间(秒)
```

### 数据库迁移

```sql
-- 创建Meet表的迁移文件
-- migrations/add_meet_table.sql

-- 创建枚举类型
CREATE TYPE "MeetType" AS ENUM ('PERFORMANCE_BRIEFING', 'STRATEGY_MEETING', 'ONE_ON_ONE', 'ONE_TO_MANY', 'SHAREHOLDER_MEETING', 'CLOSED_MEETING', 'PRODUCT_LAUNCH', 'MAJOR_DISCLOSURE', 'CONVERTIBLE_BOND', 'REVERSE_ROADSHOW', 'GENERAL');

-- 创建Meet表
CREATE TABLE "meet" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "meetingType" "MeetType" NOT NULL DEFAULT 'GENERAL',
    "scheduledStartTime" TIMESTAMP(3) NOT NULL,
    "scheduledEndTime" TIMESTAMP(3) NOT NULL,
    "location" TEXT,
    "locationUrl" TEXT,
    "hostName" TEXT,
    "hostContact" TEXT,
    "notes" TEXT,
    "meetingNumber" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "meet_pkey" PRIMARY KEY ("id")
);

-- 创建索引
CREATE INDEX "meet_createdBy_idx" ON "meet"("createdBy");
CREATE INDEX "meet_organizationId_idx" ON "meet"("organizationId");
CREATE INDEX "meet_scheduledStartTime_idx" ON "meet"("scheduledStartTime");
CREATE INDEX "meet_meetingType_idx" ON "meet"("meetingType");
CREATE INDEX "meet_createdAt_idx" ON "meet"("createdAt");
CREATE INDEX "meet_organizationId_scheduledStartTime_idx" ON "meet"("organizationId", "scheduledStartTime");
CREATE UNIQUE INDEX "meet_meetingNumber_key" ON "meet"("meetingNumber");

-- 添加外键约束
ALTER TABLE "meet" ADD CONSTRAINT "meet_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "meet" ADD CONSTRAINT "meet_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- 更新Organization表，添加meet关联关系（在Prisma schema中定义）
```

### 监控和日志

```typescript
// 添加会议操作日志
export function logMeetOperation(
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
  meetId: string,
  userId: string,
  organizationId: string,
  details?: any
) {
  logger.info('Meet operation', {
    operation,
    meetId,
    userId,
    organizationId,
    details,
    timestamp: new Date().toISOString()
  });
}

// 基础日志记录（使用项目现有的logger）
export function logMeetApiAccess(operation: string, context: any) {
  logger.info('Meet API access', {
    operation,
    organizationId: context.organizationId,
    userId: context.userId,
    timestamp: new Date().toISOString()
  });
}
```

---

## 总结

本设计文档为会议管理模块提供了完整的技术实现方案，包括：

1. **数据模型设计**: 基于现有数据库模式的Meet模型设计
2. **API接口设计**: 遵循项目架构的5个核心API接口
3. **前端组件设计**: 模块化、可复用的React组件架构
4. **状态管理**: 基于React Query的数据管理策略
5. **错误处理**: 统一的错误处理和用户反馈机制
6. **性能优化**: 前后端性能优化策略
7. **安全考虑**: 数据隔离、输入验证、权限控制
8. **测试策略**: 单元测试、组件测试、集成测试
9. **国际化**: 多语言和时区处理
10. **部署运维**: 环境配置、数据库迁移、监控日志

该设计严格遵循项目现有的技术栈和架构模式，确保新模块与现有系统的无缝集成，同时为未来的功能扩展预留了空间。
