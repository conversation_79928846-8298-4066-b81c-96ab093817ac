# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

星链·IRM平台 - 基于先进AI技术的上市公司智能化产品和服务平台，专注于资本市场智能解决方案。

## 常用命令

### 开发命令
- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建项目（注意：开发阶段禁止运行）
- `pnpm start` - 启动生产服务器
- `pnpm lint` - 运行代码检查（使用Biome）
- `pnpm format` - 格式化代码（使用Biome）
- `pnpm clean` - 清理构建缓存

### 测试命令
- `pnpm e2e` - 运行端到端测试（Playwright UI模式）
- `pnpm e2e:ci` - 运行端到端测试（CI模式）
- `pnpm type-check` - TypeScript类型检查

### 分支管理命令
- `pnpm todev` - 合并到dev分支
- `pnpm fdev` - 从dev分支合并

## 技术栈架构

### 核心技术栈
- **前端框架**: Next.js 15.2.3 (App Router)
- **React版本**: 19.0.0
- **样式**: Tailwind CSS 4.0.17
- **状态管理**: Jotai 2.12.1, TanStack Query 5.66.9
- **UI组件**: Radix UI + Ant Design 5.25.2
- **表单处理**: React Hook Form + Zod
- **认证**: Better Auth 1.1.21
- **国际化**: next-intl 3.26.5
- **包管理**: pnpm 9.3.0
- **构建工具**: Turbo 2.4.4

### 项目结构
这是一个monorepo项目，使用Turbo进行构建管理：

```
supstar/
├── apps/web/           # Next.js Web应用
├── packages/           # 共享包
│   ├── api/           # API层和路由
│   ├── auth/          # 认证系统
│   ├── database/      # 数据库（Prisma）
│   ├── i18n/          # 国际化
│   ├── payments/      # 支付系统
│   ├── storage/       # 存储系统
│   ├── utils/         # 工具函数
│   └── ...
├── tooling/           # 开发工具配置
└── docs/             # 项目文档
```

### 核心业务模块
1. **股东名册管理** (`shareholder-registry`) - 股东数据管理、分析、导入导出
2. **投资人管理** (`investor-management`) - 投资机构、基金管理
3. **会议系统** (`meeting`) - 会议创建、管理、文档处理
4. **AI分析** (`ai`) - 智能分析和报告生成
5. **组织管理** (`organizations`) - 多组织架构支持

### API架构
- 基于Hono框架的API路由系统
- 中间件架构：认证、CORS、日志、n8n代理等
- 股东数据加密传输（使用AES加密）

### 数据库
- 使用Prisma ORM
- 支持多租户架构
- 包含股东、组织、会议等核心业务实体

## 开发规范

### 代码风格
- 使用Biome进行代码格式化和检查
- TypeScript严格模式
- 组件使用函数式组件和Hooks

### 命名规范
- **强制英文命名**: 函数、变量、参数必须使用有意义的英文命名
- **函数命名**: 动词开头，描述具体功能（如getUserProfile）
- **变量命名**: 描述性英文，布尔值使用is/has/can前缀
- **组件命名**: PascalCase，使用有意义的名称

### 日志规范
- **强制中文日志**: 所有日志输出必须使用中文
- **环境级别控制**: 使用APP_LOG_LEVEL环境变量
- **结构化日志**: 单行输出，包含traceId和errorCode
- **敏感信息保护**: 统一脱敏处理token/secret/key/email/phone

### 错误处理
- 用户界面必须提供"可操作+可闭环"的错误提示
- 禁止使用"暂无数据"、"出错了"等无行动描述
- 提供统一支持入口（通过NEXT_PUBLIC_SUPPORT_URL环境变量）

### 安全要求
- 股东数据使用AES加密传输
- API路由需要认证和授权
- 禁止在前端暴露敏感信息
- 输入验证和SQL注入防护

### 测试要求
- 使用Playwright进行端到端测试
- 测试文件放在tests/目录或组件__tests__/目录
- 必须提供具体的测试数据和覆盖率报告

## 环境配置

### 关键环境变量
- `NEXT_PUBLIC_SHAREHOLDER_API_KEY` - 股东API密钥
- `NEXT_PUBLIC_SHAREHOLDER_API_SECRET` - 股东API密钥
- `NEXT_PUBLIC_SHAREHOLDER_API_IV` - 股东API初始化向量
- `APP_LOG_LEVEL` - 日志级别控制
- `NEXT_PUBLIC_SUPPORT_URL` - 统一支持入口

### 开发环境设置
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm e2e
```

## 特殊说明

### 股东名册功能
- 支持多种文件格式导入（DBF、Excel、ZIP等）
- 股东类型自动分类和匹配
- 股权变化分析和趋势图表
- 支持中登数据格式

### AI集成
- 使用OpenAI GPT模型进行智能分析
- 会议纪要自动生成
- 股东结构智能分析
- 投资建议生成

### n8n集成
- 与n8n工作流引擎集成
- 自动化数据处理和报告生成
- 外部系统数据同步

## 注意事项

- 开发阶段禁止运行`pnpm build`命令
- 所有敏感信息必须使用环境变量管理
- 代码提交前必须通过lint检查
- 新功能需要编写对应的测试用例
- 遵循Cursor代码评审规则（见.cursor/rules/codereview.mdc）