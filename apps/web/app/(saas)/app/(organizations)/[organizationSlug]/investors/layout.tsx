import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { redirect } from "next/navigation"; // 页面重定向功能
import { getTranslations } from "next-intl/server"; // 国际化翻译功能

/**
 * 路由参数接口定义
 * @interface Params
 * @description 定义市值管理布局组件接收的路由参数结构
 */
interface Params {
	organizationSlug: string; // 组织标识符
}

/**
 * 市值管理布局组件的Props接口
 * @interface MarketValueLayoutProps
 * @description 定义布局组件的属性结构
 */
interface MarketValueLayoutProps {
	children: React.ReactNode; // 子组件内容
	params: Promise<Params>; // 路由参数（异步获取）
}

/**
 * 市值管理模块的布局组件
 * @description 为市值管理相关页面提供统一的布局结构
 * 该组件负责用户身份验证、国际化初始化和页面布局渲染
 * 采用简化设计，直接显示子组件内容，不包含复杂的导航结构
 *
 * @param {MarketValueLayoutProps} props - 组件属性
 * @param {React.ReactNode} props.children - 需要在布局中渲染的子组件
 * @param {Promise<Params>} props.params - 包含组织标识符的路由参数
 *
 * @returns {Promise<JSX.Element>} 返回布局组件的JSX元素
 *
 * @throws {redirect} 当用户未登录时，重定向到登录页面
 *
 * @example
 * // 在投资人管理相关页面中使用
 * <InvestorLayout params={params}>
 *   <InvestorMonitoringPage />
 * </InvestorLayout>
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @version 1.0.0
 *
 * @history
 * - 原版本可能包含复杂的导航菜单和面包屑
 * - 当前版本采用简化设计，专注于内容展示
 * - 保留了用户身份验证和国际化支持
 * - 移除了面包屑导航，提供更简洁的用户体验
 */
export default async function MarketValueLayout({
	children,
	params
}: MarketValueLayoutProps) {
	// 获取用户会话信息，用于身份验证
	const session = await getSession();

	// 用户身份验证检查
	// 如果用户未登录，重定向到登录页面确保安全性
	if (!session) {
		return redirect("/auth/login");
	}

	// 获取国际化翻译功能（为未来的功能扩展做准备）
	const t = await getTranslations("market-value.menu");

	// 解构获取组织标识符（为未来的组织相关功能做准备）
	const { organizationSlug } = await params;

	// 渲染简化的布局结构
	// 直接显示子组件内容，不包含导航菜单或面包屑
	// 这种设计提供了更专注的用户体验
	return <>{children}</>;
}