import { getActiveOrganization } from "@saas/auth/lib/server";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { notFound } from "next/navigation";
import { InvestorSearchBubble } from "@saas/investors/components/InvestorSearchBubble";

/**
 * 生成页面元数据
 */
export async function generateMetadata() {

	return {
		title: "投资人",
	};
}

/**
 * 投资人主页面
 * 直接显示投资人列表
 */
export default async function InvestorsPage({
	params,
}: { params: Promise<{ organizationSlug: string }> }) {
	const { organizationSlug } = await params;

	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	if (!activeOrganization) {
		return notFound();
	}

	return (
		<SettingsList>
			<InvestorSearchBubble/>
		</SettingsList>
	);
}
