/**
 * 投资者筛选页面组件
 *
 * @fileoverview 投资者管理模块的主要筛选页面，提供投资者数据的查看、筛选和管理功能
 * <AUTHOR>
 * @since 2025-07-24
 * @version 1.0.0
 *
 * 功能特性：
 * - 投资者数据展示和筛选
 * - 支持收藏功能
 * - 联系人管理
 * - 响应式设计
 * - 实时数据更新
 *
 * 路由信息：
 * - 路径: /app/[organizationSlug]/investors/screen
 * - 参数: organizationSlug - 组织标识符
 * - 权限: 需要组织成员权限
 */

import ContactsMockList from "@saas/investors/components/ContactsMockList";

/**
 * 投资者筛选页面
 *
 * 这是投资者管理模块的主要页面，通过ContactsMockList组件提供完整的投资者数据管理功能。
 * 页面支持基于组织的数据隔离，确保用户只能访问所属组织的投资者信息。
 *
 * @param props - 页面组件属性
 * @param props.params - 路由参数Promise对象
 * @param props.params.organizationSlug - 组织标识符，用于数据隔离和权限控制
 * @returns 渲染的投资者筛选页面
 *
 * @example
 * ```
 * // 访问路径示例
 * /app/my-company/investors/screen
 * ```
 *
 * 修改人：Miya
 * 修改日期：2025-07-24
 * 修改内容：添加页面组件注释说明
 */
export default async function MarketScreenPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;

	return (
		<ContactsMockList/>
	);
}