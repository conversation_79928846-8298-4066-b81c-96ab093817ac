/**
 * 股东名册分析页面 - 公司概览
 * @file page.tsx
 * @description 股东名册分析的主页面，显示公司概览
 * @created 2025-06-12 17:26:08
 */

// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { redirect } from "next/navigation"; // 页面重定向功能
import { ShareholderAnalyse } from "@saas/shareholder/components"; // 导入公司概览组件


/**
 * 生成页面元数据
 * 修改时间: 2025-07-16
 * 修改人：Miya
 */
export async function generateMetadata() {
	return {
		title: "股东分析",
	};
}




/**
 * 股东名册分析主页面组件 - 公司概览
 * 这是一个服务器组件，用于显示股东名册分析功能的主页面
 * @param params 页面参数，包含组织slug
 */
export default async function AnalysisOverviewPage({
	params,
}: {
	params: { organizationSlug: string };
}) {
	// 获取用户会话信息，用于验证用户是否已登录
	const session = await getSession();

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}
// 由于 Next.js 路由系统要求 params 需要被 await，我们使用 Promise.resolve
const { organizationSlug } = await Promise.resolve(params);
	// 渲染页面内容
	return (
		<SettingsList>
			<ShareholderAnalyse organizationSlug={organizationSlug} />
		</SettingsList>
	);
}