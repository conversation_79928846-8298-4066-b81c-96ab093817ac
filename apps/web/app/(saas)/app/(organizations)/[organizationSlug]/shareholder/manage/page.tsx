// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { redirect } from "next/navigation"; // 页面重定向功能
import { ShareholderRegistryManage } from "@saas/shareholder/components/ShareholderRegistryManage";
import { Suspense } from "react";
import { ShareholderRegistrySkeleton } from "@saas/shareholder/components/ShareholderRegistrySkeleton";

/**
 * 生成页面元数据
 * 修改时间: 2025-07-16
 * 修改人：Miya
 */
export async function generateMetadata() {
	return {
		title: "名册管理",
	};
}




/**
 * 股东名册管理页面组件
 * 这是一个服务器组件，加载股东名册管理功能
 */
export default async function ShareholderRegistryManagePage({
	params
}: {
	params: { organizationSlug: string }
}) {
	// 获取用户会话信息，用于验证用户是否已登录
	const session = await getSession();

	// 如果用户未登录，重定向到登录页面
	if (!session) {
		return redirect("/auth/login");
	}
  
// 由于 Next.js 路由系统要求 params 需要被 await，我们使用 Promise.resolve
	const { organizationSlug } = await Promise.resolve(params);

	return (
		<SettingsList>
			<Suspense fallback={<ShareholderRegistrySkeleton />}>
				<ShareholderRegistryManage
					organizationSlug={organizationSlug}
				/>
			</Suspense>
		</SettingsList>
	);
}