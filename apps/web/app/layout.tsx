import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";

export const metadata: Metadata = {
	/**
	 * 网站标题配置 - 控制浏览器标签页标题和SEO标题显示
	 *
	 * 工作原理：
	 * - Next.js 会根据页面层级和配置自动生成最终标题
	 * - 子页面可以通过 metadata.title 覆盖或扩展这些配置
	 *
	 * 配置说明：
	 * - absolute: 绝对标题，当页面设置 absolute 时直接使用，不会应用 template
	 * - default: 默认标题，当页面没有设置 title 时使用
	 * - template: 标题模板，%s 占位符会被页面标题替换
	 *
	 * 示例效果：
	 * - 首页：显示 "星链" (使用 default)
	 * - 登录页面设置 title: "登录"：显示 "登录 | 星链" (使用 template)
	 * - 某页面设置 title: { absolute: "独立标题" }：显示 "独立标题" (使用 absolute)
	 *
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加详细注释说明 title 配置的作用和工作原理
	 */
	title: {
		absolute: "星链资本", // 绝对标题 - 当页面明确指定绝对标题时使用
		default: "星链资本", // 默认标题 - 当页面没有指定标题时的后备标题
		template: "%s - 星链资本", // Miya修改，2025/7/22 标题模板 - %s 会被页面标题替换，形成" 页面标题 - 星链资本"格式
	},
	description:
		"星链资本 - 专业的 AI IRM 平台，拥有全球超 40000 家投资机构资源，上市公司在这里寻找和联系最合适的投资人",
};

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
