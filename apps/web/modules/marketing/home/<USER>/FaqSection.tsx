import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";

export function FaqSection({ className }: { className?: string }) {
	const t = useTranslations();

	const items = [
		{
			question: "平台覆盖哪些类型的投资机构？",
			answer: "我们的平台覆盖全球超过 4 万家投资机构，包括私募股权基金(PE)、风险投资基金(VC)、家族办公室、主权基金、对冲基金等各类机构投资者。",
		},
		{
			question: "AI 匹配算法是如何工作的？",
			answer: "我们的 AI 算法基于机器学习技术，分析投资者的历史投资偏好、行业关注度、投资阶段、地理位置等多维度数据，为您的公司精准匹配最适合的潜在投资人。",
		},
		{
			question: "股东名册分析能提供什么洞察？",
			answer: "通过深度分析股东交易行为、持股变化趋势、机构投资者画像等，我们能帮助您预测潜在的持股变化，识别投资者关系风险，并制定针对性的沟通策略。",
		},
		{
			question: "如何开始使用智能路演功能？",
			answer: "您只需上传参会人员名单和路演材料，系统会根据目标投资者的偏好自动生成会议筹备内容，并智能设计问答预案，会议结束后会自动梳理会议内容，生成结构化会议纪要和问答记录。",
		},
	];

	if (!items) {
		return null;
	}

	return (
		<section
			className={cn("scroll-mt-20 border-t py-12 lg:py-16", className)}
			id="faq"
		>
			<div className="container max-w-5xl">
				<div className="mb-12 lg:text-center">
					<h1 className="mb-2 font-bold text-4xl lg:text-5xl">
						{t("faq.title")}
					</h1>
					<p className="text-lg opacity-50">{t("faq.description")}</p>
				</div>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{items.map((item, i) => (
						<div
							key={`faq-item-${i}`}
							className="rounded-lg bg-card border p-4 lg:p-6"
						>
							<h4 className="mb-2 font-semibold text-lg">
								{item.question}
							</h4>
							<p className="text-foreground/60">{item.answer}</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
}
