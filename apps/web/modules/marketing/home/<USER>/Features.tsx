"use client";

import { MobileIcon } from "@radix-ui/react-icons";
import { cn } from "@ui/lib";
import {
	BrainCircuitIcon,
	CloudIcon,
	ComputerIcon,
	SearchIcon,
	StarIcon,
	UsersIcon,
	VideoIcon,
	WandIcon,
} from "lucide-react";
import Image, { type StaticImageData } from "next/image";
import { type JSXElementConstructor, type ReactNode, useState } from "react";
import heroImage from "../../../../public/images/hero.svg";

export const featureTabs: Array<{
	id: string;
	title: string;
	icon: JSXElementConstructor<any>;
	subtitle?: string;
	description?: ReactNode;
	image?: StaticImageData;
	imageBorder?: boolean;
	stack?: {
		title: string;
		href: string;
		icon: JSXElementConstructor<any>;
	}[];
	highlights?: {
		title: string;
		description: string;
		icon: JSXElementConstructor<any>;
		demoLink?: string;
		docsLink?: string;
	}[];
}> = [
	{
		id: "investor-discovery",
		title: "投资人挖掘",
		icon: SearchIcon,
		subtitle: "基于 AI 算法精准匹配全球投资机构",
		description:
			"利用先进的人工智能技术，从全球 4 万+ 投资机构中智能筛选和匹配最适合您公司的潜在投资人，大幅提升融资效率。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "全球机构覆盖",
				description:
					"覆盖全球超过 4 万家投资机构，包括 PE、VC、家族办公室、主权基金等各类投资者。",
				icon: WandIcon,
			},
			{
				title: "智能匹配算法",
				description:
					"基于机器学习算法分析投资偏好、行业关注度、投资阶段等多维度数据进行精准匹配。",
				icon: BrainCircuitIcon,
			},
			{
				title: "实时数据更新",
				description:
					"投资机构信息实时更新，确保您获得的联系信息和投资动态都是最新最准确的。",
				icon: CloudIcon,
			},
		],
	},
	{
		id: "shareholder-analysis",
		title: "股东名册分析",
		icon: UsersIcon,
		subtitle: "深度解析股东结构与持股变化",
		description: "通过 AI 技术深度分析股东名册数据，为投资者关系管理提供数据驱动的洞察和决策支持。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "股东行为分析",
				description:
					"智能分析股东买卖行为模式，预测潜在的持股变化趋势，帮助制定针对性的投资者关系策略。",
				icon: StarIcon,
			},
			{
				title: "机构投资者画像",
				description:
					"为每个机构投资者建立详细画像，包括投资风格、持股偏好、交易习惯等关键信息。",
				icon: ComputerIcon,
			},
			{
				title: "风险预警系统",
				description:
					"实时监控异常交易和持股变化，提前识别潜在的投资者关系风险并提供应对建议。",
				icon: MobileIcon,
			},
		],
	},
	{
		id: "smart-roadshow",
		title: "智能路演会议",
		icon: VideoIcon,
		subtitle: "自动化路演安排与管理",
		description:
			"通过智能化系统自动安排和管理路演会议，提升沟通效率，确保与关键投资者的有效互动。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "智能会前准备",
				description:
					"自动分析参会者背景，智能生成会议规划和问答预案，确保会议高效有序进行。",
				icon: WandIcon,
			},
			{
				title: "智能电子签",
				description:
					"集成智能电子签名功能，支持会议相关文件的在线签署，提升合规性和效率。",
				icon: ComputerIcon,
			},
			{
				title: "智能会议纪要",
				description:
					"自动整理会议内容、问答记录和待办事项，生成结构化会议纪要，便于后续跟进。",
				icon: MobileIcon,
			},
		],
	},
];

export function Features() {
	const [selectedTab, setSelectedTab] = useState(featureTabs[0].id);
	return (
		<section id="features" className="scroll-my-20 pt-12 lg:pt-16">
			<div className="container max-w-5xl">
				<div className="mx-auto mb-6 lg:mb-0 lg:max-w-5xl lg:text-center">
					<h2 className="font-bold text-4xl lg:text-5xl">
						三大核心功能
					</h2>
					<p className="mt-6 text-balance text-lg opacity-50">
						基于人工智能技术，为上市公司提供全方位的投资者关系管理解决方案，
						帮助企业更高效地连接和管理投资者关系。
					</p>
				</div>

				<div className="mt-8 mb-4 hidden justify-center lg:flex">
					{featureTabs.map((tab) => {
						return (
							<button
								type="button"
								key={tab.id}
								onClick={() => setSelectedTab(tab.id)}
								className={cn(
									"flex w-24 flex-col items-center gap-2 rounded-lg px-4 py-2 md:w-32",
									selectedTab === tab.id
										? "bg-primary/5 font-bold text-primary dark:bg-primary/10"
										: "font-medium text-foreground/80",
								)}
							>
								<tab.icon
									className={cn(
										"size-6 md:size-8",
										selectedTab === tab.id
											? "text-primary"
											: "text-foreground opacity-30",
									)}
								/>
								<span className="text-xs md:text-sm">
									{tab.title}
								</span>
							</button>
						);
					})}
				</div>
			</div>

			<div>
				<div className="container max-w-5xl">
					{featureTabs.map((tab) => {
						const filteredStack = tab.stack || [];
						const filteredHighlights = tab.highlights || [];
						return (
							<div
								key={tab.id}
								className={cn(
									"border-t py-8 first:border-t-0 md:py-12 lg:border lg:first:border-t lg:rounded-3xl lg:p-6",
									selectedTab === tab.id
										? "block"
										: "block lg:hidden",
								)}
							>
								<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-12">
									<div>
										<h3 className="font-normal text-2xl text-foreground/60 leading-normal md:text-3xl">
											<strong className="text-secondary">
												{tab.title}.{" "}
											</strong>
											{tab.subtitle}
										</h3>

										{tab.description && (
											<p className="mt-4 text-foreground/60">
												{tab.description}
											</p>
										)}

										{filteredStack?.length > 0 && (
											<div className="mt-4 flex flex-wrap gap-6">
												{filteredStack.map(
													(tool, k) => (
														<a
															href={tool.href}
															target="_blank"
															key={`stack-tool-${k}`}
															className="flex items-center gap-2"
															rel="noreferrer"
														>
															<tool.icon className="size-6" />
															<strong className="block text-sm">
																{tool.title}
															</strong>
														</a>
													),
												)}
											</div>
										)}
									</div>
									<div>
										{tab.image && (
											<Image
												src={tab.image}
												alt={tab.title}
												className={cn(
													" h-auto w-full max-w-xl",
													{
														"rounded-2xl border-4 border-secondary/10":
															tab.imageBorder,
													},
												)}
											/>
										)}
									</div>
								</div>

								{filteredHighlights.length > 0 && (
									<div className="mt-8 grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
										{filteredHighlights.map(
											(highlight, k) => (
												<div
													key={`highlight-${k}`}
													className="flex flex-col items-stretch justify-between rounded-xl bg-card border p-4"
												>
													<div>
														<highlight.icon
															className="text-primary text-xl"
															width="1em"
															height="1em"
														/>
														<strong className="mt-2 block">
															{highlight.title}
														</strong>
														<p className="mt-1 text-sm opacity-50">
															{
																highlight.description
															}
														</p>
													</div>
												</div>
											),
										)}
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
		</section>
	);
}
