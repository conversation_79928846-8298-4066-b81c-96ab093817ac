"use client";

import { But<PERSON> } from "@ui/components/button";
import { KeyIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function Newsletter() {
	const t = useTranslations();

	const handleApplyExperience = () => {
		// 跳转到飞书表单 - Alson
		window.open(
			"https://starlinkcap.feishu.cn/share/base/form/shrcnTHWyu2QNbMajhLJSPnWldb?prefill_%E9%9C%80%E6%B1%82=%E7%94%B3%E8%AF%B7%E4%BA%A7%E5%93%81%E4%BD%93%E9%AA%8C",
			"_blank"
		);
	};

	return (
		<section className="py-16">
			<div className="container">
				<div className="mb-8 text-center">
					<KeyIcon className="mx-auto mb-3 size-8 text-primary" />
					<h1 className="font-bold text-3xl lg:text-4xl">
						{t("newsletter.title")}
					</h1>
					<p className="mt-3 text-lg opacity-70">
						{t("newsletter.subtitle")}
					</p>
				</div>

				<div className="flex justify-center">
					<Button
						onClick={handleApplyExperience}
						className="px-8 py-3 text-lg"
						size="lg"
					>
						{t("newsletter.submit")}
					</Button>
				</div>
			</div>
		</section>
	);
}
