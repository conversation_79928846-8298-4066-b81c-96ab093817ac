import { LocaleLink } from "@i18n/routing";
import { Logo } from "@shared/components/Logo";

export function Footer() {
	return (
		<footer className="border-t py-8 text-foreground/60 text-sm">
			<div className="container grid grid-cols-1 gap-6 lg:grid-cols-2">
				<div>
					<Logo className="opacity-70 grayscale" />
					<p className="mt-3 text-sm opacity-70">
						全球首个 AI IRM 投资者关系管理平台。
					</p>
					{/* 修改块开始: 增加备案信息（保留原公司名称）
					 * 修改范围: 在公司名称下方增加ICP备案号信息，原内容保留
					 * 修改时间: 2024-06-09
					 * 对应计划步骤: 1
					 * 恢复方法: 删除此修改块内所有代码，恢复下方被注释的原代码
					 */}
					<p className="mt-2 text-sm opacity-70">
						<a
							href="https://starlinkcap.cn"
							className="hover:opacity-100"
						>
							星链资本科技有限公司
						</a>
					</p>
					<p className="mt-1 text-sm opacity-70">
						<a
							href="mailto:<EMAIL>"
							className="hover:opacity-100"
						>
							<EMAIL>
						</a>
					</p>
					<p className="mt-1 text-xs opacity-70">
						<a
							href="https://beian.miit.gov.cn/"
							target="_blank"
							rel="noopener noreferrer"
							className="hover:opacity-100"
						>
							粤ICP备2025407273号-1
						</a>
					</p>
				</div>

				{/* 暂时屏蔽的导航链接 */}
				{/* <div className="flex flex-col gap-2">
					<LocaleLink href="/blog" className="block">
						Blog
					</LocaleLink>

					<a href="#features" className="block">
						Features
					</a>

					<a href="/#pricing" className="block">
						Pricing
					</a>
				</div> */}

				<div className="flex flex-col gap-2">
					<LocaleLink
						href="/legal/privacy-policy"
						className="block hover:opacity-100"
					>
						隐私政策
					</LocaleLink>

					<LocaleLink
						href="/legal/terms"
						className="block hover:opacity-100"
					>
						用户协议
					</LocaleLink>

					<a
						href="https://starlinkcap.feishu.cn/share/base/form/shrcnTHWyu2QNbMajhLJSPnWldb?prefill_%E9%9C%80%E6%B1%82=%E8%81%94%E7%B3%BB%E6%88%91%E4%BB%AC"
						target="_blank"
						rel="noopener noreferrer"
						className="block hover:opacity-100"
					>
						联系我们
					</a>
				</div>
			</div>
		</footer>
	);
}
