/**
 * 联系人对话框组件
 *
 * @fileoverview 提供联系人信息的增删改查功能，支持实时搜索、表单验证和状态管理
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.1
 * @modified Miya 2025-07-24 - 为 handleSave 函数添加 toast 错误提示，确保手动保存操作的错误也能显示
 *
 * 功能特性：
 * - 联系人CRUD操作（创建、读取、更新、删除）
 * - 实时搜索功能
 * - 表单验证（姓名必填、手机号必填、邮箱格式、电话格式）
 * - 编辑状态管理
 * - 错误处理和用户反馈（包含 toast 错误提示）
 * - 响应式设计
 * - 加载状态和骨架屏
 * - 动画效果支持
 * @modified Miya 2025-08-01 - 手机号改为必填字段
 */

import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { Search, SquarePlus } from "lucide-react";
import {
  Card,
  CardContent,
} from "@ui/components/card";
import { Edit, X, Save, Trash2 } from "lucide-react";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
  useContacts,
  useCreateContact,
  useUpdateContact,
  useDeleteContact
} from "../hooks/useContacts";
import type { Contact } from "../lib/contacts_data";
import { Tooltip, TooltipProvider, TooltipContent, TooltipPortal, TooltipTrigger } from "@ui/components/tooltip";
import { toast } from "sonner";

/**
 * 构建API请求参数的工具函数
 * @param cardCode 基金代码
 * @param unifiedAccountId 一码通账号
 * @returns 参数对象
 * <AUTHOR>
 * @created 2025-08-11 - 提取重复的参数处理逻辑，提高代码复用性
 */
const buildApiParams = (cardCode?: string, unifiedAccountId?: string) => {
  // 根据API要求，fundCode和unifiedAccountId两者不可缺一
  // 如果有cardCode（基金代码），则传递fundCode
  // 如果没有cardCode但有unifiedAccountId（股东账号），则传递unifiedAccountId
  return cardCode ? { fundCode: cardCode } : { unifiedAccountId };
};

/**
 * 验证邮箱格式
 *
 * @param email - 待验证的邮箱地址
 * @returns 是否为有效邮箱格式，空字符串被认为是有效的
 */
function isValidEmail(email: string): boolean {
  if (!email || email.trim() === "") {
    return true; // 空邮箱是允许的
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}



/**
 * 本地联系人状态类型
 *
 * 扩展基础Contact接口，添加编辑状态和动画状态管理
 */
interface LocalContact extends Contact {
  /** 是否处于编辑状态 */
  isEditing: boolean;
  /** 是否正在执行新增动画 */
  isAnimating?: boolean;
}

/**
 * 联系人对话框组件的ref接口
 *
 * 定义父组件可以调用的方法
 */
export interface ContactsDialogRef {
  /** 重新获取联系人数据的方法 */
  refetch: () => void;
}

/**
 * 联系人对话框组件属性接口
 * @modified hayden 2025-08-07 18:16:53 - 添加unifiedAccountId参数支持一码通账号查询，根据API要求unifiedAccountId和fundCode两者不可缺一
 */
interface ContactsDialogProps {
  /** 当前选中的卡片代码，用于触发每次切换卡片时的查询 */
  cardCode?: string;
  /** 一码通账户ID，用于标识个人投资者 */
  unifiedAccountId?: string;
}

/**
 * 联系人对话框组件
 *
 * 提供联系人管理的完整功能，包括搜索、新增、编辑、删除等操作
 *
 * @param props - 组件属性
 * @param ref - 组件引用，暴露refetch方法给父组件
 * @returns 联系人对话框组件
 *
 * @example
 * ```tsx
 * const contactsRef = useRef<ContactsDialogRef>(null);
 *
 * <ContactsDialog
 *   ref={contactsRef}
 *   cardCode={selectedCard?.code}
 *   unifiedAccountId={selectedCard?.unifiedAccountId}
 * />
 * ```
 */
export const ContactsDialog = forwardRef<ContactsDialogRef, ContactsDialogProps>(({ cardCode, unifiedAccountId }, ref) => {
  const { activeOrganization } = useActiveOrganization();



  // 搜索关键字状态
  const [search, setSearch] = useState("");
  // 错误提示状态
  const [errorMessage, setErrorMessage] = useState("");
  // 错误类型状态，用于控制不同的消失逻辑
  const [errorType, setErrorType] = useState<"" | "name_required" | "save_first" | "email_invalid">("");
  // 本地联系人状态（包含编辑状态）
  const [localContacts, setLocalContacts] = useState<LocalContact[]>([]);
  // 需要保存的联系人ID（用于让保存按钮变红）
  const [contactNeedsSave, setContactNeedsSave] = useState<string>("");
  // 姓名输入框错误状态（用于让输入框变红）
  const [nameFieldError, setNameFieldError] = useState<string>("");
  // 电话号码输入框错误状态
  const [phoneFieldError, setPhoneFieldError] = useState<string>("");
  // 邮箱输入框错误状态
  const [emailFieldError, setEmailFieldError] = useState<string>("");
  // 等待删除确认的联系人ID
  const [pendingDeleteId, setPendingDeleteId] = useState<string>("");

  const organizationId = activeOrganization?.id || "";

  /**
   * 获取联系人列表
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：添加错误处理，显示查询失败的具体错误信息
   * @modified hayden 2025-08-07 18:16:53 - 根据API要求，unifiedAccountId和fundCode两者不可缺一，如果有selectedCard?.code基金代码那么传基金代码，如果selectedCard?.code没有说明是需要使用shareholderAccount的
   */
  const { data: contactsData, isLoading, refetch, error: queryError, isError } = useContacts(
    {
      organizationId,
      cardCode, // 传入卡片代码，每次切换卡片时触发新查询
      // 修改记录 2025-08-11 - 使用提取的工具函数，减少代码重复 - hayden
      ...buildApiParams(cardCode, unifiedAccountId),
      name: search, // 将search作为name参数进行模糊搜索
      page: 1,
      limit: 100,
      queryAllContacts: false, // 使用分页获取联系人数据
    },
    {
      enabled: !!organizationId && (!!cardCode || !!unifiedAccountId), // 确保至少有一个标识符
    }
  );

  /**
   * 处理查询后端返回错误信息
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：使用toast显示错误信息，参考MeetingList组件的错误处理方式
   */
  useEffect(() => {
    if (isError && queryError) {
      const errorMsg =
							queryError.message ||
							"获取联系人列表失败，请联系管理员寻求帮助";
      toast.error(`获取联系人列表错误：${errorMsg}`);
    }
  }, [isError, queryError]);

  // 暴露 refetch 方法给父组件
  useImperativeHandle(ref, () => ({
    refetch
  }), [refetch]);



  /**
   * 创建联系人 mutation
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：使用toast显示错误信息，参考MeetingList组件的错误处理方式
   */
  const createContactMutation = useCreateContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      // 使用toast显示后端返回的具体错误信息
      const errorMsg = error.message || "创建联系人失败，请联系管理员寻求帮助";
      toast.error(`创建联系人错误：${errorMsg}`);
      console.error("创建联系人失败:", error);
    },
  });

  /**
   * 更新联系人 mutation
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：使用toast显示错误信息，参考MeetingList组件的错误处理方式
   */
  const updateContactMutation = useUpdateContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      // 使用toast显示后端返回的具体错误信息
      const errorMsg = error.message || "保存联系人失败，请联系管理员寻求帮助";
      toast.error(`更新联系人错误：${errorMsg}`);
      console.error("更新联系人失败:", error);
    },
  });

  /**
   * 删除联系人 mutation
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：优化错误处理，显示后端返回的具体错误信息
   */
  const deleteContactMutation = useDeleteContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      // 显示后端返回的具体错误信息
      const errorMsg = error.message || "删除联系人失败，请联系管理员寻求帮助";
      setErrorMessage(errorMsg);
      setErrorType("save_first");
      toast.error(`删除联系人错误：${errorMsg}`);
      console.error("删除联系人失败:", error);
    },
  });

  // 同步服务器数据到本地状态
  useEffect(() => {
    if (contactsData?.contacts) {
      setLocalContacts(prev => {
        // 保留正在动画的临时联系人
        const animatingTempContacts = prev.filter(c =>
          c.contactId.startsWith('temp_') && c.isAnimating
        );

        // 保留正在编辑的联系人的本地状态
        const editingContacts = prev.filter(c => c.isEditing);

        // 合并服务器数据，但保留正在编辑的联系人
        const serverContacts = contactsData.contacts.map(contact => {
          const editingContact = editingContacts.find(c => c.contactId === contact.contactId);
          if (editingContact) {
            // 如果正在编辑，保留编辑状态和本地数据
            return editingContact;
          }
          return {
            ...contact,
            isEditing: false,
          };
        });

        return [...animatingTempContacts, ...serverContacts];
      });
    }
  }, [contactsData]);

  /**
   * 处理搜索输入变化
   * <AUTHOR>
   * @date 2025-07-24
   * 修改内容：添加错误信息清除逻辑
   */
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setPendingDeleteId(""); // 清除等待删除状态
    // 清除错误信息，因为用户开始了新的操作
    if (errorMessage) {
      setErrorMessage("");
      setErrorType("");
    }
  };

  /**
   * 处理新增联系人
   * 新增联系人自动进入编辑状态，插入到列表首位
   */
  const handleAddContact = () => {
    if (!organizationId) {
      setErrorMessage("请先选择组织");
      setErrorType("save_first");
      return;
    }

    // 检查是否有未保存的联系人
    const unsavedContact = localContacts.find(contact => contact.isEditing);
    if (unsavedContact) {
      // 让保存按钮变红而不是弹窗
      setContactNeedsSave(unsavedContact.contactId);
      return;
    }

    // 清除之前的错误信息和状态
    setErrorMessage("");
    setErrorType("");
    setContactNeedsSave("");
    setNameFieldError("");
    setPendingDeleteId(""); // 清除等待删除状态

    // 生成临时ID（负数表示新建）
    const tempId = `temp_${Date.now()}`;
    const newContact: LocalContact = {
      contactId: tempId,
      organizationId,
      name: "",
      phoneNumber: "",
      email: "",
      address: "",
      remarks: "",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: "",
      updatedBy: "",
      isEditing: true,
      isAnimating: true,
    };

    setLocalContacts([newContact, ...localContacts]);

    // 动画完成后移除动画状态
    setTimeout(() => {
      setLocalContacts(prev => prev.map(c =>
        c.contactId === tempId
          ? { ...c, isAnimating: false }
          : c
      ));
    }, 300); // 300ms 动画时长
  };

  /**
   * 处理切换联系人编辑状态
   */
  const handleEdit = (contactId: string) => {
    // 检查是否有其他联系人正在编辑
    const editingContact = localContacts.find(contact => contact.isEditing && contact.contactId !== contactId);

    if (editingContact) {
      // 让正在编辑的联系人的保存按钮变红，而不是显示弹窗
      setContactNeedsSave(editingContact.contactId);
      return;
    }

    // 清除所有错误信息和状态，并进入编辑状态
    setErrorMessage("");
    setErrorType("");
    setContactNeedsSave("");
    setNameFieldError("");
    setPendingDeleteId(""); // 清除等待删除状态
    setLocalContacts(localContacts.map(c => c.contactId === contactId ? { ...c, isEditing: true } : c));
  };

  /**
   * 处理保存联系人编辑
   * @modified Miya 2025-08-01 - 添加手机号必填验证
   */
  const handleSave = async (contactId: string) => {
    if (!organizationId) {
      setErrorMessage("请先选择组织");
      setErrorType("save_first");
      return;
    }

    // 查找要保存的联系人
    const contactToSave = localContacts.find(c => c.contactId === contactId);

    // 如果找不到联系人，直接返回
    if (!contactToSave) {
      setErrorMessage("找不到要保存的联系人");
      setErrorType("save_first");
      return;
    }

    // 统一进行所有字段验证，收集所有错误
    // @modified Miya 2025-08-01 - 修改验证逻辑，确保所有字段都能同时显示错误
    let hasValidationError = false;

    // 验证姓名是否为空
    if (!contactToSave.name || contactToSave.name.trim() === "") {
      setNameFieldError(contactId);
      hasValidationError = true;
    } else {
      setNameFieldError(""); // 清除姓名错误状态
    }

    // 验证手机号是否为空（必填）
    if (!contactToSave.phoneNumber || contactToSave.phoneNumber.trim() === "") {
      setPhoneFieldError(contactId);
      hasValidationError = true;
    } else if (!/^\d{11}$/.test(contactToSave.phoneNumber.trim())) {
      // 验证电话号码格式（如果不为空）
      setPhoneFieldError(contactId);
      hasValidationError = true;
    } else {
      setPhoneFieldError(""); // 清除手机号错误状态
    }

    // 验证邮箱格式（如果有值且不为空字符串）
    if (contactToSave.email && contactToSave.email.trim() !== "" && !isValidEmail(contactToSave.email)) {
      setEmailFieldError(contactId);
      hasValidationError = true;
    } else {
      setEmailFieldError(""); // 清除邮箱错误状态
    }

    // 如果有任何验证错误，阻止保存
    if (hasValidationError) {
      setContactNeedsSave(""); // 清除保存按钮的红色状态
      return;
    }

    // 清除保存按钮和删除状态（字段错误状态已在验证中处理）
    setContactNeedsSave("");
    setPendingDeleteId(""); // 清除等待删除状态

    // 判断是新建还是更新
    const isNewContact = contactId.startsWith('temp_');

    try {
      if (isNewContact) {
        // 创建新联系人
        // @modified Miya 2025-07-31 - 添加fundCode参数，满足API验证要求
        // @modified Miya 2025-08-01 - 手机号为必填字段，不使用默认空值
        // @modified hayden 2025-08-07 18:16:53 - 根据API要求，fundCode和unifiedAccountId两者不可缺一，根据实际情况传递对应参数
        await createContactMutation.mutateAsync({
          organizationId,
          name: contactToSave.name,
          phoneNumber: contactToSave.phoneNumber.trim(),
          email: contactToSave.email?.trim() || "",
          address: contactToSave.address?.trim() || "",
          remarks: contactToSave.remarks?.trim() || "",
          // 修改记录 2025-08-11 - 使用提取的工具函数，减少代码重复 - hayden
          ...buildApiParams(cardCode, unifiedAccountId),
        });
      } else {
        // 更新现有联系人
        // @modified Miya 2025-07-31 - 添加fundCode参数，满足API验证要求
        // @modified Miya 2025-08-01 - 手机号为必填字段，不使用默认空值
        // @modified hayden 2025-08-07 18:16:53 - 根据API要求，fundCode和unifiedAccountId两者不可缺一，根据实际情况传递对应参数
        await updateContactMutation.mutateAsync({
          contactId: contactToSave.contactId,
          organizationId,
          name: contactToSave.name,
          phoneNumber: contactToSave.phoneNumber.trim(),
          email: contactToSave.email?.trim() || "",
          address: contactToSave.address?.trim() || "",
          remarks: contactToSave.remarks?.trim() || "",
          // 修改记录 2025-08-11 - 使用提取的工具函数，减少代码重复 - hayden
          ...buildApiParams(cardCode, unifiedAccountId),
        });
      }

      // 保存成功后更新本地状态并退出编辑状态
      // @modified Miya 2025-08-01 - 手机号为必填字段，不使用默认空值
      setLocalContacts(localContacts.map(c =>
        c.contactId === contactId ? {
          ...c,
          isEditing: false,
          // 更新为保存后的值（处理空字符串为空值）
          phoneNumber: contactToSave.phoneNumber.trim(),
          email: contactToSave.email?.trim() || "",
          address: contactToSave.address?.trim() || "",
          remarks: contactToSave.remarks?.trim() || "",
        } : c
      ));
    } catch (error: any) {
      /**
       * 错误处理优化
       * <AUTHOR>
       * @date 2025-07-24
       * 修改内容：捕获并显示具体的错误信息，确保用户能看到后端返回的错误详情
       * @modified Miya 2025-07-24 - 添加 toast 错误提示，确保手动保存操作的错误也能通过 toast 显示
       */
      const errorMsg = error?.message || (isNewContact ? "创建联系人失败" : "更新联系人失败");
      setErrorMessage(errorMsg);
      setErrorType("save_first");

      // 添加 toast 错误提示
      toast.error(`${isNewContact ? "创建" : "保存"}联系人错误：${errorMsg}`);
      console.error(`${isNewContact ? "创建" : "保存"}联系人失败:`, error);
    }
  };

  /**
   * 处理删除联系人 - 二次确认删除
   * <AUTHOR>
   * @date 2025-07-23
   */
  const handleDelete = async (contactId: string) => {
    if (!organizationId) {
      setErrorMessage("组织id为空");
      setErrorType("save_first");
      return;
    }

    // 如果当前联系人不在等待删除状态，则进入等待删除状态
    if (pendingDeleteId !== contactId) {
      setPendingDeleteId(contactId);
      return;
    }

    // 如果已经在等待删除状态，则执行真正的删除
    const isNewContact = contactId.startsWith('temp_');

    try {
      if (!isNewContact) {
        // 如果是已存在的联系人，调用删除API
        await deleteContactMutation.mutateAsync({
          contactId,
          organizationId,
        });
      } else {
        // 如果是新建的联系人，直接从本地状态删除
        setLocalContacts(prev => prev.filter(c => c.contactId !== contactId));
      }

      // 清除与该联系人相关的错误状态
      if (nameFieldError === contactId) {
        setNameFieldError("");
      }
      if (phoneFieldError === contactId) {
        setPhoneFieldError("");
      }
      if (emailFieldError === contactId) {
        setEmailFieldError("");
      }
      if (contactNeedsSave === contactId) {
        setContactNeedsSave("");
      }

      // 清除等待删除状态
      setPendingDeleteId("");
    } catch (error: any) {
      /**
       * 删除联系人错误处理优化
       * <AUTHOR>
       * @date 2025-07-24
       * 修改内容：捕获并显示具体的错误信息，确保用户能看到后端返回的错误详情
       */
      const errorMsg = error?.message || "删除联系人失败，请重试";
      setErrorMessage(errorMsg);
      setErrorType("save_first");
      // 删除失败时也要清除等待删除状态
      setPendingDeleteId("");
    }
  };

  /**
   * 处理联系人字段变化
   */
  const handleFieldChange = (contactId: string, field: keyof LocalContact, value: string) => {
    // 如果是姓名字段，清除相关错误状态
    if (field === "name") {
      if (errorType === "name_required") {
        setErrorMessage("");
        setErrorType("");
      }
      // 清除姓名输入框的红色状态
      if (nameFieldError === contactId) {
        setNameFieldError("");
      }
    }

    // 如果是电话号码字段，清除错误状态（不进行实时校验）
    if (field === "phoneNumber") {
      if (phoneFieldError === contactId) {
        setPhoneFieldError("");
      }
    }

    // 如果是邮箱字段，清除错误状态（不进行实时校验）
    if (field === "email") {
      if (emailFieldError === contactId) {
        setEmailFieldError("");
      }
    }

    setLocalContacts(localContacts.map(c =>
      c.contactId === contactId ? { ...c, [field]: value } : c
    ));
  };

  // 模糊搜索过滤联系人（搜索在服务端进行，这里只做本地显示过滤）
  const filteredContacts = localContacts.filter(c => {
    const keyword = search.trim().toLowerCase();
    if (!keyword) {return true;}
    return (
      c.name.toLowerCase().includes(keyword) ||
      c.phoneNumber.toLowerCase().includes(keyword) ||
      c.email.toLowerCase().includes(keyword)
    );
  });

  // 加载状态显示
  if (isLoading && localContacts.length === 0) {
    return (
      <div className="w-full">
        {/* 搜索栏和添加按钮骨架 */}
        <div className="flex items-center gap-2 mb-6">
          <div className="relative flex-1 pl-1.5">
            <Skeleton className="h-12 w-full rounded-md" />
          </div>
          <Skeleton className="w-12 h-12 rounded-md" />
        </div>

        {/* 联系人卡片骨架 */}
        <div className="space-y-6 pl-1.5">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card
              key={index}
              className="relative bg-white dark:bg-zinc-900 border-gray-200/50 dark:border-zinc-700/50 border p-6 w-full overflow-hidden shadow-sm"
            >
              {/* 右上角按钮骨架 */}
              <div className="absolute right-4 top-4 flex items-center gap-2 z-10">
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>

              {/* 卡片内容骨架 */}
              <CardContent className="pt-2">
                {/* 姓名骨架 */}
                <div className="mb-3">
                  <Skeleton className="h-7 w-1/3 mb-2" />
                </div>

                {/* 电话和邮箱骨架 */}
                <div className="flex flex-col md:flex-row gap-y-3 gap-x-8 mb-3">
                  <div className="flex-1 min-w-0">
                    <Skeleton className="h-5 w-24 mb-2" />
                    <Skeleton className="h-5 w-full" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Skeleton className="h-5 w-16 mb-2" />
                    <Skeleton className="h-5 w-full" />
                  </div>
                </div>

                {/* 地址骨架 */}
                <div className="mb-3">
                  <Skeleton className="h-5 w-16 mb-2" />
                  <Skeleton className="h-5 w-full" />
                </div>

                {/* 备注骨架 */}
                <div>
                  <Skeleton className="h-5 w-16 mb-2" />
                  <Skeleton className="h-5 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
			<div className="w-full">
				{/* 顶部搜索栏和加号按钮 */}
				<div className="flex items-center gap-2 mb-6 mt-2">
					{/* 搜索输入框 */}
					<div className="relative flex-1 pl-1.5">
						<Input
							placeholder="输入联系人姓名、电话号码和邮箱进行搜索"
							value={search}
							onChange={handleSearchChange}
							className="pl-10 h-12 text-base placeholder:text-sm"
						/>
						{/* 搜索图标 */}
						<Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
					</div>
					{/* 加号按钮 */}
					<TooltipProvider>
						<Tooltip delayDuration={0}>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="w-12 h-12 hover:bg-gray-100 dark:hover:bg-gray-800"
									onClick={handleAddContact}
									aria-label="新增联系人"
								>
									<SquarePlus className="w-7 h-7 hover:text-black text-gray-500 dark:text-gray-400" />
								</Button>
							</TooltipTrigger>
							<TooltipPortal>
								<TooltipContent side="top" align="center">
									新增联系人
								</TooltipContent>
							</TooltipPortal>
						</Tooltip>
					</TooltipProvider>
				</div>

				{/* 错误提示 */}
				{errorMessage && (
					<div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
						<p className="text-red-600 dark:text-red-400 text-sm text-center">
							{errorMessage}
						</p>
					</div>
				)}

				{/* 联系人卡片列表 */}
				<div className="flex flex-col pl-1.5">
					{filteredContacts.map((contact) => (
						<Card
							key={contact.contactId}
							className={`relative bg-white dark:bg-zinc-900 border-gray-200/50 dark:border-zinc-700/50 border p-6 w-full overflow-hidden mb-6 shadow-sm hover:shadow dark:hover:shadow-zinc-800/50 transition-shadow duration-200 ${
								contact.isAnimating
									? "animate-in slide-in-from-top-2 fade-in-0 duration-300"
									: ""
							}`}
						>
							{/* 右上角编辑和删除按钮 */}
							<div className="absolute right-4 top-4 flex items-center gap-2 z-10">
								{contact.isEditing ? (
									<TooltipProvider>
										<Tooltip delayDuration={0}>
											<TooltipTrigger asChild>
												<Button
													variant="ghost"
													size="icon"
													className={`p-1 h-8 w-8 ${
														contactNeedsSave ===
														contact.contactId
															? "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30"
															: "text-gray-600 dark:text-gray-300"
													}`}
													onClick={() =>
														handleSave(
															contact.contactId,
														)
													}
													aria-label="保存"
												>
													<Save className="w-6 h-6" />
												</Button>
											</TooltipTrigger>
											<TooltipPortal>
												<TooltipContent
													side="top"
													align="center"
												>
													保存
												</TooltipContent>
											</TooltipPortal>
										</Tooltip>
									</TooltipProvider>
								) : (
									<TooltipProvider>
										<Tooltip delayDuration={0}>
											<TooltipTrigger asChild>
												<Button
													variant="ghost"
													size="icon"
													className="p-1 h-8 w-8 text-gray-600 dark:text-gray-300"
													onClick={() =>
														handleEdit(
															contact.contactId,
														)
													}
													aria-label="编辑"
												>
													<Edit className="w-6 h-6" />
												</Button>
											</TooltipTrigger>
											<TooltipPortal>
												<TooltipContent
													side="top"
													align="center"
												>
													编辑
												</TooltipContent>
											</TooltipPortal>
										</Tooltip>
									</TooltipProvider>
								)}
								<TooltipProvider>
									<Tooltip delayDuration={0}>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className={`p-1 h-8 w-8 ${
													pendingDeleteId === contact.contactId
														? "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30"
														: "text-gray-600 dark:text-gray-300"
												}`}
												onClick={() =>
													handleDelete(
														contact.contactId,
													)
												}
												aria-label={pendingDeleteId === contact.contactId ? "确认删除" : "删除"}
											>
												{pendingDeleteId === contact.contactId ? (
													<Trash2 className="w-6 h-6" />
												) : (
													<X className="w-6 h-6" />
												)}
											</Button>
										</TooltipTrigger>
										<TooltipPortal>
											<TooltipContent
												side="top"
												align="center"
											>
												{pendingDeleteId === contact.contactId ? "确认删除" : "删除"}
											</TooltipContent>
										</TooltipPortal>
									</Tooltip>
								</TooltipProvider>
							</div>
							{/* 卡片内容区 */}
							<CardContent className="pt-4 space-y-4">
								{/* 姓名 */}
								<div className="space-y-2">
									{contact.isEditing ? (
										<div className="space-y-1">
											<label
												className="text-sm font-medium text-foreground flex items-center gap-1"
												htmlFor={`${contact.contactId}-name`}
											>
												<span>姓名</span>
												<span className="text-red-500">
													*
												</span>
											</label>
											<Input
												id={`${contact.contactId}-name`}
												value={contact.name}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"name",
														e.target.value,
													)
												}
												placeholder="输入姓名（必填）"
												className={`transition-all duration-200 placeholder:text-sm ${
													nameFieldError ===
													contact.contactId
														? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
														: "focus:ring-primary/20"
												}`}
											/>
											{nameFieldError ===
												contact.contactId && (
												<p className="text-xs text-red-500 mt-1">
													请填写姓名
												</p>
											)}
										</div>
									) : (
										<div className="flex items-center gap-2">
											<span className="text-sm font-medium text-muted-foreground">
												姓名:
											</span>
											<span className="text-base font-medium text-foreground">
												{contact.name || (
													<span className="text-muted-foreground italic">
														未填写姓名
													</span>
												)}
											</span>
										</div>
									)}
								</div>
								{/* 电话和邮箱 */}
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div className="space-y-2">
										{contact.isEditing ? (
											<div className="space-y-1">
												<label
													className="text-sm font-medium text-foreground flex items-center gap-1"
													htmlFor={`${contact.contactId}-phone`}
												>
													<span>电话号码</span>
													<span className="text-red-500">
														*
													</span>
												</label>
												<Input
													id={`${contact.contactId}-phone`}
													value={contact.phoneNumber}
													onChange={(e) =>
														handleFieldChange(
															contact.contactId,
															"phoneNumber",
															e.target.value,
														)
													}
													placeholder="输入电话号码（必填）"
													className={`transition-all duration-200 placeholder:text-sm ${
														phoneFieldError ===
														contact.contactId
															? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
															: "focus:ring-primary/20"
													}`}
													type="tel"
												/>
												{phoneFieldError ===
													contact.contactId && (
													<p className="text-xs text-red-500 mt-1">
														{!contact.phoneNumber || contact.phoneNumber.trim() === ""
															? "请填写电话号码"
															: "请输入11位数字"
														}
													</p>
												)}
											</div>
										) : (
											<div className="flex items-center gap-2">
												<span className="text-sm font-medium text-muted-foreground">
													电话:
												</span>
												<span className="text-sm text-foreground">
													{contact.phoneNumber || (
														<span className="text-muted-foreground">
															-
														</span>
													)}
												</span>
											</div>
										)}
									</div>
									<div className="space-y-2">
										{contact.isEditing ? (
											<div className="space-y-1">
												<label
													className="text-sm font-medium text-foreground"
													htmlFor={`${contact.contactId}-email`}
												>
													邮箱
												</label>
												<Input
													id={`${contact.contactId}-email`}
													value={contact.email}
													onChange={(e) =>
														handleFieldChange(
															contact.contactId,
															"email",
															e.target.value,
														)
													}
													className={`transition-all duration-200 ${
														emailFieldError ===
														contact.contactId
															? "border-red-500 focus:border-red-500 focus:ring-red-500/20"
															: "focus:ring-primary/20"
													}`}
													type="email"
												/>
												{emailFieldError ===
													contact.contactId && (
													<p className="text-xs text-red-500 mt-1">
														请输入正确的邮箱格式
													</p>
												)}
											</div>
										) : (
											<div className="flex items-center gap-2">
												<span className="text-sm font-medium text-muted-foreground">
													邮箱:
												</span>
												<span className="text-sm text-foreground">
													{contact.email || (
														<span className="text-muted-foreground">
															-
														</span>
													)}
												</span>
											</div>
										)}
									</div>
								</div>
								{/* 地址 */}
								<div className="space-y-2">
									{contact.isEditing ? (
										<div className="space-y-1">
											<label
												className="text-sm font-medium text-foreground"
												htmlFor={`${contact.contactId}-address`}
											>
												地址
											</label>
											<Input
												id={`${contact.contactId}-address`}
												value={contact.address}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"address",
														e.target.value,
													)
												}
												className="transition-all duration-200 focus:ring-primary/20"
											/>
										</div>
									) : contact.address ? (
										<div className="flex items-start gap-2">
											<span className="text-sm font-medium text-muted-foreground mt-0.5">
												地址:
											</span>
											<span className="text-sm text-foreground break-words">
												{contact.address}
											</span>
										</div>
									) : null}
								</div>
								{/* 备注 */}
								<div className="space-y-2">
									{contact.isEditing ? (
										<div className="space-y-1">
											<label
												className="text-sm font-medium text-foreground"
												htmlFor={`${contact.contactId}-remarks`}
											>
												备注
											</label>
											<Input
												id={`${contact.contactId}-remarks`}
												value={contact.remarks}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"remarks",
														e.target.value,
													)
												}
												className="transition-all duration-200 focus:ring-primary/20"
											/>
										</div>
									) : contact.remarks ? (
										<div className="flex items-start gap-2">
											<span className="text-sm font-medium text-muted-foreground mt-0.5">
												备注:
											</span>
											<span className="text-sm text-foreground break-words">
												{contact.remarks}
											</span>
										</div>
									) : null}
								</div>
							</CardContent>
						</Card>
					))}
					{filteredContacts.length === 0 && (
						<div className="text-center text-muted-foreground py-12">
							未找到匹配的联系人
						</div>
					)}
				</div>
			</div>
		);
});

ContactsDialog.displayName = "ContactsDialog";
