/**
 * 上下文列表组件
 *
 * @fileoverview 基金投资人数据的列表和卡片视图展示组件（当前已注释，保留作为参考）
 * <AUTHOR>
 * @since 2025-07-10
 * @version 1.0.0
 *
 * 功能特性：
 * - 支持表格和卡片两种视图模式
 * - 基金详情弹窗展示
 * - 批量选择功能
 * - 收藏状态管理
 * - 响应式设计
 *
 * 注意：此组件当前已被注释，使用ContextCard组件替代
 */

// import React, { useState } from "react";
// import { cn } from "@ui/lib";
// import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@ui/components/table";
// import { Checkbox } from "@ui/components/checkbox";
// import { Avatar, AvatarImage, AvatarFallback } from "@ui/components/avatar";
// import type { FundCard } from "../lib/TestData";
// import { InvestorDialog } from "./Dialog";
// import { fetchFundDetail } from "../lib/html_dialog";


// interface ContextListProps {
//   filtered: FundCard[];
//   viewType: "card" | "list";
// }

// export default function ContextList({ filtered, viewType }: ContextListProps) {
//   const [dialogOpen, setDialogOpen] = useState(false); // 弹窗状态
//   const [selectedCard, setSelectedCard] = useState<FundCard | null>(null);
//   const [isFavorite, setIsFavorite] = useState(false);
//   const [html, setHtml] = useState<string | undefined>(undefined);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState("");
//   const [selectedIds, setSelectedIds] = useState<string[]>([]);

//   const handleCardClick = async (f: FundCard) => {
//     setSelectedCard(f);
//     setDialogOpen(true);
//     setIsFavorite(false);
//     setLoading(true);
//     setError("");
//     setHtml(undefined);
//     try {
//       const res = await fetchFundDetail(f);
//       if (res.html) {
//         setHtml(res.html);
//       } else {
//         setError(res.error || "暂无内容");
//       }
//     } catch (e) {
//       setError("加载失败");
//     }
//     setLoading(false);
//   };

//   function handleSelectAll(checked: boolean) {
//     if (checked) {
//       setSelectedIds(filtered.map(c => c.code));
//     }else {
//       setSelectedIds([]);
//     }
//   }

//   function handleSelectOne(id: string, checked: boolean) {
//     setSelectedIds(prev =>
//       checked ? [...prev, id] : prev.filter(i => i !== id)
//     );
//   }

//   return (
//     <>
//       {/* 视图内容 */}
//       {viewType === "list" ? (
//         <Table>
//           <TableHeader className="cursor-pointer">
//             <TableRow>
//               <TableHead className="w-12">
//                 <Checkbox
//                   checked={selectedIds.length === filtered.length}
//                   indeterminate={selectedIds.length > 0 && selectedIds.length < filtered.length}
//                   onCheckedChange={handleSelectAll}
//                   aria-label="Select all"
//                   className="cursor-pointer"
//                 />
//               </TableHead>
//               <TableHead className="border-r">名称</TableHead>
//               <TableHead>代码</TableHead>
//               <TableHead>标签</TableHead>
//               <TableHead>基金规模</TableHead>
//               <TableHead>近1年收益率</TableHead>
//               <TableHead>持仓我司市值</TableHead>
//               <TableHead>持仓对标公司市值</TableHead>
//               <TableHead>基金经理</TableHead>
//               <TableHead>数据截止</TableHead>
//               <TableHead className="w-12" />
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {filtered.map((contact) => (
//               <TableRow
//                 key={contact.code}
//                 data-state={selectedIds.includes(contact.code ?? "") ? "selected" : undefined}
//                 className="data-[state=selected]:bg-sky-50 dark:data-[state=selected]:bg-zinc-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-zinc-800"
//                 onClick={() => handleCardClick(contact)}
//               >
//                 <TableCell className="w-12">
//                   <Checkbox
//                     checked={selectedIds.includes(contact.code ?? "")}
//                     onCheckedChange={(checked: boolean) => handleSelectOne(contact.code ?? "", !!checked)}
//                     aria-label={`Select ${contact.name}`}
//                     className="cursor-pointer"
//                   />
//                 </TableCell>
//                 <TableCell className="border-r dark:border-zinc-700">
//                   <span className="font-medium whitespace-nowrap">{contact.name}</span>
//                 </TableCell>
//                 <TableCell>{contact.code}</TableCell>
//                 <TableCell>
//                   <span className={cn("text-xs px-2 py-0.5 rounded-full font-medium", contact.tagColor)}>{contact.tag}</span>
//                 </TableCell>
//                 <TableCell>{contact.scale}</TableCell>
//                 <TableCell>
//                   <span className={cn("font-semibold", contact.yieldPositive ? "text-red-500" : "text-green-600")}>{contact.yield}</span>
//                 </TableCell>
//                 <TableCell>{contact.myValue}</TableCell>
//                 <TableCell>{contact.targetValue}</TableCell>
//                 <TableCell>
//                   <div className="flex items-center gap-2">
//                     <Avatar>
//                       <AvatarImage
//                         src={contact.managerAvatar}
//                         alt={contact.manager}
//                         className="rounded-full object-cover"
//                       />
//                       <AvatarFallback>
//                         {contact.manager?.[0]}
//                       </AvatarFallback>
//                     </Avatar>
//                     <span className="font-medium whitespace-nowrap">{contact.manager}</span>
//                   </div>
//                 </TableCell>
//                 <TableCell>{contact.date}</TableCell>
//                 <TableCell className="w-12 text-center">
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       ) : (
//         <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 cursor-pointer">
//           {filtered.map(f => (
//             <button
//               key={f.code}
//               type="button"
//               className="rounded-xl shadow bg-white dark:bg-zinc-900 p-6 flex flex-col justify-between min-w-[260px] h-full transition hover:shadow-lg cursor-pointer appearance-none border-0 text-left"
//               onClick={() => handleCardClick(f)}
//               onKeyUp={e => {
//                 if (e.key === 'Enter' || e.key === ' ') {
//                   handleCardClick(f);
//                 }
//               }}
//             >
//               <div className="flex items-center justify-between mb-2">
//                 <div className="font-bold text-lg text-gray-900 dark:text-gray-100">{f.name}</div>
//                 <span className={cn("text-xs px-2 py-0.5 rounded-full font-medium", f.tagColor)}>{f.tag}</span>
//               </div>
//               <div className="text-xs text-gray-500 mb-2">{f.code}</div>
//               <div className="grid grid-cols-2 gap-x-4 gap-y-1 mb-2">
//                 <div className="text-xs text-gray-500">基金规模</div>
//                 <div className="text-xs text-gray-500">近1年收益率</div>
//                 <div className="font-semibold text-base text-gray-900 dark:text-gray-100">{f.scale}</div>
//                 <div className={cn("font-semibold text-base", f.yieldPositive ? "text-red-500" : "text-green-600")}>{f.yield}</div>
//               </div>
//               <div className="grid grid-cols-2 gap-x-4 gap-y-1 mb-2">
//                 <div className="text-xs text-gray-500">持仓我司市值</div>
//                 <div className="text-xs text-gray-500">占基金净值比</div>
//                 <div className="font-medium text-gray-900 dark:text-gray-100">{f.myValue}</div>
//                 <div className="font-medium text-gray-900 dark:text-gray-100">{f.myRatio}</div>
//               </div>
//               <div className="grid grid-cols-2 gap-x-4 gap-y-1 mb-4">
//                 <div className="text-xs text-gray-500">持仓对标公司市值</div>
//                 <div className="text-xs text-gray-500">占基金净值比</div>
//                 <div className="font-medium text-gray-900 dark:text-gray-100">{f.targetValue}</div>
//                 <div className="font-medium text-gray-900 dark:text-gray-100">{f.targetRatio}</div>
//               </div>
//               <div className="flex items-center justify-between mt-auto pt-2 border-t border-gray-100 dark:border-zinc-800">
//                 <div className="flex items-center gap-2 mt-2">
//                   <img src={f.managerAvatar} alt={f.manager} className="w-8 h-8 rounded-full object-cover" />
//                   <div>
//                     <div className="text-xs font-medium text-gray-900 dark:text-gray-100">{f.manager}</div>
//                     <div className="text-xs text-gray-500">基金经理</div>
//                   </div>
//                 </div>
//                 <div className="text-xs text-gray-500 mt-2 whitespace-nowrap">{f.date}</div>
//               </div>
//             </button>
//           ))}
//         </div>
//       )}
//       {/* 投资人弹窗 */}
//       <InvestorDialog
//         open={dialogOpen}
//         onOpenChange={setDialogOpen}
//         selectedCard={selectedCard}
//         html={html}
//         loading={loading}
//         error={error}
//         isFavorite={isFavorite}
//         onToggleFavorite={() => setIsFavorite(!isFavorite)}
//       />
//     </>
//   );
// }
