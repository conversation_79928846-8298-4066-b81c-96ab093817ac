/**
 * 市场模块主页组件
 *
 * @fileoverview 投资人搜索和筛选的主页面组件，提供搜索框、快捷操作按钮和股票代码输入功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 投资人搜索输入框
 * - 收藏功能快捷按钮
 * - 本公司股票代码输入弹窗
 * - 对标公司代码输入提示
 * - 响应式设计支持
 * - 深色模式支持
 */

"use client";
import { Button } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { Plus, ArrowUp, Settings, Trash2 } from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import {
	TooltipProvider,
	Tooltip,
	TooltipTrigger,
	TooltipContent,
	TooltipPortal,
} from "@ui/components/tooltip";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useRouter } from "@shared/hooks/router";
import { useCode, useUpdateCompanyCode } from "../hooks/useCode";

/**
 * 按钮基础样式常量
 * 提炼公共样式，减少重复代码
 */
const BASE_BUTTON_CLASS =
	"text-center rounded-full px-8 h-12 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-0";

/**
 * 市场模块主页组件
 *
 * 提供投资人搜索和筛选功能的主页面，包含：
 * - 搜索输入框：支持自然语言搜索投资人
 * - 快捷操作：收藏、股票代码输入等
 * - 弹窗功能：本公司股票代码输入
 * - 响应式布局：适配不同屏幕尺寸
 *
 * @returns 市场主页组件JSX元素
 */
export function InvestorSearchBubble() {
		const router = useRouter();
		const { activeOrganization } = useActiveOrganization();
		const organizationId = activeOrganization?.id || "";

		// 查询公司代码
		const { data: codeData, isLoading: codeLoading } = useCode(
			{ organizationId },
			{ enabled: !!organizationId },
		);
		// 更新公司代码 mutation
		const updateMutation = useUpdateCompanyCode();

		// 本公司股票代码弹窗状态
		const [inputValue, setInputValue] = useState("");
		/** 股票代码输入弹窗的开关状态 */
		const [isDialogOpen, setIsDialogOpen] = useState(false);
		const [tagContent, setTagContent] = useState("");
		const [hasError, setHasError] = useState(false);
		const [errorMessage, setErrorMessage] = useState("");
		const [stockCode, setStockCode] = useState(""); // 存储已确认的股票代码

		// 对标公司代码弹窗状态
		const [isBenchmarkDialogOpen, setIsBenchmarkDialogOpen] =
			useState(false);
		const [benchmarkContent, setBenchmarkContent] = useState("");
		const [benchmarkHasError, setBenchmarkHasError] = useState(false);
		const [benchmarkErrorMessage, setBenchmarkErrorMessage] = useState("");
		const [benchmarkBubbles, setBenchmarkBubbles] = useState<string[]>([]); // 存储对标公司代码气泡数组
		const [editingBenchmarkCode, setEditingBenchmarkCode] =
			useState<string>(""); // 当前正在编辑的对标代码

		// 用 useCallback 缓存发送函数，避免不必要的渲染
		const handleSend = useCallback(() => {
			if (inputValue.trim()) {
				// 可扩展：这里建议接入实际搜索接口
				setInputValue(""); // 发送后清空输入框
			}
		}, [inputValue]);

		// 自动添加后缀的函数
		const addSuffix = useCallback((code: string): string => {
			// 如果已经有后缀，直接返回
			if (code.includes(".")) {
				return code;
			}
			// 根据开头数字自动添加后缀
			if (code.startsWith("0") || code.startsWith("3")) {
				return `${code}.SZ`;
			}
			if (code.startsWith("6")) {
				return `${code}.SH`;
			}
			return code; // 其他情况保持原样
		}, []);

		// 处理弹窗确定按钮 - 验证通过后更新代码并跳转 <AUTHOR> @date 2025-07-22
		const handleDialogConfirm = useCallback(() => {
			const trimmedContent = tagContent.trim();

			if (!trimmedContent) {
				// 输入为空时显示错误状态
				setHasError(true);
				setErrorMessage("");
				return;
			}

			// 校验股票代码格式（6位数字）
			const baseCodePattern = /^\d{6}$/;
			const fullCodePattern = /^\d{6}\.[a-zA-Z]+$/;

			if (
				!baseCodePattern.test(trimmedContent) &&
				!fullCodePattern.test(trimmedContent)
			) {
				setHasError(true);
				setErrorMessage("代码格式错误，应为6位数字");
				return;
			}

			// 自动添加后缀
			const processedCode = addSuffix(trimmedContent);
			// 将后缀转换为大写
			const normalizedCode = processedCode.replace(
				/\.([a-z]+)$/i,
				(_, suffix) => `.${suffix.toUpperCase()}`,
			);

			// 验证通过，保存股票代码
			setStockCode(normalizedCode);
			setTagContent("");
			setHasError(false);
			setErrorMessage("");
			setIsDialogOpen(false);

			// 调用更新接口
			updateMutation.mutate(
				{
					organizationId,
					companyCode: normalizedCode,
					benchmarkCompanyCodes: benchmarkBubbles.join(","),
				},
				{
					onSuccess: () => {
						// 跳转到screen页面
						if (activeOrganization?.slug) {
							router.push(
								`/app/${activeOrganization.slug}/investors/screen`,
							);
							//修改人：Miya，修改日期：2025/7/22
							// 修改说明：更新重定向路径，直接导向投资人管理页面
						}
					},
					onError: (error) => {
						console.error("更新公司代码失败:", error);
					},
				},
			);
		}, [
			tagContent,
			addSuffix,
			benchmarkBubbles,
			organizationId,
			updateMutation,
			activeOrganization?.slug,
			router,
		]);

		// 处理本公司股票代码设置按钮点击 - 打开弹窗并预填代码 <AUTHOR> @date 2025-07-22
		const handleStockCodeSettingsClick = useCallback(
			(e: React.MouseEvent) => {
				e.stopPropagation();
				// 预填当前股票代码（去掉后缀）
				if (stockCode) {
					setTagContent(stockCode.split(".")[0]);
				}
				setHasError(false);
				setErrorMessage("");
				setIsDialogOpen(true);
			},
			[stockCode],
		);

		// 处理弹窗关闭 - 保留原有内容，只清理错误状态 <AUTHOR> @date 2025-07-22
		const handleDialogClose = useCallback(
			(open: boolean) => {
				setIsDialogOpen(open);
				if (!open) {
					// 关闭时只清理错误状态，保留输入内容
					setHasError(false);
					setErrorMessage("");
					// 如果没有原始股票代码，则清空输入框
					if (!stockCode) {
						setTagContent("");
					}
				}
			},
			[stockCode],
		);

		// 当输入内容改变时，清除错误状态
		const handleInputChange = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				setTagContent(e.target.value);
				if (hasError || errorMessage) {
					setHasError(false);
					setErrorMessage("");
				}
			},
			[hasError, errorMessage],
		);

		// 处理回车键
		const handleKeyDown = useCallback(
			(e: React.KeyboardEvent<HTMLInputElement>) => {
				if (e.key === "Enter") {
					e.preventDefault();
					handleDialogConfirm();
				}
			},
			[handleDialogConfirm],
		);

		// 处理对标公司代码弹窗确定按钮
		const handleBenchmarkDialogConfirm = useCallback(() => {
			const trimmedContent = benchmarkContent.trim();

			if (!trimmedContent) {
				// 输入为空时显示错误状态
				setBenchmarkHasError(true);
				setBenchmarkErrorMessage("");
				return;
			}

			// 校验股票代码格式（6位数字）
			const baseCodePattern = /^\d{6}$/;
			const fullCodePattern = /^\d{6}\.[a-zA-Z]+$/;

			// 只处理单个代码
			if (
				!baseCodePattern.test(trimmedContent) &&
				!fullCodePattern.test(trimmedContent)
			) {
				setBenchmarkHasError(true);
				setBenchmarkErrorMessage("代码格式错误，应为6位数字");
				return;
			}

			// 自动添加后缀并转换为大写
			const processedCode = addSuffix(trimmedContent).replace(
				/\.([a-z]+)$/i,
				(_, suffix) => `.${suffix.toUpperCase()}`,
			);

			let newBubbles: string[];

			if (editingBenchmarkCode) {
				// 编辑模式：替换现有代码
				if (
					benchmarkBubbles.includes(processedCode) &&
					processedCode !== editingBenchmarkCode
				) {
					setBenchmarkHasError(true);
					setBenchmarkErrorMessage("该代码已存在");
					return;
				}
				newBubbles = benchmarkBubbles.map((code) =>
					code === editingBenchmarkCode ? processedCode : code,
				);
			} else {
				// 新增模式：添加新代码
				if (benchmarkBubbles.includes(processedCode)) {
					setBenchmarkHasError(true);
					setBenchmarkErrorMessage("该代码已存在");
					return;
				}

				// 检查是否超过最大数量
				if (benchmarkBubbles.length >= 9) {
					setBenchmarkHasError(true);
					setBenchmarkErrorMessage("最多只能添加9个对标公司");
					return;
				}

				newBubbles = [...benchmarkBubbles, processedCode];
			}

			// 验证通过，更新气泡
			setBenchmarkBubbles(newBubbles);
			setBenchmarkContent("");
			setBenchmarkHasError(false);
			setBenchmarkErrorMessage("");
			setEditingBenchmarkCode("");
			setIsBenchmarkDialogOpen(false);

			// 调用更新接口
			updateMutation.mutate(
				{
					organizationId,
					companyCode: stockCode,
					benchmarkCompanyCodes: newBubbles.join(","),
				},
				{
					onSuccess: () => {
						// 跳转到screen页面
						if (activeOrganization?.slug) {
							router.push(
								`/app/${activeOrganization.slug}/investors/screen`,
							);
							//修改人：Miya，修改日期：2025/7/22
							// 修改说明：更新重定向路径，直接导向投资人管理页面
						}
					},
					onError: (error) => {
						console.error("更新对标公司代码失败:", error);
					},
				},
			);
		}, [
			benchmarkContent,
			addSuffix,
			benchmarkBubbles,
			editingBenchmarkCode,
			stockCode,
			organizationId,
			updateMutation,
			activeOrganization?.slug,
			router,
		]);

		// 当对标公司代码输入内容改变时，清除错误状态
		const handleBenchmarkInputChange = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				setBenchmarkContent(e.target.value);
				if (benchmarkHasError || benchmarkErrorMessage) {
					setBenchmarkHasError(false);
					setBenchmarkErrorMessage("");
				}
			},
			[benchmarkHasError, benchmarkErrorMessage],
		);

		// 处理对标公司代码弹窗回车键
		const handleBenchmarkKeyDown = useCallback(
			(e: React.KeyboardEvent<HTMLInputElement>) => {
				if (e.key === "Enter") {
					e.preventDefault();
					handleBenchmarkDialogConfirm();
				}
			},
			[handleBenchmarkDialogConfirm],
		);

		// 处理编辑对标公司代码 - 阻止事件冒泡并预填代码 <AUTHOR> @date 2025-07-22
		const handleEditBenchmarkCode = useCallback(
			(e: React.MouseEvent, code: string) => {
				e.stopPropagation();
				setEditingBenchmarkCode(code);
				setBenchmarkContent(code.split(".")[0]); // 只显示6位数字，不显示后缀
				setBenchmarkHasError(false);
				setBenchmarkErrorMessage("");
				setIsBenchmarkDialogOpen(true);
			},
			[],
		);

		// 处理新增对标公司代码
		const handleAddBenchmarkCode = useCallback(() => {
			setEditingBenchmarkCode("");
			setBenchmarkContent("");
			setBenchmarkHasError(false);
			setBenchmarkErrorMessage("");
			setIsBenchmarkDialogOpen(true);
		}, []);

		// 处理对标公司代码弹窗关闭 - 保留原有内容，只清理错误状态 <AUTHOR> @date 2025-07-22
		const handleBenchmarkDialogClose = useCallback(
			(open: boolean) => {
				setIsBenchmarkDialogOpen(open);
				if (!open) {
					// 关闭时只清理错误状态，保留输入内容
					setBenchmarkHasError(false);
					setBenchmarkErrorMessage("");
					// 如果不是编辑模式，则清空输入框和编辑状态
					if (!editingBenchmarkCode) {
						setBenchmarkContent("");
					}
					// 注意：editingBenchmarkCode 保留，这样重新打开时还能知道是编辑模式
				}
			},
			[editingBenchmarkCode],
		);

		// 删除对标公司代码气泡
		const handleRemoveBenchmarkBubble = useCallback(
			(codeToRemove: string) => {
				const newBubbles = benchmarkBubbles.filter(
					(code) => code !== codeToRemove,
				);
				setBenchmarkBubbles(newBubbles);

				// 调用更新接口
				updateMutation.mutate(
					{
						organizationId,
						companyCode: stockCode,
						benchmarkCompanyCodes: newBubbles.join(","),
					},
					{
						onError: (error) => {
							console.error("删除对标公司代码失败:", error);
						},
					},
				);
			},
			[benchmarkBubbles, stockCode, organizationId, updateMutation],
		);

		// 处理本公司股票代码气泡点击（跳转）<AUTHOR> @date 2025-07-22
		const handleStockCodeBubbleClick = useCallback(() => {
			if (activeOrganization?.slug) {
				router.push(`/app/${activeOrganization.slug}/investors/screen`);
				//修改人：Miya，修改日期：2025/7/22
				// 修改说明：更新重定向路径，直接导向投资人管理页面
			}
		}, [activeOrganization?.slug, router]);

		// 处理对标公司代码气泡点击（跳转）<AUTHOR> @date 2025-07-22
		const handleBenchmarkBubbleClick = useCallback(() => {
			if (activeOrganization?.slug) {
				router.push(`/app/${activeOrganization.slug}/investors/screen`);
				//修改人：Miya，修改日期：2025/7/22
				// 修改说明：更新重定向路径，直接导向投资人管理页面
			}
		}, [activeOrganization?.slug, router]);

		// 首次进入页面拿到公司代码、对标公司代码数据时自动赋值
		useEffect(() => {
			if (codeData?.companyFilter) {
				setStockCode(codeData.companyFilter.companyCode || "");

				// 解析对标公司代码
				const benchmarkCodes =
					codeData.companyFilter.benchmarkCompanyCodes || "";
				if (benchmarkCodes) {
					const codes = benchmarkCodes
						.split(",")
						.map((s) => s.trim())
						.filter(Boolean);
					setBenchmarkBubbles(codes);
				}
			}
		}, [codeData]);

		return (
			<div className="flex flex-col items-center justify-center -my-6 md:-my-8 -mx-4 md:-mx-8 min-h-[calc(100vh-120px)] px-4">
				<div className="flex flex-col items-center justify-center w-full max-w-4xl mx-auto">
					{/* 主标题区域 - 保持简洁 */}
					<div className="text-center mb-8 flex-shrink-0">
						<h1 className="text-3xl md:text-3xl lg:text-4xl font-medium text-gray-900 dark:text-gray-100">
							在这里，寻找最合适的投资人
						</h1>
					</div>

					{/* 搜索区域 - 加大宽度，让一行能放5个气泡 <AUTHOR> @date 2025-07-22 */}
					<div className="w-full max-w-6xl flex-shrink-0">
						<div className="relative mb-8 rounded-3xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-4">
							<div className="flex items-end gap-2">
								<Textarea
									value={inputValue}
									onChange={(e) =>
										setInputValue(e.target.value)
									}
									placeholder="投资固态电池领域的机构"
									className="border-0 shadow-none flex-1 rounded-lg resize-none min-h-[110px] max-h-40 focus:outline-none focus:ring-0 focus:border-transparent focus-visible:ring-0 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 !text-lg placeholder:!text-lg"
								/>
								<Button
									onClick={handleSend}
									disabled={!inputValue.trim()}
									size="icon"
									className={`rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0 transition-colors duration-200 ${
										inputValue.trim()
											? "bg-blue-500 hover:bg-blue-600 text-white"
											: "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
									}`}
								>
									<ArrowUp className="h-5 w-5" />
								</Button>
							</div>
						</div>

						{/* 快捷操作区 - 所有气泡统一显示，一行放5个 <AUTHOR> @date 2025-07-22 */}
						<TooltipProvider>
							<div className="flex flex-wrap justify-center gap-4 max-w-6xl mx-auto">
								{/* 数据加载中显示骨架屏 <AUTHOR> @date 2025-07-22 */}
								{codeLoading ? (
									<>
										{/* 收藏气泡骨架屏 */}
										<Skeleton className="h-9 w-12 rounded-full" />
										{/* 本公司股票代码气泡骨架屏 */}
										<Skeleton className="h-9 w-24 rounded-full" />
										{/* 对标公司代码气泡骨架屏 */}
										<Skeleton className="h-9 w-20 rounded-full" />
										<Skeleton className="h-9 w-20 rounded-full" />
										<Skeleton className="h-9 w-20 rounded-full" />
									</>
								) : (
									<>
										{/* 收藏气泡 */}
										<Button
											variant="outline"
											className={`${BASE_BUTTON_CLASS} text-gray-500 dark:text-gray-300`}
										>
											收藏
										</Button>

										{/* 本公司股票代码气泡 <AUTHOR> @date 2025-07-22 */}
										{stockCode ? (
											<Tooltip delayDuration={0}>
												<TooltipTrigger asChild>
													{/* 气泡容器 - 包含主按钮和设置按钮 <AUTHOR> @date 2025-07-22 */}
													<div
														className={`${BASE_BUTTON_CLASS} text-gray-500 dark:text-gray-300 pr-2 pl-4 flex items-center justify-between border rounded-full relative`}
													>
														{/* 主要点击区域 - 点击跳转 <AUTHOR> @date 2025-07-22 */}
														<button
															type="button"
															className="flex-1 text-left bg-transparent border-none p-0 text-gray-500 dark:text-gray-300 text-sm font-medium hover:text-gray-700 dark:hover:text-gray-100 focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 rounded"
															onClick={
																handleStockCodeBubbleClick
															}
															aria-label={`跳转到持有本司股票 ${stockCode.split(".")[0]} 的筛选页面`}
														>
															持有本司:{" "}
															{
																stockCode.split(
																	".",
																)[0]
															}
														</button>

														{/* Settings图标 - 点击打开设置弹窗，阻止事件冒泡 <AUTHOR> @date 2025-07-22 */}
														<button
															type="button"
															className="w-6 h-6 p-0 ml-2 dark:bg-gray-600 rounded flex items-center justify-center flex-shrink-0"
															onClick={
																handleStockCodeSettingsClick
															}
														>
															<Settings className="w-4 h-4 hover:text-black text-gray-400 dark:text-gray-400" />
														</button>
													</div>
												</TooltipTrigger>
												<TooltipPortal>
													<TooltipContent
														side="bottom"
														align="start"
													>
														自动标记持仓机构和推荐潜在投资人
													</TooltipContent>
												</TooltipPortal>
											</Tooltip>
										) : (
											/* 添加本公司股票代码按钮 - 当没有股票代码时显示 <AUTHOR> @date 2025-07-22 */
											<Tooltip delayDuration={0}>
												<TooltipTrigger asChild>
													<Dialog
														open={isDialogOpen}
														onOpenChange={
															handleDialogClose
														}
													>
														<DialogTrigger asChild>
															<Button
																variant="outline"
																className={`${BASE_BUTTON_CLASS} text-gray-500 dark:text-gray-300 pr-3 pl-6`}
															>
																本公司股票代码
																<Plus className="ml-3 w-5 h-5" />
															</Button>
														</DialogTrigger>
													</Dialog>
												</TooltipTrigger>
												<TooltipPortal>
													<TooltipContent
														side="bottom"
														align="start"
													>
														自动标记持仓机构和推荐潜在投资人
													</TooltipContent>
												</TooltipPortal>
											</Tooltip>
										)}

										{/* 对标公司代码气泡 - 移到统一区域 <AUTHOR> @date 2025-07-22 */}
										{benchmarkBubbles.map((code, index) => (
											<Tooltip
												key={index}
												delayDuration={0}
											>
												<TooltipTrigger asChild>
													{/* 气泡容器 - 包含主按钮和设置按钮 <AUTHOR> @date 2025-07-22 */}
													<div
														className={`${BASE_BUTTON_CLASS} text-gray-500 dark:text-gray-300 pr-2 pl-4 flex items-center justify-between border rounded-full relative`}
													>
														{/* 主要点击区域 - 点击跳转 <AUTHOR> @date 2025-07-22 */}
														<button
															type="button"
															className="flex-1 text-left bg-transparent border-none p-0 text-gray-500 dark:text-gray-300 text-sm font-medium hover:text-gray-700 dark:hover:text-gray-100 focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 rounded"
															onClick={
																handleBenchmarkBubbleClick
															}
															aria-label={`跳转到持有对标公司股票 ${code.split(".")[0]} 的筛选页面`}
														>
															持有对标:{" "}
															{code.split(".")[0]}
														</button>

														{/* Settings图标 - 点击打开编辑弹窗，阻止事件冒泡 <AUTHOR> @date 2025-07-22 */}
														<button
															type="button"
															className="w-6 h-6 p-0 ml-2 dark:bg-gray-600 rounded flex items-center justify-center flex-shrink-0"
															onClick={(e) =>
																handleEditBenchmarkCode(
																	e,
																	code,
																)
															}
														>
															<Settings className="w-4 h-4 hover:text-black text-gray-400 dark:text-gray-400" />
														</button>
													</div>
												</TooltipTrigger>
												<TooltipPortal>
													<TooltipContent
														side="bottom"
														align="start"
													>
														自动标记其持仓机构
													</TooltipContent>
												</TooltipPortal>
											</Tooltip>
										))}

										{/* 添加对标公司代码按钮 - 只有在少于9个时才显示 <AUTHOR> @date 2025-07-22 */}
										{benchmarkBubbles.length < 9 && (
											<Tooltip delayDuration={0}>
												<TooltipTrigger asChild>
													<Button
														variant="outline"
														className={`${BASE_BUTTON_CLASS} h-8 w-8 px-0 mt-2 flex items-center justify-center text-gray-400 text-2xl font-light`}
														onClick={
															handleAddBenchmarkCode
														}
													>
														+
													</Button>
												</TooltipTrigger>
												<TooltipPortal>
													<TooltipContent
														side="bottom"
														align="start"
													>
														输入对标公司股票代码，自动标记其持仓机构
													</TooltipContent>
												</TooltipPortal>
											</Tooltip>
										)}
									</>
								)}
							</div>
						</TooltipProvider>

						{/* 本公司股票代码设置弹窗 - 独立在外部 <AUTHOR> @date 2025-07-22 */}
						<Dialog
							open={isDialogOpen}
							onOpenChange={handleDialogClose}
						>
							<DialogContent
								className="sm:max-w-[600px] p-8"
								onClick={(e) => e.stopPropagation()}
							>
								{/* 标题 <AUTHOR> @date 2025-07-22 */}
								<DialogHeader className="mb-0">
									<DialogTitle className="text-xl font-medium text-black dark:text-white">
										本公司股票代码
									</DialogTitle>
									<DialogDescription className="sr-only">
										输入本公司股票代码，自动筛选持仓机构，挖掘潜在投资人
									</DialogDescription>
								</DialogHeader>

								{/* 描述文字 <AUTHOR> @date 2025-07-22 */}
								<div className="mb-2">
									<p className="text-gray-600 dark:text-gray-400 text-base">
										输入本公司股票代码，自动筛选持仓机构，挖掘潜在投资人。
									</p>
								</div>

								{/* 输入框 <AUTHOR> @date 2025-07-22 */}
								<div className="mb-2">
									<Input
										value={tagContent}
										onChange={handleInputChange}
										onKeyDown={handleKeyDown}
										placeholder="请输入股票代码"
										className={`w-full h-16 text-lg text-center border-2 rounded-lg bg-gray-100 dark:bg-gray-800 focus-visible:outline-none focus-visible:ring-0 focus-visible:border-gray-200 focus:outline-none focus:ring-0 focus:border-gray-200 ${
											hasError
												? "border-red-500 placeholder:text-red-500"
												: "border-gray-200 dark:border-gray-600"
										}`}
									/>
									{/* 错误提示信息 */}
									{errorMessage && (
										<p className="text-red-500 text-sm text-center mt-2">
											{errorMessage}
										</p>
									)}
								</div>

								{/* 确定按钮 <AUTHOR> @date 2025-07-22 */}
								<div className="flex justify-end">
									<Button
										onClick={handleDialogConfirm}
										className="px-8 py-3 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
									>
										确定
									</Button>
								</div>
							</DialogContent>
						</Dialog>

						{/* 对标公司代码弹窗 - 移到外部 <AUTHOR> @date 2025-07-22 */}
						<Dialog
							open={isBenchmarkDialogOpen}
							onOpenChange={handleBenchmarkDialogClose}
						>
							<DialogContent
								className="sm:max-w-[600px] p-8"
								onClick={(e) => e.stopPropagation()}
							>
								{/* 标题 <AUTHOR> @date 2025-07-22 */}
								<DialogHeader className="mb-0">
									<DialogTitle className="text-xl font-medium text-black dark:text-white">
										对标公司代码
									</DialogTitle>
									<DialogDescription className="sr-only">
										输入对标公司股票代码，自动筛选持仓机构
									</DialogDescription>
								</DialogHeader>

								{/* 描述文字 <AUTHOR> @date 2025-07-22 */}
								<div className="mb-2">
									<p className="text-gray-600 dark:text-gray-400 text-base">
										输入对标公司股票代码，自动筛选持仓机构
									</p>
								</div>

								{/* 输入框 <AUTHOR> @date 2025-07-22 */}
								<div className="mb-2">
									<Input
										value={benchmarkContent}
										onChange={handleBenchmarkInputChange}
										onKeyDown={handleBenchmarkKeyDown}
										placeholder="请输入对标公司代码"
										className={`w-full h-16 text-lg text-center border-2 rounded-lg bg-gray-100 dark:bg-gray-800 focus-visible:outline-none focus-visible:ring-0 focus-visible:border-gray-200 focus:outline-none focus:ring-0 focus:border-gray-200 ${
											benchmarkHasError
												? "border-red-500 placeholder:text-red-500"
												: "border-gray-200 dark:border-gray-600"
										}`}
									/>
									{/* 错误提示信息 */}
									{benchmarkErrorMessage && (
										<p className="text-red-500 text-sm text-center mt-2">
											{benchmarkErrorMessage}
										</p>
									)}
								</div>

								{/* 按钮区域 <AUTHOR> @date 2025-07-22 */}
								<div className="flex justify-end items-center gap-3">
									{/* 删除按钮 - 只有在编辑模式时才显示 */}
									{editingBenchmarkCode && (
										<Button
											onClick={() => {
												handleRemoveBenchmarkBubble(
													editingBenchmarkCode,
												);
												setIsBenchmarkDialogOpen(false);
											}}
											variant="outline"
											className="px-6 py-3 text-lg border-gray-300 text-gray-500 hover:border-red-500 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200"
										>
											<Trash2 className="w-5 h-5 mr-2" />
											删除
										</Button>
									)}

									{/* 确定按钮 */}
									<Button
										onClick={handleBenchmarkDialogConfirm}
										className="px-8 py-3 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
									>
										确定
									</Button>
								</div>
							</DialogContent>
						</Dialog>
					</div>
				</div>
			</div>
		);
	}