/**
 * 公司股票代码管理Hook
 *
 * @fileoverview 提供公司股票代码的查询和更新功能，支持组织级别的代码管理
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 公司股票代码查询
 * - 股票代码创建和更新
 * - React Query集成
 * - 错误处理和状态管理
 * - 组织级别的代码管理
 */

import { useQuery, useMutation } from "@tanstack/react-query";
import { fetchCompanyCode, updateCompanyCode } from "../lib/code";
import type { CodeParams, CodeResponse, UpdateCompanyCodeParams, CompanyFilterSaveResponse } from "../lib/code";
import type { UseMutationOptions } from "@tanstack/react-query";

/**
 * 公司代码查询Hook返回值接口
 */
export interface UseCodeReturn {
	/** 查询返回的代码数据 */
	data: CodeResponse | undefined;
	/** 是否正在初次加载 */
	isLoading: boolean;
	/** 是否正在获取数据（包括后台刷新） */
	isFetching: boolean;
	/** 错误对象，无错误时为null */
	error: Error | null;
	/** 是否发生错误 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动重新获取数据的函数 */
	refetch: () => Promise<any>;
	/** 是否正在重新获取数据 */
	isRefetching: boolean;
}

/**
 * 查询公司股票代码的Hook
 *
 * @param params - 查询参数对象，包含组织ID等信息
 * @param options - React Query配置选项
 * @param options.enabled - 是否启用查询，默认为true
 * @param options.staleTime - 数据保持新鲜的时间（毫秒），默认为0
 * @param options.gcTime - 垃圾回收时间（毫秒），默认为10分钟
 * @returns 查询结果和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useCode(
 *   { organizationId: 'org-123' },
 *   { enabled: !!organizationId }
 * );
 * ```
 */
export function useCode(
  params: CodeParams,
  options: { enabled?: boolean; staleTime?: number; gcTime?: number } = {}
): UseCodeReturn {
  const {
    enabled = true,
    staleTime = 0,
    gcTime = 10 * 60 * 1000,
  } = options;

  const query = useQuery<CodeResponse, Error>({
    queryKey: ["companyCode", params.organizationId],
    queryFn: () => fetchCompanyCode(params),
    enabled: enabled && !!params.organizationId,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}

/**
 * 更新公司股票代码的Hook
 *
 * 用于创建或更新公司股票代码，返回mutation对象便于表单调用
 *
 * @param options - React Query mutation配置选项
 * @returns mutation对象，包含mutate、mutateAsync等方法
 *
 */
export function useUpdateCompanyCode(
  options?: UseMutationOptions<CompanyFilterSaveResponse, Error, UpdateCompanyCodeParams, unknown>
) {
  return useMutation<CompanyFilterSaveResponse, Error, UpdateCompanyCodeParams, unknown>({
    mutationFn: updateCompanyCode,
    ...options,
  });
}
