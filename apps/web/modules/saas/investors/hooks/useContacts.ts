/**
 * 联系人管理Hook集合
 *
 * @fileoverview 提供联系人数据的CRUD操作Hook，包括查询、创建、更新和删除功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 联系人列表查询（支持搜索和分页）
 * - 联系人创建、更新、删除
 * - React Query集成
 * - 智能缓存管理
 * - 错误处理和状态管理
 * - 卡片切换时自动刷新
 */

import { useQuery, useMutation } from "@tanstack/react-query";
import type { UseMutationOptions } from "@tanstack/react-query";
import {
  fetchContactsList,
  createContact,
  updateContact,
  deleteContact
} from "../lib/contacts_data";
import type {
  ContactsListParams,
  ContactsListResponse,
  CreateContactParams,
  CreateContactResponse,
  UpdateContactParams,
  UpdateContactResponse,
  DeleteContactParams,
  DeleteContactResponse
} from "../lib/contacts_data";

/**
 * 联系人查询Hook返回值接口
 */
export interface UseContactsReturn {
	/** 联系人列表数据 */
	data: ContactsListResponse | undefined;
	/** 是否正在初次加载 */
	isLoading: boolean;
	/** 是否正在获取数据（包括后台刷新） */
	isFetching: boolean;
	/** 错误对象，无错误时为null */
	error: Error | null;
	/** 是否发生错误 用于抛出错误信息  修改人：Miya  修改时间：2025-07-24  修改内容：添加错误信息 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动重新获取数据的函数 */
	refetch: () => Promise<any>;
	/** 是否正在重新获取数据 */
	isRefetching: boolean;
}

/**
 * 获取联系人列表的Hook
 *
 * 提供联系人数据的查询功能，支持搜索、分页和卡片切换时的自动刷新
 * @modified Miya 2025-07-31 - 更新参数说明，支持基金代码和一码通ID查询
 *
 * @param params - 查询参数对象
 * @param params.organizationId - 组织ID（必填）
 * @param params.cardCode - 卡片代码，用于触发切换时的查询
 * @param params.fundCode - 基金代码（精确匹配）
 * @param params.unifiedAccountId - 一码通账号（精确匹配）
 * @param params.name - 联系人姓名（模糊匹配）
 * @param params.phoneNumber - 电话号码（模糊匹配）
 * @param params.email - 邮箱地址（模糊匹配）
 * @param params.page - 页码
 * @param params.limit - 每页数量
 * @param options - React Query配置选项
 * @param options.enabled - 是否启用查询，默认为true
 * @param options.staleTime - 数据保持新鲜的时间，默认为0（每次切换卡片都重新请求）
 * @param options.gcTime - 垃圾回收时间，默认为10分钟
 * @returns 联系人查询结果和相关状态
 *
 */
export function useContacts(
  params: ContactsListParams & { cardCode?: string },
  options: { enabled?: boolean; staleTime?: number; gcTime?: number } = {}
): UseContactsReturn {
  const {
    enabled = true,
    staleTime = 0, // 设置为0，每次切换卡片都重新请求
    gcTime = 10 * 60 * 1000,   // 10分钟
  } = options;

  const query = useQuery<ContactsListResponse, Error>({
    queryKey: ["contactsList", params.organizationId, params.cardCode, params.fundCode, params.unifiedAccountId, params.name, params.phoneNumber, params.email, params.page, params.limit],
    queryFn: () => fetchContactsList(params),
    enabled: enabled && !!params.organizationId,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}

/**
 * useCreateContact hook
 * 用于创建联系人，返回 mutation 对象，便于表单调用
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 创建联系人
 * 恢复方法: 删除本文件新增内容
 */
export function useCreateContact(
  options?: UseMutationOptions<CreateContactResponse, Error, CreateContactParams, unknown>
) {
  return useMutation<CreateContactResponse, Error, CreateContactParams, unknown>({
    mutationFn: createContact,
    ...options,
  });
}

/**
 * useUpdateContact hook
 * 用于更新联系人，返回 mutation 对象，便于表单调用
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 更新联系人
 * 恢复方法: 删除本文件新增内容
 */
export function useUpdateContact(
  options?: UseMutationOptions<UpdateContactResponse, Error, UpdateContactParams, unknown>
) {
  return useMutation<UpdateContactResponse, Error, UpdateContactParams, unknown>({
    mutationFn: updateContact,
    ...options,
  });
}

/**
 * useDeleteContact hook
 * 用于删除联系人，返回 mutation 对象，便于表单调用
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 删除联系人
 * 恢复方法: 删除本文件新增内容
 */
export function useDeleteContact(
  options?: UseMutationOptions<DeleteContactResponse, Error, DeleteContactParams, unknown>
) {
  return useMutation<DeleteContactResponse, Error, DeleteContactParams, unknown>({
    mutationFn: deleteContact,
    ...options,
  });
}
