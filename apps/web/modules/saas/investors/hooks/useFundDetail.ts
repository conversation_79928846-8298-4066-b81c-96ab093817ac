/**
 * 基金详情数据获取Hook
 *
 * @fileoverview 提供基金详情HTML数据的获取功能，支持缓存管理和智能刷新策略
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 基金详情HTML数据获取
 * - 智能缓存策略（5分钟内使用缓存）
 * - 支持Markdown格式输出
 * - 错误处理和状态管理
 * - React Query集成
 */

import { useQuery } from "@tanstack/react-query";
import { fetchFundDetail } from "../lib/html_dialog";
import type { FundCard } from "../lib/html_dialog";

/**
 * 基金详情Hook配置选项接口
 */
export interface UseFundDetailOptions {
  /** 是否启用查询，默认为true */
  enabled?: boolean;
  /** 数据保持新鲜的时间（毫秒），默认为0 */
  staleTime?: number;
  /** 垃圾回收时间（毫秒），默认为10分钟 */
  gcTime?: number;
}

/**
 * 基金详情Hook返回值接口
 */
export interface UseFundDetailReturn {
  /** 基金详情数据，包含HTML、Markdown或错误信息 */
  data: { html?: string; markdown?: string; error?: string } | undefined;
  /** 是否正在初次加载 */
  isLoading: boolean;
  /** 是否正在获取数据（包括后台刷新） */
  isFetching: boolean;
  /** 错误对象，无错误时为null */
  error: Error | null;
  /** 是否发生错误 */
  isError: boolean;
  /** 是否成功获取数据 */
  isSuccess: boolean;
  /** 手动重新获取数据的函数 */
  refetch: () => Promise<any>;
  /** 是否正在重新获取数据 */
  isRefetching: boolean;
}

/**
 * 获取基金详情HTML的Hook
 *
 * 实现智能缓存策略：5分钟内点击刷新直接使用缓存，5分钟后才请求接口
 *
 * @param card - 当前选中的基金卡片对象
 * @param options - Hook配置选项
 * @returns 基金详情数据和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useFundDetail(selectedCard, {
 *   enabled: !!selectedCard,
 *   staleTime: 5 * 60 * 1000 // 5分钟缓存
 * });
 * ```
 */
export function useFundDetail(
  card: FundCard | null,
  options: UseFundDetailOptions = {}
): UseFundDetailReturn {
  const {
			enabled = true,
			staleTime = 5 * 60 * 1000,
			gcTime = 5 * 60 * 1000,
		} = options;

  const query = useQuery<{ html?: string; markdown?: string; error?: string }, Error>({
    queryKey: ["fundDetail", card?.code],
    queryFn: () => (card ? fetchFundDetail(card) : Promise.resolve({ html: "" })),
    enabled: enabled && !!card,
    refetchOnWindowFocus: false,
    staleTime,
    gcTime,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}
