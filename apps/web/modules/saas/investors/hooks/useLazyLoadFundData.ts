/**
 * 懒加载基金数据管理Hook
 *
 * @fileoverview 提供基金数据的懒加载功能，支持分批次加载、缓存管理和刷新控制
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.1.0
 * @updated 2025-07-23 Miya 删除基金代码格式验证逻辑，直接使用原始代码
 *
 * 功能特性：
 * - 分批次懒加载基金数据（每批8个）
 * - 智能缓存管理，避免重复请求
 * - 支持刷新和重置功能
 * - 加载状态管理和进度跟踪
 */

import { useState, useEffect, useMemo } from "react";
import { useFundManagerData } from "./useSequence";
import type { SequenceItem, FundManagerFundItem } from "../lib/sequence_data";



/** 每页显示的卡片数量，懒加载时每次加载的数据量 */
const PAGE_SIZE = 8;

/**
 * 批次信息接口
 * 用于描述每个加载批次的范围和包含的基金代码
 */
interface BatchInfo {
	/** 批次开始索引 */
	startIndex: number;
	/** 批次结束索引 */
	endIndex: number;
	/** 批次包含的基金代码列表 */
	codes: string[];
}

/**
 * 懒加载基金数据Hook的参数接口
 */
interface UseLazyLoadFundDataProps {
	/** 有效的序列数据列表 */
	validSequenceData: SequenceItem[];
	/** 当前可见的卡片数量 */
	visibleCount: number;
	/** 是否启用基金数据获取 */
	enableFundDataFetch: boolean;
	/** 基金数据刷新键，用于强制刷新 */
	fundDataRefreshKey?: number;
	/** 是否正在刷新状态 */
	isRefreshing: boolean;
}

/**
 * 懒加载基金数据管理Hook
 *
 * @param props - Hook参数对象
 * @param props.validSequenceData - 有效的序列数据列表
 * @param props.visibleCount - 当前可见的卡片数量
 * @param props.enableFundDataFetch - 是否启用基金数据获取
 * @param props.fundDataRefreshKey - 基金数据刷新键，用于强制刷新
 * @param props.isRefreshing - 是否正在刷新状态
 *
 * @returns Hook返回对象
 * @returns loadedFundData - 已加载的基金数据缓存Map
 * @returns isFundDataLoading - 是否正在加载基金数据
 * @returns currentRequestingCodes - 当前正在请求的基金代码列表
 * @returns resetLoadedData - 重置已加载数据的函数
 * @returns lastLoadedCount - 最后加载到的数量
 * @returns setLastLoadedCount - 设置最后加载数量的函数
 * @returns fundDataError - 基金数据获取错误对象 修改人：Miya 修改日期：2025-07-24
 * @returns isFundDataError - 是否发生基金数据获取错误 修改人：Miya 修改日期：2025-07-24
 *
 */
export function useLazyLoadFundData({
	validSequenceData,
	visibleCount,
	enableFundDataFetch,
	fundDataRefreshKey,
	isRefreshing
}: UseLazyLoadFundDataProps) {
	/** 已加载的基金数据缓存，使用Map结构提高查询效率 */
	const [loadedFundData, setLoadedFundData] = useState<Map<string, FundManagerFundItem>>(new Map());
	/** 记录已加载到的位置，用于懒加载进度跟踪 */
	const [lastLoadedCount, setLastLoadedCount] = useState<number>(0);

	// 计算需要请求基金数据的批次
	const batchesToLoad = useMemo(() => {
		const batches: BatchInfo[] = [];

		// 计算已经加载的批次数
		const loadedBatches = Math.floor(lastLoadedCount / PAGE_SIZE);
		// 计算当前需要显示的批次数
		const visibleBatches = Math.ceil(visibleCount / PAGE_SIZE);

		// 如果正在刷新，重新加载第一批
		if (isRefreshing) {
			const endIndex = Math.min(PAGE_SIZE, visibleCount);
			const batchItems = validSequenceData.slice(0, endIndex);
			const codes = batchItems
				.map(item => item?.investorCode)
				.filter((code): code is string => code !== null && code !== undefined);

			if (codes.length > 0) {
				batches.push({ startIndex: 0, endIndex, codes });
			}
		} else {
			// 正常懒加载：加载未加载的批次
			for (let batchIndex = loadedBatches; batchIndex < visibleBatches; batchIndex++) {
				const startIndex = batchIndex * PAGE_SIZE;
				const endIndex = Math.min(startIndex + PAGE_SIZE, visibleCount);

				if (startIndex < validSequenceData.length) {
					const batchItems = validSequenceData.slice(startIndex, endIndex);
					const codes = batchItems
						.map(item => item?.investorCode)
						.filter((code): code is string => code !== null && code !== undefined);

					if (codes.length > 0) {
						batches.push({ startIndex, endIndex, codes });
					}
				}
			}
		}

		return batches;
	}, [validSequenceData, visibleCount, lastLoadedCount, isRefreshing]);

	// 当前正在处理的批次索引
	const [currentBatchIndex, setCurrentBatchIndex] = useState(0);

	// 获取当前要请求的批次
	const currentBatch = batchesToLoad[currentBatchIndex];
	const currentBatchCodes = currentBatch?.codes || [];

	// 只请求当前批次的数据
	const {
		data: currentBatchFundManagerData,
		isLoading: isFundDataLoading,
		error: fundDataError,
		isError: isFundDataError,
	} = useFundManagerData(
		{ investors: currentBatchCodes },
		{
			enabled: enableFundDataFetch && currentBatchCodes.length > 0,
			staleTime: 2 * 60 * 1000, // 2分钟缓存
			refreshKey: fundDataRefreshKey,
		}
	);

	// 处理当前批次的查询结果
	useEffect(() => {
		if (currentBatchFundManagerData && !('code' in currentBatchFundManagerData) && currentBatchFundManagerData.funds && currentBatch) {
			// 立即更新当前批次的数据到缓存
			setLoadedFundData(prev => {
				const newMap = new Map(prev);
				currentBatchFundManagerData.funds.forEach(fund => {
					if (fund?.fundCode) {
						newMap.set(fund.fundCode, fund);
					}
				});
				return newMap;
			});

			// 更新已加载的位置到当前批次的结束位置
			if (!isRefreshing) {
				setLastLoadedCount(prev => Math.max(prev, currentBatch.endIndex));
			}

			// 当前批次完成后，自动请求下一个批次
			if (currentBatchIndex < batchesToLoad.length - 1) {
				setCurrentBatchIndex(prev => prev + 1);
			}
		}
	}, [currentBatchFundManagerData, isRefreshing, currentBatch, currentBatchIndex, batchesToLoad.length]);

	// 重置批次索引当批次列表变化时
	useEffect(() => {
		setCurrentBatchIndex(0);
	}, [batchesToLoad.length, isRefreshing]);

	// 计算当前正在请求的投资人代码列表（用于判断卡片加载状态）
	// 包含当前正在请求的批次 + 所有等待中的批次
	const currentRequestingCodes = useMemo(() => {
		const requestingCodes: string[] = [];

		// 如果当前批次正在加载，添加当前批次的代码
		if (isFundDataLoading && currentBatchCodes.length > 0) {
			requestingCodes.push(...currentBatchCodes);
		}

		// 添加所有等待中批次的代码（从当前批次+1开始的所有批次）
		for (let i = currentBatchIndex + 1; i < batchesToLoad.length; i++) {
			const waitingBatch = batchesToLoad[i];
			if (waitingBatch?.codes) {
				requestingCodes.push(...waitingBatch.codes);
			}
		}

		return requestingCodes;
	}, [isFundDataLoading, currentBatchCodes, currentBatchIndex, batchesToLoad]);

	// 重置函数 - 供外部调用清除缓存
	const resetLoadedData = () => {
		setLoadedFundData(new Map());
		setLastLoadedCount(0);
		setCurrentBatchIndex(0);
	};

	return {
		loadedFundData,
		isFundDataLoading,
		currentRequestingCodes,
		resetLoadedData,
		lastLoadedCount,
		setLastLoadedCount,
		fundDataError,
		isFundDataError
	};
}
