/**
 * 公司股票代码管理工具库
 *
 * @fileoverview 提供公司股票代码的查询和更新功能，支持本司代码和对标代码管理
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 公司股票代码查询
 * - 股票代码创建和更新
 * - 对标公司代码管理
 * - 数据加密传输
 * - 错误处理和验证
 */

import {
	encryptRequestData,
	generateSign,
	decryptData,
} from "@repo/utils/lib/crypto";

/**
 * 公司股票代码查询请求参数接口
 */
export interface CodeParams {
	/** 组织ID，必填 */
	organizationId: string;
}

/**
 * 公司过滤器数据接口
 */
export interface CompanyFilter {
	/** 过滤器ID */
	id: string;
	/** 组织ID */
	organizationId: string;
	/** 本公司股票代码 */
	companyCode: string;
	/** 对标公司股票代码，逗号分隔字符串 */
	benchmarkCompanyCodes: string;
	/** 修改时间，ISO时间字符串 */
	modifiedAt: string;
}

/**
 * 公司股票代码查询响应数据接口
 */
export interface CodeResponse {
	/** 公司过滤器配置 */
	companyFilter: CompanyFilter;
}

/**
 * 查询公司股票代码配置
 *
 * 获取指定组织的公司股票代码配置信息，包括本司代码和对标代码
 *
 * @param params - 查询参数对象
 * @param params.organizationId - 组织ID
 * @returns 公司股票代码配置数据
 * @throws 请求失败或解密失败时抛出错误
 *
 * 处理流程：
 * 1. 参数加密和签名
 * 2. 发送POST请求到API
 * 3. 响应数据解密
 * 4. 返回解析后的配置数据
 *
 */
export async function fetchCompanyCode(
	params: CodeParams,
): Promise<CodeResponse> {
	// 1. 参数加密（需要 await）
	const encrypted = await encryptRequestData(params);

	// 2. 生成签名（使用加密后的内容）
	const sign = generateSign(encrypted);

	// 3. 发送POST请求
	const requestBody = { content: encrypted, sign };
	const res = await fetch("/api/investor-management/company-filter/get", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Sign": sign,
		},
		body: JSON.stringify(requestBody),
	});

	if (!res.ok) {
		const errorText = await res.text();
		throw new Error(`请求失败: ${res.status} - ${errorText}`);
	}

	// 4. 解密响应（需先 parse 再断言类型）
	const result = await res.json();

	if (result.code !== 200) {
		throw new Error(result.message || "接口返回错误");
	}
	// decryptData 返回 string，需 JSON.parse
	const data = JSON.parse(decryptData(result.data)) as CodeResponse;
	return data;
}



/**
 * 投资人页面本公司股票代码更新/创建请求参数
 * @property {string} organizationId - 组织ID，必填
 * @property {string} companyCode - 本公司股票代码，必填
 * @property {string} benchmarkCompanyCodes - 对标公司股票代码，逗号分隔，选填
 */

export interface UpdateCompanyCodeParams {
	organizationId: string;
	companyCode: string;
	benchmarkCompanyCodes?: string;
}

export interface CompanyFilterSaveData {
  id: string;
}

export interface CompanyFilterSaveResponse {
  code: number;
  message: string;
  data: CompanyFilterSaveData;
}

/**
 * 投资人页面本公司股票代码更新/创建异步函数
 * @param {UpdateCompanyCodeParams} params - 更新/创建参数
 * @returns {Promise<CompanyFilterSaveResponse>} 更新/创建结果
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-11
 * 修改人: Miya
 * 关联需求: 更新/创建本公司股票代码
 * 恢复方法: 删除本文件新增内容
 */
export async function updateCompanyCode(
  params: UpdateCompanyCodeParams,
): Promise<CompanyFilterSaveResponse> {
  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/company-filter/upsert", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });

  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as CompanyFilterSaveData;
  return {
    code: result.code,
    message: result.message,
    data,
  };
}




