/**
 * 联系人数据管理工具库
 *
 * @fileoverview 提供联系人数据的CRUD操作函数，包括查询、创建、更新和删除功能
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 联系人列表查询（支持搜索和分页）
 * - 联系人创建、更新、删除
 * - 数据加密传输
 * - 错误处理和验证
 * - 类型安全的接口定义
 */

import { encryptRequestData, generateSign, decryptData } from "@repo/utils/lib/crypto";

/**
 * 联系人列表查询请求参数接口
 * @modified Miya 2025-08-01 - 添加queryAllContacts参数用于获取全部联系人数据
 * @modified Miya 2025-07-31 - 添加基金代码和一码通ID查询参数
 */
export interface ContactsListParams {
		/** 组织ID，必填 */
		organizationId: string;
		/** 基金代码（精确匹配） */
		fundCode?: string;
		/** 一码通账号（精确匹配） */
		unifiedAccountId?: string;
		/** 基金代码和一码通账号两者必须有一个，但是queryAllContacts为true 的时候这个规则作废*/
		/** 联系人姓名（模糊匹配） */
		name?: string;
		/** 电话号码（模糊匹配） */
		phoneNumber?: string;
		/** 邮箱地址（模糊匹配） */
		email?: string;
		/** 页码，正整数，默认1 */
		page?: number;
		/** 每页数量，正整数，最大100，默认10 */
		limit?: number;
		/** 是否查询全部联系人，true时忽略分页参数获取所有数据 */
		queryAllContacts?: boolean;
	}

/**
 * 联系人信息数据接口
 * @modified Miya 2025-07-31 - 添加基金代码和一码通ID字段
 */
export interface Contact {
  /** 联系人ID */
  contactId: string;
  /** 组织ID */
  organizationId: string;
  /** 姓名 */
  name: string;
  /** 手机号码 */
  phoneNumber: string;
  /** 邮箱地址 */
  email: string;
  /** 地址 */
  address: string;
  /** 备注信息 */
  remarks: string;
  /** 基金代码，用于标识机构投资者 */
  fundCode?: string;
  /** 一码通账户ID，用于标识个人投资者 */
  unifiedAccountId?: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 创建者 */
  createdBy: string;
  /** 更新者 */
  updatedBy: string;
}

/**
 * 分页信息接口
 * @modified Miya 2025-08-01 - 新增分页信息接口
 */
export interface Pagination {
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 联系人列表响应数据类型
 * @modified Miya 2025-08-01 - 修改数据结构，将分页信息放入pagination对象中
 */
export interface ContactsListResponse {
  /** 联系人列表 */
  contacts: Contact[];
  /** 分页信息 */
  pagination: Pagination;
}

/**
 * 获取联系人列表的异步函数
 * @param {ContactsListParams} params - 查询参数
 * @returns {Promise<ContactsListResponse>} 联系人列表数据
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 * - queryAllContacts为true时，忽略分页参数获取全部联系人数据
 *
 * @modified Miya 2025-08-01 - 添加queryAllContacts参数支持
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 获取联系人列表
 * 恢复方法: 删除本文件新增内容
 */
export async function fetchContactsList(params: ContactsListParams): Promise<ContactsListResponse> {
  // 1. 参数加密
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    /* 修改块开始: 请求体字段名与后端统一
     * 修改范围: body由{ data: encrypted }改为{ content: encrypted, sign }
     * 修改时间: 2025-07-10
     * 修改人: Miya
     */
    body: JSON.stringify(requestBody),
    /* 修改块结束: 请求体字段名与后端统一
     * 修改时间: 2025-07-10
     */
  });

  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as ContactsListResponse;
  // 修改记录 2025-08-11 - 删除调试日志，提高生产环境安全性 - hayden
  return data;
}

/**
 * 创建联系人请求参数
 * @modified Miya 2025-07-31 - 添加基金代码和一码通ID参数
 * @modified Miya 2025-08-01 - 手机号改为必填字段
 */
export interface CreateContactParams {
  organizationId: string; //必填
  name: string; //必填
  phoneNumber: string; //必填
  email?: string; //可选
  address?: string; //可选
  remarks?: string; //可选
  fundCode?: string; //基金代码，用于标识机构投资者
  unifiedAccountId?: string; //一码通账户ID，用于标识个人投资者
}

/**
 * 创建联系人响应数据类型
 */
export interface CreateContactResponse {
  contactId: string;
}

/**
 * 创建联系人异步函数
 * @param {CreateContactParams} params - 创建参数
 * @returns {Promise<CreateContactResponse>} 创建结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 创建联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function createContact(params: CreateContactParams): Promise<CreateContactResponse> {
  // 修改记录 2025-08-11 - 删除调试日志，提高生产环境安全性 - hayden
  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as CreateContactResponse;
  return data;
}

/**
 * 更新联系人请求参数
 * @modified Miya 2025-07-31 - 添加基金代码和一码通ID参数
 */
export interface UpdateContactParams {
  contactId: string;
  organizationId: string;
  name?: string; // name改为可选
  phoneNumber?: string;
  email?: string;
  address?: string;
  remarks?: string;
  fundCode?: string; //基金代码
  unifiedAccountId?: string; //一码通账户ID
}

/**
 * 更新联系人响应数据类型
 */
export interface UpdateContactResponse {
  contactId: string;
}

/**
 * 更新联系人异步函数
 * @param {UpdateContactParams} params - 更新参数
 * @returns {Promise<UpdateContactResponse>} 更新结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 更新联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function updateContact(params: UpdateContactParams): Promise<UpdateContactResponse> {
  // 修改记录 2025-08-11 - 删除调试日志，提高生产环境安全性 - hayden
  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();

  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as UpdateContactResponse;
  return data;
}

/**
 * 删除联系人请求参数
 */
export interface DeleteContactParams {
  contactId: string;
  organizationId: string;
}

/**
 * 删除联系人响应数据类型
 */
export interface DeleteContactResponse {
  contactId: string;
}

/**
 * 删除联系人异步函数
 * @param {DeleteContactParams} params - 删除参数
 * @returns {Promise<DeleteContactResponse>} 删除结果，含contactId
 * @throws {Error} 请求或解密失败时抛出
 * @description
 * - 参数会被加密并签名
 * - 响应数据会被解密
 * - 仅处理200响应，其他状态抛出异常
 * - 错误码需由调用方处理
 *
 * 修改时间: 2025-07-10
 * 修改人: Miya
 * 关联需求: 删除联系人
 * 恢复方法: 删除本文件新增内容
 */
export async function deleteContact(params: DeleteContactParams): Promise<DeleteContactResponse> {

  // 1. 参数加密（需要 await）
  const encrypted = await encryptRequestData(params);

  // 2. 生成签名（使用加密后的内容）
  const sign = generateSign(encrypted);

  // 3. 发送POST请求
  const requestBody = { content: encrypted, sign };

  const res = await fetch("/api/investor-management/contacts/delete", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Sign": sign,
    },
    body: JSON.stringify(requestBody),
  });
  if (!res.ok) {
		const errorText = await res.text();
    throw new Error(`请求失败: ${res.status} - ${errorText}`);
  }

  // 4. 解密响应（需先 parse 再断言类型）
  const result = await res.json();
  if (result.code !== 200) {
    throw new Error(result.message || "接口返回错误");
  }
  // decryptData 返回 string，需 JSON.parse
  const data = JSON.parse(decryptData(result.data)) as DeleteContactResponse;
  return data;
}
