/**
 * 资料页 HTML数据获取工具库
 *
 * @fileoverview 提供基金详情HTML和Markdown数据的获取功能，支持多种数据格式解析
 * <AUTHOR>
 * @since 2025-07-22
 * @version 1.0.0
 *
 * 功能特性：
 * - 基金详情数据获取
 * - HTML和Markdown格式支持
 * - 数据加密传输
 * - 智能数据格式解析
 * - 错误处理和容错机制
 */

import { encryptRequestData, generateSign, decryptData } from "@repo/utils/lib/crypto";

/**
 * 基金卡片数据类型定义
 */
export type FundCard = {
  /** 基金代码（唯一标识） */
  code: string;
  /** 基金名称 */
  name: string;
  /** 基金类型（如"混合型"、"股票型"等） */
  type: string;
  /** 成立时间 */
  date: string;
  /** 总资产规模 */
  scale: string;
  /** 基金净值 */
  yield: string;
  /** 基金经理名字 */
  manager: string;
  /** 基金公司 */
  company: string;
  /** 标签类型："持仓本司" | "持仓对标" */
  tag: string;
};

/**
 * 获取基金详情数据
 *
 * 从后端API获取基金的HTML或Markdown格式详情数据
 *
 * @param card - 基金卡片数据对象
 * @returns 包含HTML、Markdown或错误信息的Promise对象
 *
 * 数据处理流程：
 * 1. 参数加密和签名
 * 2. 发送POST请求到API
 * 3. 响应数据解密
 * 4. 智能解析数据格式（JSON/HTML/纯文本）
 * 5. 返回格式化的数据
 *
 */
export async function fetchFundDetail(card: FundCard): Promise<{ html?: string; markdown?: string; error?: string }> {
  try {
    const businessData = { data: { stockCode: card.code }, timestamp: Date.now() };
    const content = await encryptRequestData(businessData);
    const sign = await generateSign(content);
    const requestBody = { content, sign };

    // 构建请求URL，包含stockCode查询参数
    const url = `/api/n8n_proxy/fund_details?fundcode=${card.code}`;


    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Sign": sign,
      },
      body: JSON.stringify(requestBody)
    });
    const data = await res.json();
    if (data.code === 200 && data.data) {
      let decrypted: any = await decryptData(data.data);
      // 如果decrypted是字符串，尝试解析JSON
      if (typeof decrypted === "string") {
        // 尝试解析为JSON
        try {
          decrypted = JSON.parse(decrypted);

          // 如果解析后是数组，取第一个元素
          if (Array.isArray(decrypted) && decrypted.length > 0) {
            decrypted = decrypted[0];
          }
        } catch (e) {
          // 如果不是JSON，检查是否是HTML内容
          const htmlPattern = /<[^>]+>/;
          if (htmlPattern.test(decrypted)) {
            return { html: decrypted };
          }
          // 如果不是JSON也不是HTML，可能是纯文本
          return { html: `<div style="padding: 20px; font-family: Arial, sans-serif;">${decrypted}</div>` };
        }
      }

      // 检查是否有html字段
      if (decrypted && typeof decrypted === "object" && decrypted.html) {
        return { html: decrypted.html };
      }

      return { error: "暂无内容" };
    }
    return { error: data.message || "接口请求失败" };
  } catch (e) {
    return { error: "加载失败" };
  }
}