"use client";

import { <PERSON>, Z<PERSON>, Sparkles } from "lucide-react";

interface AIAnalysisLoadingProps {
  currentParsingFile?: string;
}

/**
 * AI分析加载动画组件
 * 显示现代化的加载动画效果，用于AI分析处理状态
 */
export function AIAnalysisLoading({ currentParsingFile }: AIAnalysisLoadingProps) {
  return (
    <div className="flex items-center justify-center flex-1 relative overflow-hidden">
      <div className="text-center space-y-6 z-10">
        {/* 主要动画区域 */}
        <div className="relative">
          {/* 外圈脉冲动画 */}
          <div className="absolute inset-0 w-24 h-24 rounded-full bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 opacity-20 animate-ping"></div>
          <div className="absolute inset-2 w-20 h-20 rounded-full bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 opacity-30 animate-pulse"></div>

          {/* 中心图标容器 */}
          <div className="relative w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-2xl">
            {/* 旋转的外环 */}
            <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-white/40 border-r-white/30 animate-spin"></div>
            <div className="absolute inset-1 rounded-full border-2 border-transparent border-b-white/20 border-l-white/10 animate-spin" style={{ animationDirection: 'reverse', animationDuration: '3s' }}></div>

            {/* AI大脑图标 */}
            <Brain className="w-8 h-8 text-white animate-pulse" />

            {/* 闪烁的装饰元素 */}
            <Sparkles className="absolute -top-2 -right-2 w-4 h-4 text-yellow-300 animate-bounce" />
            <Zap className="absolute -bottom-1 -left-2 w-3 h-3 text-blue-300 animate-pulse" />
          </div>
        </div>

        {/* 文本内容 */}
        <div className="space-y-3">
          <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-pulse">
            AI分析中
          </h3>

          {/* 动态文本显示 */}
          <div className="text-sm text-muted-foreground space-y-2">
            <p className="animate-pulse font-medium">正在为您生成专属会前规划...</p>
            {currentParsingFile && currentParsingFile !== "批量发送数据中..." && (
              <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg px-3 py-2 border border-blue-200 dark:border-blue-800">
                <p className="text-xs text-blue-700 dark:text-blue-300 font-medium">
                  📄 处理文件: {currentParsingFile}
                </p>
              </div>
            )}
            {currentParsingFile === "批量发送数据中..." && (
              <div className="bg-purple-50 dark:bg-purple-950/20 rounded-lg px-3 py-2 border border-purple-200 dark:border-purple-800">
                <p className="text-xs text-purple-700 dark:text-purple-300 font-medium">
                  🚀 正在生成AI报告...
                </p>
              </div>
            )}
          </div>
        </div>

        {/* 进度指示器 */}
        <div className="flex justify-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full animate-bounce shadow-lg" style={{ animationDelay: '0ms' }}></div>
          <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full animate-bounce shadow-lg" style={{ animationDelay: '200ms' }}></div>
          <div className="w-3 h-3 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full animate-bounce shadow-lg" style={{ animationDelay: '400ms' }}></div>
        </div>

      </div>

      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-60">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-3/4 right-1/4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-1 h-1 bg-pink-400 rounded-full animate-ping" style={{ animationDelay: '3s' }}></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-yellow-400 rounded-full animate-ping" style={{ animationDelay: '4s' }}></div>
        <div className="absolute top-1/6 right-1/2 w-1.5 h-1.5 bg-green-400 rounded-full animate-ping" style={{ animationDelay: '5s' }}></div>

        {/* 渐变背景光晕 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse"></div>
      </div>
    </div>
  );
}
