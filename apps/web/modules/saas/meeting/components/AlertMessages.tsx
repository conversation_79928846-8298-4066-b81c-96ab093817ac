"use client";

import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { AlertCircleIcon, CheckCircleIcon } from "lucide-react";

interface AlertMessagesProps {
  error: string | null;
  success: boolean;
  fileCount: number;
}

/**
 * 错误和成功提示组件
 */
export function AlertMessages({ error, success, fileCount }: AlertMessagesProps) {
  return (
    <>
      {/* 错误提示 */}
      {error && (
        <Alert className="border-destructive bg-destructive/5 text-destructive mb-4">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>
            {error === "所选文件已在列表中"
              ? "文件已存在"
              : "上传失败"}
          </AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 成功提示 */}
      {success && (
        <Alert className="border-green-500 bg-green-500/5 text-green-500 mb-4">
          <CheckCircleIcon className="h-4 w-4" />
          <AlertTitle>分析成功</AlertTitle>
          <AlertDescription>
            成功分析 {fileCount} 个文件
          </AlertDescription>
        </Alert>
      )}
    </>
  );
}
