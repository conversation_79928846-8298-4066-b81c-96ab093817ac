"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ui/components/button";
import { Form } from "@ui/components/form";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useSession } from "../../auth/hooks/use-session";
import { toast } from "sonner";
import { useState, useEffect } from "react";

// 导入重构后的组件和 hooks
import { meetingFormSchema, type MeetingFormValues } from "../utils/meetingFormValidation";
import { useMeetingEditData } from "../hooks/use-meeting-edit-data";
import { useMeetingEditSubmit } from "../hooks/use-meeting-edit-submit";
import { MeetingBasicInfo } from "./MeetingBasicInfo";
import { MeetingRecurringSettings } from "./MeetingRecurringSettings";
import { MeetingGuestManagement } from "./MeetingGuestManagement";
import { MeetingSecuritySettings } from "./MeetingSecuritySettings";
import { MeetingFunctionSettings } from "./MeetingFunctionSettings";
import type { GuestData } from "../utils/guestExcelParser";
import {  smartFillGuestsWithDuplicateDetection } from "../utils/guestValidation";
import { InfoDialog } from "./MeetingDialogs";

/**
 * 会议编辑表单组件
 *
 * @param organizationSlug - 组织的 slug
 * @param meetingId - 会议ID
 * @returns 渲染的表单组件
 */
export function ChangeMeetingForm({ organizationSlug, meetingId }: { organizationSlug: string, meetingId: string }) {
  const { user } = useSession();

  const router = useRouter();

  // 嘉宾管理相关状态
  const [batchImportDialogOpen, setBatchImportDialogOpen] = useState(false);
  const [systemImportDialogOpen, setSystemImportDialogOpen] = useState(false);
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [validationTrigger, setValidationTrigger] = useState(0);

  // 检查是否应该显示嘉宾功能（这里简化为true，实际应该根据用户权限判断）
  const shouldShowGuestFeature = true;

  // 使用会议编辑数据获取hook
  const { meetingData, isLoading, error, formData } = useMeetingEditData(meetingId);

  // 使用会议编辑提交hook
  const { handleSubmit } = useMeetingEditSubmit(organizationSlug, meetingId, shouldShowGuestFeature);

  // 初始化表单
  const form = useForm<MeetingFormValues>({
    resolver: zodResolver(meetingFormSchema),
    defaultValues: {
      title: "",
      date: new Date().toISOString().split('T')[0],
      startTime: "09:00",
      endTime: "10:00",
      location: "",
      isRecurring: false,
      timezone: "Asia/Shanghai",
      password: "",
      requirePassword: true,
      muteParticipantsOnEntry: "muteOn",
      waitingRoom: false,
      recordMeeting: false,
      allowChat: true,
      enterInAdvance: true,
      multiPlatform: true,
      allowScreenShare: true,
      allowParticipantsToRename: true,
      enableHostKey: false,
      hostKey: "",
      enableDocUpload: true,
      screenWatermark: false,
      disableScreenshot: false,
      autoTranscribe: false,
      playIvrOnJoin: false,
      playIvrOnLeave: false,
      onlyEnterpriseUserAllowed: false,
      recurringType: 0,
      untilType: 0,
      untilDate: "",
      untilCount: 7,
      guests: [{ area: "86", phone_number: "", guest_name: "" }],
      enable_enroll: false,
      approval_mode: "manual",
      ...formData,
    },
  });


  // 当formData更新时，重置表单
  useEffect(() => {

    if (formData && Object.keys(formData).length > 0) {
      // 检查是否有有效的会议数据（不仅仅是默认值）
      

      // 重置表单数据
      form.reset(formData);
    }
  }, [formData]); // 移除 form 依赖，避免无限循环



  // 处理批量导入嘉宾
  const handleBatchImport = (importedGuests: GuestData[]) => {
    const currentGuests = form.getValues("guests") || [];
    // 使用智能填充逻辑，优先填充空白行，并检测重复参会成员
    const result = smartFillGuestsWithDuplicateDetection(currentGuests, importedGuests);
    form.setValue("guests", result.guests);
    setBatchImportDialogOpen(false);

    // 显示重复参会成员反馈
    if (result.duplicateInfo.count > 0) {
      const duplicateNames = result.duplicateInfo.details
        .map((detail: any) => detail.guest.guest_name || '未知')
        .slice(0, 5); // 最多显示5个名字

      const message = result.duplicateInfo.count <= 5
        ? `已跳过重复成员：${duplicateNames.join('、')}`
        : `已跳过${result.duplicateInfo.count}位重复成员：${duplicateNames.join('、')}等`;

      toast.warning(message);
    }

    // 触发验证
    setValidationTrigger(Date.now());
  };

  // 处理系统导入嘉宾
  const handleSystemImport = (importedGuests: GuestData[]) => {
    const currentGuests = form.getValues("guests") || [];
    // 使用智能填充逻辑，优先填充空白行，并检测重复参会成员
    const result = smartFillGuestsWithDuplicateDetection(currentGuests, importedGuests);
    form.setValue("guests", result.guests);
    setSystemImportDialogOpen(false);

    // 显示重复参会成员反馈
    if (result.duplicateInfo.count > 0) {
      const duplicateNames = result.duplicateInfo.details
        .map((detail: any) => detail.guest.guest_name || '未知')
        .slice(0, 5); // 最多显示5个名字

      const message = result.duplicateInfo.count <= 5
        ? `已跳过重复参会成员：${duplicateNames.join('、')}`
        : `已跳过${result.duplicateInfo.count}位重复参会成员：${duplicateNames.join('、')}等`;

      toast.warning(message);
    }

    // 触发验证
    setValidationTrigger(Date.now());
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-sm text-muted-foreground">正在加载会议信息...</div>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-sm text-destructive">加载会议信息失败: {error}</div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <InfoDialog open={infoDialogOpen} onOpenChange={setInfoDialogOpen} email={user?.email || ""} />
      <form
        onSubmit={form.handleSubmit((values) => handleSubmit(values, form.setError))}
        className="space-y-6"
      >
        {/* 会议基本信息 */}
        <MeetingBasicInfo control={form.control} />

        {/* 周期性会议设置 */}
        <MeetingRecurringSettings control={form.control} watch={form.watch} />

        {/* 会议嘉宾管理 */}
        <MeetingGuestManagement
          control={form.control}
          getValues={form.getValues}
          setValue={form.setValue}
          watch={form.watch}
          shouldShowGuestFeature={shouldShowGuestFeature}
          batchImportDialogOpen={batchImportDialogOpen}
          setBatchImportDialogOpen={setBatchImportDialogOpen}
          handleBatchImport={handleBatchImport}
          systemImportDialogOpen={systemImportDialogOpen}
          setSystemImportDialogOpen={setSystemImportDialogOpen}
          handleSystemImport={handleSystemImport}
          triggerValidation={validationTrigger}
        />

        {/* 安全设置 */}
        <MeetingSecuritySettings control={form.control} watch={form.watch} />

        {/* 会议功能设置 */}
        <MeetingFunctionSettings control={form.control} />



        {/* 错误提示和提交按钮 */}
        {form.formState.errors.root && (
          <p className="text-xs font-medium text-destructive">
            {form.formState.errors.root.message}
          </p>
        )}
        <div className="sticky bottom-0 bg-background p-4 -mx-6 mt-8 z-10">
        <div className="flex justify-start gap-4">
          <Button
            type="submit"
            loading={form.formState.isSubmitting}
            className="w-[150px]"
          >
            修改会议
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(
                `/app/${organizationSlug}/meeting/list`,
              )
            }
            className="w-[150px]"
          >
            取消
          </Button>
        </div>
        </div>
      </form>
    </Form>
  );
} 