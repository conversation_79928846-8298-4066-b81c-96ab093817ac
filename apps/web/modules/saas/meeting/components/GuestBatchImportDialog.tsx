"use client";

import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { ExcelFileUpload } from "./ExcelFileUpload";
import { parseGuestExcelFile, validateExcelFileFormat } from "../utils/guestExcelParser";
import type { GuestData } from "../utils/guestExcelParser";
import { countValidGuests } from "../utils/guestValidation";
import { toast } from "sonner";

interface GuestBatchImportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: (guests: GuestData[]) => void;
  currentGuestCount: number;
}

/**
 * 成员批量导入对话框
 * 简化的导入流程：选择文件 → 直接导入
 */
export function GuestBatchImportDialog({
  isOpen,
  onOpenChange,
  onImportComplete,
  currentGuestCount,
}: GuestBatchImportDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  // 处理对话框关闭
  const handleClose = () => {
    if (!isLoading) {
      setIsLoading(false);
      onOpenChange(false);
    }
  };

  // 处理文件导入
  const handleFileImport = async (file: File) => {
    // 验证文件格式
    const validation = validateExcelFileFormat(file);
    if (!validation.valid) {
      toast.error(validation.error);
      return;
    }

    setIsLoading(true);

    try {
      // 解析Excel文件
      const result = await parseGuestExcelFile(file);

      // 检查解析是否成功
      if (!result.success) {
        toast.error(`文件解析失败：${result.errors.join(', ')}`);
        return;
      }

      // 检查总数量限制
      const totalAfterImport = currentGuestCount + result.data.length;
      if (totalAfterImport > 2000) {
        toast.error(
          `导入后总成员数量将达到${totalAfterImport}人，超过2000人限制。当前已有${currentGuestCount}人，最多还能导入${2000 - currentGuestCount}人。`
        );
        return;
      }

      // 直接导入数据
      onImportComplete(result.data);
      toast.success(`成功导入${result.data.length}位成员`);

      // 关闭对话框
      handleClose();

    } catch (error: any) {
      toast.error(`文件解析失败: ${error.message || '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>批量导入成员</DialogTitle>
          <DialogDescription>
            上传Excel文件批量导入成员信息。文件应包含3列：国家代码、联系方式（手机号）、成员名称。
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <ExcelFileUpload
            onImport={handleFileImport}
            onCancel={handleClose}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </div>

      </DialogContent>
    </Dialog>
  );
}
