"use client";

import { useState, use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Database, Users, Search, X, Trash2 } from "lucide-react";
import type { GuestData } from "../utils/guestExcelParser";
import { ActiveOrganizationContext } from "../../organizations/lib/active-organization-context";
import { toast } from "sonner";
import { Table, ConfigProvider } from "antd";
import type { TableColumnType } from "antd";

import zhCN from "antd/lib/locale/zh_CN";
import React from "react";
import { AntMeetingTable } from "@saas/meeting/components/ant-meeting-table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipPortal
} from "@ui/components/tooltip";
import {
  useGuestShareholders,
  type SelectableShareholderData,
  type ShareholderData
} from "../hooks/useGuestShareholders";
import { useContacts } from "../../investors/hooks/useContacts";
import { useFundManagerData } from "../../investors/hooks/useSequence";
import type { Contact } from "../../investors/lib/contacts_data";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

// 扩展SelectableShareholderData以包含添加时的模式信息
interface ExtendedSelectableShareholderData extends SelectableShareholderData {
  addedAsType?: string; // 记录添加到右侧时的模式：1-股东模式，2-联系人模式
}
import { PaginationStatus } from "./PaginationStatus";

/**
 * 渲染带有Tooltip的截断文本
 * 用于在表格中显示长文本时提供截断和悬停提示功能
 * @param text 要显示的文本
 * @param maxLength 最大文本长度，超过则截断并显示tooltip
 * @returns 截断后的文本组件或原文本
 * 修改人：miya 修改时间：2025/7/30
 */
function renderTruncatedWithTooltip(text: string | undefined | null, maxLength = 15) {
  // 如果文本为空、undefined或null，返回占位符
  if (text === undefined || text === null) {
    return "-";
  }

  if (text === "") {
    return "-";
  }

  // 确保text是字符串类型
  const textStr = String(text);

  // 使用更智能的判断：只有当文本实际超出maxLength时才截断
  const shouldTruncate = textStr.length > maxLength;

  if (!shouldTruncate) {
    return textStr;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-default truncate inline-block max-w-full">
            {textStr.slice(0, maxLength)}...
          </span>
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side="top" align="start" className="max-w-[300px]">
            {textStr}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
}
interface GuestSystemImportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete: (guests: GuestData[]) => void;
  currentGuestCount: number;
}

/**
 * 嘉宾系统导入对话框
 * 采用左右双面板布局设计，参考MeetingFileUpload.tsx的实现模式
 * 支持股东和联系人两种模式的成员导入：
 * - 股东模式：显示股东名字、联系方式、期数
 * - 联系人模式：显示投资人、股东、电话号码
 * 修改人：miya 修改时间：2025/7/30
 */
export function GuestSystemImportDialog({
  isOpen,
  onOpenChange,
  onImportComplete,
}: GuestSystemImportDialogProps) {
  const activeOrganizationContext = React.useContext(ActiveOrganizationContext);
  const orgId = activeOrganizationContext?.activeOrganization?.id;
  const { activeOrganization } = useActiveOrganization();

  /**
   * 选择类型状态：1-股东模式，2-联系人模式
   * 控制表格列标题和数据显示方式：
   * - 股东模式：显示"股东名字"、"联系方式"、"期数"
   * - 联系人模式：显示"投资人"、"股东"、"电话号码"
   * 修改人：miya 修改时间：2025/7/30
   */
  const [selectedType, setSelectedType] = useState<string>("1");

  // 使用新的分页Hook - 股东数据
  const {
    shareholders,
    pagination,
    isLoading: isShareholdersLoading,
    isLoadingMore,
    error: shareholdersError,
    searchTerm,
    selectedShareholders,
    selectedCount,
    loadMore,
    handleSearch,
    resetSearch,
    resetSearchKeepData,
    handleShareholderSelect,
    handleBatchSelect,
    clearAllSelections,
  } = useGuestShareholders(orgId || "", {
    enabled: !!orgId && selectedType === "1", // 只在股东模式下启用
    pageSize: 20
  });

  /**
   * 联系人数据获取
   * 当选择联系人模式时，获取所有联系人数据
   * 修改人：miya 修改时间：2025-08-01
   */
  const { data: contactsData, isLoading: isContactsLoading, error: contactsError } = useContacts(
    {
      organizationId: activeOrganization?.id || "",
      queryAllContacts: true, // 获取全部联系人数据
      name: searchTerm, // 使用搜索词进行过滤
    },
    {
      enabled: !!activeOrganization?.id && selectedType === "2", // 只在联系人模式下启用
    }
  );

  /**
   * 基金经理数据获取
   * 根据联系人的fundCode获取基金名称
   * 修改人：miya 修改时间：2025-08-01
   */
  const fundCodes = useMemo(() => {
    if (selectedType !== "2" || !contactsData?.contacts) return [];
    return Array.from(new Set(contactsData.contacts.map(contact => contact.fundCode).filter(Boolean))) as string[];
  }, [selectedType, contactsData?.contacts]);

  const { data: fundManagerData, isLoading: isFundManagerLoading } = useFundManagerData(
    { investors: fundCodes },
    {
      enabled: selectedType === "2" && fundCodes.length > 0,
    }
  );

  /**
   * 已确认的成员列表（右侧面板）- 支持股东和联系人两种类型
   * 用于存储用户从左侧选择并确认的成员数据，包含添加时的模式信息
   * 修改人：miya 修改时间：2025/7/30
   */
  const [confirmedShareholders, setConfirmedShareholders] = useState<ExtendedSelectableShareholderData[]>([]);
  const [rightCurrentPage, setRightCurrentPage] = useState(1);
  const [isRightScrollLoading, setIsRightScrollLoading] = useState(false);

  // 滚动相关引用
  const scrollRef = useRef({
    left: 0,
    top: 0,
  });
  const leftTableScrollRef = useRef<HTMLDivElement>(null);
  const rightTableScrollRef = useRef<HTMLDivElement>(null);

  /**
   * 统一的加载状态
   * 根据当前模式返回对应的加载状态
   * 修改人：miya 修改时间：2025-08-01
   */
  const isLoading = useMemo(() => {
    if (selectedType === "1") {
      return isShareholdersLoading;
    } else {
      return isContactsLoading || isFundManagerLoading;
    }
  }, [selectedType, isShareholdersLoading, isContactsLoading, isFundManagerLoading]);

  /**
   * 统一的错误状态
   * 根据当前模式返回对应的错误状态
   * 修改人：miya 修改时间：2025-08-01
   */
  const error = useMemo(() => {
    if (selectedType === "1") {
      return shareholdersError;
    } else {
      return contactsError;
    }
  }, [selectedType, shareholdersError, contactsError]);

  /**
   * 当前显示的成员列表 - 根据selectedType显示股东或联系人
   * 联系人模式下，将联系人数据转换为股东数据格式，并添加基金名称信息
   * 修改人：miya 修改时间：2025-08-01
   */
  const displayedShareholders = useMemo(() => {
    if (selectedType === "1") {
      // 股东模式：使用股东数据
      return shareholders.map(shareholder => ({
        ...shareholder,
        id: shareholder.id,
      }));
    } else {
      // 联系人模式：将联系人数据转换为股东数据格式
      if (!contactsData?.contacts) return [];

      return contactsData.contacts.map(contact => {
        // 根据fundCode查找对应的基金名称
        const fundInfo = fundManagerData?.funds?.find(fund => fund.fundCode === contact.fundCode);

        return {
          id: contact.contactId,
          securitiesAccountName: contact.name, // 联系人姓名作为股东名字
          contactNumber: contact.phoneNumber, // 联系人电话
          registerDate: contact.createdAt, // 创建时间作为期数
          isSelected: false,
          // 添加联系人特有的字段
          fundName: fundInfo?.fundName || '', // 基金名称作为投资人信息
          fundCode: contact.fundCode || '',
        };
      });
    }
  }, [selectedType, shareholders, contactsData?.contacts, fundManagerData?.funds]);

  // 是否还有更多数据
  const hasMoreData = useMemo(() => {
    return pagination?.hasNext || false;
  }, [pagination?.hasNext]);

  // 右侧表格的分页显示逻辑 - 已选成员（股东或联系人）
  const rightPageSize = 20;
  const displayedConfirmedShareholders = useMemo(() => {
    const endIndex = rightCurrentPage * rightPageSize;
    const result = confirmedShareholders.slice(0, endIndex);

    return result.map(shareholder => ({
      ...shareholder,
      id: shareholder.id,
    }));
  }, [confirmedShareholders, rightCurrentPage]);

  // 右侧表格是否还有更多数据
  const rightHasMoreData = useMemo(() => {
    return rightCurrentPage * rightPageSize < confirmedShareholders.length;
  }, [rightCurrentPage, confirmedShareholders.length]);



  // 处理对话框关闭
  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
      // 只清空右侧已选成员，保留左侧数据和搜索状态
      handleClearConfirmedShareholders();
      // 清空左侧选择状态，但保留数据
      clearAllSelections();
    }
  };

  /**
   * 联系人模式的选择状态管理
   * 修改人：miya 修改时间：2025-08-01
   */
  const [contactSelections, setContactSelections] = useState<Record<string, boolean>>({});

  /**
   * 处理联系人选择
   * 修改人：miya 修改时间：2025-08-01
   */
  const handleContactSelect = useCallback((contactId: string, isSelected: boolean) => {
    setContactSelections(prev => ({
      ...prev,
      [contactId]: isSelected
    }));
  }, []);

  /**
   * 处理联系人批量选择
   * 修改人：miya 修改时间：2025-08-01
   */
  const handleContactBatchSelect = useCallback((contactIds: string[], isSelected: boolean) => {
    setContactSelections(prev => {
      const newSelections = { ...prev };
      contactIds.forEach(id => {
        newSelections[id] = isSelected;
      });
      return newSelections;
    });
  }, []);

  // 处理全选/取消全选
  const handleSelectAll = useCallback((isSelected: boolean) => {
    if (selectedType === "1") {
      // 股东模式：使用原有逻辑
      const displayedIds = displayedShareholders.map(s => s.id);
      handleBatchSelect(displayedIds, isSelected);
    } else {
      // 联系人模式：使用联系人选择逻辑
      const displayedIds = displayedShareholders.map(s => s.id);
      handleContactBatchSelect(displayedIds, isSelected);
    }
  }, [selectedType, displayedShareholders, handleBatchSelect, handleContactBatchSelect]);

  // 计算全选状态
  const selectAllState = useMemo(() => {
    const visibleShareholders = displayedShareholders;
    if (visibleShareholders.length === 0) return { checked: false, indeterminate: false };

    let selectedCount = 0;
    if (selectedType === "1") {
      selectedCount = visibleShareholders.filter((s: any) => s.isSelected).length;
    } else {
      selectedCount = visibleShareholders.filter((s: any) => contactSelections[s.id]).length;
    }

    if (selectedCount === 0) return { checked: false, indeterminate: false };
    if (selectedCount === visibleShareholders.length) return { checked: true, indeterminate: false };
    return { checked: false, indeterminate: true };
  }, [displayedShareholders, selectedType, contactSelections]);

  // 处理左侧表格滚动事件
  const handleLeftTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 记录滚动容器引用
    if (!leftTableScrollRef.current && event.target) {
      leftTableScrollRef.current = event.target as HTMLDivElement;
    }

    // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
    if (
      Math.abs(scrollTop - scrollRef.current.top) > 0 &&
      scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
      hasMoreData &&
      !isLoadingMore &&
      !isLoading
    ) {
      // 调用Hook提供的loadMore函数
      loadMore();
    }

    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  }, [hasMoreData, isLoadingMore, isLoading, loadMore]);

  // 处理右侧表格滚动事件
  const handleRightTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 记录滚动容器引用
    if (!rightTableScrollRef.current && event.target) {
      rightTableScrollRef.current = event.target as HTMLDivElement;
    }

    // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
    if (
      Math.abs(scrollTop - scrollRef.current.top) > 0 &&
      scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
      rightHasMoreData &&
      !isRightScrollLoading
    ) {
      // 设置滚动加载状态
      setIsRightScrollLoading(true);

      // 加载下一页
      setRightCurrentPage(prev => prev + 1);

      // 延迟重置加载状态，避免频繁触发
      setTimeout(() => {
        setIsRightScrollLoading(false);
      }, 500);
    }

    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  }, [rightHasMoreData, isRightScrollLoading]);

  // 左侧表格滚动配置
  const leftScrollConfig = useMemo(() => {
    // 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
    const shouldEnableYScroll = displayedShareholders.length > 10;

    return {
      x: "max-content", // 启用水平滚动
      y: shouldEnableYScroll ? "calc(90vh - 350px)" : undefined, // 根据数据量决定是否启用垂直滚动
      scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
    };
  }, [displayedShareholders.length]);

  // 右侧表格滚动配置
  const rightScrollConfig = useMemo(() => {
    // 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
    const shouldEnableYScroll = displayedConfirmedShareholders.length > 10;

    return {
      x: "max-content", // 启用水平滚动
      y: shouldEnableYScroll ? "calc(90vh - 350px)" : undefined, // 根据数据量决定是否启用垂直滚动
      scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
    };
  }, [displayedConfirmedShareholders.length]);

  // 清空左侧选择
  const handleClearLeftSelection = useCallback(() => {
    if (selectedType === "1") {
      // 股东模式：使用原有逻辑
      const displayedIds = displayedShareholders.map(s => s.id);
      handleBatchSelect(displayedIds, false);
    } else {
      // 联系人模式：清空联系人选择状态
      setContactSelections({});
    }
  }, [selectedType, displayedShareholders, handleBatchSelect]);

  /**
   * 获取当前选中的成员列表
   * 根据模式返回不同的选中数据
   * 修改人：miya 修改时间：2025-08-01
   */
  const currentSelectedMembers = useMemo(() => {
    if (selectedType === "1") {
      return selectedShareholders;
    } else {
      // 联系人模式：从displayedShareholders中筛选出被选中的
      return displayedShareholders.filter(member => contactSelections[member.id]);
    }
  }, [selectedType, selectedShareholders, displayedShareholders, contactSelections]);

  /**
   * 当前选中的成员数量
   * 修改人：miya 修改时间：2025-08-01
   */
  const currentSelectedCount = useMemo(() => {
    return currentSelectedMembers.length;
  }, [currentSelectedMembers]);

  // 确认选择，将选中的成员移动到右侧
  const handleConfirmSelection = useCallback(() => {
    if (currentSelectedMembers.length === 0) {
      // toast.warning(`请先选择要添加的${selectedType === "1" ? "股东" : "联系人"}`);
      return;
    }

    setConfirmedShareholders(prev => {
      // 避免重复添加
      const existingIds = prev.map(s => s.id);
      const newMembers = currentSelectedMembers.filter(s => !existingIds.includes(s.id));
      const duplicateCount = currentSelectedMembers.length - newMembers.length;

      if (duplicateCount > 0) {
        // toast.warning(`已跳过 ${duplicateCount} 位重复的${selectedType === "1" ? "股东" : "联系人"}`);
      }

      if (newMembers.length > 0) {
        // toast.success(`成功添加 ${newMembers.length} 位${selectedType === "1" ? "股东" : "联系人"}到待导入列表`);
      }

      // 为新添加的数据标记当前的模式
      const newMembersWithType: ExtendedSelectableShareholderData[] = newMembers.map(member => ({
        ...member,
        addedAsType: selectedType // 记录添加时的模式
      }));

      return [...prev, ...newMembersWithType];
    });

    // 重置右侧分页到第一页
    setRightCurrentPage(1);

    // 清空左侧选择
    if (selectedType === "1") {
      clearAllSelections();
    } else {
      setContactSelections({});
    }
  }, [currentSelectedMembers, selectedType, clearAllSelections]);

  // 从右侧移除单个成员
  const handleRemoveConfirmedShareholder = useCallback((id: string) => {
    setConfirmedShareholders(prev =>
      prev.filter(shareholder => shareholder.id !== id)
    );
  }, []);

  // 清空右侧所有已选成员
  const handleClearConfirmedShareholders = useCallback(() => {
    setConfirmedShareholders([]);
  }, []);

  /**
   * 最终导入成员数据（股东或联系人）
   * 将已选择的成员数据转换为会议嘉宾格式并导入
   * 无论是股东模式还是联系人模式，都统一转换为嘉宾数据格式
   * 修改人：miya 修改时间：2025/7/30
   */
  const handleFinalImport = useCallback(() => {
    if (confirmedShareholders.length === 0) {
      // toast.warning(`请先选择要导入的${selectedType === "1" ? "股东" : "联系人"}`);
      return;
    }

    try {
      // 转换为GuestData格式 - 无论是股东还是联系人，都统一转换为嘉宾数据
      const guestData: GuestData[] = confirmedShareholders.map(shareholder => ({
        area: "86", // 默认添加区号86
        phone_number: shareholder.contactNumber,
        guest_name: shareholder.securitiesAccountName
      }));

      // 调用导入完成回调
      onImportComplete(guestData);
      // toast.success(`成功导入 ${guestData.length} 位${selectedType === "1" ? "股东" : "联系人"}到会议嘉宾列表`);
      handleClose();
    } catch (error: any) {
      toast.error(`导入${selectedType === "1" ? "股东" : "联系人"}数据失败，请重试`);
      console.error("导入数据失败:", error);
    }
  }, [confirmedShareholders, onImportComplete, selectedType]);

  // Hook会自动处理数据获取，不需要手动调用

  /**
   * 格式化期数日期显示（仅在股东模式下使用）
   * 将日期字符串转换为 YYYY-MM-DD 格式
   * 如果日期无效则返回原字符串或"-"
   * 修改人：miya 修改时间：2025/7/30
   */
  const formatRegisterDate = (dateString: string) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
    } catch (error) {
      return dateString;
    }
  };



  /**
   * 左侧表格的columns配置 - 根据selectedType动态显示股东或联系人列标题
   * 股东模式：股东名字、联系方式、期数
   * 联系人模式：投资人、股东、电话号码
   * 修改人：miya 修改时间：2025/7/30
   */
  const leftTableColumns: TableColumnType<SelectableShareholderData>[] = useMemo(() => [
    {
      title: () => (
        <input
          type="checkbox"
          checked={selectAllState.checked}
          ref={(input) => {
            if (input) input.indeterminate = selectAllState.indeterminate;
          }}
          onChange={(e) => handleSelectAll(e.target.checked)}
          className="h-4 w-4"
        />
      ),
      dataIndex: "isSelected",
      key: "isSelected",
      width: 50, // 减小宽度
      render: (_, record) => (
        <input
          type="checkbox"
          checked={selectedType === "1" ? record.isSelected : contactSelections[record.id] || false}
          onChange={(e) => {
            if (selectedType === "1") {
              handleShareholderSelect(record.id, e.target.checked);
            } else {
              handleContactSelect(record.id, e.target.checked);
            }
          }}
          className="h-4 w-4" // 减小复选框大小
        />
      ),
    },
    {
      title: selectedType === "1" ? "股东名字" : "投资机构",
      dataIndex: selectedType === "1" ? "securitiesAccountName" : "fundName",
      key: selectedType === "1" ? "securitiesAccountName" : "fundName",
      width: 120, // 保持原有宽度设置
      className: "text-center font-medium", // 参考 ShareholderTable 的样式
      render: (value: string, record: any) => {
        const displayValue = selectedType === "1" ? record.securitiesAccountName : record.fundName;
        // 股东模式：15个字符，联系人模式：12个字符
        const maxLength = selectedType === "1" ? 15 : 12;
        return renderTruncatedWithTooltip(String(displayValue || ''), maxLength);
      },
    },
    {
      title: selectedType === "1" ? "联系方式" : "姓名",
      dataIndex: selectedType === "1" ? "contactNumber" : "securitiesAccountName",
      key: selectedType === "1" ? "contactNumber" : "securitiesAccountName",
      width: 90, // 减小宽度
      ellipsis: true, // 启用省略号
      render: (value: string) => (
        <div className="text-xs text-muted-foreground truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      title: selectedType === "1" ? "期数" : "电话号码",
      dataIndex: selectedType === "1" ? "registerDate" : "contactNumber",
      key: selectedType === "1" ? "registerDate" : "contactNumber",
      width: 80, // 设置宽度
      ellipsis: true, // 启用省略号
      render: (value: string) => (
        <div className="text-xs text-muted-foreground truncate" title={selectedType === "1" ? formatRegisterDate(value) : value}>
          {selectedType === "1" ? formatRegisterDate(value) : value}
        </div>
      ),
    },
  ], [handleShareholderSelect, handleSelectAll, selectAllState, formatRegisterDate, selectedType]);

  /**
   * 右侧已选成员表格的columns配置 - 固定列标题，根据当前模式显示数据
   * 列标题固定为：股东名字、联系方式、期数、操作
   * 数据显示逻辑：
   * - 股东模式：正常显示股东名字、联系方式、期数
   * - 联系人模式：股东名字显示股东字段、联系方式显示电话号码、期数显示"-"
   * 修改人：miya 修改时间：2025/7/30
   */
  const rightTableColumns: TableColumnType<ExtendedSelectableShareholderData>[] = useMemo(() => [
    {
      title: "姓名",
      dataIndex: "securitiesAccountName",
      key: "securitiesAccountName",
      width: 120, // 参考 ShareholderTable 的宽度设置
      className: "text-center font-medium", // 参考 ShareholderTable 的样式
      render: (name: string) => {
        // 在联系人模式下，股东名字列显示的是股东字段（实际还是securitiesAccountName）
        return renderTruncatedWithTooltip(String(name), 15);
      },
    },
    {
      title: "联系方式",
      dataIndex: "contactNumber",
      key: "contactNumber",
      width: 90, // 减小宽度
      ellipsis: true, // 启用省略号
      render: (contact: string) => {
        // 在联系人模式下，联系方式列显示的是电话号码（实际还是contactNumber）
        return (
          <div className="text-xs text-muted-foreground truncate" title={contact}>
            {contact}
          </div>
        );
      },
    },
    {
      title: "期数",
      dataIndex: "registerDate",
      key: "registerDate",
      width: 80, // 设置宽度
      ellipsis: true, // 启用省略号
      render: (registerDate: string, record: ExtendedSelectableShareholderData) => {
        // 根据数据添加时的模式来显示：联系人模式添加的显示"-"，股东模式添加的显示格式化的期数
        const displayValue = record.addedAsType === "2" ? "-" : formatRegisterDate(registerDate);
        return (
          <div className="text-xs text-muted-foreground truncate" title={displayValue}>
            {displayValue}
          </div>
        );
      },
    },
    {
      title: "操作",
      key: "action",
      width: 50,
      ellipsis: true,
      render: (_, record: ExtendedSelectableShareholderData) => (
        <div className="flex justify-center items-center">
          <Button
            onClick={() => handleRemoveConfirmedShareholder(record.id)}
            variant="ghost"
            size="icon"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      ),
    },
  ], [handleRemoveConfirmedShareholder, formatRegisterDate, selectedType]);

  return (
			<Dialog open={isOpen} onOpenChange={handleClose}>
				<DialogContent className="max-w-7xl w-[100vw] h-[90vh] overflow-hidden flex flex-col">
					<DialogHeader className="flex-shrink-0 pb-4">
						<DialogTitle>系统导入成员</DialogTitle>
						<DialogDescription>
							可从系统联系人或数据源导入成员，仅支持手机号
						</DialogDescription>
					</DialogHeader>

					{/* 双面板布局 - 使用固定高度和独立滚动 */}
					<div className="flex flex-col xl:flex-row gap-4 flex-1 min-h-0 py-2">
						{/* 左侧面板 - 成员列表（股东或联系人） */}
						<div className="flex-1 xl:w-1/2 min-h-0 flex flex-col">
							<CardContent className="flex-1 flex flex-col min-h-0 p-0 pb-8">
								{/* 搜索和筛选区域 - 固定高度 */}
								<div className="flex-shrink-0 mb-12">
									<div className="flex gap-6">
										{/*
										  成员类型选择器 - 控制表格显示模式
										  修改人：miya 修改时间：2025/7/30
										  修改说明：添加联系人选项，支持股东和联系人两种显示模式
										*/}
										<Select value={selectedType} onValueChange={setSelectedType}>
											<SelectTrigger
												className="w-23 text-sm text-muted-foreground hover:text-foreground"
												onFocus={(e) => e.target.blur()}
											>
												<SelectValue />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="1">
													股东
												</SelectItem>
												<SelectItem value="2">
													联系人
												</SelectItem>
											</SelectContent>
										</Select>
										{/* 搜索框 */}
										<div className="relative flex-1">
											<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
											<Input
												placeholder={selectedType === "1" ? "搜索股东姓名或联系方式..." : "搜索投资人姓名或联系方式..."}
												value={searchTerm || ''}
												onChange={(e) =>
													handleSearch(e.target.value)
												}
												className="pl-10"
												disabled={isLoading}
											/>
											{/* 添加删除按钮，在已有输入的情况下展示，点击后可删除输入 */}
											{searchTerm && (
												<button
													type="button"
													className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 flex items-center justify-center text-muted-foreground hover:text-foreground"
													onClick={() =>
														resetSearch()
													}
													title="清除搜索"
												>
													<X className="h-4 w-4" />
												</button>
											)}
										</div>
									</div>

									{/* 搜索结果统计 */}
									{searchTerm && (
										<div className="px-6 pb-2 text-sm text-muted-foreground">
											{isLoading
												? "正在搜索..."
												: `找到 ${pagination?.total || 0} 条匹配结果`}
										</div>
									)}
								</div>

								{/* 错误提示 */}
								{error && (
									<div className="flex-shrink-0 mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-2 text-destructive">
												<X className="h-4 w-4" />
												<span className="text-sm">
													{error.message ||
														`加载${selectedType === "1" ? "股东" : "联系人"}数据失败`}
												</span>
											</div>
											<Button
												variant="outline"
												size="sm"
												onClick={() =>
													window.location.reload()
												}
												className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
											>
												重试
											</Button>
										</div>
									</div>
								)}

								{/* 股东表格 - 设置固定高度确保一致性 */}
								<div className="flex-1 min-h-[calc(60vh-200px)]">
									<AntMeetingTable
										columns={leftTableColumns}
										data={displayedShareholders}
										emptyContent={
											error
												? "加载失败，请重试"
												: isLoading
													? `正在加载${selectedType === "1" ? "股东" : "联系人"}数据...`
													: `暂无${selectedType === "1" ? "股东" : "联系人"}数据`
										}
										loading={isLoading || isLoadingMore}
										onScroll={handleLeftTableScroll}
										scroll={leftScrollConfig}
									/>

									<PaginationStatus
										currentLoaded={
											displayedShareholders.length
										}
										total={pagination?.total || 0}
										isLoading={
											isLoading &&
											displayedShareholders.length === 0
										}
										className="justify-end flex-shrink-0 "
									/>
								</div>

								{/* 分页状态显示 */}
							</CardContent>

							{/* 左侧操作按钮 - 固定在底部 */}
							<div className="flex-shrink-0 flex justify-between">
								<Button
									variant="outline"
									onClick={handleClearLeftSelection}
									disabled={currentSelectedCount === 0 || isLoading}
								>
									清空
								</Button>
								<Button
									onClick={handleConfirmSelection}
									disabled={currentSelectedCount === 0 || isLoading}
								>
									确认选择 ({currentSelectedCount})
								</Button>
							</div>
						</div>

						{/* 右侧面板 - 已选成员管理（股东或联系人） */}
						<div className="flex-1 xl:w-1/2 min-h-0 flex flex-col">
							<CardContent className="flex-1 flex flex-col min-h-0 p-0 pb-8">
								{/* 标题区域 - 固定高度，与左侧搜索区域高度保持一致 */}
								<div className="flex-shrink-0 mb-12  flex items-center justify-end">
									<span className="text-lg text-muted-foreground text-bold h-9">
										已选
									</span>
								</div>

								{/* 已选成员表格 - 固定列标题（股东名字、联系方式、期数、操作），根据添加时的模式显示数据 */}
								<div className="flex-1 min-h-[calc(60vh-200px)]">
									<AntMeetingTable
										data={displayedConfirmedShareholders}
										columns={rightTableColumns}
										emptyContent={
											<div className="h-[calc(60vh-200px)] flex items-center justify-center text-muted-foreground">
												从左侧选择成员后，已选成员将显示在这里
											</div>
										}
										loading={isRightScrollLoading}
										onScroll={handleRightTableScroll}
										scroll={rightScrollConfig}
									/>
									{/* 分页状态显示 - 与左侧保持一致 字体居中*/}
									<PaginationStatus
										currentLoaded={
											displayedConfirmedShareholders.length
										}
										total={
											confirmedShareholders.length || 0
										}
										isLoading={
											isLoading &&
											displayedConfirmedShareholders.length ===
												0
										}
										className="flex-shrink-0"
									/>
								</div>
							</CardContent>

							{/* 右侧操作按钮 - 固定在底部 */}
							<div className="flex-shrink-0 flex justify-between  mt-3">
								<Button
									variant="outline"
									onClick={handleClearConfirmedShareholders}
									disabled={
										confirmedShareholders.length === 0
									}
								>
									清空
								</Button>
								<Button
									onClick={handleFinalImport}
									disabled={
										confirmedShareholders.length === 0
									}
									className="bg-primary hover:bg-primary/90"
								>
									确认导入 ({confirmedShareholders.length})
								</Button>
							</div>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		);
}









