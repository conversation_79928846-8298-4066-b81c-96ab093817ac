"use client";

import { memo } from "react";

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

/**
 * Markdown渲染器组件
 * 将Markdown字符串渲染为格式化的HTML
 */
export const MarkdownRenderer = memo(function MarkdownRenderer({
  content,
  className = "",
}: MarkdownRendererProps) {
  // 解析Markdown内容并转换为JSX
  const parseMarkdown = (text: string) => {
    const lines = text.split('\n');
    const elements: JSX.Element[] = [];
    let currentList: JSX.Element[] = [];
    let listLevel = 0;
    let inList = false;

    const flushList = () => {
      if (currentList.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="mb-4 list-disc pl-6 space-y-1">
            {currentList}
          </ul>
        );
        currentList = [];
        inList = false;
        listLevel = 0;
      }
    };

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      if (!trimmedLine) {
        if (inList) {
          flushList();
        }
        return;
      }

      // 处理标题
      if (trimmedLine.startsWith('####')) {
        if (inList) flushList();
        const title = trimmedLine.replace(/^####\s*/, '');
        elements.push(
          <h4 key={`h4-${index}`} className="font-medium text-base mb-2 mt-4 first:mt-0 text-foreground">
            {title}
          </h4>
        );
      } else if (trimmedLine.startsWith('###')) {
        if (inList) flushList();
        const title = trimmedLine.replace(/^###\s*/, '');
        elements.push(
          <h3 key={`h3-${index}`} className="font-semibold text-lg mb-3 mt-6 first:mt-0 text-foreground">
            {title}
          </h3>
        );
      } else if (trimmedLine.startsWith('##')) {
        if (inList) flushList();
        const title = trimmedLine.replace(/^##\s*/, '');
        elements.push(
          <h2 key={`h2-${index}`} className="font-bold text-xl mb-4 mt-8 first:mt-0 text-foreground">
            {title}
          </h2>
        );
      } else if (trimmedLine.startsWith('#')) {
        if (inList) flushList();
        const title = trimmedLine.replace(/^#\s*/, '');
        elements.push(
          <h1 key={`h1-${index}`} className="font-bold text-2xl mb-6 mt-10 first:mt-0 text-foreground">
            {title}
          </h1>
        );
      }
      // 处理列表项
      else if (trimmedLine.match(/^\s*[-*+]\s+/)) {
        const currentLevel = Math.floor((line.length - line.trimStart().length) / 4);
        const content = trimmedLine.replace(/^[-*+]\s+/, '');
        
        if (!inList || currentLevel !== listLevel) {
          if (inList) flushList();
          inList = true;
          listLevel = currentLevel;
        }
        
        // 处理粗体文本和时间戳
        let formattedContent = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 处理时间戳格式 (00:00:05 - 00:01:25)
        formattedContent = formattedContent.replace(
          /(\d{2}:\d{2}:\d{2}\s*-\s*\d{2}:\d{2}:\d{2})/g,
          '<span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs font-mono ml-2">$1</span>'
        );

        currentList.push(
          <li key={`li-${index}`} className="text-sm text-foreground/80 leading-relaxed">
            <span dangerouslySetInnerHTML={{ __html: formattedContent }} />
          </li>
        );
      }
      // 处理普通段落
      else {
        if (inList) flushList();

        // 处理粗体文本和时间戳
        let formattedContent = trimmedLine.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 处理时间戳格式 (00:00:05 - 00:01:25)
        formattedContent = formattedContent.replace(
          /(\d{2}:\d{2}:\d{2}\s*-\s*\d{2}:\d{2}:\d{2})/g,
          '<span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs font-mono ml-2">$1</span>'
        );

        elements.push(
          <p key={`p-${index}`} className="mb-4 text-sm text-foreground/80 leading-relaxed">
            <span dangerouslySetInnerHTML={{ __html: formattedContent }} />
          </p>
        );
      }
    });

    // 处理最后的列表
    if (inList) {
      flushList();
    }

    return elements;
  };

  const renderedContent = parseMarkdown(content);

  return (
    <div className={`prose prose-sm max-w-none ${className}`}>
      {renderedContent}
    </div>
  );
});
