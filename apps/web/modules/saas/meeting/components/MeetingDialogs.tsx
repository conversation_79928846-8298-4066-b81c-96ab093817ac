"use client";

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
	DialogDescription,
} from "@ui/components/dialog";
import { File, Users } from "lucide-react";
import type { MeetingDocsResponse } from "./types";
import { AntMeetingTable } from "./ant-meeting-table";
import { useState } from "react";
import { toast } from "sonner";
import { Check, Copy } from "lucide-react";
import { MarkdownRenderer } from "./MarkdownRenderer";

interface CopyableEmailProps {
	email: string;
	className?: string;
}

function CopyableEmail({ email, className = "" }: CopyableEmailProps) {
	const [copied, setCopied] = useState(false);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(email);
			setCopied(true);
			toast.success("邮箱已复制到剪贴板");
			// setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			toast.error("复制失败，请手动复制");
		}
	};

	return (
		<div className={`inline-flex items-center gap-2 ${className}`}>
			<span className="text-muted-foreground">{email}</span>
			<button
				type="button"
				onClick={handleCopy}
				className="inline-flex items-center justify-center w-6 h-6 rounded-md hover:bg-muted transition-colors cursor-pointer"
				title={copied ? "已复制" : "点击复制邮箱"}
			>
				{copied ? (
					<Check className="w-4 h-4 text-green-600" />
				) : (
					<Copy className="w-4 h-4 text-muted-foreground hover:text-foreground" />
				)}
			</button>
		</div>
	);
}


/**
 * 会议文档对话框组件的属性接口
 * @interface DocsDialogProps
 */
interface DocsDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 当前会议的文档列表数据，可能为空 */
	currentMeetingDocs: MeetingDocsResponse | null;
	/** 文档加载状态标志 */
	loadingDocs: boolean;
	/** 时间戳格式化函数，用于显示文档的最后编辑时间 */
	formatTimestamp: (timestamp: number | string) => string;
}

export function DocsDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	currentMeetingDocs,
	loadingDocs,
	formatTimestamp,
}: DocsDialogProps) {
	// 会议文档表格 columns 和数据
	const docColumns = [
		{ title: "文档标题", dataIndex: "doc_title", key: "doc_title" },
		{
			title: "创建者",
			dataIndex: "doc_creator_user_name",
			key: "doc_creator_user_name",
		},
		{
			title: "最后编辑",
			dataIndex: "doc_edit_time",
			key: "doc_edit_time",
			render: (value: string) => formatTimestamp(value),
		},
		{
			title: "最后编辑者",
			dataIndex: "doc_editor_user_name",
			key: "doc_editor_user_name",
		},
	];
	const docRows = (currentMeetingDocs?.doc_info_list || []).map(
		(row: any, idx: number) => ({ ...row, id: String(idx) }),
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<File className="h-5 w-5" />
						会议文档
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的会议文档
						{/* {currentMeetingDocs && currentMeetingDocs.total_count > 0 && (
              <span className="ml-1 text-xs">（共 {currentMeetingDocs.total_count} 个）</span>
            )} */}
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingDocs ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<>
							{(currentMeetingDocs?.doc_info_list?.length ?? 0) >
							0 ? (
								<div className="border rounded-lg overflow-hidden">
									<div className="divide-y divide-border">
										<AntMeetingTable
											columns={docColumns}
											data={docRows}
											loading={loadingDocs}
										/>
									</div>
								</div>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<File className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
									<p>暂无会议文档</p>
								</div>
							)}
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// 获取文件类型对应图标
function getFileTypeIcon(fileType: string) {
	switch (fileType.toLowerCase()) {
		case "pdf":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>PDF图标</title>
					<path d="M12 16H16V20H8V4H16V8H12V16Z" fill="#FF5252" />
					<path d="M16 4L20 8H16V4Z" fill="#FF5252" />
				</svg>
			);
		case "docx":
		case "docs":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Word图标</title>
					<path
						d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
						fill="#4285F4"
					/>
					<path d="M14 2V8H20L14 2Z" fill="#A1C2FA" />
					<path d="M8 14H16V16H8V14Z" fill="white" />
					<path d="M8 10H16V12H8V10Z" fill="white" />
					<path d="M8 18H12V20H8V18Z" fill="white" />
				</svg>
			);
		case "txt":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Text图标</title>
					<path
						d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
						fill="#607D8B"
					/>
					<path d="M14 2V8H20L14 2Z" fill="#B0BEC5" />
					<path d="M8 14H16V16H8V14Z" fill="white" />
					<path d="M8 10H16V12H8V10Z" fill="white" />
					<path d="M8 18H12V20H8V18Z" fill="white" />
				</svg>
			);
		case "mp4":
			return (
				<svg
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<title>Video图标</title>
					<path
						d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z"
						fill="#FF5722"
					/>
				</svg>
			);
		default:
			return <File className="size-4" />;
	}
}







export interface SignInDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	currentMeetingTitle: string;
	currentMeetingSignIn: any | null;
	loadingSignIn: boolean;
	formatTimestamp: (timestamp: number | string) => string;
}

export function SignInDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	currentMeetingSignIn,
	loadingSignIn,
	formatTimestamp,
}: SignInDialogProps) {
	// 状态显示辅助函数
	function getSignInStatusDisplay(status: string | number) {
		switch (status) {
			case 1:
			case "1":
				return {
					text: "进行中",
					color: "text-green-600 dark:text-green-400",
				};
			case 0:
			case "0":
				return { text: "未开始", color: "text-muted-foreground" };
			case 2:
			case "2":
				return {
					text: "已结束",
					color: "text-yellow-600 dark:text-yellow-400",
				};
			default:
				return { text: "未知", color: "text-muted-foreground" };
		}
	}

	// 在签到记录展示处，替换为 AntMeetingTable 渲染
	// 假设 currentMeetingSignIn.sign_in_list 为签到记录数组，loadingSignIn 为加载状态
	const statusMap: Record<string, string> = {
		"0": "未开始",
		"1": "进行中",
		"2": "已结束",
	};
	const signInColumns = [
		{ title: "签到ID", dataIndex: "sign_in_id", key: "sign_in_id" },
		{
			title: "开始时间",
			dataIndex: "start_time",
			key: "start_time",
			render: (value: string) => formatTimestamp(value),
		},
		{
			title: "状态",
			dataIndex: "sign_in_status",
			key: "sign_in_status",
			render: (status: number) => statusMap[String(status)],
		},
		// { title: "说明", dataIndex: "sign_in_specification", key: "sign_in_specification" },
	];
	const signInRows = (currentMeetingSignIn?.sign_in_list || []).map(
		(row: any, idx: number) => ({ ...row, id: String(idx) }),
	);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						会议签到记录
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的签到记录
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingSignIn ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : currentMeetingSignIn &&
						typeof currentMeetingSignIn === "object" ? (
						<>
							{/* 签到记录列表 */}
							{currentMeetingSignIn.sign_in_list &&
							currentMeetingSignIn.sign_in_list.length > 0 ? (
								<AntMeetingTable
									columns={signInColumns}
									data={signInRows}
									loading={loadingSignIn}
								/>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
									<p>暂无签到记录</p>
								</div>
							)}
						</>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<p>获取签到记录失败</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

/**
 * 参会人员对话框组件的属性接口
 * @interface ParticipantsDialogProps
 */
interface ParticipantsDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于下载参会人员列表 */
	currentMeetingId: string;
	/** 参会人员数据加载状态标志 */
	loadingParticipants: boolean;
	/** 导出参会人员函数 */

	/** 新增导出数据props */
	exportUrl: string | null;
	sheetData: any;
}

export function ParticipantsDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingParticipants,

	exportUrl,
	sheetData,
}: ParticipantsDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-7xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Users className="h-5 w-5" />
						参会人员
					</DialogTitle>
					<DialogDescription>
						<span className="flex items-center gap-2">
							{currentMeetingTitle} 的参会人员
							{exportUrl && (
								<button
									type="button"
									onClick={() => {
										window.open(exportUrl, "_blank");
									}}
									title="下载参会人员表"
									className="hover:underline text-blue-500"
									// className="justify-center border font-medium enabled:cursor-pointer transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:pointer-events-none disabled:opacity-50 [&>svg+svg]:hidden size-9 [&>svg]:m-0 [&>svg]:opacity-100 w-9 flex items-center gap-1.5 shrink-0 text-sm h-9 rounded-md bg-background text-foreground border-border hover:bg-accent"
								>
									下载
								</button>
							)}
						</span>
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingParticipants ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<div className="text-center py-8">
							<div>
								{sheetData &&
								Array.isArray(sheetData) &&
								sheetData.length > 0 ? (
									(() => {
										
										// 1. 找到表头行（value包含"用户昵称（入会昵称）"）
										const headerRow = sheetData.find(
											(row) =>
												Object.values(row).includes(
													"用户昵称（入会昵称）",
												),
										);
										const headerKeys =
											Object.keys(headerRow);
										const headers = headerKeys.map(
											(key) => headerRow[key],
										);
										// 2. 过滤出数据行（排除表头行，且只保留所有headerKeys都存在的行）
										// 同时过滤掉第二列的数据
										const filteredHeaderKeys = headerKeys.filter((_, idx) => idx !== 1);
										const dataRows = sheetData
											.filter(
												(row) =>
													row !== headerRow &&
													headerKeys.every(
														(key) => key in row,
													),
											)
											.map((row, idx) => {
												// 创建新的行对象，排除第二列的数据
												const filteredRow: any = {};
												filteredHeaderKeys.forEach((key) => {
													filteredRow[key] = row[key];
												});
												return {
													...filteredRow,
													id: String(idx),
												};
											});

										// 新增：生成 columns，过滤掉第二列（索引为1）
										const filteredHeaders = headers.filter((_, idx) => idx !== 1);
										const columns = filteredHeaderKeys.map(
											(key, idx) => ({
												title: filteredHeaders[idx],
												dataIndex: key,
												key,
											}),
										);

										return (
											<AntMeetingTable
												columns={columns}
												data={dataRows}
												loading={false}
											/>
										);
									})()
								) : (
									<div className="text-muted-foreground mt-4">
										暂无参会人员数据
									</div>
								)}
							</div>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

interface InfoDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	email: string;
}

/**
 * 统一的会议纪要对话框组件的属性接口
 * @interface MeetingMinutesDialogProps
 */
interface MeetingMinutesDialogProps {
	/** 控制对话框是否打开 */
	open: boolean;
	/** 对话框打开状态变化的回调函数 */
	onOpenChange: (open: boolean) => void;
	/** 当前会议标题 */
	currentMeetingTitle: string;
	/** 会议ID，用于获取纪要信息 */
	currentMeetingId: string;
	/** 纪要数据加载状态标志 */
	loadingMinutes: boolean;
	/** AI总结内容 */
	aiSummaryContent: string;
	/** 下载记录文件的URL */
	downloadUrl: string;
	/** 观看视频的地址 */
	viewAddress: string;
	/** 刷新AI总结的回调函数 */
	onRefreshAISummary?: () => Promise<void>;
}

export function MeetingMinutesDialog({
	open,
	onOpenChange,
	currentMeetingTitle,
	loadingMinutes,
	aiSummaryContent,
	downloadUrl,
	viewAddress,
	onRefreshAISummary,
}: MeetingMinutesDialogProps) {
	const [isRefreshing, setIsRefreshing] = useState(false);

	// 处理刷新AI总结
	const handleRefresh = async () => {
		if (!onRefreshAISummary || isRefreshing) return;

		try {
			setIsRefreshing(true);
			await onRefreshAISummary();
			toast.success("AI总结已刷新");
		} catch (error) {
			toast.error("刷新失败，请稍后重试");
		} finally {
			setIsRefreshing(false);
		}
	};

	// 处理复制AI总结
	const handleCopyAISummary = async () => {
		if (!aiSummaryContent) {
			toast.error("暂无AI总结内容可复制");
			return;
		}

		try {
			await navigator.clipboard.writeText(aiSummaryContent);
			toast.success("AI总结已复制到剪贴板");
		} catch (error) {
			toast.error("复制失败，请手动复制");
		}
	};

	// 处理下载AI总结
	const handleDownloadAISummary = () => {
		if (!aiSummaryContent) {
			toast.error("暂无AI总结内容可下载");
			return;
		}

		try {
			//
			// 创建一个Blob对象
			const blob = new Blob([aiSummaryContent], { type: 'text/plain;charset=utf-8' });
			// 创建一个临时URL
			const url = URL.createObjectURL(blob);
			// 创建一个a标签用于下载
			const a = document.createElement('a');
			a.href = url;
			a.download = `${currentMeetingTitle}-AI会议纪要.txt`;
			document.body.appendChild(a);
			a.click();
			// 清理
			setTimeout(() => {
				document.body.removeChild(a);
				URL.revokeObjectURL(url);
			}, 0);
			toast.success("AI总结下载成功");
		} catch (error) {
			toast.error("下载失败，请稍后重试");
		}
	};
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<svg
							className="h-5 w-5"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<title>会议纪要图标</title>
							<path d="M12 6V4a2 2 0 0 1 2-2h5a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2" />
							<path d="M3 10a2 2 0 0 1 2-2h5a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-9z" />
							<circle cx="7" cy="14" r="1" />
							<path d="m15.5 17.5 2.5 2.5L22 16" />
						</svg>
						会议纪要
					</DialogTitle>
					<DialogDescription>
						{currentMeetingTitle} 的会议纪要
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{loadingMinutes ? (
						<div className="flex justify-center items-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
						</div>
					) : (
						<>
							{/* AI总结内容区域 */}
							<div className="bg-muted/50 p-6 rounded-lg">
								<div className="flex items-center justify-between mb-4">
									<h3 className="text-lg font-semibold">AI会议总结</h3>
									<button
										type="button"
										onClick={handleRefresh}
										disabled={isRefreshing || loadingMinutes}
										className="flex items-center gap-1 px-2 py-1 text-sm text-muted-foreground hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
										title="刷新AI总结"
									>
										<svg
											className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
										>
											<title>刷新图标</title>
											<path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
											<path d="M21 3v5h-5" />
											<path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
											<path d="M3 21v-5h5" />
										</svg>
										{isRefreshing ? '刷新中...' : '刷新'}
									</button>
								</div>
								<div className="text-left text-sm max-h-96 overflow-y-auto border rounded p-4 bg-white dark:bg-muted/30">
									{aiSummaryContent ? (
										(() => {
											// 检查是否是Markdown格式的内容
											const isMarkdown = aiSummaryContent.includes('###') ||
															 aiSummaryContent.includes('####') ||
															 aiSummaryContent.includes('- **') ||
															 aiSummaryContent.includes('00:');

											if (isMarkdown) {
												// 使用Markdown渲染器
												return <MarkdownRenderer content={aiSummaryContent} />;
											} else {
												// 普通文本，使用原有样式
												return <div className="whitespace-pre-line">{aiSummaryContent}</div>;
											}
										})()
									) : (
										<span className="text-muted-foreground">
											暂无AI总结内容
										</span>
									)}
								</div>
							</div>

							
							{/* 底部操作按钮区域 */}
							<div className="flex justify-between items-center gap-4 pt-4">
								{/* 左侧按钮组 */}
								<div className="flex gap-4">
									<button
										type="button"
										onClick={() => {
											if (downloadUrl) {
												window.open(downloadUrl, "_blank");
											} else {
												toast.error("暂无记录文件可下载");
											}
										}}
										disabled={!downloadUrl}
										className="flex items-center gap-2 px-3 py-2 text-muted-foreground rounded-md  hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
										>
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
										>
											<title>下载图标</title>
											<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
											<polyline points="7,10 12,15 17,10" />
											<line x1="12" y1="15" x2="12" y2="3" />
										</svg>
										导出文字转录
									</button>

									<button
										type="button"
										onClick={() => {
											if (viewAddress) {
												window.open(viewAddress, "_blank");
											} else {
												toast.error("暂无视频可观看");
											}
										}}
										disabled={!viewAddress}
										className="flex items-center gap-2 px-3 py-2 text-muted-foreground rounded-md  hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
										>
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
										>
											<title>播放图标</title>
											<circle cx="12" cy="12" r="10" />
											<polygon points="10,8 16,12 10,16 10,8" />
										</svg>
										跳转视频回放
									</button>
								</div>

								{/* 右侧按钮组 */}
								<div className="flex gap-2">
									<button
										type="button"
										onClick={handleCopyAISummary}
										disabled={!aiSummaryContent}
										className="flex items-center gap-2 px-3 py-2 text-muted-foreground rounded-md  hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
										title="复制AI总结"
									>
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
										>
											<title>复制图标</title>
											<rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
											<path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
										</svg>
										复制
									</button>

									<button
										type="button"
										onClick={handleDownloadAISummary}
										disabled={!aiSummaryContent}
										className="flex items-center gap-2 px-3 py-2 text-muted-foreground rounded-md  hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
										title="下载AI总结"
									>
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
										>
											<title>下载图标</title>
											<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
											<polyline points="7,10 12,15 17,10" />
											<line x1="12" y1="15" x2="12" y2="3" />
										</svg>
										下载
									</button>
								</div>
							</div>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

export function InfoDialog({
	open,
	onOpenChange,
	email,
}: InfoDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl max-h-[60vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 justify-center">
						温馨提示
					</DialogTitle>
				</DialogHeader>
				<div className="text-center">
					<div className="text-muted-foreground mt-4 text-center text-sm">
						本月平台预约次数已用完，如需预约新会议，可前往腾讯会议官网或APP进行。
					</div>
					<div className="text-muted-foreground mt-4 text-center text-sm">
						登录账号：
						<CopyableEmail email={email} />
					</div>
					<div className="text-muted-foreground mt-4 text-center text-sm">
						腾讯会议官网：
						<button
							type="button"
							className="underline text-blue-500 cursor-pointer"
							onClick={() =>
								window.open(
									"https://meeting.tencent.com/user-center/user-meeting-list/schedule",
									"_blank",
								)
							}
						>
							https://meeting.tencent.com/user-center/user-meeting-list/schedule
						</button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
