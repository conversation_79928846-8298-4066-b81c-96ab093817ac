// 用于展示用户所有文档，暂时不使用此功能，后续需要时再恢复
"use client";

import { useState, useEffect } from "react";
import { Button } from "@ui/components/button";
import { 
  Card, 
  CardContent,
  CardHeader,
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { Skeleton } from "@ui/components/skeleton";
import { FileText, Search, RefreshCw, Download, ExternalLink, Clock } from "lucide-react";
import { toast } from "sonner";
import type { MeetingDocsProps, UserDocsResponse } from "./types";


// 此组件是展示用户所有文档的组件，目前仅使用默认用户ID作为演示，实际使用中应该从用户配置或者环境变量中获取
export function MeetingDocs({ organizationSlug }: MeetingDocsProps) {
  const [docs, setDocs] = useState<UserDocsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState("wemeeting6043617");
  const [pageSize, setPageSize] = useState(20);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");

  // 加载用户文档列表
  const loadUserDocs = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/meetings/user/docs?userId=${userId}&pageSize=${pageSize}&page=${page}`);
      
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "获取文档失败");
      }
      
      // console.log("获取到的文档数据:", data.data);
      // console.log("文档总数:", data.data?.total_count);
      // console.log("当前偏移量:", data.data?.current_offset);
      // console.log("当前大小:", data.data?.current_size);
      // console.log("总页数:", data.data?.total_page);
      // console.log("会议信息列表:", data.data?.meeting_info_list);
      
      setDocs(data.data);
    } catch (err: any) {
      // toast.error("获取文档列表错误:", err);
      setError(err.message || "获取文档列表失败");
      toast.error("获取文档列表失败", {
        description: err.message || "请稍后重试",
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取文档列表
  useEffect(() => {
    loadUserDocs();
  }, [userId, pageSize, page]);

  // 格式化时间戳为可读格式
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 处理搜索
  const handleSearch = () => {
    // 在实际应用中，可能需要调用API进行搜索
    // 这里简单实现本地过滤
    loadUserDocs();
  };

  // 处理刷新
  const handleRefresh = () => {
    loadUserDocs();
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // 渲染文档列表
  const renderDocsList = () => {
    if (loading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-1/3" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-4 text-center">
          <p className="text-red-500">{error}</p>
          <Button onClick={handleRefresh} className="mt-4">重试</Button>
        </div>
      );
    }

    if (!docs || !docs.meeting_info_list || docs.meeting_info_list.length === 0) {
      return (
        <div className="p-4 text-center">
          <p className="text-gray-500">没有找到文档</p>
          <Button onClick={handleRefresh} className="mt-4">刷新</Button>
        </div>
      );
    }

    // 将所有会议的文档合并到一个扁平数组中，同时保留会议信息
    const allDocs = docs.meeting_info_list.flatMap(meeting => 
      meeting.doc_info_list.map(doc => ({
        ...doc,
        meeting_subject: meeting.subject,
        meeting_id: meeting.meeting_id,
        meeting_code: meeting.meeting_code,
        meeting_start_time: meeting.start_time
      }))
    );

    return (
      <div className="space-y-6">
        <div className="text-sm text-muted-foreground">
          共找到 {docs?.total_count || 0} 个文档，显示 {(docs?.current_offset || 0) + 1} - {(docs?.current_offset || 0) + (docs?.current_size || 0)} 条
        </div>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>文档名称</TableHead>
              <TableHead>所属会议</TableHead>
              <TableHead>创建者</TableHead>
              <TableHead>编辑时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {allDocs.map((doc) => (
              <TableRow key={doc.doc_id}>
                <TableCell className="font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  {doc.doc_title}
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span>{doc.meeting_subject}</span>
                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatTimestamp(Number.parseInt(doc.meeting_start_time))}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{doc.doc_creator_user_name}</TableCell>
                <TableCell>{formatTimestamp(doc.doc_edit_time)}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" title="查看文档">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" title="下载文档">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {/* 分页 */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            第 {page} 页，共 {docs?.total_page || 1} 页
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={() => handlePageChange(page - 1)}
              disabled={page <= 1}
            >
              上一页
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handlePageChange(page + 1)}
              disabled={page >= (docs?.total_page || 1)}
            >
              下一页
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 搜索和筛选栏 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 flex gap-2">
          <Input
            placeholder="搜索文档名称..."
            value={searchQuery || ''}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
          <Button onClick={handleSearch}>
            <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>
        </div>
        <div className="flex gap-2">
          <Select value={pageSize.toString()} onValueChange={(val) => setPageSize(Number.parseInt(val))}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="每页数量" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10条/页</SelectItem>
              <SelectItem value="20">20条/页</SelectItem>
              <SelectItem value="50">50条/页</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 文档列表 */}
      {renderDocsList()}
    </div>
  );
} 