"use client";


import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@ui/components/card";
import { Button } from "@ui/components/button";
import { FilesIcon, Loader2Icon } from "lucide-react";

// 导入自定义 Hook 和组件
import { useFileUpload } from "../hooks/useFileUpload";
import { FileUploadArea } from "./FileUploadArea";
import { ProgressDisplay } from "./ProgressDisplay";
import { ResultDisplay } from "./ResultDisplay";
import { AlertMessages } from "./AlertMessages";
import { AIAnalysisLoading } from "./AIAnalysisLoading";

interface MeetingFileUploadProps {
  meetingId: string;
  meetingTitle: string;
  onComplete?: () => void;
}

/**
 * 会议文件上传组件
 * 支持拖拽上传和文件选择
 * 用于会前速览功能的文件上传
 * 支持上传多个文件
 */
export function MeetingFileUpload({
  meetingId,
  meetingTitle,
}: MeetingFileUploadProps) {

  // 使用自定义 Hook 管理文件上传状态和逻辑
  const {
    files,
    isDragging,
    error,
    success,
    uploadProgress,
    isUploading,
    isParsing,
    parsedData,
    currentParsingFile,
    apiResult,
    processedFileCount,
    processingTimestamp,
    isRegenerating,
    addFiles,
    removeFile,
    clearFiles,
    handleUpload,
    resetToUploadMode,
    setDragging,
    handleRegenerate,
  } = useFileUpload(meetingId, meetingTitle);

  // 处理上传
  const handleUploadSuccess = async () => {
    if (files.length === 0) {
      // 使用toast提示用户需要上传文件
      const { toast } = await import('sonner');
      toast.error("请上传文件一键生成会议规划");
      return;
    }
    await handleUpload();
  };

  // 重置到上传模式
  const handleResetToUpload = () => {
    resetToUploadMode();
  };

  // 处理重新生成
  const handleRegenerateContent = async () => {
    await handleRegenerate();
  };

  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  };

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    addFiles(selectedFiles);
  };











  // 渲染组件
  return (
    <div className="flex flex-col xl:flex-row gap-4 h-full min-h-[600px]">
      {/* 左侧面板 - 文件上传 */}
      <div className="xl:w-1/4">
        <Card className="h-full min-h-[500px] flex flex-col">
          <CardHeader>
            <div className="flex items-center gap-2">
              {/* <FilesIcon className="text-primary h-5 w-5" /> */}
              <CardTitle>会前速览 - 文件上传</CardTitle>
            </div>
            <CardDescription>
              为会议"{meetingTitle}"上传相关文件
            </CardDescription>
          </CardHeader>

          <CardContent className="flex-1 flex flex-col">
            {/* 上部内容区域 */}
            <div className="flex-1 overflow-hidden">
              {/* 错误和成功提示 */}
              <AlertMessages
                error={error}
                success={success}
                fileCount={files.length}
              />

              {/* 文件上传区域 - 固定高度 */}
              <div className="h-[500px] overflow-hidden">
                <FileUploadArea
                  isDragging={isDragging}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onFileChange={handleFileChange}
                  disabled={isUploading}
                  files={files}
                  onRemoveFile={removeFile}
                  onClearFiles={clearFiles}
                  hasApiResult={!!apiResult}
                />
              </div>

              {/* 进度显示 */}
              <ProgressDisplay
                isUploading={isUploading}
                uploadProgress={uploadProgress}
                isParsing={isParsing}
                currentParsingFile={currentParsingFile}
                parsedData={parsedData}
              />
            </div>

            {/* 底部按钮区域 - 固定在底部 */}
            <div className="mt-4 pt-4 border-t border-border">
              {!apiResult ? (
                /* 首次生成按钮 */
                <div className="flex justify-center">
                  <Button
                    onClick={handleUploadSuccess}
                    disabled={files.length === 0 || isUploading || isParsing}
                    className="min-w-[120px]"
                  >
                    {isUploading || isParsing ? (
                      <>
                        <Loader2Icon className="animate-spin mr-2 h-4 w-4" />
                        {isUploading ? "上传中..." : "生成中..."}
                      </>
                    ) : (
                      <>一键生成</>
                    )}
                  </Button>
                </div>
              ) : (
                /* 生成成功后只显示重新生成按钮 */
                <div className="flex justify-center">
                  <Button
                    onClick={handleRegenerateContent}
                    disabled={isUploading || isParsing || isRegenerating}
                    className="min-w-[120px]"
                  >
                    {isRegenerating || isParsing ? (
                      <>
                        <Loader2Icon className="animate-spin mr-2 h-4 w-4" />
                        重新生成中...
                      </>
                    ) : (
                      <>重新生成</>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右侧面板 - 处理结果 */}
      <div className="flex-1 xl:w-3/5">
        <Card className="h-full min-h-[500px] flex flex-col">
          <CardHeader>
            <div className="flex items-center gap-2">
              {/* <FilesIcon className="text-primary h-5 w-5" /> */}
              <CardTitle>会前速览 - AI报告</CardTitle>
            </div>
            <CardDescription>
              会议"{meetingTitle}"的会前速览报告
            </CardDescription>
          </CardHeader>

          {apiResult && !isRegenerating ? (
            <ResultDisplay
              apiResult={apiResult}
              processedFileCount={processedFileCount}
              meetingTitle={meetingTitle}
              onResetToUpload={handleResetToUpload}
            />
          ) : (isParsing && !isUploading) || isRegenerating ? (
            <CardContent className="flex-1 flex flex-col">
              <AIAnalysisLoading
                currentParsingFile={isRegenerating ? "重新生成AI内容中..." : currentParsingFile}
              />
            </CardContent>
          ) : (
            <CardContent className="flex-1 flex flex-col">
              <div className="flex items-center justify-center flex-1 text-muted-foreground">
                <div className="text-center">
                  <FilesIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>上传材料，我将为您生成专属规划</p>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
