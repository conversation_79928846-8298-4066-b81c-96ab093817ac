import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@ui/components/dialog";
import { MeetingFileUpload } from "./MeetingFileUpload";

interface MeetingFileUploadDialogProps {
  isOpen: boolean;
  meetingId: string;
  meetingTitle: string;
  onOpenChange: (open: boolean) => void;
}

/**
 * 会议文件上传对话框容器组件
 * 用于会前速览功能的文件上传模态框
 *
 * 支持通过右上角关闭按钮、点击外部区域和ESC键关闭弹窗
 */
export function MeetingFileUploadDialog({
  isOpen,
  meetingId,
  meetingTitle,
  onOpenChange,
}: MeetingFileUploadDialogProps) {

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onOpenChange}
    >
      <DialogContent
        className="sm:max-w-[1000px] lg:max-w-[1200px] p-0 max-h-[90vh] flex flex-col overflow-auto"
      >
        <DialogTitle className="sr-only">会前速览 - 文件上传</DialogTitle>
        <DialogDescription className="sr-only">
          为会议上传相关文件，支持PDF、Word、Excel、PPT等多种文档格式，可同时上传多个文件
        </DialogDescription>
        <MeetingFileUpload 
          meetingId={meetingId}
          meetingTitle={meetingTitle}
          onComplete={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
