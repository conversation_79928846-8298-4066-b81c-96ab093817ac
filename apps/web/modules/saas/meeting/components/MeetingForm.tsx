"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@ui/components/button";
import { Form } from "@ui/components/form";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useSession } from "../../auth/hooks/use-session";
import { InfoDialog } from "./MeetingDialogs";
import { useState } from "react";
import type { GuestData } from "../utils/guestExcelParser";
import { smartFillGuestsWithDuplicateDetection} from "../utils/guestValidation";
import { toast } from "sonner";
import { useUserAccountType } from "../hooks/use-user-account-type";

// 导入重构后的组件和 hooks
import { meetingFormSchema, type MeetingFormValues } from "../utils/meetingFormValidation";
import { useMeetingFormDefaults } from "../hooks/use-meeting-form-defaults";
import { useMeetingFormSubmit } from "../hooks/use-meeting-form-submit";
import { MeetingBasicInfo } from "./MeetingBasicInfo";
import { MeetingRecurringSettings } from "./MeetingRecurringSettings";
import { MeetingGuestManagement } from "./MeetingGuestManagement";
import { MeetingSecuritySettings } from "./MeetingSecuritySettings";
import { MeetingFunctionSettings } from "./MeetingFunctionSettings";



/**
 * 预定会议表单组件
 * 
 * @param organizationSlug - 组织的 slug
 * @returns 渲染的表单组件
 */
export function MeetingForm({ organizationSlug }: { organizationSlug: string }) {
  const router = useRouter();
  const { user } = useSession();
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [batchImportDialogOpen, setBatchImportDialogOpen] = useState(false);
  const [systemImportDialogOpen, setSystemImportDialogOpen] = useState(false);
  const [validationTrigger, setValidationTrigger] = useState(0);

  // 获取用户账户类型信息
  const { accountType, isLoading: isLoadingAccountType, error: accountTypeError, isGuestFeatureAllowed } = useUserAccountType();

  // 在账户类型加载失败时，默认允许嘉宾功能（保持向后兼容）
  const shouldShowGuestFeature = accountTypeError ? true : isGuestFeatureAllowed;

 

  // 获取表单默认值
  const defaultValues = useMeetingFormDefaults();

  // 初始化表单
  const form = useForm<MeetingFormValues>({
    resolver: zodResolver(meetingFormSchema),
    defaultValues,
  });

  // 获取表单提交处理函数
  const { handleSubmit } = useMeetingFormSubmit(organizationSlug, shouldShowGuestFeature, setInfoDialogOpen);

  // 处理批量导入嘉宾
  const handleBatchImport = (importedGuests: GuestData[]) => {
    const currentGuests = form.getValues("guests") || [];
    // 使用智能填充逻辑，优先填充空白行，并检测重复参会成员
    const result = smartFillGuestsWithDuplicateDetection(currentGuests, importedGuests);
    form.setValue("guests", result.guests);
    setBatchImportDialogOpen(false);

    // 显示重复参会成员反馈
    if (result.duplicateInfo.count > 0) {
      const duplicateNames = result.duplicateInfo.details
        .map((detail: any) => detail.guest.guest_name || '未知')
        .slice(0, 5); // 最多显示5个名字

      const message = result.duplicateInfo.count <= 5
        ? `已跳过重复成员：${duplicateNames.join('、')}`
        : `已跳过${result.duplicateInfo.count}位重复成员：${duplicateNames.join('、')}等`;

      toast.warning(message);
    }

    // 触发验证
    setValidationTrigger(Date.now());
  };

  // 处理系统导入嘉宾
  const handleSystemImport = (importedGuests: GuestData[]) => {
    const currentGuests = form.getValues("guests") || [];
    // 使用智能填充逻辑，优先填充空白行，并检测重复参会成员
    const result = smartFillGuestsWithDuplicateDetection(currentGuests, importedGuests);
    form.setValue("guests", result.guests);
    setSystemImportDialogOpen(false);

    // 显示重复参会成员反馈
    if (result.duplicateInfo.count > 0) {
      const duplicateNames = result.duplicateInfo.details
        .map((detail: any) => detail.guest.guest_name || '未知')
        .slice(0, 5); // 最多显示5个名字

      const message = result.duplicateInfo.count <= 5
        ? `已跳过重复参会成员：${duplicateNames.join('、')}`
        : `已跳过${result.duplicateInfo.count}位重复参会成员：${duplicateNames.join('、')}等`;

      toast.warning(message);
    }

    // 触发验证
    setValidationTrigger(Date.now());
  };

  return (
			<Form {...form}>
				<InfoDialog open={infoDialogOpen} onOpenChange={setInfoDialogOpen} email={user?.email || ""} />
				<form
					onSubmit={form.handleSubmit((values) => handleSubmit(values, form.setError))}
					className="space-y-6"
				>
					{/* 会议基本信息 */}
					<MeetingBasicInfo control={form.control} />

					{/* 周期性会议设置 */}
					<MeetingRecurringSettings control={form.control} watch={form.watch} />



					{/* 会议嘉宾管理 */}
					<MeetingGuestManagement
						control={form.control}
						getValues={form.getValues}
						setValue={form.setValue}
						watch={form.watch}
						shouldShowGuestFeature={shouldShowGuestFeature}
						batchImportDialogOpen={batchImportDialogOpen}
						setBatchImportDialogOpen={setBatchImportDialogOpen}
						handleBatchImport={handleBatchImport}
						systemImportDialogOpen={systemImportDialogOpen}
						setSystemImportDialogOpen={setSystemImportDialogOpen}
						handleSystemImport={handleSystemImport}
						triggerValidation={validationTrigger}
					/>

					{/* 安全设置 */}
					<MeetingSecuritySettings control={form.control} watch={form.watch} />

					{/* 会议功能设置 */}
					<MeetingFunctionSettings control={form.control} />

					{/* 错误提示和提交按钮 */}
					{form.formState.errors.root && (
						<p className="text-xs font-medium text-destructive">
							{form.formState.errors.root.message}
						</p>
					)}

					{/* 悬浮按钮组 - 保持在表单底部但具有悬浮效果 */}
					<div className="sticky bottom-0 bg-background p-4 -mx-6 mt-8 z-10">
						<div className="flex justify-start gap-4">
							<Button
								type="submit"
								loading={form.formState.isSubmitting}
								className="w-[150px]"
							>
								预定会议
							</Button>
							<Button
								type="button"
								variant="outline"
								onClick={() =>
									router.push(
										`/app/${organizationSlug}/meeting/list`,
									)
								}
								className="w-[150px]"
							>
								取消
							</Button>
						</div>
					</div>
				</form>
			</Form>
		);
} 