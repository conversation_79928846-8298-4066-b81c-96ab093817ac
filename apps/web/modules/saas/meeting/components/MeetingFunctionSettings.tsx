"use client";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { useTheme } from "next-themes";
import type { Control } from "react-hook-form";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

interface MeetingFunctionSettingsProps {
  control: Control<MeetingFormValues>;
}

/**
 * 会议功能设置组件
 * 包含静音设置、录制设置、文档上传等功能设置
 */
export function MeetingFunctionSettings({ control }: MeetingFunctionSettingsProps) {
  const { resolvedTheme } = useTheme();

  return (
    <div className="space-y-6">
      {/* 成员入会时静音设置 */}
      <div className="space-y-4">
        <FormField
          control={control}
          name="muteParticipantsOnEntry"
          render={({ field }) => (
            <FormItem className="space-y-4">
              <FormLabel className="text-sm font-medium">
                成员入会时静音
              </FormLabel>
              <FormControl>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="mute-on"
                      value="muteOn"
                      checked={field.value === "muteOn"}
                      onChange={() => field.onChange("muteOn")}
                      className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <label htmlFor="mute-on" className="text-xs font-medium">
                      开启
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="mute-off"
                      value="muteOff"
                      checked={field.value === "muteOff"}
                      onChange={() => field.onChange("muteOff")}
                      className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <label htmlFor="mute-off" className="text-xs font-medium">
                      关闭
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="mute-auto"
                      value="muteAuto"
                      checked={field.value === "muteAuto"}
                      onChange={() => field.onChange("muteAuto")}
                      className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                    />
                    <label htmlFor="mute-auto" className="text-xs font-medium">
                      超过6人后自动开启静音
                    </label>
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="space-y-4">
        {/* 是否开启报名 */}
          <FormLabel className="text-sm font-medium">
                  报名
          </FormLabel>
          <FormField
            control={control}
            name="enable_enroll"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 max-w-[400px]">

                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    成员需报名入会
                  </FormLabel>
                  <FormDescription className="text-xs">
                    参会者需要先报名通过会议创建者审批后才能参加会议
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 审批模式选择 - 仅在开启报名时显示 */}
          <FormField
            control={control}
            name="enable_enroll"
            render={({ field: enrollField }) => (
              <div>
                {enrollField.value && (
                  <FormField
                    control={control}
                    name="approval_mode"
                    render={({ field: approvalField }) => (
                      <FormItem className="ml-7 space-y-4">
                        <FormControl>
                          <div className="flex space-x-6">
                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id="approval-manual"
                                value="manual"
                                checked={approvalField.value === "manual"}
                                onChange={() => approvalField.onChange("manual")}
                                className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                              />
                              <label htmlFor="approval-manual" className="text-xs font-medium">
                                手动审批
                              </label>
                            </div>

                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id="approval-auto"
                                value="auto"
                                checked={approvalField.value === "auto"}
                                onChange={() => approvalField.onChange("auto")}
                                className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                              />
                              <label htmlFor="approval-auto" className="text-xs font-medium">
                                自动审批
                              </label>
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}
          />
      </div>

      {/* 会议功能设置 */}
      <div className="space-y-4">
        <FormLabel className="text-sm font-medium">
          设置
        </FormLabel>

        {/* UI提示自动增强功能已开启 */}
        <div className={`flex items-start col-span-2 mb-2 p-2 rounded-md ${
          resolvedTheme === 'dark' 
            ? 'bg-slate-800 border border-slate-700' 
            : 'bg-blue-50 border border-blue-200'
        }`}>
          <div className="space-y-1 leading-none">
            <span className={`text-xs font-medium ${
              resolvedTheme === 'dark'
                ? 'text-blue-300'
                : 'text-blue-700'
            }`}>
              自动增强功能已开启
            </span>
            <p className={`text-xs ${
              resolvedTheme === 'dark'
                ? 'text-blue-200'
                : 'text-blue-600'
            }`}>
              为确保会议质量，系统已默认开启自动录制和自动文字转写功能
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 p-4">
          {/* 自动录制 - 固定开启 */}
          <FormField
            control={control}
            name="recordMeeting"
            render={() => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={true}
                    disabled
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-not-allowed"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    自动录制
                  </FormLabel>
                  <FormDescription className="text-xs text-red-500">
                    主持人必须入会才能开启云录制
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 自动文字转写 - 固定开启 */}
          <FormField
            control={control}
            name="autoTranscribe"
            render={() => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={true}
                    disabled
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary cursor-not-allowed"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    自动文字转写
                  </FormLabel>
                  <FormDescription className="text-xs">
                    会议内容将自动转写为文字
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 等候室 */}
          <FormField
            control={control}
            name="waitingRoom"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    启用等候室
                  </FormLabel>
                  <FormDescription className="text-xs">
                    参会者需要等待主持人批准才能加入会议
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 允许成员多端入会 */}
          <FormField
            control={control}
            name="multiPlatform"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    允许成员多端入会
                  </FormLabel>
                  <FormDescription className="text-xs">
                    成员可以从多平台入会
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 成员可提前进入会议 */}
          <FormField
            control={control}
            name="enterInAdvance"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    允许成员在主持人在进会前加入会议
                  </FormLabel>
                  <FormDescription className="text-xs">
                    成员可提前进入会议，消耗会议时长
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 开启屏幕水印 */}
          <FormField
            control={control}
            name="screenWatermark"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    开启屏幕共享水印
                  </FormLabel>
                  <FormDescription className="text-xs">
                    在共享的内容上显示用户身份水印
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 入会提示音 */}
          <FormField
            control={control}
            name="playIvrOnJoin"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    入会提示音
                  </FormLabel>
                  <FormDescription className="text-xs">
                    有新参会者加入时播放提示音
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* 离会提示音 */}
          <FormField
            control={control}
            name="playIvrOnLeave"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    离会提示音
                  </FormLabel>
                  <FormDescription className="text-xs">
                    参会者离开时播放提示音
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* 文档上传设置 */}
      <div className="space-y-4">
        <FormLabel className="text-sm font-medium">
          文档管理
        </FormLabel>
        <div className="space-y-4 rounded-md p-4">
          {/* 允许成员上传文档 */}
          <FormField
            control={control}
            name="enableDocUpload"
            render={({ field }) => (
              <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
                <FormControl>
                  <input
                    type="checkbox"
                    checked={field.value || false}
                    onChange={field.onChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-xs font-medium">
                    允许成员上传文档
                  </FormLabel>
                  <FormDescription className="text-xs">
                    参会者可以在会议中上传和分享文档
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          
        </div>
      </div>
    </div>
  );
}
