"use client";
//用于管理会议参会成员名单
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ui/components/select";
import { Trash2, Plus, Database, AlertCircle } from "lucide-react";
import type { Control, UseFormGetValues, UseFormSetValue, UseFormWatch } from "react-hook-form";
import type { MeetingFormValues } from "../utils/meetingFormValidation";
import { GuestBatchImportDialog } from "./GuestBatchImportDialog";
import { GuestSystemImportDialog } from "./GuestSystemImportDialog";
import type { GuestData } from "../utils/guestExcelParser";
import {
  countValidGuests,
  canAddNewGuest,
  validateContactFormat,
  validateGuestListContacts
} from "../utils/guestValidation";
import { useState, useEffect } from "react";

interface MeetingGuestManagementProps {
  control: Control<MeetingFormValues>;
  getValues: UseFormGetValues<MeetingFormValues>;
  setValue: UseFormSetValue<MeetingFormValues>;
  watch: UseFormWatch<MeetingFormValues>;
  shouldShowGuestFeature: boolean;
  batchImportDialogOpen: boolean;
  setBatchImportDialogOpen: (open: boolean) => void;
  handleBatchImport: (importedGuests: GuestData[]) => void;
  systemImportDialogOpen: boolean;
  setSystemImportDialogOpen: (open: boolean) => void;
  handleSystemImport: (importedGuests: GuestData[]) => void;
  triggerValidation?: number; // 用于触发验证的时间戳
}

/**
 * 会议成员管理组件
 * 包含成员列表管理、添加、删除、批量导入等功能
 */
export function MeetingGuestManagement({
  getValues,
  setValue,
  watch,
  shouldShowGuestFeature,
  batchImportDialogOpen,
  setBatchImportDialogOpen,
  handleBatchImport,
  systemImportDialogOpen,
  setSystemImportDialogOpen,
  handleSystemImport,
  triggerValidation
}: MeetingGuestManagementProps) {
  const guests = watch("guests") || [];

  // 用于跟踪每个成员的验证错误
  const [validationErrors, setValidationErrors] = useState<Record<number, string>>({});
  // 用于跟踪每个成员的姓名验证错误
  const [nameValidationErrors, setNameValidationErrors] = useState<Record<number, string>>({});

  // 当 triggerValidation 改变时，重新验证所有成员
  useEffect(() => {
    if (triggerValidation) {
      const errors = validateGuestListContacts(guests);
      setValidationErrors(errors);
    }
  }, [triggerValidation, guests]);

  if (!shouldShowGuestFeature) {
    return null;
  }

  // 验证单个成员的联系方式
  const validateGuestContact = (index: number, areaCode: string, phoneNumber: string) => {
    if (!phoneNumber.trim()) {
      // 如果手机号为空，清除错误
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
      return;
    }

    const validation = validateContactFormat(areaCode, phoneNumber);
    if (!validation.valid && validation.error) {
      setValidationErrors(prev => ({
        ...prev,
        [index]: validation.error as string
      }));
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
    }
  };

  // 验证单个成员的姓名
  const validateGuestName = (index: number, guestName: string) => {
    if (!guestName.trim()) {
      setNameValidationErrors(prev => ({
        ...prev,
        [index]: "请输入成员姓名"
      }));
    } else {
      setNameValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
    }
  };

  

  return (
    <div className="space-y-4">
      <GuestBatchImportDialog
        isOpen={batchImportDialogOpen}
        onOpenChange={setBatchImportDialogOpen}
        onImportComplete={handleBatchImport}
        currentGuestCount={countValidGuests(guests)}
      />

      <GuestSystemImportDialog
        isOpen={systemImportDialogOpen}
        onOpenChange={setSystemImportDialogOpen}
        onImportComplete={handleSystemImport}
        currentGuestCount={countValidGuests(guests)}
      />
      
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          {/* <input
            type="checkbox"
            id="enableGuests"
            checked={guests.length > 0}
            onChange={(e) => {
              if (!e.target.checked) {
                setValue("guests", []);
              } else {
                setValue("guests", [{ area: "86", phone_number: "", guest_name: "" }]);
              }
            }}
            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          /> */}
          <label htmlFor="enableGuests" className="text-sm font-medium">
            设置成员名单
          </label>
        </div>
        <p className="text-xs text-gray-600">
          添加或导入参会成员，授予其完整互动权限。
        </p>

        {/* 成员列表 */}
        {guests.length > 0 && (
          <div className="space-y-3 border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
            <div className="flex justify-between items-center">
              <span className="text-xs font-medium">成员列表</span>
              <span className="text-xs text-gray-500">
                已添加 {countValidGuests(guests)} 人 / 最多 2000 人
              </span>
            </div>

            {/* 表头 */}
            <div className="grid grid-cols-12 gap-2 text-xs font-medium text-gray-600 pb-2 border-b">
              <div className="col-span-3">国家/地区代码<span className="text-red-500">*</span></div>
              <div className="col-span-4">联系方式<span className="text-red-500">*</span></div>
              <div className="col-span-4">成员名称 <span className="text-red-500">*</span></div>
              <div className="col-span-1">操作</div>
            </div>

            {/* 成员输入行 - 带滚动容器 */}
            <div className="max-h-96 overflow-y-auto space-y-2 pr-2" style={{contain: 'layout style',isolation: 'isolate'}}>
              {guests.map((guest, index) => (
                <div key={index} className="grid grid-cols-12 gap-2 items-start">
                  <div className="col-span-3">
                    <Select
                      value={guest.area ?? "86"}
                      onValueChange={(value) => {
                        const currentGuests = getValues("guests") || [];
                        currentGuests[index].area = value;
                        setValue("guests", currentGuests);

                        // 当地区代码改变时，重新验证手机号
                        if (guest.phone_number) {
                          validateGuestContact(index, value, guest.phone_number);
                        }
                      }}
                    >
                      {/* 修改人：miya，修改日期：2025-08-06 */}
                      {/* 修改说明：统一焦点样式，移除蓝色ring效果 */}
                      {/* 修改记录 2025-08-12 miya: 使用CSS变量替代硬编码颜色值 */}
                      <SelectTrigger className="h-8 text-xs focus:ring-0 focus:ring-offset-0 focus:border-[hsl(var(--primary))] focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-[hsl(var(--primary))] focus-visible:outline-none data-[state=open]:border-[hsl(var(--primary))]">
                        <SelectValue placeholder="选择" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="86">+86</SelectItem>
                        <SelectItem value="1">+1</SelectItem>
                        <SelectItem value="44">+44</SelectItem>
                        <SelectItem value="81">+81</SelectItem>
                        <SelectItem value="82">+82</SelectItem>
                        <SelectItem value="65">+65</SelectItem>
                        <SelectItem value="852">+852</SelectItem>
                        <SelectItem value="853">+853</SelectItem>
                        <SelectItem value="886">+886</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-4">
                    <div className="space-y-1">
                      {/* 修改人：miya，修改日期：2025-08-06 */}
                      {/* 修改说明：统一焦点样式，移除蓝色ring效果 */}
                      {/* 修改记录 2025-08-12 miya: 使用CSS变量替代硬编码颜色值 */}
                      <Input
                        placeholder="联系方式（手机号或座机号）"
                        className={`h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-[hsl(var(--primary))] focus-visible:outline-none ${validationErrors[index] ? 'border-red-500 focus-visible:border-red-500' : ''}`}
                        value={guest.phone_number ?? ""}
                        onChange={(e) => {
                          const currentGuests = getValues("guests") || [];
                          currentGuests[index].phone_number = e.target.value;
                          setValue("guests", currentGuests);

                          // 实时验证
                          validateGuestContact(index, guest.area, e.target.value);
                        }}
                      />
                      {validationErrors[index] && (
                        <div className="flex items-center gap-1 text-xs text-red-500">
                          <AlertCircle className="h-3 w-3" />
                          <span>{validationErrors[index]}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-span-4">
                    <div className="space-y-1">
                      {/* 修改人：miya，修改日期：2025-08-06 */}
                      {/* 修改说明：统一焦点样式，移除蓝色ring效果 */}
                      {/* 修改记录 2025-08-12 miya: 使用CSS变量替代硬编码颜色值 */}
                      <Input
                        placeholder="请输入成员姓名"
                        className={`h-8 text-xs focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-[hsl(var(--primary))] focus-visible:outline-none ${nameValidationErrors[index] ? 'border-red-500 focus-visible:border-red-500' : ''}`}
                        value={guest.guest_name ?? ""}
                        onChange={(e) => {
                          const currentGuests = getValues("guests") || [];
                          currentGuests[index].guest_name = e.target.value;
                          setValue("guests", currentGuests);

                          // 实时验证姓名
                          validateGuestName(index, e.target.value);
                        }}
                      />
                      {nameValidationErrors[index] && (
                        <div className="flex items-center gap-1 text-xs text-red-500">
                          <AlertCircle className="h-3 w-3" />
                          <span>{nameValidationErrors[index]}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      onClick={() => {
                        //只有一个成员时，先添加一个空成员再删除防止数组为空
                        const currentGuests = getValues("guests") || [];
                        if (currentGuests.length === 1) {
                          currentGuests.push({ area: "86", phone_number: "", guest_name: "" });
                        }
                        currentGuests.splice(index, 1);
                        setValue("guests", currentGuests);
                      }}
                    >
                      {/* 修改人：miya 修改时间：2025/7/30 修改说明：图标居中 */}
                      <Trash2 className="h-3 w-3 ml-1.5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* 添加成员按钮 */}
            {/* 三个按钮 第一个在最左边，两个在最右边 */}
            <div className="flex justify-between items-center pt-2">
              <div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    const currentGuests = getValues("guests") || [];
                    const validGuestCount = countValidGuests(currentGuests);

                    // 检查是否可以添加新成员
                    if (validGuestCount < 2000 && canAddNewGuest(currentGuests)) {
                      currentGuests.push({ area: "86", phone_number: "", guest_name: "" });
                      setValue("guests", currentGuests);
                    }
                  }}
                  disabled={
                    countValidGuests(guests) >= 2000 ||
                    !canAddNewGuest(guests)
                  }
                >
                  <Plus className="h-3 w-3 mr-1" />
                  添加成员
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => setBatchImportDialogOpen(true)}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Excel导入
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => setSystemImportDialogOpen(true)}
                >
                  <Database className="h-3 w-3 mr-1" />
                  系统导入
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    // 清空后保留一个空白行（与添加新成员的默认值保持一致）
                    setValue("guests", [{ area: "86", phone_number: "", guest_name: "" }]);
                  }}
                >
                  <Trash2 className="h-3 w-3 mr-2" />
                  清空
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
