"use client";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { CopyIcon, CheckIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import type { Control, UseFormWatch } from "react-hook-form";
import type { MeetingFormValues } from "../utils/meetingFormValidation";

interface MeetingSecuritySettingsProps {
  control: Control<MeetingFormValues>;
  watch: UseFormWatch<MeetingFormValues>;
}

/**
 * 会议安全设置组件
 * 包含密码设置、主持人密钥等安全相关设置
 */
export function MeetingSecuritySettings({ control, watch }: MeetingSecuritySettingsProps) {
  const requirePassword = watch("requirePassword");
  const enableHostKey = watch("enableHostKey");
  const [passwordCopied, setPasswordCopied] = useState(false);
  const [hostKeyCopied, setHostKeyCopied] = useState(false);

  const handleCopy = async (text: string, isPassword: boolean) => {
    try {
      await navigator.clipboard.writeText(text);
      if (isPassword) {
        setPasswordCopied(true);
        toast.success("密码已复制到剪贴板");
        setTimeout(() => setPasswordCopied(false), 2000);
      } else {
        setHostKeyCopied(true);
        toast.success("主持人密钥已复制到剪贴板");
        setTimeout(() => setHostKeyCopied(false), 2000);
      }
    } catch (error) {
      toast.error("复制失败，请手动复制");
    }
  };

  return (
    <div className="space-y-4">
      <FormLabel className="text-sm font-medium">
        保密
      </FormLabel>
      
      {/* 密码输入框 - 默认一定设置 */}
      {requirePassword && (
        <FormField
          control={control}
          name="password"
          render={({ field }) => (
            <FormItem className="max-w-[400px]">
              <FormLabel className="text-xs font-medium">设置入会密码</FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    type="text"
                    className="text-xs pr-10"
                    placeholder="设置会议密码"
                    value={field.value || ''}
                    onChange={field.onChange}
                  />
                </FormControl>
                <button
                  type="button"
                  onClick={() => handleCopy(field.value||"" , true)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
                  title={passwordCopied ? "已复制" : "复制密码"}
                >
                  {passwordCopied ? (
                    <CheckIcon className="size-4 text-green-600" />
                  ) : (
                    <CopyIcon className="size-4" />
                  )}
                </button>
              </div>
              <FormDescription className="text-xs">
                密码将在会议邀请中分享给参会者
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {/* 主持人密钥 */}
      <FormField
        control={control}
        name="enableHostKey"
        render={({ field }) => (
          <FormItem className="flex items-start space-x-3 space-y-0 max-w-[400px]">
            <FormControl>
              <input
                type="checkbox"
                checked={field.value || false}
                onChange={field.onChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel className="text-xs font-medium">
                开启主持人密钥
              </FormLabel>
              <FormDescription className="text-xs">
                使用密钥验证主持人身份
              </FormDescription>
            </div>
          </FormItem>
        )}
      />

      {/* 主持人密钥输入框 - 条件渲染 */}
      {enableHostKey && (
        <FormField
          control={control}
          name="hostKey"
          render={({ field }) => (
            <FormItem className="max-w-[400px]">
              <FormLabel className="text-xs font-medium">主持人密钥</FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    type="text"
                    className="text-xs pr-10"
                    placeholder="设置主持人密钥"
                    value={field.value || ''}
                    onChange={field.onChange}
                  />
                </FormControl>
                <button
                  type="button"
                  onClick={() => handleCopy(field.value||"" , false)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
                  title={hostKeyCopied ? "已复制" : "复制主持人密钥"}
                >
                  {hostKeyCopied ? (
                    <CheckIcon className="size-4 text-green-600" />
                  ) : (
                    <CopyIcon className="size-4" />
                  )}
                </button>
              </div>
              <FormDescription className="text-xs">
                密钥仅分享给会议主持人
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}
