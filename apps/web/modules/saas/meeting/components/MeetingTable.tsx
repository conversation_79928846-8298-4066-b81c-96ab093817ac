"use client";

import { useState } from "react";
import { XCircle} from "lucide-react";
import { DocsDialog, SignInDialog, ParticipantsDialog, MeetingMinutesDialog } from "./MeetingDialogs";
import { MeetingFileUploadDialog } from "./MeetingFileUploadDialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { formatTimestamp} from "./meeting-utils";

import type { MeetingTableProps } from "./types";
import { useSession } from "../../auth/hooks/use-session";
import { toast } from "sonner";
// import { cn } from "@ui/lib";
import { AntMeetingTable } from "./ant-meeting-table";
import type { TableColumnType } from "antd";
import Link from "next/link";
import { ChevronDown, ChevronUp } from "lucide-react";

// 签到信息类型定义
interface SignInInfo {
  sign_in_id: string;
  start_time: string;
  sign_in_specification?: string;
  sign_in_status: number; // 0：未开始 1：进行中 2：已结束
}

interface SignInListData {
  sign_in_list: SignInInfo[];
  total_count: number;
  total_page: number;
  current_page: number;
  current_size: number;
}



export function MeetingTable({
  meetings,
  organizationSlug,
  sortDirection,
  toggleSortDirection,
  isLoading,
//   cancelingMeetingId,
  handleCancelMeeting,
  handleCancelMeetingDirect, // 新增：直接取消会议的函数
//   handleGetMeetingDocs,
//   handleGetMeetingRecord,
  showDocAndRecordButtons,
  hasCheckedUserStatus = true,
  isUserActive = true,
  isActivating = false,
  handleActivation,
  showActivationDialog = false,
  clearActivationCheck,
}: MeetingTableProps) {
const { user } = useSession();

// 签到记录相关状态
const [signInDialogOpen, setSignInDialogOpen] = useState(false);
const [currentMeetingSignIn, setCurrentMeetingSignIn] = useState<SignInListData | null>(null);
const [loadingSignIn, setLoadingSignIn] = useState(false);
const [currentMeetingTitle, setCurrentMeetingTitle] = useState("");

// 取消会议确认对话框相关状态
// 修改人：miya，修改日期：2025-08-06
// 修改说明：添加取消会议确认对话框状态和加载态
const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
const [meetingToCancel, setMeetingToCancel] = useState<{id: string, title: string} | null>(null);
const [isCanceling, setIsCanceling] = useState(false);

// 处理取消会议确认对话框
// 修改人：miya，修改日期：2025-08-06
// 修改说明：显示取消会议确认对话框
const handleShowCancelDialog = (meetingId: string, meetingTitle: string) => {
  setMeetingToCancel({ id: meetingId, title: meetingTitle });
  setCancelDialogOpen(true);
};

// 确认取消会议
// 修改人：miya，修改日期：2025-08-06
// 修改说明：使用直接取消函数，绕过系统弹窗，添加加载态
const handleConfirmCancel = async () => {
  if (!meetingToCancel) {
    return;
  }

  setIsCanceling(true); // 开始加载

  try {
    // 使用父组件提供的直接取消函数
    if (handleCancelMeetingDirect) {
      await handleCancelMeetingDirect(meetingToCancel.id);
    } else if (handleCancelMeeting) {
      // 如果没有直接取消函数，使用原有函数（但这会触发系统弹窗）
      handleCancelMeeting(meetingToCancel.id);
    }

    setCancelDialogOpen(false);
    setMeetingToCancel(null);

  } catch (error: any) {
    console.error("取消会议失败:", error);
    // 这里可以添加错误提示
    alert(error.message || "取消会议失败");
  } finally {
    setIsCanceling(false); // 结束加载
  }
};

// 分页相关状态
const [currentPage, setCurrentPage] = useState(1);
const [itemsPerPage, setItemsPerPage] = useState(10); // 默认每页显示10条
const [pageInput, setPageInput] = useState(""); // 用于页码输入框
const [itemsPerPageInput, setItemsPerPageInput] = useState(itemsPerPage.toString()); // 用于每页条数输入框

//会议文档记录相关状态
const [docsDialogOpen, setDocsDialogOpen] = useState(false);
const [currentMeetingDocs, setCurrentMeetingDocs] = useState<any>(null);
const [loadingDocs, setLoadingDocs] = useState(false);

// 参会人员相关状态
const [participantsDialogOpen, setParticipantsDialogOpen] = useState(false);
const [currentMeetingId, setCurrentMeetingId] = useState("");
const [loadingParticipants, setLoadingParticipants] = useState(false);
/* 修改块开始: 参会人员导出数据状态
 * 修改范围: 新增导出url和sheetData的state
 * 修改时间: [TIME]
 * 对应计划步骤: 1
 */
const [participantsExportUrl, setParticipantsExportUrl] = useState<string | null>(null);
const [participantsSheetData, setParticipantsSheetData] = useState<any>(null);
/* 修改块结束: 参会人员导出数据状态
 * 修改时间: [TIME]
 */

// 统一的会议纪要相关状态
const [meetingMinutesDialogOpen, setMeetingMinutesDialogOpen] = useState(false);
const [loadingMeetingMinutes, setLoadingMeetingMinutes] = useState(false);
const [meetingMinutesData, setMeetingMinutesData] = useState({
	aiSummaryContent: "",
	downloadUrl: "",
	viewAddress: ""
});

// 文件上传对话框相关状态
const [fileUploadDialogOpen, setFileUploadDialogOpen] = useState(false);
const [currentUploadMeetingId, setCurrentUploadMeetingId] = useState("");
const [currentUploadMeetingTitle, setCurrentUploadMeetingTitle] = useState("");
  
  // 计算总页数
  const totalPages = Math.ceil(meetings.length / itemsPerPage);
  
  // 获取当前页的数据
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return meetings.slice(startIndex, endIndex);
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      setPageInput(""); // 清空页码输入框
    }
  };

  // 处理页码输入跳转
  const handlePageJump = () => {
    const pageNumber = Number.parseInt(pageInput, 10);
    if (pageNumber && pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setPageInput(""); // 清空输入框
    } else {
      toast.error(`请输入有效的页码 (1-${totalPages})`);
    }
  };

  // 处理每页条数变化
  const handleItemsPerPageChange = () => {
    const newItemsPerPage = Number.parseInt(itemsPerPageInput, 10);
    if (newItemsPerPage && newItemsPerPage >= 1) {
      setItemsPerPage(newItemsPerPage);
      setCurrentPage(1); // 重置到第一页
      setItemsPerPageInput(newItemsPerPage.toString()); // 更新输入框值
    } else {
      toast.error("请输入有效的每页显示条数");
      setItemsPerPageInput(itemsPerPage.toString()); // 恢复原值
    }
  };

  // 处理输入框回车事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, type: 'page' | 'itemsPerPage') => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (type === 'page') {
        handlePageJump();
      } else {
        handleItemsPerPageChange();
      }
    }
  };

  

  /* 修改块开始: 新增签到记录获取功能
   * 修改范围: 添加获取会议签到记录的函数
   * 对应需求: 调用后端API获取签到列表并在模态框中展示
   * 恢复方法: 删除此函数和相关状态管理
   */
  // 获取会议签到记录
  const handleGetSignInRecord = async (meetingId: string, meetingTitle: string) => {
    if (!user?.id) {
      return;
    }
    setLoadingSignIn(true);
    setCurrentMeetingTitle(meetingTitle);
    setSignInDialogOpen(true);
    
    try {
      const response = await fetch(`/api/meetings/sign-in/list/${meetingId}?userId=${user.id}`);
      
      if (!response.ok) {
        throw new Error(`获取签到记录失败 (状态码: ${response.status})`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || "获取签到记录失败");
      }
      
      
      // 验证返回的数据结构
      if (data.data && typeof data.data === 'object') {
        // 确保 sign_in_list 存在，如果不存在则设置为空数组
        const signInData = {
          sign_in_list: data.data.sign_in_list || [],
          total_count: data.data.total_count || 0,
          total_page: data.data.total_page || 1,
          current_page: data.data.current_page || 1,
          current_size: data.data.current_size || 0,
        };
        setCurrentMeetingSignIn(signInData);
      } else {
        // 如果数据结构不正确，设置默认值
        setCurrentMeetingSignIn({
          sign_in_list: [],
          total_count: 0,
          total_page: 1,
          current_page: 1,
          current_size: 0,
        });
      }
      
    } catch (error: any) {
      toast.error("获取会议签到记录错误:", error);
      
      // 设置空的签到数据以避免undefined错误
      setCurrentMeetingSignIn({
        sign_in_list: [],
        total_count: 0,
        total_page: 1,
        current_page: 1,
        current_size: 0,
      });
      
    } finally {
      setLoadingSignIn(false);
    }
  };

  // 处理显示参会人员弹窗
  /* 修改块开始: 参会人员弹窗自动导出逻辑
   * 修改范围: handleShowParticipants合并导出逻辑，自动请求导出接口并存储数据
   * 修改时间: [TIME]
   * 对应计划步骤: 2
   */
  const handleShowParticipants = async (meetingId: string, meetingTitle: string, startTime: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
    setParticipantsDialogOpen(true);
    setLoadingParticipants(true);
    setParticipantsExportUrl(null);
    setParticipantsSheetData(null);
    try {
      const { url, sheetData } = await handleExportParticipants(meetingId, startTime);
      setParticipantsExportUrl(url);
      setParticipantsSheetData(sheetData);
    } catch (e) {
      setParticipantsExportUrl(null);
      setParticipantsSheetData(null);
    } finally {
      setLoadingParticipants(false);
    }
  };
  /* 修改块结束: 参会人员弹窗自动导出逻辑
   * 修改时间: [TIME]
   */

  // 处理导出参会成员 (在弹窗中使用)
  /* 修改块开始: 导出参会成员返回数据
   * 修改范围: handleExportParticipants返回{url, sheetData}，不再直接toast
   * 修改时间: [TIME]
   * 对应计划步骤: 3
   */
  const handleExportParticipants = async (meetingId: string, startTime: string) => {
	//获取真实会议开始时间，转为时间戳传入后端函数
	const [datePart] = startTime.split(' 星期');
	const timePart = startTime.split(' ').pop() || '';
	const timestamp = new Date(`${datePart}T${timePart}:00`).getTime() / 1000;
    const response = await fetch(`/api/meetings/export-participants/${meetingId}?startTime=${timestamp}`, { method: 'POST' });
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || "导出失败");
    }
    if (!result.data?.url) {
      throw new Error("未获取到导出文件URL");
    }
    return { url: result.data.url, sheetData: result.sheetData };
  };
  /* 修改块结束: 导出参会成员返回数据
   * 修改时间: [TIME]
   */



  // 处理显示统一的会议纪要弹窗
  const handleShowMeetingMinutes = async (meetingId: string, meetingTitle: string, startTime: string) => {
    setCurrentMeetingTitle(meetingTitle);
    setCurrentMeetingId(meetingId);
    setMeetingMinutesDialogOpen(true);
    setLoadingMeetingMinutes(true);

    await fetchMeetingMinutesData(meetingId, meetingTitle, startTime);
  };

  // 提取获取会议纪要数据的逻辑，供刷新功能复用
  const fetchMeetingMinutesData = async (meetingId: string, meetingTitle: string, startTime: string) => {
    try {
      // 获取会议录制数据，包含所有需要的信息
      const recordData: any = await handleGetMeetingRecord?.(meetingId, startTime);

      if (!recordData) {
        setMeetingMinutesData({
          aiSummaryContent: "暂无会议记录数据",
          downloadUrl: "",
          viewAddress: ""
        });
        return;
      }

      const transcript = recordData?.txtContent;
      let aiSummaryContent = "";

      // 如果有转写内容，发送到n8n进行AI总结
      if (transcript) {
        try {
          // 使用n8n会议代理路由发送转写内容进行AI总结
          const n8nResponse = await fetch('/api/n8n_meeting_proxy/ai-meeting-summary', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text: transcript,
              meetingId: meetingId,
              meetingTitle: meetingTitle,
              timestamp: new Date().toISOString()
            })
          });

          const n8nResult = await n8nResponse.json();

          if (n8nResult.code === 200) {
            // n8n处理成功，使用返回的AI总结
            aiSummaryContent = n8nResult.data?.data?.output || "AI总结处理中，请稍后查看...";
          } else {
            // n8n处理失败，回退到原有的AI总结内容
            aiSummaryContent = recordData?.aiSummaryContent ?? "暂无AI总结内容";
          }
        } catch (n8nError) {
          console.error("调用n8n AI总结服务失败:", n8nError);
          toast.error("AI总结服务暂时不可用");
          // 回退到原有的AI总结内容
          aiSummaryContent = recordData?.aiSummaryContent ?? "AI总结服务暂时不可用";
        }
      } else {
        // 没有转写内容，使用原有的AI总结
        aiSummaryContent = recordData?.aiSummaryContent ?? "暂无转写内容，无法生成AI总结";
      }

      // 设置统一的会议纪要数据
      setMeetingMinutesData({
        aiSummaryContent: aiSummaryContent,
        downloadUrl: recordData?.docx_download_address ?? "",
        viewAddress: recordData?.view_address ?? ""
      });

    } catch (error: any) {
      console.error("获取会议记录失败:", error);
      toast.error("获取会议记录失败");
      setMeetingMinutesData({
        aiSummaryContent: "获取会议记录失败，无法生成AI总结",
        downloadUrl: "",
        viewAddress: ""
      });
    } finally {
      setLoadingMeetingMinutes(false);
    }
  };

  // 处理刷新AI总结
  const handleRefreshMeetingMinutes = async () => {
    if (!currentMeetingId || !currentMeetingTitle) return;

    setLoadingMeetingMinutes(true);

    // 需要获取会议开始时间，这里从meetings数组中查找
    const currentMeeting = meetings.find(meeting => meeting.id === currentMeetingId);
    if (currentMeeting) {
      await fetchMeetingMinutesData(currentMeetingId, currentMeetingTitle, currentMeeting.startTime);
    } else {
      setLoadingMeetingMinutes(false);
      toast.error("无法获取会议信息");
    }
  };

  // 处理显示文件上传对话框
  const handleShowFileUpload = (meetingId: string, meetingTitle: string, guests: any[]) => {
    setCurrentUploadMeetingId(meetingId);
    setCurrentUploadMeetingTitle(meetingTitle);
    setFileUploadDialogOpen(true);
	console.log(guests);
  };

  const handleGetMeetingDocs = async (
			meetingId: string,
			meetingTitle: string,
		) => {
			// 检查用户信息是否存在
			if (!user?.id) {
				toast.error("用户信息不存在，无法获取会议文档");
				toast.error("用户信息不存在，请重新登录");
				return;
			}

			setLoadingDocs(true);
			setCurrentMeetingTitle(meetingTitle);
			setDocsDialogOpen(true);

			try {
				const response = await fetch(
					`/api/meetings/docs/${meetingId}/?userId=${user.id}`,
				);

				if (!response.ok) {
					throw new Error(`获取文档失败: ${response.status}`);
				}

				const data = await response.json();

				if (!data.success) {
					throw new Error(data.error || "获取文档失败");
				}

				// console.log("会议文档列表:", data.data);

				// 根据实际返回的数据结构进行处理
				const docsData = data.data;
				if (
					docsData &&
					(docsData.doc_info_list?.length > 0 ||
						docsData.doc_list?.length > 0)
				) {
					setCurrentMeetingDocs({
						total_count: docsData.total_count || 0,
						// API可能返回doc_info_list或doc_list，我们都做兼容处理
						doc_info_list:
							docsData.doc_info_list || docsData.doc_list || [],
					});
				} else {
					// 如果没有文档，设置为null
					setCurrentMeetingDocs(null);
				}

				return data.data;
			} catch (error: any) {
				toast.error("获取会议文档错误:", error);
				setCurrentMeetingDocs(null);
				toast.error(error.message || "获取会议文档失败");
			} finally {
				setLoadingDocs(false);
			}
		};

		// 获取会议录制文件
		const handleGetMeetingRecord = async (
			meetingId: string,
			startTime: string,
		): Promise<any | null> => {
			// setCurrentMeetingTitle(meetingTitle);

			//获取真实会议开始时间，转为时间戳传入后端函数
			const [datePart] = startTime.split(' 星期');
			const timePart = startTime.split(' ').pop() || '';
			const timestamp = new Date(`${datePart}T${timePart}:00`).getTime() / 1000;
			try {
				const response = await fetch(
					`/api/meetings/records/detail/${meetingId}?userid=${user?.id}&startTime=${timestamp}`,
				);

				// 如果响应不是200，直接设置为null
				if (!response.ok) {
					console.log("获取录制文件响应不是200:", response.status);
					toast.error(`获取录制文件失败: ${response.status}`);
					return;
				}

				const data = await response.json();

				// 如果API返回不成功，直接设置为null
				if (!data.success) {
					console.log("获取录制文件API返回不成功:", data.error);
					toast.error(data.error || "获取录制文件失败");
					return;
				}
				return data.data;
			} catch (error: any) {
				toast.error("获取会议录制错误:", error);
				toast.error(error.message || "获取会议录制失败");
				return null;
			}
		};

  // 加载状态
//   if (isLoading && meetings.length === 0) {
//     return (
//       <div className="flex justify-center items-center h-40">
//         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
//       </div>
//     );
//   }

  

  // 如果用户未激活，显示激活UI
  if (hasCheckedUserStatus && !isUserActive) {
    return (
					<div className="flex flex-col items-center justify-center py-12 text-center">
						<div className="bg-blue-50 p-4 rounded-full mb-4">
							<XCircle className="size-12 text-blue-400" />
						</div>
						<h2 className="text-gray-500 mb-2">用户未激活</h2>
						<div className="flex justify-center items-center gap-4">
							<Button
								variant="outline"
								onClick={handleActivation}
								className="flex items-center gap-2"
								disabled={isActivating}
							>
								<>
									<svg
										className="size-4"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										aria-hidden="true"
									>
										<path
											d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
											strokeLinecap="round"
											strokeLinejoin="round"
										/>
									</svg>
									激活腾讯会议账号
								</>
							</Button>
							{isActivating && (
								<div className="text-sm text-blue-500 flex items-center gap-2">
									<div className="inline-block animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
									正在处理激活请求...
								</div>
							)}
						</div>

						{/* 激活对话框 */}
						{showActivationDialog && (
							<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
								<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
									<h3 className="text-lg font-semibold mb-4">
										请完成激活
									</h3>
									<p className="text-gray-600 mb-4">
										请在新打开的窗口中完成账号激活。激活完成后，请点击下方按钮刷新页面。
									</p>
									<div className="flex justify-end gap-4">
										<Button
											variant="primary"
											onClick={async () => {
												clearActivationCheck?.();
												window.location.reload();
											}}
										>
											我已完成激活
										</Button>
									</div>
								</div>
							</div>
						)}
					</div>
				);
  }

  

  /* columns 结构定义，render 里直接用父组件状态和回调 */
		const columns: TableColumnType<any>[] = [
			{
				title: (
					<button
						type="button"
						className="flex items-center cursor-pointer text-left hover:text-gray-700 text-sm font-medium"
						onClick={toggleSortDirection}
						onKeyDown={(e) =>
							e.key === "Enter" && toggleSortDirection?.()
						}
					>
						开始时间
						<span
							className="ml-1 focus:outline-none"
							title={
								sortDirection === "asc"
									? "点击降序排列"
									: "点击升序排列"
							}
						>
							{sortDirection === "asc" ? (
								<ChevronUp className="size-4" />
							) : (
								<ChevronDown className="size-4" />
							)}
						</span>
					</button>
				),
				dataIndex: "startTime",
				key: "startTime",
				width: 200,
				render: (startTime: string) => (
					<div className="flex items-center text-black-700 text-sm">
						{startTime}
					</div>
				),
			},
			{
				title: "会议主题",
				dataIndex: "title",
				key: "title",
				width: 300,
				render: (title: string) => (
					<div className="text-black-700 text-sm">{title}</div>
				),
			},
			{
				title: "会议号",
				dataIndex: "meetingId",
				key: "meetingId",
				width: 150,
				render: (meetingId: string) => (
					<div className="text-black-700 text-sm">{meetingId}</div>
				),
			},
			showDocAndRecordButtons
				? {
						title: "会议内容",
						key: "meetingRecord",
						width: 300,
						render: (_: any, record: any) => (
							<div className="flex items-center justify-center gap-2">
								<button
									type="button"
									onClick={() =>
										handleGetSignInRecord(
											record.id,
											record.title,
										)
									}
									className="text-blue-500 hover:underline truncate"
								>
									签到记录
								</button>
								<button
									type="button"
									onClick={() =>
										handleShowParticipants(
											record.id,
											record.title,
											record.startTime,
										)
									}
									className="text-blue-500 hover:underline truncate"
								>
									参会人员
								</button>
								<button
									type="button"
									onClick={() =>
										handleGetMeetingDocs(
											record.id,
											record.title,
										)
									}
									className="text-blue-500 hover:underline truncate"
								>
									会议文档
								</button>
								<button
									type="button"
									onClick={() =>
										handleShowMeetingMinutes(
											record.id,
											record.title,
											record.startTime,
										)
									}
									className="text-blue-500 hover:underline truncate"
								>
									<span className="inline-flex items-center">
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											xmlns="http://www.w3.org/2000/svg"
										>
											<title>会议纪要</title>
											<path
												d="M12 3L14.5 8.5L20 11L14.5 13.5L12 19L9.5 13.5L4 11L9.5 8.5L12 3Z"
												fill="#FFCC33"
												stroke="#FFCC33"
												strokeWidth="0.5"
											/>
											<path
												d="M18 2L19 5L22 6L19 7L18 10L17 7L14 6L17 5L18 2Z"
												fill="#FFE066"
												stroke="#FFE066"
												strokeWidth="0.5"
											/>
											<path
												d="M6 6L7 8L9 9L7 10L6 12L5 10L3 9L5 8L6 6Z"
												fill="#FFDD66"
												stroke="#FFDD66"
												strokeWidth="0.5"
											/>
										</svg>
										<span className="hover:underline">会议纪要</span>
									</span>
								</button>
							</div>
						),
					}
				: {
						title: "操作",
						key: "actions",
						width: 200,
						render: (_: any, record: any) => (
							<div className="flex items-center justify-center gap-2">
								{record.status !== "MEETING_STATE_ENDED" && (
									<a
										className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm"
										href={record.joinURL || "#"}
										target="_blank"
										rel="noopener noreferrer"
									>
										进入
									</a>
								)}
								{record.status !== "MEETING_STATE_ENDED" && (
									<Link
										className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm"
										href={`/app/${organizationSlug}/meeting/list/change_meeting/${record.id}`}
									>
										修改
									</Link>
								)}
								{handleCancelMeeting &&
									record.status !== "MEETING_STATE_ENDED" && (
										<button
											type="button"
											className="h-7 px-2 flex items-center gap-1 cursor-pointer text-black-700 text-sm hover:text-red-500"
											onClick={() =>
												handleShowCancelDialog(record.id, record.title)
											}
										>
											取消
										</button>
									)}
								{record.status !== "MEETING_STATE_ENDED" && (
									<button
										type="button"
										className="h-7 px-2 flex items-center gap-1 cursor-pointer text-black-700 text-sm hover:text-blue-500 text-sm"
										onClick={() =>
											handleShowFileUpload(record.id, record.title,record.guests)
										}
									>
										<span className="inline-flex items-center">
										<svg
											className="h-4 w-4"
											viewBox="0 0 24 24"
											fill="none"
											xmlns="http://www.w3.org/2000/svg"
										>
											<title>会前准备</title>
											<path
												d="M12 3L14.5 8.5L20 11L14.5 13.5L12 19L9.5 13.5L4 11L9.5 8.5L12 3Z"
												fill="#FFCC33"
												stroke="#FFCC33"
												strokeWidth="0.5"
											/>
											<path
												d="M18 2L19 5L22 6L19 7L18 10L17 7L14 6L17 5L18 2Z"
												fill="#FFE066"
												stroke="#FFE066"
												strokeWidth="0.5"
											/>
											<path
												d="M6 6L7 8L9 9L7 10L6 12L5 10L3 9L5 8L6 6Z"
												fill="#FFDD66"
												stroke="#FFDD66"
												strokeWidth="0.5"
											/>
										</svg>
										<span className="hover:underline">会前准备</span>
									</span>
									</button>
								)}
							</div>
						),
					},
		];
  /* 修改块结束: columns 外提到 MeetingTable 顶部
   * 修改时间: 2025-06-17
   */

  return (
			<>
				{/* 签到记录模态框 */}
				<SignInDialog
					open={signInDialogOpen}
					onOpenChange={setSignInDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingSignIn={currentMeetingSignIn}
					loadingSignIn={loadingSignIn}
					formatTimestamp={formatTimestamp}
				/>

				<DocsDialog
					open={docsDialogOpen}
					onOpenChange={setDocsDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingDocs={currentMeetingDocs}
					loadingDocs={loadingDocs}
					formatTimestamp={formatTimestamp}
				/>

				{/* 参会人员模态框 */}
				<ParticipantsDialog
					open={participantsDialogOpen}
					onOpenChange={setParticipantsDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingParticipants={loadingParticipants}
					exportUrl={participantsExportUrl}
					sheetData={participantsSheetData}
				/>

				{/* 统一的会议纪要模态框 */}
				<MeetingMinutesDialog
					open={meetingMinutesDialogOpen}
					onOpenChange={setMeetingMinutesDialogOpen}
					currentMeetingTitle={currentMeetingTitle}
					currentMeetingId={currentMeetingId}
					loadingMinutes={loadingMeetingMinutes}
					aiSummaryContent={meetingMinutesData.aiSummaryContent}
					downloadUrl={meetingMinutesData.downloadUrl}
					viewAddress={meetingMinutesData.viewAddress}
					onRefreshAISummary={handleRefreshMeetingMinutes}
				/>

				{/* 文件上传模态框 */}
				<MeetingFileUploadDialog
					isOpen={fileUploadDialogOpen}
					onOpenChange={setFileUploadDialogOpen}
					meetingId={currentUploadMeetingId}
					meetingTitle={currentUploadMeetingTitle}
				/>

				<div>
					<AntMeetingTable
						data={meetings}
						columns={columns}
						loading={isLoading}
					/>
				</div>

				{/* 分页器 暂时不使用*/}
				{/* {meetings.length > 0 && (
					<div className="flex items-center justify-between px-4 py-3 border-t">
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage - 1)
								}
								disabled={currentPage === 1}
							>
								<ChevronLeft className="h-4 w-4" />
								上一页
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage + 1)
								}
								disabled={currentPage === totalPages}
							>
								下一页
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>

						<div className="flex items-center gap-4">
							<div className="flex items-center gap-2">
								<span className="text-sm text-gray-500">
									第
								</span>
								<Input
									type="number"
									min={1}
									max={totalPages}
									value={pageInput || currentPage}
									onChange={(e) =>
										setPageInput(e.target.value)
									}
									onKeyDown={(e) => handleKeyDown(e, "page")}
									className="w-12 h-8 text-sm text-center"
									onFocus={() => setPageInput("")}
									onBlur={() => setPageInput("")}
								/>
								<span className="text-sm text-gray-500">
									页，共 {totalPages} 页
								</span>
							</div>

							
							<div className="flex items-center gap-2">
								<span className="text-sm text-gray-500">
									每页
								</span>
								<Input
									type="number"
									min={1}
									value={itemsPerPageInput || ''}
									onChange={(e) =>
										setItemsPerPageInput(e.target.value)
									}
									onKeyDown={(e) =>
										handleKeyDown(e, "itemsPerPage")
									}
									className="w-16 h-8 text-sm"
								/>
								<span className="text-sm text-gray-500">
									条
								</span>
							</div>
						</div>
					</div>
				)} */}
			{/* 取消会议确认对话框 */}
			{/* 修改人：miya，修改日期：2025-08-06 */}
			{/* 修改说明：添加取消会议确认对话框 */}
			<Dialog open={cancelDialogOpen} onOpenChange={isCanceling ? undefined : setCancelDialogOpen}>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle className="mb-3">确认取消会议</DialogTitle>
						<DialogDescription className="mb-2">
							确定要取消会议 "{meetingToCancel?.title}" 吗？此操作不可撤销。
						</DialogDescription>
					</DialogHeader>
					<DialogFooter className="flex gap-2 sm:gap-0">
						<Button
							variant="outline"
							onClick={() => {
								setCancelDialogOpen(false);
								setMeetingToCancel(null);
							}}
							disabled={isCanceling}
							className="focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-[#1677ff] focus-visible:outline-none"
						>
							返回
						</Button>
						<Button
							variant="error"
							onClick={handleConfirmCancel}
							disabled={isCanceling}
							className="focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-red-500 focus-visible:outline-none"
						>
							{isCanceling ? (
								<>
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
									取消中...
								</>
							) : (
								"确认取消"
							)}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
			</>
		);
}
