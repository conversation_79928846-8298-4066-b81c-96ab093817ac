"use client";

import React from "react";

interface PaginationStatusProps {
  currentLoaded: number;
  total: number;
  isLoading?: boolean;
  className?: string;
}

/**
 * 分页状态显示组件
 * 显示格式：当前已加载数量/总数量 (例如：30/200)
 * 
 * @param currentLoaded 当前已加载的数据数量
 * @param total 总数据数量
 * @param isLoading 是否正在加载中
 * @param className 自定义样式类名
 */
export function PaginationStatus({
  currentLoaded,
  total,
  isLoading = false,
  className = ""
}: PaginationStatusProps) {
  // 确保数值有效性
  const safeCurrentLoaded = Math.max(0, currentLoaded);
  const safeTotal = Math.max(0, total);

  // 处理边界情况：数据为空或无效时不显示
  if (safeTotal <= 0 && !isLoading) {
    return null;
  }

  // 加载中状态显示
  if (isLoading && safeTotal === 0) {
    return (
      <div className={`text-xs text-muted-foreground ${className}`}>
        加载中...
      </div>
    );
  }

  return (
    <div className={`text-xs text-muted-foreground font-medium text-end ${className}`}>
      {safeCurrentLoaded}/{safeTotal}
    </div>
  );
}
