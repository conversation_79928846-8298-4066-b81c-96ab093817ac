// 修改人：Miya，修改日期：2025/7/22
// 原代码：import { Input } from "@ui/components/input";
// 修改说明：替换为DateRangePicker组件，提供更好的日期范围选择体验
import { DateRangePicker, type DateRange } from "@ui/components/data-range-picker";
import dayjs from "dayjs";
import type { DateFilterProps } from "./types";

/**
 * 格式化时间戳为可读时间
 *
 * @param timestamp - 时间戳（秒或毫秒）
 * @returns 格式化后的时间字符串
 */
export function formatTimestamp(timestamp: number | string): string {
  // 修改人：Miya，修改日期：2025/7/22
  // 原代码：if (!timestamp) return '';
  // 修改说明：统一使用单引号格式，保持代码风格一致性
  if (!timestamp) {return '';}

  // 如果时间戳是字符串，先转换为数字
  const ts = typeof timestamp === 'string' ? Number.parseInt(timestamp) : timestamp;

  // 判断时间戳是否为毫秒级别，如果是秒级别的时间戳，则转为毫秒
  const date = new Date(ts > 10000000000 ? ts : ts * 1000);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

/**
 * 将日期字符串转换为时间戳（秒）
 *
 * @param dateString - 日期字符串，格式为 YYYY-MM-DD
 * @param isEndDate - 是否为结束日期，如果是则设置为当天的23:59:59
 * @returns 时间戳（秒）
 */
export function dateStringToTimestamp(dateString: string, isEndDate = false): string {
  // 修改人：Miya，修改日期：2025/7/22
  // 原代码：if (!dateString) return '';
  // 修改说明：统一使用单引号格式，保持代码风格一致性
  if (!dateString) {return '';}

  // 创建日期对象，使用本地时区
  const date = new Date(dateString);

  if (isEndDate) {
    // 如果是结束日期，设置为当天的23:59:59
    date.setHours(23, 59, 59, 999);
  } else {
    // 如果是开始日期，设置为当天的00:00:00
    date.setHours(0, 0, 0, 0);
  }

  // 转换为秒级时间戳
  return Math.floor(date.getTime() / 1000).toString();
}

export function getDefaultDateRange(meetingType: 'upcoming' | 'ongoing' | 'completed'): { startDate: string; endDate: string } {
  const today = new Date();

  if (meetingType === 'upcoming' || meetingType === 'ongoing') {
    // 即将开始和进行中的会议：当前时间到一个月后
    const oneMonthLater = new Date(today);
    oneMonthLater.setMonth(today.getMonth() + 1);

    return {
      startDate: today.toISOString().split('T')[0],
      endDate: oneMonthLater.toISOString().split('T')[0]
    };
  }

  // 历史会议：一个月前到当前时间
  const oneMonthAgo = new Date(today);
  oneMonthAgo.setMonth(today.getMonth() - 1);

  return {
    startDate: oneMonthAgo.toISOString().split('T')[0],
    endDate: today.toISOString().split('T')[0]
  };
}

/**
 * 格式化会议时长为小时和分钟
 * 
 * @param minutes - 分钟数
 * @returns 格式化的时长字符串
 */
export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours > 0 ? `${hours}小时` : ''}${mins > 0 ? `${mins}分钟` : ''}`;
}

/**
 * 格式化API返回的会议数据
 * 
 * @param meetingData - API返回的会议数据
 * @returns 格式化后的会议对象
 */
export function formatMeetingData(meetingData: any) {
  // 转换时间戳为日期对象
  const timestamp = Number.parseInt(meetingData.start_time || "0") * 1000; // 转换为毫秒
  const dateObj = new Date(timestamp);
      
  // 获取星期
  const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
  const weekday = weekdays[dateObj.getDay()];
      
  // 格式化为：年-月-日-星期-时间
  const formattedTime = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} 星期${weekday} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}`;
  
  // 处理结束时间
  const endTimestamp = Number.parseInt(meetingData.end_time || "0") * 1000;
  const endDateObj = new Date(endTimestamp);
  const endWeekday = weekdays[endDateObj.getDay()];
  const formattedEndTime = endTimestamp ? 
    `${endDateObj.getFullYear()}-${String(endDateObj.getMonth() + 1).padStart(2, '0')}-${String(endDateObj.getDate()).padStart(2, '0')} 星期${endWeekday} ${String(endDateObj.getHours()).padStart(2, '0')}:${String(endDateObj.getMinutes()).padStart(2, '0')}` :
    "";

  // 计算会议时长（分钟）
  const durationInMinutes = endTimestamp && timestamp 
    ? Math.round((endTimestamp - timestamp) / 60000) 
    : (meetingData.duration || 60); // 默认60分钟
  
  // 使用API返回的真实状态
  const status = meetingData.status || "MEETING_STATE_INIT";
  
  // 准备joinURL
  const joinURL = meetingData.join_url || "";
  
  return {
    id: meetingData.meeting_id || String(Math.random()),
    title: meetingData.subject || "未命名会议",
    startTime: formattedTime,
    endTime: formattedEndTime,
    meetingId: meetingData.meeting_code || meetingData.meeting_id || "000 000 000",
    status: status,
    duration: durationInMinutes,
    host: typeof meetingData.current_hosts?.[0] === 'object' 
      ? JSON.stringify(meetingData.current_hosts?.[0]) 
      : (meetingData.current_hosts?.[0] || meetingData.host_user_id || "未指定"),
    description: meetingData.meeting_description || meetingData.location || "暂无描述",
    joinURL: joinURL,
    joinUrl: joinURL, // 保留两个属性名以兼容不同调用
  };
} 


/**
 * 日期过滤器组件
 * @description 提供日期范围选择功能，用于过滤会议数据
 * 修改人：Miya，修改日期：2025/7/22
 * 原代码使用两个独立的Input组件分别选择开始和结束日期
 * 修改说明：重构为使用DateRangePicker组件，提供更好的用户体验
 */
export function DateFilter({
	startDate,
	endDate,
	handleStartDateChange,
	handleEndDateChange,
	disabled = false,
}: DateFilterProps & { disabled?: boolean }) {
	// 修改人：Miya，修改日期：2025/7/22
	// 新增功能：将字符串日期转换为 DateRange 格式
	// 说明：DateRangePicker组件需要dayjs对象数组作为value，而原有的startDate和endDate是字符串格式
	const dateRange: DateRange = startDate && endDate
		? [dayjs(startDate), dayjs(endDate)] // 将字符串转换为dayjs对象数组
		: null; // 如果没有日期则返回null

	// 修改人：Miya，修改日期：2025/7/22
	// 新增功能：处理日期范围变化的回调函数
	// 说明：DateRangePicker的onChange回调返回dayjs对象数组，需要转换为原有的事件格式
	const handleDateRangeChange = (dates: DateRange) => {
		if (dates?.[0] && dates?.[1]) {
			// 模拟原有的 onChange 事件格式，保持与原有接口的兼容性
			// 原有的handleStartDateChange和handleEndDateChange期望接收React.ChangeEvent<HTMLInputElement>格式
			const startEvent = {
				target: { value: dates[0].format('YYYY-MM-DD') } // 将dayjs对象格式化为YYYY-MM-DD字符串
			} as React.ChangeEvent<HTMLInputElement>;

			const endEvent = {
				target: { value: dates[1].format('YYYY-MM-DD') } // 将dayjs对象格式化为YYYY-MM-DD字符串
			} as React.ChangeEvent<HTMLInputElement>;

			// 调用原有的事件处理函数，保持组件接口不变
			handleStartDateChange(startEvent);
			handleEndDateChange(endEvent);
		}
	};

	return (
		<div className="flex items-center gap-4">
			<div className="flex items-center gap-2">
				{/* <div className="text-sm whitespace-nowrap mr-2 ml-2">日期范围:</div> */}
        {/* 修改人：Miya，修改日期：2025/7/22 */}
        {/* 原代码：两个独立的Input组件 */}
        {/* 原代码示例：
            <div className="text-sm">开始日期:</div>
            <Input type="date" value={startDate} onChange={handleStartDateChange} className="h-9 w-33 text-sm pl-2 pr-2 text-right" placeholder="2025/01/01" />
            <div className="text-sm">结束日期:</div>
            <Input type="date" value={endDate} onChange={handleEndDateChange} className="h-9 w-33 text-sm pl-2 pr-2 text-right" disabled={disabled} />
        */}
        {/* 修改说明：使用DateRangePicker替代两个独立的Input，提供更好的日期范围选择体验 */}
				<DateRangePicker
					value={dateRange}
					onChange={handleDateRangeChange}
					className="h-9 text-sm min-w-[320px] [&_.ant-picker]:rounded-lg [&_.ant-picker-input]:rounded-lg [&_.ant-picker-input>input]:min-w-[120px] [&_.ant-picker-range-separator]:whitespace-nowrap [&_.ant-picker-range-separator]:mx-2"
					disabled={disabled}
					placeholder={["开始日期", "结束日期"]}
					format="YYYY-MM-DD"
					showTime={false}
					showPresets={true}
					size="middle"
					allowClear={false}
					disablePastDates={true} // 禁用今天之前的日期
				/>
			</div>
		</div>
	);
}