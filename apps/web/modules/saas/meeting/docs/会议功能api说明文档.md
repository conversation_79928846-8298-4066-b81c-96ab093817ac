# 会议功能API说明文档

## 概述
本文档描述了会议路演功能的技术实施方案，包括API接口和安全加密方案。会议路演功能用于展示和管理企业所有会议，包括会议列表、录制回放、文档资料等信息，支持预约、取消及邀请用户激活账号等操作，完整复现腾讯会议的使用体验，满足企业会议管理和知识沉淀需求。

## 系统架构

```
项目根目录
├── packages/
│   ├── api/              # API接口模块
│   │   ├── src/
│   │   │   ├── routes/
│   │   │   │   ├── meeting/     # 会议管理API路由
│   │   │   │   │   ├── lib/
│   │   │   │   │   │   ├── router.ts          # 主路由文件
│   │   │   │   │   │   ├── config.ts          # 环境变量等参数配置
│   │   │   │   │   │   ├── meetinglist.ts     # 获取会议列表
│   │   │   │   │   │   ├── create.ts          # 创建会议
│   │   │   │   │   │   ├── cancel.ts          # 取消会议
│   │   │   │   │   │   ├── detail.ts          # 获取会议详情
│   │   │   │   │   │   ├── docs.ts            # 会议文档及用户文档
│   │   │   │   │   │   ├── recording.ts       # 会议录制详情信息
│   │   │   │   │   │   ├── participants.ts    # 参会人员导出
│   │   │   │   │   │   ├── update.ts          # 更新会议
│   │   │   │   │   │   ├── modify.ts          # 修改会议
│   │   │   │   │   │   ├── account.ts         # 账号管理
│   │   │   │   │   │   ├── invite.ts          # 邀请激活
│   │   │   ├── lib/
│   │   │   │   ├── meeting-sign.ts        # 请求拦截器 baseurl
```

## API接口概览

### 2.1 会议管理接口
- **GET** `/meetings/upcoming` - 获取即将开始的会议列表
- **POST** `/meetings/create` - 创建新会议
- **GET** `/meetings/history` - 获取历史会议列表
- **GET** `/meetings/details/:id` - 获取会议详情
- **POST** `/meetings/cancel/:id` - 取消会议
- **PUT** `/meetings/update/:meetingId` - 更新会议
- **PUT** `/meetings/modify/:meetingId` - 修改会议

### 2.2 会议文档接口
- **GET** `/meetings/docs/:meetingId` - 获取会议文档列表
- **GET** `/meetings/user/docs` - 获取用户文档列表

### 2.3 会议录制接口
- **GET** `/meetings/records/detail/:meetingId` - 获取会议录制详情
- **GET** `/meetings/sign-in/list/:meetingId` - 获取会议签到列表
- **POST** `/meetings/export-participants/:meetingId` - 导出参会成员列表

### 2.4 账号管理接口
- **GET** `/meetings/departments` - 获取部门列表
- **POST** `/meetings/departments` - 创建部门
- **GET** `/meetings/users` - 获取用户信息
- **POST** `/meetings/create/users` - 创建用户
- **PUT** `/meetings/users/:userid` - 更新用户
- **DELETE** `/meetings/users/:userid` - 删除用户

### 2.5 邀请激活接口
- **POST** `/meetings/invite-activate` - 获取用户激活链接
- **GET** `/meetings/invite-shareholders/:orgId` - 获取股东列表

## 接口详细说明

### 1. 获取即将开始的会议列表
**GET** `/meetings/upcoming`

**Query Parameters:**
- `userId?: string` - 用户ID，可选

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_number": number,
    "meeting_info_list": Array<{
      "meeting_id": string,
      "subject": string,
      "start_time": string,
      "end_time": string,
      "join_url": string
    }>,
    "next_pos": number,
    "remaining": number,
    "next_cursory": number
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/meetings`

### 2. 创建会议
**POST** `/meetings/create`

**Request:**
```json
{
  "subject": string,           // 会议主题
  "type": number,             // 会议类型：0-简单会议，1-复杂会议
  "start_time": string,       // 开始时间
  "end_time": string,         // 结束时间
  "userid"?: string,          // 用户ID
  "password"?: string,        // 会议密码
  "hosts"?: Array<{          // 主持人列表
    "userid": string,
    "is_anonymous"?: boolean
  }>,
  "settings"?: object,        // 会议设置
  "guests"?: Array<object>,   // 嘉宾列表
  "enable_live"?: boolean,    // 是否开启直播
  "time_zone"?: string,       // 时区
  "location"?: string         // 会议地点
}
```

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_id": string,
    "join_url": string
  },
  "error"?: string
}
```

**对应腾讯会议API:** `POST /v1/meetings`

### 3. 获取历史会议列表
**GET** `/meetings/history`

**Query Parameters:**
- `pageSize?: number` - 每页记录数，默认20
- `page?: number` - 页码，默认1
- `userId?: string` - 用户ID，可选
- `startTime?: string` - 开始时间，可选
- `endTime?: string` - 结束时间，可选

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_info_list": Array<{
      "meeting_id": string,
      "subject": string,
      "start_time": string,
      "end_time": string
    }>,
    "total_count": number,
    "current_size": number,
    "total_page": number
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/meetings`

### 4. 获取会议详情
**GET** `/meetings/details/:id`

**Path Parameters:**
- `id: string` - 会议ID

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_id": string,
    "subject": string,
    "start_time": string,
    "end_time": string,
    "join_url": string
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/meetings/{meetingId}`

### 5. 取消会议
**POST** `/meetings/cancel/:id`

**Path Parameters:**
- `id: string` - 会议ID

**Request:**
```json
{
  "reason_code"?: number,  // 取消原因代码，默认1
  "userId"?: string        // 用户ID
}
```

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_id": string
  },
  "error"?: string
}
```

**对应腾讯会议API:** `POST /v1/meetings/{meetingId}/cancel`
### 6. 更新会议
**PUT** `/meetings/update/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Request:**
```json
{
  "subject"?: string,
  "start_time"?: string,
  "end_time"?: string,
  "password"?: string,
  "settings"?: object
}
```

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `PUT /v1/meetings`

### 7. 修改会议
**PUT** `/meetings/modify/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Request:**
```json
{
  "subject": string,
  "start_time": string,
  "end_time": string,
  "userid"?: string,
  "type"?: number,
  "password"?: string,
  "hosts"?: Array<object>,
  "settings"?: object,
  "guests"?: Array<object>
}
```

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `PUT /v1/meetings/{meetingId}` 和 `PUT /v1/meetings/{meetingId}/enroll/config`

### 8. 获取会议文档列表
**GET** `/meetings/docs/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Query Parameters:**
- `userId?: string` - 用户ID

**Response:**
```json
{
  "success": boolean,
  "data": {
    "doc_info_list": Array<{
      "doc_id": string,
      "doc_name": string,
      "doc_type": string,
      "doc_size": number
    }>
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/meetings/{meetingId}/docs`

### 9. 获取用户文档列表
**GET** `/meetings/user/docs`

**Query Parameters:**
- `userId?: string` - 用户ID
- `pageSize?: number` - 每页记录数，默认20
- `page?: number` - 页码，默认1

**Response:**
```json
{
  "success": boolean,
  "data": {
    "meeting_info_list": Array<{
      "meeting_id": string,
      "doc_info_list": Array<{
        "doc_id": string,
        "doc_name": string
      }>
    }>,
    "total_count": number,
    "current_size": number,
    "total_page": number
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/docs`

### 10. 获取会议录制详情
**GET** `/meetings/records/detail/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Query Parameters:**
- `recordFileId?: string` - 录制文件ID，可选
- `userid?: string` - 用户ID
- `startTime?: string` - 开始时间

**Response:**
```json
{
  "success": boolean,
  "data": {
    "docx_download_address": string,
    "view_address": string,
    "txtContent": string,
    "aiSummaryContent": string
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/records` 和 `GET /v1/addresses/{fileId}`
### 11. 获取会议签到列表
**GET** `/meetings/sign-in/list/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Query Parameters:**
- `userId?: string` - 用户ID

**Response:**
```json
{
  "success": boolean,
  "data": {
    "sign_in_list": Array<object>
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/meetings/sign-in/list`

### 12. 导出参会成员列表
**POST** `/meetings/export-participants/:meetingId`

**Path Parameters:**
- `meetingId: string` - 会议ID

**Query Parameters:**
- `startTime?: string` - 开始时间

**Response:**
```json
{
  "success": boolean,
  "data": {
    "url": string
  },
  "sheetData": Array<object>
}
```

**对应腾讯会议API:** `POST /v1/meetings/export-participants-list` 和 `GET /v1/export/{jobId}`

### 13. 获取部门列表
**GET** `/meetings/departments`

**Response:**
```json
{
  "success": boolean,
  "data": {
    "total_count": number,
    "total_page": number,
    "current_page": number,
    "current_size": number,
    "department_list": Array<{
      "department_id": string,
      "department_name": string,
      "department_full_name": string,
      "is_main_department": boolean
    }>
  },
  "error"?: string
}
```

**对应腾讯会议API:** `GET /v1/departments`

### 14. 创建部门
**POST** `/meetings/departments`

**Request:**
```json
{
  "departmentName": string
}
```

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `POST /v1/departments`

### 15. 获取用户信息
**GET** `/meetings/users`

**Query Parameters:**
- `userId: string` - 用户ID（必填）

**Response:**
```json
{
  "success": boolean,
  "exists": boolean,
  "activated"?: boolean,
  "account_type"?: number
}
```

**对应腾讯会议API:** `GET /v1/users/{userId}`

### 16. 创建用户
**POST** `/meetings/create/users`

**Request:**
```json
{
  "username": string,           // 用户昵称（必填）
  "userid": string,            // 用户ID（必填）
  "phone"?: string,            // 手机号码
  "email"?: string,            // 邮箱地址
  "area"?: string,             // 地区编码，默认86
  "staff_id"?: string,         // 员工工号
  "job_title"?: string,        // 员工职位
  "entry_time"?: number,       // 入职时间
  "department_list"?: Array<string>, // 员工部门ID
  "auto_invite"?: boolean,     // 自动发送邀请，默认true
  "user_account_type"?: number // 账号类型
}
```

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `POST /v1/users`
### 17. 更新用户
**PUT** `/meetings/users/:userid`

**Path Parameters:**
- `userid: string` - 用户ID

**Request:**
```json
{
  "department_list": Array<string>
}
```

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `PUT /v1/users/{userid}`

### 18. 删除用户
**DELETE** `/meetings/users/:userid`

**Path Parameters:**
- `userid: string` - 用户ID

**Response:**
```json
{
  "success": boolean,
  "data": object,
  "error"?: string
}
```

**对应腾讯会议API:** `DELETE /v1/users/{userid}`

### 19. 获取用户激活链接
**POST** `/meetings/invite-activate`

**Request:**
```json
{
  "useridList": Array<string>
}
```

**Response:**
```json
{
  "success": boolean,
  "data": {
    "invite_activate_list": Array<{
      "userid": string,
      "invite_activate_url": string
    }>
  },
  "error"?: string
}
```

**对应腾讯会议API:** `POST /v1/users/invite-activate`

### 20. 获取股东列表
**GET** `/meetings/invite-shareholders/:orgId`

**Path Parameters:**
- `orgId: string` - 组织ID

**Query Parameters:**
- `page?: number` - 页码
- `pageSize?: number` - 每页条数
- `searchTerm?: string` - 搜索关键词

**Response:**
```json
{
  "success": boolean,
  "data": Array<object>,
  "pagination": {
    "page": number,
    "pageSize": number,
    "total": number,
    "totalPages": number,
    "hasNext": boolean,
    "hasPrev": boolean
  },
  "error"?: string
}
```

**对应腾讯会议API:** 内部数据库查询

## 错误处理
所有接口都采用统一的错误响应格式：
```json
{
  "success": false,
  "error": string
}
```

错误码都是接收腾讯会议API传回的信息，详情错误码可参考腾讯会议官方文档。

## 安全认证
所有接口都需要进行签名认证，认证信息包含在请求头中：
```json
{
  "X-TC-Key": string,        // 密钥ID
  "X-TC-Timestamp": number,  // 时间戳
  "X-TC-Nonce": number,      // 随机数
  "X-TC-Signature": string,  // 签名
  "AppId": string,           // 应用ID
  "SdkId": string            // SDK ID
}
```

### 签名生成流程
1. 获取请求参数
2. 构建签名字符串
3. 使用HMAC-SHA256计算签名
4. Base64编码

### 环境变量配置
```bash
# 腾讯会议API参数
TENCENT_APP_ID=""
TENCENT_SDK_ID=""
TENCENT_SECRET_ID=""
TENCENT_SECRET_KEY=""
TENCENT_OPERATOR_ID=""
```
## 技术实施详情
- 基于Hono框架实现RESTful API
- 所有会议功能统一封装在`lib/`目录中
- 按功能模块拆分路由逻辑
- 支持用户ID参数传递
- 统一错误处理和响应格式

### API实现架构
本项目后端基于 Hono 实现 RESTful API，并将所有会议功能统一封装在 lib/ 目录中，按功能模块拆分路由逻辑。

**各模块职责：**
- `router.ts` - 主路由入口，导入各功能模块并配置路径
- `config.ts` - 集中管理腾讯会议所需的环境变量和配置
- `meetinglist.ts` - 获取会议列表（即将开始/历史）
- `create.ts` - 创建会议接口
- `cancel.ts` - 取消会议接口
- `detail.ts` - 获取会议详情
- `docs.ts` - 会议文档与用户文档接口
- `recording.ts` - 会议录制与签到列表接口
- `participants.ts` - 参会人员导出接口
- `update.ts` - 更新会议接口
- `modify.ts` - 修改会议接口
- `account.ts` - 账号管理接口
- `invite.ts` - 邀请激活接口

### 签名认证实现
所有请求通过签名函数生成签名并附加至请求头，确保腾讯会议API调用的合法性。
## 开发注意事项
- 所有会议相关API路由统一组织在`/routes/meeting/`下
- 所有请求通过签名函数生成签名并附加至请求头
- 注意处理腾讯会议API的各类异常响应
- 返回统一格式的错误信息

## 维护与拓展
本功能在设计时注重灵活性和通用性，为后续扩展留足空间：
- 支持更多维度的会议查询与筛选（如按时间范围、会议状态）
- 提供会议导出与归档能力，便于历史记录保存与审计
- 逐步支持更多第三方字段或配置项，以适配腾讯会议接口变更

系统整体采用模块化设计，便于功能迭代与维护，具备较好的可扩展性与长期演进能力。

## 前端集成方案
前端通过调用统一封装的接口函数实现会议管理，包括创建、查询、取消会议等操作：
- 接口请求格式保持与腾讯会议官方文档一致，并支持传入自定义参数
- 所有会议相关功能组件统一封装在相应模块下，便于维护与复用