import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { validateFiles, parseFiles, ParsedFileResult } from '../utils/fileParser';
import { sendBatchParsedDataToAPI, formatApiError, ApiResponse } from '../utils/meetingFileService';

/**
 * 文件处理结果接口
 */
export interface FileProcessResult {
  fileName: string;
  success: boolean;
  parsed?: boolean;
  sent?: boolean;
  skipped?: boolean;
  reason?: string;
  error?: string;
  data?: any;
}

/**
 * 文件上传状态接口
 */
export interface FileUploadState {
  files: File[];
  isDragging: boolean;
  error: string | null;
  success: boolean;
  uploadProgress: number;
  isUploading: boolean;
  isParsing: boolean;
  parsedData: FileProcessResult[];
  currentParsingFile: string;
  apiResult: ApiResponse | null;
  processedFileCount: number;
  processingTimestamp: string;
  isRegenerating: boolean;
}

/**
 * 文件上传 Hook
 */
export function useFileUpload(meetingId: string, meetingTitle: string) {
  // 状态管理
  const [state, setState] = useState<FileUploadState>({
    files: [],
    isDragging: false,
    error: null,
    success: false,
    uploadProgress: 0,
    isUploading: false,
    isParsing: false,
    parsedData: [],
    currentParsingFile: "",
    apiResult: null,
    processedFileCount: 0,
    processingTimestamp: "",
    isRegenerating: false,
  });

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<FileUploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 重置状态
  const resetState = useCallback(() => {
    updateState({
      error: null,
      success: false,
      uploadProgress: 0,
      isUploading: false,
      isParsing: false,
      parsedData: [],
      currentParsingFile: "",
    });
  }, [updateState]);

  // 重置到初始上传状态
  const resetToUploadMode = useCallback(() => {
    setState({
      files: [],
      isDragging: false,
      error: null,
      success: false,
      uploadProgress: 0,
      isUploading: false,
      isParsing: false,
      parsedData: [],
      currentParsingFile: "",
      apiResult: null,
      processedFileCount: 0,
      processingTimestamp: "",
      isRegenerating: false,
    });
  }, []);

  // 清除文件
  const clearFiles = useCallback(() => {
    updateState({ files: [] });
    resetState();
  }, [updateState, resetState]);

  // 移除单个文件
  const removeFile = useCallback((index: number) => {
    updateState({
      files: state.files.filter((_, i) => i !== index),
      error: null,
    });
  }, [state.files, updateState]);

  // 设置拖拽状态
  const setDragging = useCallback((dragging: boolean) => {
    updateState({ isDragging: dragging });
  }, [updateState]);

  // 添加文件
  const addFiles = useCallback((newFiles: File[]) => {
    const { validFiles, errors } = validateFiles(newFiles);

    // 如果有错误，显示详细错误信息
    if (errors.length > 0) {
      const errorMessage = `文件选择失败：\n${errors.join('\n')}\n\n支持格式：.doc, .docx, .xls, .xlsx, .txt`;
      updateState({ error: errorMessage });
      toast.error(`${errors.length} 个文件不符合要求`);
    }

    if (validFiles.length === 0) {
      return;
    }

    // 如果当前有文件，则追加新文件而不是替换
    if (state.files.length > 0) {
      // 检查是否有重复文件（根据文件名和大小判断）
      const existingFileNames = state.files.map(f => `${f.name}-${f.size}`);
      const uniqueFiles = validFiles.filter(
        file => !existingFileNames.includes(`${file.name}-${file.size}`)
      );

      if (uniqueFiles.length === 0) {
        updateState({ error: "所选文件已在列表中" });
        return;
      }

      // 追加新文件
      updateState({
        files: [...state.files, ...uniqueFiles],
        error: null,
      });
    } else {
      // 重置状态并设置新文件
      resetState();
      updateState({ files: validFiles });
    }
  }, [state.files, updateState, resetState]);

  // 处理文件解析和批量数据发送
  const processFilesAfterUpload = useCallback(async () => {
    try {
      updateState({ isParsing: true });
      const results: FileProcessResult[] = [];
      let parsedFiles: ParsedFileResult[] = [];

      // 第一阶段：解析所有文件
      try {
        parsedFiles = await parseFiles(state.files, (fileName) => {
          updateState({ currentParsingFile: fileName });
        });

        // 收集解析成功的结果
        parsedFiles.forEach(parsed => {
          results.push({
            fileName: parsed.fileName,
            success: true,
            parsed: true,
          });
        });

      } catch (error) {
        console.error('文件解析失败:', error);
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        
        // 标记失败的文件
        state.files.forEach(file => {
          if (!results.find(r => r.fileName === file.name)) {
            results.push({
              fileName: file.name,
              success: false,
              error: errorMessage,
            });
          }
        });
      }

      // 第二阶段：批量发送解析结果
      if (parsedFiles.length > 0) {
        updateState({ currentParsingFile: "批量发送数据中..." });

        try {
          const batchData = {
            meetingId,
            meetingTitle,
            timestamp: new Date().toISOString(),
            files: parsedFiles,
          };

          const apiResponse = await sendBatchParsedDataToAPI(batchData);

          // 更新结果状态
          results.forEach(result => {
            if (result.parsed) {
              result.data = apiResponse;
              result.sent = true;
            }
          });

          // 设置API结果数据
          updateState({
            apiResult: apiResponse,
            processedFileCount: parsedFiles.length,
            processingTimestamp: batchData.timestamp,
          });

        } catch (error) {
          console.error('批量发送失败:', error);
          // 将所有已解析但发送失败的文件标记为失败
          results.forEach(result => {
            if (result.parsed) {
              result.success = false;
              result.error = `批量发送失败: ${error instanceof Error ? error.message : '未知错误'}`;
            }
          });
        }
      }

      updateState({
        parsedData: results,
        isParsing: false,
        currentParsingFile: "",
      });

      // 显示处理结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;
      const parsedCount = parsedFiles.length;

      if (failCount === 0) {
        if (parsedCount > 0) {
          toast.success(`成功分析 ${parsedCount} 个文件`);
          return true; // 成功处理
        } else {
          toast.success(`文件处理完成！`);
        }
      } else {
        toast.warning(`批量处理完成：成功 ${successCount} 个，失败 ${failCount} 个`);
        return false; // 有失败的情况
      }

    } catch (error) {
      updateState({
        isParsing: false,
        currentParsingFile: "",
      });
      console.error('批量文件处理过程中发生错误:', error);

      const errorMessage = formatApiError(error);
      toast.error(errorMessage);
      updateState({ error: errorMessage });
      return false;
    }
    
    return false;
  }, [state.files, meetingId, meetingTitle, updateState]);

  // 处理文件上传
  const handleUpload = useCallback(async () => {
    if (state.files.length === 0 || !meetingId) {
      updateState({ error: "请先选择要上传的文件" });
      return false;
    }

    try {
      updateState({
        error: null,
        isUploading: true,
        uploadProgress: 0,
      });

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setState(prev => {
          if (prev.uploadProgress >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return { ...prev, uploadProgress: prev.uploadProgress + 10 };
        });
      }, 200);

      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      updateState({
        uploadProgress: 100,
        isUploading: false,
      });

      toast.success(`成功解析 ${state.files.length} 个文件`);

      // 开始文件解析和数据发送
      const success = await processFilesAfterUpload();
      
      updateState({ success: true });
      return success;
      
    } catch (err: any) {
      console.error('文件上传过程中发生错误:', err);

      // 重置状态
      updateState({
        isUploading: false,
        isParsing: false,
        uploadProgress: 0,
        currentParsingFile: "",
      });

      const errorMessage = formatApiError(err);
      updateState({ error: errorMessage });
      toast.error(errorMessage);
      return false;
    }
  }, [state.files, meetingId, meetingTitle, updateState, processFilesAfterUpload]);

  // 重新生成AI内容
  const handleRegenerate = useCallback(async () => {
    if (state.files.length === 0 || !meetingId) {
      updateState({ error: "没有文件可以重新生成" });
      return false;
    }

    try {
      updateState({
        error: null,
        isRegenerating: true,
        isParsing: true,
        apiResult: null,
        success: false,
      });

      // 重新处理文件并生成AI内容
      const success = await processFilesAfterUpload();

      updateState({
        success: true,
        isRegenerating: false,
      });
      return success;

    } catch (err: any) {
      console.error('重新生成过程中发生错误:', err);

      // 重置状态
      updateState({
        isRegenerating: false,
        isParsing: false,
        currentParsingFile: "",
      });

      const errorMessage = formatApiError(err);
      updateState({ error: errorMessage });
      toast.error(errorMessage);
      return false;
    }
  }, [state.files, meetingId, meetingTitle, updateState, processFilesAfterUpload]);

  return {
    // 状态
    ...state,

    // 操作函数
    addFiles,
    removeFile,
    clearFiles,
    handleUpload,
    resetToUploadMode,
    resetState,
    setDragging,
    handleRegenerate,
  };
}
