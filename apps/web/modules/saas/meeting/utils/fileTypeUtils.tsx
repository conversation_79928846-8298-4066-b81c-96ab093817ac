import { 
  FileTextIcon, 
  FileTypeIcon 
} from "lucide-react";

/**
 * 文件类型信息接口
 */
export interface FileTypeInfo {
  icon: React.ReactNode;
  text: string;
}

/**
 * 获取文件类型信息
 */
export function getFileTypeInfo(fileName: string): FileTypeInfo {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  switch (extension) {
    case '.pdf':
      return { icon: <FileTextIcon className="h-4 w-4 text-red-500" />, text: 'PDF文档' };
    case '.doc':
    case '.docx':
      return { icon: <FileTextIcon className="h-4 w-4 text-blue-500" />, text: 'Word文档' };
    case '.xls':
    case '.xlsx':
      return { icon: <FileTextIcon className="h-4 w-4 text-green-500" />, text: 'Excel表格' };
    case '.ppt':
    case '.pptx':
      return { icon: <FileTextIcon className="h-4 w-4 text-orange-500" />, text: 'PowerPoint演示' };
    case '.txt':
      return { icon: <FileTextIcon className="h-4 w-4 text-gray-500" />, text: '文本文档' };
    default:
      return { icon: <FileTypeIcon className="h-4 w-4 text-gray-500" />, text: '文档' };
  }
}
