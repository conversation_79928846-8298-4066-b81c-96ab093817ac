import * as XLSX from 'xlsx';

/**
 * 嘉宾数据接口
 */
export interface GuestData {
  area: string;        // 国家/地区代码
  phone_number: string; // 手机号
  guest_name?: string;  // 嘉宾名称（可选）
}

/**
 * Excel解析结果接口
 */
export interface ExcelParseResult {
  success: boolean;
  data: GuestData[];
  errors: string[];
  totalRows: number;
  validRows: number;
  skippedRows: number;
}

/**
 * 检查是否为表头行
 * 通过检查第一列是否包含常见的表头关键词
 */
function isHeaderRow(row: any[]): boolean {
  if (!row || row.length === 0) return false;
  
  const firstCell = String(row[0] || '').toLowerCase().trim();
  const headerKeywords = [
    '国家', '地区', '代码', 'country', 'area', 'code',
    '手机', '电话', 'phone', 'mobile', 'tel',
    '姓名', '名称', 'name', '嘉宾'
  ];
  
  return headerKeywords.some(keyword => firstCell.includes(keyword));
}

/**
 * 检查是否为空行
 */
function isEmptyRow(row: any[]): boolean {
  if (!row || row.length === 0) return true;
  return row.every(cell => !cell || String(cell).trim() === '');
}

/**
 * 验证和清理国家代码
 */
function validateAndCleanAreaCode(value: any): string | null {
  if (!value) return null;
  
  const cleaned = String(value).trim();
  // 移除可能的前缀符号
  const areaCode = cleaned.replace(/^\+/, '').replace(/^00/, '');
  
  // 检查是否为纯数字
  if (!/^\d+$/.test(areaCode)) {
    return null;
  }
  
  // 常见的国家代码范围检查
  const code = parseInt(areaCode);
  if (code < 1 || code > 9999) {
    return null;
  }
  
  return areaCode;
}

/**
 * 清理嘉宾名称
 */
function cleanGuestName(value: any): string {
  if (!value) return '';
  return String(value).trim();
}

/**
 * 解析Excel文件中的嘉宾数据
 */
export async function parseGuestExcelFile(file: File): Promise<ExcelParseResult> {
  const result: ExcelParseResult = {
    success: false,
    data: [],
    errors: [],
    totalRows: 0,
    validRows: 0,
    skippedRows: 0,
  };

  try {
    // 读取文件
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0];
    if (!sheetName) {
      result.errors.push('Excel文件中没有找到工作表');
      return result;
    }
    
    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) {
      result.errors.push('无法读取工作表数据');
      return result;
    }
    
    // 将工作表转换为数组
    const rawData = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1, // 使用数组格式而不是对象格式
      defval: '', // 空单元格的默认值
    }) as any[][];
    
    if (!rawData || rawData.length === 0) {
      result.errors.push('Excel文件中没有数据');
      return result;
    }
    
    result.totalRows = rawData.length;
    
    // 查找数据开始行（跳过表头）
    let dataStartIndex = 0;
    for (let i = 0; i < Math.min(3, rawData.length); i++) {
      if (isHeaderRow(rawData[i])) {
        dataStartIndex = i + 1;
        break;
      }
    }
    
    // 如果没有找到明确的表头，从第一行开始，但如果第一行看起来像表头则跳过
    if (dataStartIndex === 0 && rawData.length > 0) {
      if (isHeaderRow(rawData[0])) {
        dataStartIndex = 1;
      }
    }
    
    // 解析数据行
    for (let i = dataStartIndex; i < rawData.length; i++) {
      const row = rawData[i];
      
      // 跳过空行
      if (isEmptyRow(row)) {
        result.skippedRows++;
        continue;
      }
      
      // 检查列数
      if (row.length < 2) {
        result.errors.push(`第${i + 1}行：数据列数不足，至少需要2列（国家代码、联系方式）`);
        result.skippedRows++;
        continue;
      }
      
      // 解析各列数据
      const areaCode = validateAndCleanAreaCode(row[0]);
      const guestName = row.length > 2 ? cleanGuestName(row[2]) : '';

      // 验证必填字段
      if (!areaCode) {
        result.errors.push(`第${i + 1}行：国家/地区代码无效 "${row[0]}"`);
        result.skippedRows++;
        continue;
      }

      // 获取联系方式（不进行格式验证，让UI层处理）
      const rawPhoneNumber = row[1] ? String(row[1]).trim() : '';

      // 如果联系方式为空，跳过该行
      if (!rawPhoneNumber) {
        result.errors.push(`第${i + 1}行：联系方式不能为空`);
        result.skippedRows++;
        continue;
      }

      // 添加数据（包括格式可能不正确的联系方式）
      result.data.push({
        area: areaCode,
        phone_number: rawPhoneNumber,
        guest_name: guestName || undefined,
      });

      result.validRows++;
    }
    
    // 检查是否有有效数据
    if (result.data.length === 0) {
      result.errors.push('没有找到有效的嘉宾数据');
      return result;
    }
    
    // 检查数据量限制
    if (result.data.length > 2000) {
      result.errors.push(`导入的嘉宾数量(${result.data.length})超过限制(2000人)`);
      return result;
    }
    
    result.success = true;
    return result;
    
  } catch (error: any) {
    result.errors.push(`文件解析失败: ${error.message || '未知错误'}`);
    return result;
  }
}

/**
 * 验证Excel文件格式
 */
export function validateExcelFileFormat(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
  ];
  
  const validExtensions = ['.xlsx', '.xls'];
  const fileName = file.name.toLowerCase();
  
  const isValidType = validTypes.includes(file.type) || 
                     validExtensions.some(ext => fileName.endsWith(ext));
  
  if (!isValidType) {
    return { valid: false, error: '请选择Excel文件（.xlsx 或 .xls 格式）' };
  }
  
  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: '文件大小不能超过10MB' };
  }
  
  if (file.size === 0) {
    return { valid: false, error: '文件为空' };
  }
  
  return { valid: true };
}
