


/**
 * 嘉宾数据验证工具函数
 * 用于判断嘉宾数据的有效性、空白状态等
 */

// 嘉宾数据接口（与现有接口保持一致）
export interface Guest {
  area: string;        // 国家/地区代码
  phone_number: string; // 手机号
  guest_name?: string;  // 嘉宾名称（可选）
}

/**
 * 检查嘉宾数据是否为有效嘉宾
 * 有效嘉宾定义：国家代码、联系方式和姓名都已填入且格式正确
 */
export function isValidGuest(guest: Guest): boolean {
  if (!guest){
    return false;
  }

  const area = guest.area?.trim();
  const phoneNumber = guest.phone_number?.trim();
  const guestName = guest.guest_name?.trim();

  // 基本检查：三个字段都不能为空
  if (!area || !phoneNumber || !guestName) {
    return false;
  }

  // 使用联系方式验证函数进行格式检查
  const validation = validateContactFormat(area, phoneNumber);
  return validation.valid;
}

/**
 * 检查嘉宾数据是否为空白行
 * 空白行定义：手机号为空或未填入（国家代码可能有默认值）
 * 修复：由于添加新行时会设置默认国家代码，所以主要检查手机号是否为空
 */
export function isEmptyGuest(guest: Guest): boolean {
  if (!guest) {
    return true;
  }

  const phoneNumber = guest.phone_number?.trim();

  // 主要检查手机号是否为空，因为国家代码可能有默认值
  return !phoneNumber;
}

/**
 * 检查嘉宾数据是否为部分填充
 * 部分填充定义：国家代码或手机号其中一个已填入，另一个为空
 */
export function isPartialGuest(guest: Guest): boolean {
  if (!guest) {
    return false;
  }
  
  const area = guest.area?.trim();
  const phoneNumber = guest.phone_number?.trim();
  
  return (!area && !!phoneNumber) || (!!area && !phoneNumber);
}

/**
 * 统计嘉宾列表中的有效嘉宾数量
 */
export function countValidGuests(guests: Guest[]): number {
  if (!Array.isArray(guests)) {
    return 0;
  }
    
  
  return guests.filter(isValidGuest).length;
}

/**
 * 统计嘉宾列表中的空白行数量
 */
export function countEmptyGuests(guests: Guest[]): number {
  if (!Array.isArray(guests)) {
    return 0;
  }
  
  return guests.filter(isEmptyGuest).length;
}

/**
 * 查找第一个空白行的索引
 * 返回 -1 表示没有找到空白行
 */
export function findFirstEmptyGuestIndex(guests: Guest[]): number {
  if (!Array.isArray(guests)) {return -1;}
  
  return guests.findIndex(isEmptyGuest);
}

/**
 * 查找所有空白行的索引
 */
export function findAllEmptyGuestIndexes(guests: Guest[]): number[] {
  if (!Array.isArray(guests)) {return [];}
  
  const indexes: number[] = [];
  guests.forEach((guest, index) => {
    if (isEmptyGuest(guest)) {
      indexes.push(index);
    }
  });
  
  return indexes;
}

/**
 * 检查是否可以添加新嘉宾
 * 规则：只有当前所有行都是有效嘉宾时才允许添加新行
 */
export function canAddNewGuest(guests: Guest[]): boolean {
  if (!Array.isArray(guests)) {return true;}
  
  // 如果没有嘉宾，可以添加
  if (guests.length === 0) {return true;}
  
  // 如果存在空白行或部分填充行，不允许添加新行
  return guests.every(guest => isValidGuest(guest));
}

/**
 * 智能填充嘉宾数据结果接口
 */
export interface SmartFillResult {
  guests: Guest[];              // 最终的嘉宾列表
  duplicateInfo: {              // 重复参会成员信息
    count: number;              // 重复参会成员数量
    details: Array<{            // 重复参会成员详细信息
      guest: Guest;
      reason: string;
    }>;
  };
}

/**
 * 智能填充嘉宾数据（带重复检测）
 * 优先填充空白行，如果没有空白行则追加新行
 * 自动检测并跳过重复参会成员
 */
export function smartFillGuests(currentGuests: Guest[], newGuests: Guest[]): Guest[] {
  const result = smartFillGuestsWithDuplicateDetection(currentGuests, newGuests);
  return result.guests;
}

/**
 * 智能填充嘉宾数据（带重复检测和详细信息）
 * 优先填充空白行，如果没有空白行则追加新行
 * 自动检测并跳过重复参会成员，返回详细的重复信息
 */
export function smartFillGuestsWithDuplicateDetection(
  currentGuests: Guest[],
  newGuests: Guest[]
): SmartFillResult {
  const result: SmartFillResult = {
    guests: [],
    duplicateInfo: {
      count: 0,
      details: []
    }
  };

  // if (!Array.isArray(currentGuests)){
  //   currentGuests = [];
  // } 
  if (!Array.isArray(newGuests) || newGuests.length === 0) {
    result.guests = [...currentGuests];
    return result;
  }

  // 检测重复参会成员
  const duplicateDetection = detectDuplicateGuests(currentGuests, newGuests);

  // 设置重复信息
  result.duplicateInfo.count = duplicateDetection.duplicates.length;
  result.duplicateInfo.details = duplicateDetection.duplicateDetails.map(detail => ({
    guest: detail.guest,
    reason: detail.reason
  }));

  // 使用去重后的嘉宾进行填充
  const uniqueNewGuests = duplicateDetection.unique;
  const guestList = [...currentGuests];
  let newGuestIndex = 0;

  // 首先填充现有的空白行
  for (let i = 0; i < guestList.length && newGuestIndex < uniqueNewGuests.length; i++) {
    if (isEmptyGuest(guestList[i])) {
      guestList[i] = { ...uniqueNewGuests[newGuestIndex] };
      newGuestIndex++;
    }
  }

  // 如果还有新嘉宾需要添加，则追加到末尾
  while (newGuestIndex < uniqueNewGuests.length) {
    guestList.push({ ...uniqueNewGuests[newGuestIndex] });
    newGuestIndex++;
  }

  result.guests = guestList;
  return result;
}

/**
 * 清理嘉宾列表，移除末尾的空白行
 * 保留至少一个空白行用于用户输入
 */
export function cleanupGuestList(guests: Guest[]): Guest[] {
  if (!Array.isArray(guests)) {return [];}

  // 如果列表为空，返回一个空白行
  if (guests.length === 0) {
    return [{ area: "86", phone_number: "", guest_name: "" }];
  }

  // 从末尾开始移除连续的空白行，但保留至少一个
  let lastValidIndex = guests.length - 1;

  // 找到最后一个非空白行的位置
  while (lastValidIndex >= 0 && isEmptyGuest(guests[lastValidIndex])) {
    lastValidIndex--;
  }

  // 保留到最后一个有效行 + 1个空白行
  const result = guests.slice(0, lastValidIndex + 2);

  // 确保至少有一个空白行
  if (result.length === 0 || !isEmptyGuest(result[result.length - 1])) {
    result.push({ area: "86", phone_number: "", guest_name: "" });
  }

  return result;
}

/**
 * 批量验证嘉宾列表的联系方式
 * 返回验证错误映射表，用于UI显示
 */
export function validateGuestListContacts(guests: Guest[]): Record<number, string> {
  const validationErrors: Record<number, string> = {};

  if (!Array.isArray(guests)) {return validationErrors;}

  guests.forEach((guest, index) => {
    if (!guest || !guest.phone_number?.trim()) {
      // 空的联系方式不显示错误
      return;
    }

    const validation = validateContactFormat(guest.area || "86", guest.phone_number);
    if (!validation.valid && validation.error) {
      validationErrors[index] = validation.error;
    }
  });

  return validationErrors;
}

/**
 * 验证联系方式格式
 * 支持手机号和座机号，兼容不同地区格式
 */
export function validateContactFormat(areaCode: string, phoneNumber: string): { valid: boolean; error?: string } {
  if (!phoneNumber || !phoneNumber.trim()) {
    return { valid: false, error: "请输入联系方式" };
  }

  const cleanPhone = phoneNumber.trim();

  // 根据不同地区代码验证格式
  switch (areaCode) {
    case "86": // 中国大陆
      return validateChinaPhone(cleanPhone);
    case "1": // 美国/加拿大
      return validateNorthAmericaPhone(cleanPhone);
    case "44": // 英国
      return validateUKPhone(cleanPhone);
    case "81": // 日本
      return validateJapanPhone(cleanPhone);
    case "82": // 韩国
      return validateKoreaPhone(cleanPhone);
    case "65": // 新加坡
      return validateSingaporePhone(cleanPhone);
    case "852": // 香港
      return validateHongKongPhone(cleanPhone);
    case "853": // 澳门
      return validateMacauPhone(cleanPhone);
    case "886": // 台湾
      return validateTaiwanPhone(cleanPhone);
    default:
      // 通用验证：5-20位数字，可包含连字符和空格
      return validateGenericPhone(cleanPhone);
  }
}

/**
 * 验证中国大陆手机号码（仅支持11位手机号）
 */
function validateChinaPhone(phone: string): { valid: boolean; error?: string } {
  // 移除所有非数字字符进行验证
  const phoneForValidation = phone.replace(/[^\d]/g, "");

  // 手机号：11位数字
  if (!/^\d{11}$/.test(phoneForValidation)) {
    return { valid: false, error: "请输入正确的11位手机号（如：13812345678）" };
  }

  // 验证手机号段（1开头的11位数字）
  if (!/^1[3-9]\d{9}$/.test(phoneForValidation)) {
    return { valid: false, error: "请输入正确的11位手机号（如：13812345678）" };
  }

  return { valid: true };
}

/**
 * 验证北美电话号码（美国/加拿大）
 */
function validateNorthAmericaPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-\(\)\.]/g, '');

  // 10位数字，格式如：(************* 或 ************
  if (/^\d{10}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（如：************）" };
}

/**
 * 验证英国电话号码
 */
function validateUKPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 英国电话号码：10-11位数字
  if (/^\d{10,11}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（10-11位数字）" };
}

/**
 * 验证日本电话号码
 */
function validateJapanPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 日本电话号码：10-11位数字
  if (/^\d{10,11}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（10-11位数字）" };
}

/**
 * 验证韩国电话号码
 */
function validateKoreaPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 韩国电话号码：9-11位数字
  if (/^\d{9,11}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（9-11位数字）" };
}

/**
 * 验证新加坡电话号码
 */
function validateSingaporePhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 新加坡电话号码：8位数字
  if (/^\d{8}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（8位数字）" };
}

/**
 * 验证香港电话号码
 */
function validateHongKongPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 香港电话号码：8位数字
  if (/^\d{8}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（8位数字）" };
}

/**
 * 验证澳门电话号码
 */
function validateMacauPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 澳门电话号码：8位数字
  if (/^\d{8}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（8位数字）" };
}

/**
 * 验证台湾电话号码
 */
function validateTaiwanPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-]/g, '');

  // 台湾电话号码：9-10位数字
  if (/^\d{9,10}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（9-10位数字）" };
}

/**
 * 通用电话号码验证（用于其他地区）
 */
function validateGenericPhone(phone: string): { valid: boolean; error?: string } {
  const phoneForValidation = phone.replace(/[\s\-\(\)\.]/g, '');

  // 通用格式：5-20位数字
  if (/^\d{5,20}$/.test(phoneForValidation)) {
    return { valid: true };
  }

  return { valid: false, error: "请输入正确的电话号码（5-20位数字）" };
}

/**
 * 生成嘉宾的唯一标识符
 * 基于姓名和联系方式的组合生成唯一标识
 */
export function generateGuestIdentifier(guest: Guest): string {
  if (!guest) {return '';}

  const guestName = guest.guest_name?.trim() || '';
  const phoneNumber = guest.phone_number?.trim() || '';

  // 如果姓名或联系方式为空，返回空字符串（表示无法生成有效标识符）
  if (!guestName || !phoneNumber) {return '';}

  // 使用姓名和联系方式的组合作为唯一标识符
  // 联系方式需要标准化处理，移除空格和特殊字符
  const normalizedPhone = phoneNumber.replace(/[\s\-\(\)]/g, '');

  return `${guestName}:${normalizedPhone}`;
}

/**
 * 检测重复参会成员结果接口
 */
export interface DuplicateDetectionResult {
  duplicates: Guest[];           // 重复的嘉宾列表
  unique: Guest[];              // 去重后的嘉宾列表
  duplicateDetails: Array<{     // 重复参会成员的详细信息
    guest: Guest;
    identifier: string;
    reason: string;
  }>;
}

/**
 * 检测嘉宾列表中的重复项
 * 基于姓名+联系方式的组合进行重复检测
 */
export function detectDuplicateGuests(
  existingGuests: Guest[],
  newGuests: Guest[]
): DuplicateDetectionResult {
  const result: DuplicateDetectionResult = {
    duplicates: [],
    unique: [],
    duplicateDetails: []
  };

  // if (!Array.isArray(existingGuests)) {
  //   existingGuests = [];
  // }
  if (!Array.isArray(newGuests) || newGuests.length === 0) {
    return result;
  }

  // 构建现有嘉宾的标识符映射表，提高查找性能
  const existingIdentifiers = new Set<string>();

  // 只考虑有效的现有嘉宾（有姓名和联系方式的）
  existingGuests.forEach(guest => {
    const identifier = generateGuestIdentifier(guest);
    if (identifier) {
      existingIdentifiers.add(identifier);
    }
  });

  // 检测新嘉宾中的重复项
  const processedIdentifiers = new Set<string>();

  newGuests.forEach(guest => {
    const identifier = generateGuestIdentifier(guest);

    // 如果无法生成有效标识符（姓名或联系方式为空），直接添加到unique列表
    if (!identifier) {
      result.unique.push(guest);
      return;
    }

    // 检查是否与现有嘉宾重复
    if (existingIdentifiers.has(identifier)) {
      result.duplicates.push(guest);
      result.duplicateDetails.push({
        guest,
        identifier,
        reason: `与现有嘉宾重复：${guest.guest_name} (${guest.phone_number})`
      });
      return;
    }

    // 检查是否在当前批次中重复
    if (processedIdentifiers.has(identifier)) {
      result.duplicates.push(guest);
      result.duplicateDetails.push({
        guest,
        identifier,
        reason: `在当前导入中重复：${guest.guest_name} (${guest.phone_number})`
      });
      return;
    }

    // 添加到唯一列表和已处理标识符集合
    result.unique.push(guest);
    processedIdentifiers.add(identifier);
  });

  return result;
}

/**
 * 验证嘉宾数据的完整性
 * 返回验证结果和错误信息
 */
export function validateGuestData(guest: Guest): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!guest) {
    errors.push("嘉宾数据不能为空");
    return { valid: false, errors };
  }

  const area = guest.area?.trim();
  const phoneNumber = guest.phone_number?.trim();
  const guestName = guest.guest_name?.trim();

  if (!area) {
    errors.push("请选择国家/地区代码");
  } else if (!/^\d+$/.test(area)) {
    errors.push("国家/地区代码必须为数字");
  }

  if (!phoneNumber) {
    errors.push("请输入联系方式");
  } else if (area) {
    // 使用新的联系方式验证函数
    const contactValidation = validateContactFormat(area, phoneNumber);
    if (!contactValidation.valid && contactValidation.error) {
      errors.push(contactValidation.error);
    }
  }

  if (!guestName) {
    errors.push("请输入嘉宾姓名");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
