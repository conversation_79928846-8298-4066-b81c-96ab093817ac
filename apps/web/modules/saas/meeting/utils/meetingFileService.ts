import type { ParsedFileResult } from './fileParser';

/**
 * 批量数据接口
 */
export interface BatchDataRequest {
  meetingId: string;
  meetingTitle: string;
  timestamp: string;
  files: ParsedFileResult[];
}

/**
 * API响应接口
 */
export interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
  originalResponse?: any;
}

/**
 * 批量数据发送API调用函数
 */
export async function sendBatchParsedDataToAPI(batchData: BatchDataRequest): Promise<ApiResponse> {
  try {
    // 使用n8n会议代理路由调用真实的webhook端点
    const apiEndpoint = '/api/n8n_meeting_proxy/ai-pre-meeting-overview';

    // 直接使用batchData作为请求体，保持现有的批量数据结构
    const requestData = {
      meetingData:{
        meetingId: batchData.meetingId,
        meetingTitle: batchData.meetingTitle,
        timestamp: batchData.timestamp,
        files: batchData.files,
        totalFiles: batchData.files.length,
      }
    };

    // console.log('批量发送到后端的数据:', requestData);

    // 发送真实的HTTP请求到n8n webhook
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API调用失败:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`批量API调用失败: ${response.status} ${response.statusText}`);
    }

    // 解析响应数据
    const responseData = await response.json();
    console.log('API真实返回值:', responseData);

    // 根据n8n代理的响应格式处理返回值
    if (responseData.code && responseData.code >= 200 && responseData.code < 300) {
      // 成功响应，返回标准化格式
      return {
        success: true,
        message: `批量文件解析数据发送成功，共处理 ${batchData.files.length} 个文件`,
        data: responseData.data || responseData,
        originalResponse: responseData
      };
    } 
    // 错误响应
    throw new Error(`API返回错误: ${responseData.message || '未知错误'}`);
    

  } catch (error) {
    console.error('批量API调用过程中发生错误:', error);

    // 根据错误类型提供更具体的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('网络连接失败，请检查网络连接');
    } 
    if (error instanceof Error) {
      throw new Error(`批量API调用失败: ${error.message}`);
    } 
    throw new Error('批量API调用失败: 未知错误');
    
  }
}

/**
 * 提取API返回数据中的output字段
 */
export function extractOutputData(apiResult: any): any {
  try {
    // 尝试从嵌套结构中提取output: apiResult.data.data.output
    if (apiResult?.data?.data?.output) {
      return apiResult.data.data.output;
    }
    // 如果没有嵌套结构，尝试直接从data中获取output
    if (apiResult?.data?.output) {
      return apiResult.data.output;
    }
    // 如果都没有，返回原始data
    return apiResult?.data || apiResult;
  } catch (error) {
    console.warn('提取output数据时出错:', error);
    return apiResult?.data || apiResult;
  }
}

/**
 * 格式化API错误信息
 */
export function formatApiError(error: unknown): string {
  if (error instanceof Error) {
    if (error.message.includes('网络')) {
      return '网络连接失败，批量发送中断，请检查网络后重试';
    } 
    if (error.message.includes('解析')) {
      return '文件解析过程中出现错误，请检查文件格式';
    }
    if (error.message.includes('批量API')) {
      return '服务器批量处理失败，请稍后重试';
    } 
    return `批量处理失败: ${error.message}`;
    
  }
  return '批量文件处理失败，请重试';
}
