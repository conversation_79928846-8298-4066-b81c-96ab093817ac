/**
 * 星标图标组件
 *
 * @fileoverview 收藏投资人功能的星标图标组件，结合人物和星形元素的设计
 * <AUTHOR>
 * @since 2025-01-21
 * @version 1.0.0
 *
 * 功能特性：
 * - 自定义样式类名支持
 * - 响应式设计（1em尺寸）
 * - 可访问性支持
 * - 人物+星形组合图标
 * - 与其他图标一致的视觉风格
 */

import * as React from "react";

/**
 * 星标图标组件属性接口
 */
interface StarIconProps {
  /** 额外的样式类名 */
  className?: string;
}

/**
 * 星标图标组件
 *
 * 用于展示"收藏投资人"功能的图标，结合人物轮廓和星形元素
 *
 * @param props - 组件属性
 * @param props.className - 额外的样式类名
 * @returns 星标图标组件
 *
 * @example
 * ```tsx
 * <StarIcon className="w-5 h-5 text-blue-500" />
 * ```
 */
export function StarIcon({ className = "" }: StarIconProps) {
  return (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      aria-label="星链投资人"
      width="1em"
      height="1em"
    >
      <title>星链投资人</title>
      {/* 圆形头部 - 人物部分使用当前颜色，粗细与其他图标一致 */}
      <circle cx="11" cy="8" r="4" strokeWidth="2.2" />
      {/* 半圆形身体 - 人物部分使用当前颜色，粗细与其他图标一致 */}
      <path d="M19 21v-1.5a4 4 0 0 0-4-4H8a4 4 0 0 0-3.5 4v5" strokeWidth="2.2" />
      {/* 右下角的大星星 - 标准五角星整体左移2.5个单位，上移1.1个单位，放大1.25倍，内部空白适应主题 */}
      <path
        className="fill-white dark:fill-black stroke-current"
        d="M18.3 12.7l1.86 3.72 4.092 0.62-2.976 2.852 0.744 3.968-3.72-1.984-3.72 1.984 0.744-3.968-2.976-2.852 4.092-0.62L18.3 12.7z"
        strokeWidth="1.9" //1.75
      />
    </svg>
  );
}
