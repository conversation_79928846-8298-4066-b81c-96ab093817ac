"use client";

import { DropdownMenuSub } from "@radix-ui/react-dropdown-menu";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserAvatar } from "@shared/components/UserAvatar";
import { clearCache } from "@shared/lib/cache";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuPortal,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	BookIcon,
	HardDriveIcon,
	HomeIcon,
	LogOutIcon,
	MessageSquareIcon,
	MoonIcon,
	//MoreVerticalIcon,
	SettingsIcon,
	SunIcon,
	UserCogIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import Link from "next/link";
import { useState } from "react";

export function UserMenu({ showUserName }: { showUserName?: boolean }) {
	const t = useTranslations();
	const { user } = useSession();
	const { activeOrganization, isOrganizationAdmin } = useActiveOrganization();
	const { setTheme: setCurrentTheme, theme: currentTheme } = useTheme();
	const [theme, setTheme] = useState<string>(currentTheme ?? "system");

	const colorModeOptions = [
		{
			value: "system",
			label: "自动",
			icon: HardDriveIcon,
		},
		{
			value: "light",
			label: "亮",
			icon: SunIcon,
		},
		{
			value: "dark",
			label: "暗",
			icon: MoonIcon,
		},
	];

	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					window.location.href = new URL(
						config.auth.redirectAfterLogout,
						window.location.origin,
					).toString();
				},
			},
		});
	};

	if (!user) {
		return null;
	}

	const { name, email, image } = user;

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				<button
					type="button"
					className="flex cursor-pointer w-full items-center justify-between gap-2 rounded-lg outline-hidden focus-visible:ring-2 focus-visible:ring-primary md:w-[100%+1rem] md:px-2 md:py-1.5 md:hover:bg-primary/5"
					aria-label="User menu"
					data-testid="user-menu-button"
				>
					<span className="flex items-center gap-2">
						<UserAvatar name={name ?? ""} avatarUrl={image} />
						{showUserName && (
							<span className="text-left leading-tight">
								<span className="font-medium text-sm">
									{name}
								</span>
								<span className="block text-xs opacity-70">
									{email}
								</span>
							</span>
						)}
					</span>

					{/* 
					 * 原代码：
					 * {showUserName && <MoreVerticalIcon className="size-4" />}
					 * 修改原因: 隐藏用户菜单中的三点图标
					 * 修改时间: 2025-04-17
					 */}

				</button>
			</DropdownMenuTrigger>

			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{name}
					<span className="block font-normal text-xs opacity-70">
						{email}
					</span>
				</DropdownMenuLabel>

				<DropdownMenuSeparator />

				{/* Color mode selection */}
				<DropdownMenuSub>
					<DropdownMenuSubTrigger>
						<SunIcon className="mr-2 size-4" />
						{t("app.userMenu.colorMode")}
					</DropdownMenuSubTrigger>
					<DropdownMenuPortal>
						<DropdownMenuSubContent>
							<DropdownMenuRadioGroup
								value={theme}
								onValueChange={(value) => {
									setTheme(value);
									setCurrentTheme(value);
								}}
							>
								{colorModeOptions.map((option) => (
									<DropdownMenuRadioItem
										key={option.value}
										value={option.value}
									>
										<option.icon className="mr-2 size-4 opacity-50" />
										{option.label}
									</DropdownMenuRadioItem>
								))}
							</DropdownMenuRadioGroup>
						</DropdownMenuSubContent>
					</DropdownMenuPortal>
				</DropdownMenuSub>

				<DropdownMenuSeparator />

				<DropdownMenuItem asChild>
					<Link href="/app/settings/general">
						<SettingsIcon className="mr-2 size-4" />
						{t("app.userMenu.accountSettings")}
					</Link>
				</DropdownMenuItem>

				{/* 组织设置选项 - 仅对有权限的用户显示 */}
				{activeOrganization && isOrganizationAdmin && (
					<DropdownMenuItem asChild>
						<Link href={`/app/${activeOrganization.slug}/settings/general`}>
							<SettingsIcon className="mr-2 size-4" />
							{t("app.menu.organizationSettings")}
						</Link>
					</DropdownMenuItem>
				)}

				{/* 管理后台选项 - 仅对组织页面的系统管理员显示 */}
				{activeOrganization && user?.role === "admin" && (
					<DropdownMenuItem asChild>
						<Link href="/app/admin">
							<UserCogIcon className="mr-2 size-4" />
							{t("app.menu.admin")}
						</Link>
					</DropdownMenuItem>
				)}

				<DropdownMenuItem asChild>
					<a href="https://starlinkcap.feishu.cn/wiki/DMj7wlH3IiNOhzkyVmEcBsorntg?from=from_copylink" target="_blank" rel="noopener noreferrer">
						<BookIcon className="mr-2 size-4" />
						使用文档
					</a>
				</DropdownMenuItem>

				<DropdownMenuItem asChild>
					<a href="https://starlinkcap.feishu.cn/share/base/form/shrcnTHWyu2QNbMajhLJSPnWldb?prefill_%E9%9C%80%E6%B1%82=%E5%8F%8D%E9%A6%88%E4%B8%8E%E5%BB%BA%E8%AE%AE" target="_blank" rel="noopener noreferrer">
						<MessageSquareIcon className="mr-2 size-4" />
						反馈与建议
					</a>
				</DropdownMenuItem>

				 <DropdownMenuItem asChild>
				   <Link href="/">
				     <HomeIcon className="mr-2 size-4" />
				     {t("app.userMenu.home")}
				   </Link>
				 </DropdownMenuItem>

				<DropdownMenuItem onClick={onLogout}>
					<LogOutIcon className="mr-2 size-4" />
					{t("app.userMenu.logout")}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
