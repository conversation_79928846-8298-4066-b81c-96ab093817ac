"use client";
import { cn } from "@ui/lib";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";

/**
 * 空数据状态组件类型定义
 * <AUTHOR>
 * @version 1.2.0 (2025-06-26 11:02:31) - 支持三种状态：搜索无结果、无数据、加载中
 * @version 1.1.0 (2025-05-19) - 优化视觉效果，添加图标和更好的布局
 * @version 1.0.0 - 初始版本
 */
export interface EmptyStateProps {
  /** 搜索关键词，存在时显示搜索无结果状态 */
  searchTerm?: string;
  /** 显示文本内容 */
  text: string;
  /** 状态类型：search-搜索无结果，empty-无数据，loading-加载中 */
  type?: "search" | "empty" | "loading";
  /** 自定义高度，默认500px */
  height?: string | number;
  /** 自定义类名 */
  className?: string;
}

/**
 * 空数据状态组件
 * 在表格中没有数据时显示友好的提示信息，支持三种状态
 *
 * @param props - 组件属性
 * @returns JSX.Element
 *
 * <AUTHOR>
 * @version 1.2.0 (2025-06-26 11:02:31) - 改造支持三种情况：搜索无结果、无数据、加载中
 *
 * 修改记录：
 * - 2025-06-26 11:02:31: 重构组件支持三种状态，添加图标和更好的视觉效果
 * - 2025-05-19: 优化视觉效果，添加图标和更好的布局
 */
export function EmptyState({
  searchTerm,
  text,
  type,
  height = "500px",
  className
}: EmptyStateProps): JSX.Element {
  // 使用系统缩放hook进行样式适配
  const { scale } = useSystemScale();

  // 根据参数自动判断状态类型
  const stateType = type || (searchTerm ? "search" : "empty");

  // 获取对应状态的图标和样式
  const getStateConfig = () => {
    switch (stateType) {
      case "search":
        return {
          iconColor: "text-slate-400",
          title: "没有找到匹配的股东",
          description: searchTerm ? "" : undefined ,//暂时不需要搜索关键字展示
        };
      case "loading":
        return {
          iconColor: "text-slate-400 animate-pulse",
          title: text || "数据加载中...",
          description: undefined
        };
      default:
        return {
          iconColor: "text-slate-400",
          title: text || "暂无数据",
          description: undefined
        };
    }
  };

  const config = getStateConfig();

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-12 text-center px-4",
        className
      )}
      style={{ height: typeof height === "number" ? `${height}px` : height }}
    >

      {/* 主要文本 */}
      <p
        className={cn(
          "font-medium text-slate-700 mb-2",
          scale > 1.25 ? "text-sm" : "text-base"
        )}
      >
        {config.title}
      </p>

      {/* 描述文本 */}
      {config.description && (
        <p
          className={cn(
            "text-slate-500",
            scale > 1.25 ? "text-xs" : "text-sm"
          )}
        >
          {config.description}
        </p>
      )}
    </div>
  );
}