"use client";

import {
	CompanyOverview,
	StructureTrend,
	ShareholdingChanges,
	AISummary,
	CompanyOverviewSkeleton,
	StructureTrendSkeleton,
	ShareholdingChangesSkeleton,
	AISummarySkeleton,
} from "@saas/shareholder/components/analyse";
import { useOrganization } from "@saas/organizations/hooks/useOrganization";

/**
 * 股东分析组件属性接口
 * @interface ShareholderAnalyseProps
 */
interface ShareholderAnalyseProps {
	/** 组织slug */
	organizationSlug: string;
}

/**
 * 股东分析组件，包含公司概况、结构趋势、持股变动和AI总结等子组件
 * @description 股东分析组件，包含公司概况、结构趋势、持股变动和AI总结等子组件
 * <AUTHOR>
 * @time 2025-06-16 11:38:47
 * @modified 2025-06-16 11:38:47 - 添加组织ID传递功能，支持真实数据获取
 * @modified 2025-06-16 17:26:26 - 添加StructureTrendSkeleton骨架屏组件，在加载时显示
 * @modified 2025-06-17 12:20:47 - 添加ShareholdingChangesSkeleton骨架屏组件，替换简单的"加载中..."文本，提供更好的用户体验
 * @param props 组件属性
 * @returns JSX.Element
 */
export function ShareholderAnalyse({ organizationSlug }: ShareholderAnalyseProps): JSX.Element {
	// 获取组织信息
	const { data: organization } = useOrganization(organizationSlug);

	return (
		<div className="flex flex-col gap-8">
			{/* 如果没有组织信息，显示骨架屏 */}
			{!organization ? (
				<CompanyOverviewSkeleton />
			) : (
				<CompanyOverview organizationId={organization.id} />
			)}

			{/* AI分析总结组件 */}
			{organization ? (
				<AISummary organizationId={organization.id} />
			) : (
				<AISummarySkeleton />
			)}

			{/* 结构趋势组件 */}
			{organization ? (
				<StructureTrend organizationId={organization.id} />
			) : (
				<StructureTrendSkeleton />
			)}
			 {/* 持股变动组件 */}
			{organization ? (
				<ShareholdingChanges organizationId={organization.id} />
			) : (
				<ShareholdingChangesSkeleton />
			)} 
			
		</div>
	);
}