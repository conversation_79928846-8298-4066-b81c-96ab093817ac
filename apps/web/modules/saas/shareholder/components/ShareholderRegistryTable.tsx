"use client";

import { Trash2Icon, FileTextIcon, XIcon } from "lucide-react";
import {
  Button
} from "@ui/components/button";
import { TablePagination } from "@saas/shareholder/components/TablePagination";
import type { ShareholderRegistryItem, PaginationInfo } from "@saas/shareholder/lib/types";
import { formatDate, formatNumber } from "@saas/shareholder/lib/utils";
import { cn } from "@ui/lib";
import React, { useState, useCallback, useEffect, useMemo, useRef } from "react";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { AntShareholderTable } from "@saas/shareholder/components/ant-shareholder-table";
import type { ShareholderColumn, ShareholderRowSelection } from "@saas/shareholder/components/ant-shareholder-table";
import { EmptyState } from "@saas/shareholder/components";

// 删除状态枚举
type DeleteState = "normal" | "confirm";

// 名册类型枚举
type RegistryType = "c01" | "c05" | "t1" | "t2" | "t3" | "unknown";

// 验证错误类型
interface ValidationError {
  hasError: boolean;
  message?: string;
}

/**
 * 根据文件名判断名册类型（支持多个文件名用换行符分隔）
 * @param fileName 文件名（可能包含多个文件，用换行符分隔）
 * @returns 名册类型数组
 */
function getRegistryTypes(fileName: string): RegistryType[] {
  // 分割文件名（支持换行符分隔的多个文件）
  const fileNames = fileName.split('\n').filter(Boolean);
  const types: RegistryType[] = [];

  fileNames.forEach(name => {
    const lowerFileName = name.toLowerCase();

    if (lowerFileName.includes("c01")) {
      types.push("c01");
    } else if (lowerFileName.includes("c05")) {
      types.push("c05");
    } else if (lowerFileName.includes("t1")) {
      types.push("t1");
    } else if (lowerFileName.includes("t2")) {
      types.push("t2");
    } else if (lowerFileName.includes("t3")) {
      types.push("t3");
    } else {
      types.push("unknown");
    }
  });

  return types;
}

/**
 * 验证同一期的名册是否满足规则
 * @param registriesInPeriod 同一期的所有名册
 * @returns 验证结果，包含每个名册的错误信息
 */
function validateRegistriesInPeriod(registriesInPeriod: ShareholderRegistryItem[]): Map<string, ValidationError> {
  const validationResults = new Map<string, ValidationError>();

  // 统计各类型名册的数量
  const typeCount = {
    c01: 0,
    c05: 0,
    t1: 0,
    t2: 0,
    t3: 0,
    unknown: 0
  };

  // 记录每种类型的名册ID
  const typeRegistries = {
    c01: [] as string[],
    c05: [] as string[],
    t1: [] as string[],
    t2: [] as string[],
    t3: [] as string[],
    unknown: [] as string[]
  };

  // 统计各类型名册
  registriesInPeriod.forEach(registry => {
    const types = getRegistryTypes(registry.fileName);
    types.forEach(type => {
      if (type !== "unknown") {
        typeCount[type]++;
        typeRegistries[type].push(registry.id);
      }
    });
  });

  // 检查是否满足验证规则
  const hasC01AndC05 = typeCount.c01 > 0 && typeCount.c05 > 0;
  const hasT1OrT2 = typeCount.t1 > 0 || typeCount.t2 > 0;
  const hasT3 = typeCount.t3 > 0;
  const hasT1OrT2AndT3 = hasT1OrT2 && hasT3;

  const isValid = hasC01AndC05 || hasT1OrT2AndT3;

  // 如果验证失败，为所有名册添加错误信息
  if (!isValid) {
    const errorMessage = generateErrorMessage(typeCount);

    registriesInPeriod.forEach(registry => {
      validationResults.set(registry.id, {
        hasError: true,
        message: errorMessage
      });
    });
  } else {
    // 验证通过，所有名册都没有错误
    registriesInPeriod.forEach(registry => {
      validationResults.set(registry.id, {
        hasError: false
      });
    });
  }

  return validationResults;
}

/**
 * 生成错误信息
 * @param typeCount 各类型名册数量统计
 * @returns 错误信息字符串
 */
function generateErrorMessage(typeCount: Record<string, number>): string {
  const hasC01 = typeCount.c01 > 0;
  const hasC05 = typeCount.c05 > 0;
  const hasT1 = typeCount.t1 > 0;
  const hasT2 = typeCount.t2 > 0;
  const hasT3 = typeCount.t3 > 0;

  if (!hasC01 && !hasC05 && !hasT1 && !hasT2 && !hasT3) {
    return "缺少必要的名册文件";
  }

  if (hasC01 && !hasC05) {
    return "数据缺失，请继续上传c05名册";
  }

  if (hasC05 && !hasC01) {
    return "数据缺失，请继续上传c01名册";
  }

  if ((hasT1 || hasT2) && !hasT3) {
    return "数据缺失，请继续上传t3名册";
  }

  if (hasT3 && !hasT1 && !hasT2) {
    return "数据缺失，请继续上传t1或t2名册";
  }

  return "名册组合不符合规则";
}

// 为表格数据定义一个自定义类型，继承自组件所需的类型
interface RegistryTableItem {
  id: string;
  period: string;
  totalShares: string;
  accountCount: string;
  registry: React.ReactNode;
  importDate: string;
  user: string;
  // selected属性不再需要，将由AntShareholderTable内部管理
  originalData: ShareholderRegistryItem;
  // 添加ShareholderItem所需的必要属性
  name: string;
  shares: number | string;
  // 添加验证错误信息
  validationError?: ValidationError;
}

interface ShareholderRegistryTableProps {
  registries: ShareholderRegistryItem[];
  pagination?: PaginationInfo;
  isLoading?: boolean;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  // 原代码保留于2025-06-17 15:07:40 - onLimitChange?: (limit: number) => void; // 已移除，不再需要
  onDeleteRegistry: (registry: ShareholderRegistryItem) => void;
  selectedItems: string[];
  onSelectItem: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  disableSelection?: boolean;
}



/**
 * 直接显示文件名组件，不使用Tooltip
 * @version 1.1.0 (2025-07-17) - 添加验证错误信息显示支持
 * @version 1.0.1 (2025-06-13) - 添加暗色主题支持
 */
function RegistryFileDisplay({
  fileName,
  validationError
}: {
  fileName: string;
  validationError?: ValidationError;
}) {
  // 使用系统缩放hook进行样式适配
  const { scale, styles } = useSystemScale();

  // 检查fileName是否包含多个文件（通过换行符分隔）
  const fileNames = fileName.split('\n').filter(Boolean);

  return (
			<div className="flex flex-col items-center cursor-default w-full">
				{fileNames.map((name, index) => (
					<div
						key={index}
						className={cn(
							"flex items-center justify-center",
							"text-foreground", // 使用主题颜色变量
							styles.fontSize.content,
							index > 0 && "mt-1",
							"w-full",
						)}
					>
						<FileTextIcon
							className={cn(
								"flex-shrink-0 mr-1.5",
								scale > 1.25 ? "h-2.5 w-2.5" : "h-3 w-3",
							)}
						/>
						<span className="text-center break-all">{name}</span>
					</div>
				))}
				{/* 显示验证错误信息 */}
				{validationError?.hasError && (
					<div className="mt-2 w-full">
						<div
							className={cn(
								"flex items-center justify-center text-foreground text-sm w-full",
								"text-red-500",
								"text-center break-words",
							)}
						>
              
							{validationError.message}
						</div>
					</div>
				)}
			</div>
		);
}



/**
 * 股东名册表格组件
 * 显示已上传的股东名册列表，支持选择、分页、详情查看和删除操作
 *
 * <AUTHOR>
 * @version 6.0.0 (2025-06-17 15:07:40) - 移除footer参数，使用div包裹TablePagination并设置98vh高度，解决表格高度不足问题
 * @version 5.0.0 (2025-06-13 10:46:51) - 添加滚动加载分页功能，优化用户体验，支持表格滚动到底部自动加载更多数据
 * @version 4.0.2 (2025-05-22) - 优化空数据展示逻辑，移除冗余的条件渲染
 * @version 4.0.1 (2025-05-22) - 优化加载状态显示，确保表格尺寸一致性
 * @version 4.0.0 (2025-05-22) - 重构为使用通用的AntShareholderTable组件，保持功能不变
 * @version 3.3.0 (2025-05-22) - 添加控股股东和前十大股东数据展示，添加Tooltip交互
 * @version 3.2.0 (2025-05-19) - 添加系统缩放适配，优化在高DPI显示器上的视觉体验
 * @version 3.1.0 (2025-05-19) - 添加公司详细信息字段展示，包括总股本、总户数等
 * @version 3.0.0 (2025-05-19) - 改进跨分辨率显示支持，优化滚动体验，增加期数列宽度
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function ShareholderRegistryTable({
  registries,
  pagination,
  page,
  onPageChange,
  onDeleteRegistry,
  selectedItems,
  onSelectItem,
  onSelectAll,
  disableSelection = false,
  isLoading = false
}: ShareholderRegistryTableProps) {
  // 使用系统缩放hook获取缩放比例和样式配置
  const { scale, styles } = useSystemScale();

  // 验证名册数据
  const validRegistries = Array.isArray(registries) ? registries : [];

  // 原代码保留于2025-06-17 15:07:40 - 全选状态计算（已移除，不再需要）
  // const allSelected = validRegistries.length > 0 && validRegistries.every(reg => selectedItems.includes(reg.id));

  // 滚动相关状态 - 添加于2025年06月13日10:46:51
  const [isScrollLoading, setIsScrollLoading] = useState(false);
  const [accumulatedData, setAccumulatedData] = useState<ShareholderRegistryItem[]>([]);
  const scrollRef = useRef({
    left: 0,
    top: 0,
  });
  const tableScrollRef = useRef<HTMLDivElement>(null); // 表格滚动容器引用

  // 添加删除确认状态 - 保存处于"删除确认"状态的记录ID和阶段
  const [deletingStatus, setDeletingStatus] = useState<{ id: string | null; state: DeleteState }>({
    id: null,
    state: "normal"
  });

  // 清除删除确认状态的定时器ID
  const [deleteConfirmTimer, setDeleteConfirmTimer] = useState<NodeJS.Timeout | null>(null);

  // 当数据变化时，根据页码决定是替换还是累积 - 添加于2025年06月13日10:46:51
  useEffect(() => {
    if (page === 1) {
      // 第一页数据，直接替换
      setAccumulatedData(validRegistries);
    } else {
      // 后续页数据，进行累积（去重）
      setAccumulatedData((prevData) => {
        const existingIds = new Set(prevData.map((item) => item.id));
        const newItems = validRegistries.filter((item) => !existingIds.has(item.id));
        return [...prevData, ...newItems];
      });
    }
  }, [validRegistries, page]);

  // 原代码保留于2025-06-17 15:07:40 - 当搜索或排序变化时，重置累积数据、分页状态和滚动位置（已移除，不再需要重置分页标志）
  useEffect(() => {
    setAccumulatedData(validRegistries); // 重置累积数据为当前数据

    // 重置滚动位置到顶部
    if (tableScrollRef.current) {
      tableScrollRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // 重置滚动记录
    scrollRef.current = { left: 0, top: 0 };
  }, [selectedItems.length]); // 依赖于选择状态变化，可以根据需要调整

  // 处理删除按钮点击
  const handleDeleteClick = useCallback((registry: ShareholderRegistryItem) => {
    // 如果当前点击的是已经处在删除流程中的记录
    if (deletingStatus.id === registry.id) {
      // 如果当前是确认阶段，执行删除操作
      onDeleteRegistry(registry);
      
      // 清除定时器
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
        setDeleteConfirmTimer(null);
      }
      
      // 重置删除状态
      setDeletingStatus({
        id: null,
        state: "normal"
      });
    } else {
      // 如果是新的记录，进入确认阶段
      // 先清除之前的定时器
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
      }
      
      // 设置为确认阶段
      setDeletingStatus({
        id: registry.id,
        state: "confirm"
      });
      
      // 设置3秒后自动取消确认状态的定时器
      const timerId = setTimeout(() => {
        setDeletingStatus({
          id: null,
          state: "normal"
        });
      }, 3000);
      
      setDeleteConfirmTimer(timerId);
    }
  }, [deletingStatus, deleteConfirmTimer, onDeleteRegistry]);
  
  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (deleteConfirmTimer) {
        clearTimeout(deleteConfirmTimer);
      }
    };
  }, [deleteConfirmTimer]);
  
  // 格式化时间
  const formatDateTime = (dateString: string) => {
    try {
      // 使用formatDate函数，设置为full格式，保证显示为"2025-05-20 13:55"的格式
      return formatDate(dateString, { 
        format: 'full',
        locale: 'zh-CN'
      }).replace(/(\d{4})\/(\d{2})\/(\d{2})/, '$1-$2-$3').replace(/:\d{2}$/, ''); // 替换格式并移除秒数
    } catch (e) {
      return dateString;
    }
  };

  // 格式化金额数字（例如：10,000,000）
  const formatShareAmount = (amount: string | undefined) => {
    if (!amount) {
      return "-";
    }
    
    try {
      // 处理科学计数法表示的数字
      const num = Number.parseFloat(amount);
      // 修改：使用formatNumber函数时设置decimals为0，不显示小数点
      return formatNumber(num, { decimals: 0 });
    } catch (e) {
      return amount;
    }
  };
  
  // 处理表格滚动事件 - 添加于2025年06月13日10:46:51
  const handleTableScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 记录滚动容器引用
    if (!tableScrollRef.current && event.target) {
      tableScrollRef.current = event.target as HTMLDivElement;
    }

    // 计算是否还有更多数据可以加载
    const totalPages = pagination?.totalPages || 1;
    const hasMoreData = page < totalPages;

    // 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
    if (
      Math.abs(scrollTop - scrollRef.current.top) > 0 &&
      scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
      hasMoreData &&
      !isScrollLoading &&
      !isLoading
    ) {
      // 设置滚动加载状态
      setIsScrollLoading(true);

      // 调用页面变更函数，加载下一页
      onPageChange(page + 1);

      // 延迟重置加载状态，避免频繁触发
      setTimeout(() => {
        setIsScrollLoading(false);
      }, 500);
    }

    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  }, [pagination?.totalPages, page, isScrollLoading, isLoading, onPageChange]);

  // 将注册表数据转换为AntShareholderTable需要的格式 - 修改于2025年06月13日10:46:51，使用累积数据
  const tableData = useMemo(() => {
    // 按期数分组进行验证
    const registriesByPeriod = new Map<string, ShareholderRegistryItem[]>();
    accumulatedData.forEach(registry => {
      const period = registry.registerDate;
      if (!registriesByPeriod.has(period)) {
        registriesByPeriod.set(period, []);
      }
      const periodRegistries = registriesByPeriod.get(period);
      if (periodRegistries) {
        periodRegistries.push(registry);
      }
    });

    // 对每个期数进行验证
    const allValidationResults = new Map<string, ValidationError>();
    registriesByPeriod.forEach((registriesInPeriod) => {
      const validationResults = validateRegistriesInPeriod(registriesInPeriod);
      validationResults.forEach((result, registryId) => {
        allValidationResults.set(registryId, result);
      });
    });

    return accumulatedData.map(registry => {
      const validationError = allValidationResults.get(registry.id);

      return {
        id: registry.id,
        // ShareholderItem所需的必要属性
        name: registry.companyDetail?.companyName || registry.fileName || "名册",
        shares: registry.companyDetail?.totalShares || "0",
        // 自定义属性
        period: registry.registerDate,
        totalShares: registry.companyDetail ? formatShareAmount(registry.companyDetail.totalShares) : "-",
        accountCount: registry.companyDetail ? registry.companyDetail.totalShareholders.toLocaleString() : "-",
        registry: <RegistryFileDisplay fileName={registry.fileName} validationError={validationError} />,
        importDate: formatDateTime(registry.uploadedAt),
        user: registry.userName || "未知用户",
        originalData: registry,
        validationError,
      } as RegistryTableItem;
    });
  }, [accumulatedData, formatDateTime]);
  
  // 处理表格行选择变化
  const handleSelectionChange = useCallback((selectedRowIds: string[]) => {
    // 如果是全选操作
    if (selectedRowIds.length === validRegistries.length && validRegistries.length > 0) {
      onSelectAll(true);
      return;
    }
    
    // 如果是全不选操作
    if (selectedRowIds.length === 0 && selectedItems.length > 0) {
      onSelectAll(false);
      return;
    }
    
    // 对于新选中的项目，调用onSelectItem
    selectedRowIds.forEach(id => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id, true);
      }
    });
    
    // 对于取消选中的项目，调用onSelectItem
    selectedItems.forEach(id => {
      if (!selectedRowIds.includes(id)) {
        onSelectItem(id, false);
      }
    });
  }, [selectedItems, onSelectItem, onSelectAll, validRegistries.length]);
  
  // 创建行选择配置
  const rowSelection: ShareholderRowSelection = useMemo(() => {
    return {
      selectedRowKeys: selectedItems,
      onChange: handleSelectionChange,
      getCheckboxProps: () => ({
        disabled: disableSelection
      })
    };
  }, [selectedItems, handleSelectionChange, disableSelection]);
  
  // 定义列配置
  const columns: ShareholderColumn[] = useMemo(() => {
    // 创建一个通用的单元格类名 - 移除硬编码的文本颜色
				const cellClass = "text-center cursor-default";
    
    return [
					{
						key: "period",
						title: "期数",
						width: 100,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "totalShares",
						title: "总股数",
						width: 80,
						className: "text-center",
						render: (value: string) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "accountCount",
						title: "总户数",
						width: 80,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "registry",
						title: "名册",
						width: 250,
						className: "text-center",
						render: (value) => value,
					},
					{
						key: "importDate",
						title: "导入日期",
						width: 170,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "user",
						title: "上传用户",
						width: 120,
						className: "text-center",
						render: (value) => (
							<span className={cellClass}>{value}</span>
						),
					},
					{
						key: "action",
						title: "操作",
						width: 60,
						className: "text-center",
						render: (_, record) => (
							<div className="flex justify-center items-center">
                <Button
									variant="ghost"
									size="icon"
									className={cn(
                    "rounded-md hover:bg-transparent",
                    deletingStatus.id === record.id
                      ? "text-red-600"
                      : "text-slate-500 hover:text-red-600",
										scale > 1.25 ? "h-5 w-5" : "h-6 w-6",
									)}
									onClick={() =>
										handleDeleteClick(record.originalData)
									}
								>
									{deletingStatus.id === record.id ? (
										<Trash2Icon
											className={cn(
												scale > 1.25
													? "h-3 w-3"
													: "h-3.5 w-3.5",
											)}
										/>
									) : (
										<XIcon
											className={cn(
												scale > 1.25
													? "h-3 w-3"
													: "h-3.5 w-3.5",
											)}
										/>
									)}
								</Button>
							</div>
						),
					},
				];
  }, [scale, deletingStatus, handleDeleteClick]);
  
  // 定义加载状态的配置
  const loadingConfig = useMemo(() => {
    return {
      spinning: isLoading,
      size: "large" as const,
    };
  }, [isLoading]);

  // 设置滚动配置 - 修改于2025年06月17日15:25:33，添加智能垂直滚动逻辑
  // 原代码保留作为注释：
  // const scrollConfig = useMemo(() => {
  //   return {
  //     x: "max-content", // 启用水平滚动
  //     y: "calc(98vh - 300px)", // 固定高度，与ShareholderTable保持一致
  //     scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
  //   };
  // }, []);
  const scrollConfig = useMemo(() => {
    // 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
    const shouldEnableYScroll = tableData.length > 10;

    return {
      x: "max-content", // 启用水平滚动
      y: shouldEnableYScroll ? "calc(98vh - 300px)" : undefined, // 根据数据量决定是否启用垂直滚动
      scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
    };
  }, [tableData.length]);
  
  // 移除冗余的条件渲染，直接使用 AntShareholderTable 进行渲染
		// AntShareholderTable 组件会根据数据是否为空自动显示 emptyContent
		// 修改于2025年06月17日15:07:40，移除footer参数，使用div包裹TablePagination并设置98vh高度
		return (
			<>
				<div
					className={cn(
						"rounded-md",
						"h-[calc(96vh-200px)]",
						"flex",
						"flex-col",
					)}
				>
					{/* 表格容器，设置flex-1确保占满剩余空间 */}
					<div className="flex-1 overflow-hidden">
						<AntShareholderTable
							data={tableData}
							columns={columns}
							loading={loadingConfig}
							className="w-full h-full"
							headerClassName="sticky top-0 z-10 font-medium"
							cellClassName={styles.fontSize.content}
							emptyContent={<EmptyState
								text={`${loadingConfig.spinning ? "" : "请上传股东名册"}`}
								type={loadingConfig.spinning ? "loading" : "empty"}
							/>}
							rowSelection={rowSelection}
							onScroll={handleTableScroll}
							scroll={scrollConfig}
						/>
					</div>
					{/* 分页组件固定在底部 */}
					<div className="flex-shrink-0">
            {/* 当数据为空时不显示分页组件 */}
						{!tableData.length|| (
						<TablePagination
							pagination={
								pagination || {
									total: 0,
									page: 1,
									limit: 10,
									totalPages: 1,
								}
							}
							page={page}
						/>
						)}
					</div>
				</div>
			</>
		);
} 