/**
 * 股东名册分析 - 核心摘要组件
 * @file AISummary.tsx
 * @description 用于展示AI生成的股东结构智能分析总结，使用Markdown格式渲染
 * <AUTHOR>
 * @created 2025-06-12 18:54:57
 * @updated 2025-06-16 17:53:20
 * @modified 2025-06-18 13:52:14 - 集成公司总体报告API，支持真实数据获取和控制台日志输出
 * @modified 2025-06-18 15:23:13 - 优化重新生成时使用骨架屏，移除未使用的isFetching，禁用生成中的按钮
 * @modified 2025-06-18 16:29:03 - 在标题上添加API返回的时间戳显示功能
 * @modified 2025-08-07 14:01:54 - 为ReactMarkdown容器添加滚动条和开关功能，防止内容过多撑开界面
 */

import { useState, useEffect, useRef } from "react";
import { SparklesIcon, RefreshCcw, Loader2, Copy, Check, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@ui/components/button";
import { toast } from "sonner";
import ReactMarkdown from "react-markdown";
import { useCompanyGeneralReport } from "@saas/shareholder/hooks/useCompanyGeneralReport";
import { AISummarySkeleton } from "@saas/shareholder/components/analyse/AISummarySkeleton";

// 修改记录 2025-06-18 14:06:44 hayden: 移除模拟数据，改为显示暂无数据或使用骨架屏
// 原模拟数据已删除，不再使用模拟数据

/**
 * 核心摘要组件属性接口
 * @interface AISummaryProps
 */
interface AISummaryProps {
	/** 组织ID，可选参数，当提供时会获取真实的AI报告数据 */
	organizationId?: string;
}

/**
 * 核心摘要组件
 * @param props 组件属性
 * @returns 核心摘要UI组件
 */
export function AISummary({ organizationId }: AISummaryProps): JSX.Element {
	// 获取公司总体报告数据
	// 修改记录 2025-06-18 15:23:13 hayden: 移除未使用的isFetching变量
	const {
		data: reportData,
		isLoading,
		// isFetching, // 原代码保留作为注释: 未使用的变量已移除
		error,
		refetch
	} = useCompanyGeneralReport(organizationId || "", {
		enabled: !!organizationId, // 只有当有组织ID时才启用查询
	});

	// 刷新加载状态
	const [isRefreshing, setIsRefreshing] = useState(false);

	// 复制状态
	const [isCopied, setIsCopied] = useState(false);

	// 滚动条开关状态
	// 修改记录 2025-08-07 14:01:54 hayden: 添加滚动条开关状态，默认关闭
	// 修改记录 2025-08-07 14:15:54 hayden: 修改为根据内容长度自动判断是否开启滚动条
	const [isScrollEnabled, setIsScrollEnabled] = useState(false);
	const [isManualControl, setIsManualControl] = useState(false); // 是否为手动控制

	// 定时器引用，用于清理内存泄漏
	// 修改记录 2025-08-12 miya: 添加定时器引用，防止内存泄漏
	const copyTimerRef = useRef<NodeJS.Timeout | null>(null);

	/**
	 * 处理刷新核心摘要
	 * 重新生成核心摘要报告
	 */
	const handleRefresh = async () => {
		setIsRefreshing(true);
		try {
			if (organizationId) {
				// 如果有组织ID，调用API重新获取数据
				await refetch();

			} else {
				// 如果没有组织ID，使用模拟延迟
				await new Promise(resolve => setTimeout(resolve, 2000));

			}
		} catch (error) {
			// console.error('刷新核心摘要失败:', error);
		} finally {
			setIsRefreshing(false);
		}
	};

	/**
	 * 处理切换滚动条开关
	 * 切换ReactMarkdown容器的滚动条显示状态
	 * <AUTHOR>
	 * @created 2025-08-07 14:01:54
	 * @modified 2025-08-07 14:15:54 - 添加手动控制标记，防止自动判断覆盖手动设置
	 */
	const handleToggleScroll = () => {
		setIsScrollEnabled(!isScrollEnabled);
		setIsManualControl(true); // 标记为手动控制
	};

	/**
	 * 处理复制核心摘要内容
	 * 将所有核心纪要内容复制到剪贴板
	 * 修改记录 2025-08-12 miya: 添加定时器清理逻辑，避免内存泄漏
	 */
	const handleCopy = async () => {
		try {
			// 获取要复制的内容
			let contentToCopy = "暂无核心摘要内容";

			if (organizationId && reportData?.data && Array.isArray(reportData.data) && reportData.data.length > 0) {
				const firstItem = reportData.data[0];
				const reportOutput = firstItem?.data?.output || "";
				if (reportOutput) {
					contentToCopy = reportOutput;
				}
			}

			// 复制到剪贴板
			await navigator.clipboard.writeText(contentToCopy);

			// 设置复制成功状态
			setIsCopied(true);
			toast.success("核心摘要已复制到剪贴板");

			// 清除之前的定时器
			if (copyTimerRef.current) {
				clearTimeout(copyTimerRef.current);
			}

			// 2秒后重置复制状态
			copyTimerRef.current = setTimeout(() => {
				setIsCopied(false);
				copyTimerRef.current = null;
			}, 2000);
		} catch (error) {
			console.error('复制失败:', error);
			toast.error("复制失败，请手动选择文本复制");
		}
	};

	// 获取时间戳用于标题显示
	// 修改记录 2025-06-18 16:29:03 hayden: 提取时间戳用于标题显示
	const getTimestampForDisplay = (): string => {
		if (organizationId && reportData?.timestamp) {
			const timestamp = new Date(reportData.timestamp);
			return timestamp.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			});
		}
		return "";
	};

	// 确定要显示的内容
	// 修改记录 2025-06-18 16:40:12 hayden: 在内容底部添加AI分析仅供参考的标识
	// 修改记录 2025-06-18 16:43:07 hayden: 优化参考标识显示，移除内容中的参考标识，改为组件底部单独显示
	const displayContent = (() => {
		if (organizationId) {
			// 有组织ID时，使用API数据
			if (error) {
				return `# 加载失败\n\n无法获取核心摘要数据，请稍后重试。\n\n错误信息：${error.message}`;
			}

			// 修改记录 2025-06-18 13:59:50 hayden: 修复数据结构解析，API返回的是数组格式
			// 原代码保留作为注释: if (reportData?.data?.output) { return reportData.data.output; }
			if (reportData?.data && Array.isArray(reportData.data) && reportData.data.length > 0) {
				const firstItem = reportData.data[0];
				const reportOutput = firstItem?.data?.output || "";

				if (reportOutput) {
					// 修改记录 2025-06-18 16:43:07 hayden: 移除内容中的参考标识，改为组件底部单独显示
					return reportOutput;
				}
			}

			return "# 暂无数据";
		}

		// 没有组织ID时，显示暂无数据
		return "# 暂无数据";
	})();

	// 修改记录 2025-08-07 14:15:54 hayden: 根据内容长度自动判断是否需要滚动条
	// 修改记录 2025-08-07 14:57:18 hayden: 修复React Hook规则违反问题，将useEffect移动到条件返回之前
	useEffect(() => {
		if (!isManualControl) {
			// 只有在非手动控制状态下才自动判断
			const contentLength = displayContent.length;
			const lineCount = displayContent.split('\n').length;

			// 判断条件：内容长度超过1000字符或行数超过20行时开启滚动条
			const shouldEnableScroll = contentLength > 1000 || lineCount > 20;
			setIsScrollEnabled(shouldEnableScroll);
		}
	}, [displayContent, isManualControl]);

	// 组件卸载时清理定时器
	// 修改记录 2025-08-12 miya: 添加定时器清理逻辑，防止内存泄漏
	useEffect(() => {
		return () => {
			if (copyTimerRef.current) {
				clearTimeout(copyTimerRef.current);
			}
		};
	}, []);

	// 修改记录 2025-06-18 15:23:13 hayden: 优化加载状态，包括重新生成时也显示骨架屏
	// 当正在加载或重新生成时，显示骨架屏
	if (isLoading || isRefreshing) {
		return <AISummarySkeleton />;
	}

	return (
		<div className="w-full space-y-2">
			{/* 修改记录 2025-06-16 17:53:20 hayden: 修复标题和刷新按钮的水平对齐问题，移除h2的margin样式 */}
			{/* 修改记录 2025-06-18 16:29:03 hayden: 在标题上添加API返回的时间戳显示 */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-2">
					<SparklesIcon className="size-5 text-primary" />
					<h2 className="text-lg font-semibold text-foreground">
						核心摘要
					</h2>
					{/* 修改记录 2025-06-18 15:23:13 hayden: 在生成中时禁用生成按钮，防止重复点击 */}
				</div>
				{/* 按钮和更新时间在同一行，右对齐 */}
				<div className="flex items-center gap-3">
					{/* 修改记录 2025-06-18 16:29:03 hayden: 显示API返回的时间戳 */}
					{getTimestampForDisplay() && (
						// {* 修改记录 2025-08-12 miya: 调整距离 *}
						<div className="text-xs text-muted-foreground mr-1.5">
							更新时间: {getTimestampForDisplay()}
						</div>
					)}
					{/* 修改记录 2025-08-07 14:01:54 hayden: 添加滚动条开关按钮 */}
					<Button
						variant="ghost"
						size="sm"
						className="h-6 w-6 p-0 hover:bg-muted/50"
						title={isScrollEnabled ? "关闭滚动条" : "开启滚动条"}
						aria-label={isScrollEnabled ? "关闭滚动条" : "开启滚动条"}
						onClick={handleToggleScroll}
						disabled={isRefreshing || Boolean(organizationId && isLoading)}
					>
						{isScrollEnabled ? (
							<ChevronUp className="size-4 text-primary" />
						) : (
							<ChevronDown className="size-4 text-muted-foreground hover:text-foreground" />
						)}
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className="h-6 w-6 p-0 hover:bg-muted/50"
						title="复制核心摘要内容"
						aria-label={isCopied ? "核心摘要已复制" : "复制核心摘要内容"}
						onClick={handleCopy}
						disabled={
							isRefreshing || Boolean(organizationId && isLoading)
						}
					>
						{isCopied ? (
							<Check className="size-4 text-green-600" />
						) : (
							<Copy className="size-4 text-muted-foreground hover:text-foreground" />
						)}
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className="h-6 w-6 p-0 hover:bg-muted/50"
						title={
							isRefreshing || (organizationId && isLoading)
								? "正在生成中..."
								: "刷新核心摘要"
						}
						aria-label={
							isRefreshing || (organizationId && isLoading)
								? "正在生成核心摘要..."
								: "刷新核心摘要"
						}
						onClick={handleRefresh}
						disabled={
							isRefreshing || Boolean(organizationId && isLoading)
						}
					>
						{isRefreshing || (organizationId && isLoading) ? (
							<Loader2 className="size-4 text-muted-foreground animate-spin" />
						) : (
							<RefreshCcw className="size-4 text-muted-foreground hover:text-foreground" />
						)}
					</Button>
				</div>
			</div>
			{/* 修改记录 2025-08-07 14:01:54 hayden: 为ReactMarkdown容器添加条件滚动条样式 */}
			<div className={`prose prose-sm dark:prose-invert max-w-none ${
				isScrollEnabled
					? "max-h-96 overflow-y-auto ai-summary-scrollbar"
					: ""
			}`}>
				<ReactMarkdown
					components={{
						// 自定义标题样式
						h1: ({ children }) => (
							<h1 className="text-xl font-bold text-foreground mb-4 mt-6 first:mt-0">
								{children}
							</h1>
						),
						h2: ({ children }) => (
							<h2 className="text-lg font-semibold text-foreground mb-3 mt-5 first:mt-0">
								{children}
							</h2>
						),
						h3: ({ children }) => (
							<h3 className="text-base font-medium text-foreground mb-2 mt-4 first:mt-0">
								{children}
							</h3>
						),
						// 自定义段落样式
						p: ({ children }) => (
							<p className="text-sm text-muted-foreground mb-3 leading-relaxed">
								{children}
							</p>
						),
						// 自定义列表样式
						ul: ({ children }) => (
							<ul className="text-sm text-muted-foreground mb-3 pl-4 space-y-1">
								{children}
							</ul>
						),
						li: ({ children }) => (
							<li className="list-disc marker:text-primary/60">
								{children}
							</li>
						),
						// 自定义强调文本样式
						strong: ({ children }) => (
							<strong className="font-semibold text-foreground">
								{children}
							</strong>
						),
						// 自定义分割线样式
						hr: () => <hr className="my-6 border-border" />,
						// 自定义代码块样式
						code: ({ children }) => (
							<code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono">
								{children}
							</code>
						),
					}}
				>
					{displayContent}
				</ReactMarkdown>
			</div>
			{/* 修改记录 2025-06-18 16:43:07 hayden: 添加美观的AI分析参考标识 */}
			{displayContent !== "# 暂无数据" && (
				<div className="flex items-center justify-center gap-2 text-xs text-muted-foreground/80">
					<SparklesIcon className="size-3 text-primary/60" />
					<span className="font-medium">
						AI分析仅供参考，请结合实际情况进行判断
					</span>
					<SparklesIcon className="size-3 text-primary/60" />
				</div>
			)}
		</div>
	);
}