/**
 * 股东名册分析 - 公司概览组件
 * @file CompanyOverview.tsx
 * @description 用于展示公司股东结构的概览信息
 * <AUTHOR>
 * @created 2025-06-12 17:26:08
 * @modified 2025-06-12 19:16:13
 * @modified 2025-06-18 15:59:48 - 调整头部背景渐变颜色，从深紫色改为柔和的蓝灰色调，更适合数据分析场景
 * @modified 2025-06-18 16:02:35 - 移除灰色调，改用纯净蓝色系渐变(from-blue-600 via-blue-700 to-indigo-800)，更契合数据分析场景
 * @modified 2025-06-23 11:16:15 - 在持股比例分布图表的圆环内部添加Label显示文字标签，移除数值显示
 * @modified 2025-06-23 11:18:20 - 修正Label位置配置，使文字正确显示在各自圆环的中间位置
 * @modified 2025-06-23 11:20:15 - 调整Label位置为insideTop，使文字显示在圆环的上方位置
 * @modified 2025-06-23 11:45:48 - 修正Label位置为top，确保文字正确显示在圆环的上方
 * @modified 2025-06-23 11:48:34 - 修正圆环显示和Label位置：显示完整圆环，文字标签正确定位在上方
 * @modified 2025-06-23 12:12:55 - 调整同心圆标识显示方式：将标识从垂直排列改为显示在各自圆环的灰色部分，移除Label组件改用自定义label函数
 * @modified 2025-06-23 12:17:51 - 重新实现标识定位：使用独立的text元素替代label函数，精确控制标识在圆环中的位置
 * @modified 2025-06-23 12:20:46 - 调整标识布局：将三个标识在12点钟方向垂直并列显示，类似参考图片效果
 * @modified 2025-06-23 12:42:05 - 调整标识位置：使每个标识显示在其对应的同心圆上，增加间距以匹配圆环层级
 * @modified 2025-06-23 14:17:43 - 优化小屏幕适配：圆环标签在小屏幕下隐藏，中等屏幕及以上显示，并调整字体大小为8px
 * @modified 2025-06-23 14:24:29 - 修复小屏幕超出问题：调整容器高度，优化底部图例布局，缩小字体和图标尺寸，添加截断处理
 * @modified 2025-06-23 14:28:39 - 修复中屏图例覆盖问题：调整响应式断点从lg改为xl，让中等屏幕也使用底部图例
 * @modified 2025-06-23 14:35:58 - 优化图例显示格式：添加持股数量和比例信息，格式为"股东群体 持股数量（比例%）"
 */

"use client";

import { useState, useEffect } from "react";
import {
	ResponsiveContainer,
	PieChart,
	Pie,
	Cell,
	Tooltip,
} from "recharts";
import { RefreshCcw, InfoIcon } from "lucide-react";
import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import {
	Tooltip as UITooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	TooltipPortal,
} from "@ui/components/tooltip";
import { useCompanyOverview } from "@saas/shareholder/hooks/useCompanyOverview";
import { useStockData } from "@saas/shareholder/hooks/useStockData";
import { CompanyOverviewSkeleton } from "./CompanyOverviewSkeleton";
import type { ShareholderTypeItem } from "@saas/shareholder/lib/company-overview-api";
// import type { StockDataItem } from "@saas/shareholder/lib/stock-data-api";
import { useQueryClient } from "@tanstack/react-query";
// import { message } from "antd";
// 修改记录 2025-08-12 miya: 导入独立CSS文件替代全局样式注入，避免样式污染
import "../../styles/components/recharts-fixes.css";
// 修改记录 2025-08-12 miya: 导入toast用于错误反馈
import { toast } from "sonner";

/**
 * 公司概览渲染数据接口
 * @interface CompanyOverviewRenderData
 * @description 用于组件渲染的数据结构，基于 API 数据转换而来
 * <AUTHOR>
 * @created 2025-06-16 13:55:57
 */
interface CompanyOverviewRenderData {
	companyName: string;
	stockCode: string;
	analysisDateRange: string;
	generatedTime: string;
	metrics: {
		latestPeriod: {
			date: string;
		};
		latestProduct: {
			value: number;
			change: number;
		};
		latestTotalCapital: {
			value: number;
			changePercent: string;
		};
		totalPersonalShareholders: {
			value: number;
			change: string;
		};
		totalInstitutionalShareholders: {
			value: number;
			change: string;
		};
		totalShareholders: {
			value: number;
			change: string;
		};
		totalPersonalShares: {
			value: number;
			changePercent: string;
		};
		totalInstitutionalShares: {
			value: number;
			changePercent: string;
		};
		totalProductShares: {
			value: number;
			change: string;
		};
		topTenConcentration: {
			value: string;
			changePercent: string;
		};
		topTwentyConcentration: {
			value: string;
			changePercent: string;
		};
	};
}



/**
 * 指标卡片组件
 * @param title 指标标题
 * @param value 指标值
 * @param change 变化量
 * @param tooltipContent 可选的提示内容
 * @returns 指标卡片UI组件
 * @modified 2025-06-16 18:02:15 - 添加响应式字体大小支持，解决小屏幕字体过大问题
 * @modified 2025-06-18 15:54:03 - 统一变化数值为黑色字体，正数带+号，负数带-号，移除未使用的isPositive参数
 * @modified 2025-06-30 10:21:12 - 添加border边框样式，解决暗色主题下卡片边界不可见问题 - hayden
 * @modified 2025-07-21 - 添加可选的tooltip信息提示功能 - hayden
 * <AUTHOR>
 */
export function MetricCard({
	title,
	value,
	change,
	tooltipContent,
	ratio,
}: {
	title: string;
	value: string | number;
	change?: string | number;
	tooltipContent?: string;
	ratio?: string | number;
}): JSX.Element {
	/**
	 * 格式化变化数值，确保正数带+号，负数带-号
	 * @param changeValue 变化值
	 * @returns 格式化后的变化值字符串
	 * <AUTHOR>
	 * @created 2025-06-18 15:54:03
	 */
	// 修改记录 2025-08-12 miya: 改进类型安全，添加更严格的类型检查和边界条件处理
	function formatChangeValue(changeValue: string | number): string {
		// 处理 null、undefined、空字符串
		if (changeValue === null || changeValue === undefined || changeValue === '') {
			return "0";
		}

		const changeStr = String(changeValue).trim();

		// 处理空字符串和只有空格的情况
		if (!changeStr) {
			return "0";
		}

		// 如果已经有符号，进行验证后返回
		if (changeStr.startsWith("+") || changeStr.startsWith("-")) {
			// 验证符号后的内容是否为有效数字
			const numStr = changeStr.slice(1);
			const numValue = Number.parseFloat(numStr);
			if (Number.isNaN(numValue) || !Number.isFinite(numValue)) {
				return "0";
			}
			return changeStr;
		}

		// 提取数值，处理各种边界情况
		const numValue = Number.parseFloat(changeStr.replace(/[^\d.-]/g, ''));

		// 处理 NaN、Infinity、-Infinity
		if (Number.isNaN(numValue)) {
			return "0";
		}

		if (!Number.isFinite(numValue)) {
			// 处理无穷大值
			if (numValue === Number.POSITIVE_INFINITY) {
				return "+∞";
			}
			if (numValue === Number.NEGATIVE_INFINITY) {
				return "-∞";
			}
			return "0";
		}

		// 处理极大数值（超过JavaScript安全整数范围）
		if (Math.abs(numValue) > Number.MAX_SAFE_INTEGER) {
			const formatted = numValue.toExponential(2);
			return numValue > 0 ? `+${formatted}` : formatted;
		}

		// 处理极小数值（接近0的小数）
		if (Math.abs(numValue) > 0 && Math.abs(numValue) < 1e-10) {
			return numValue > 0 ? "+0" : "-0";
		}

		// 标准数值处理
		if (numValue < 0) {
			// 如果是负数但没有负号，添加负号
			return changeStr.startsWith("-") ? changeStr : `-${changeStr}`;
		}

		if (numValue > 0) {
			// 如果是正数，添加正号
			return `+${changeStr}`;
		}

		// 如果是0，不添加符号
		return changeStr;
	}

	return (
		<div className="rounded-lg bg-card border border-border p-2 sm:p-3 shadow-sm min-h-[72px] sm:min-h-[80px] flex flex-col">
			{/* 响应式标题字体：小屏幕使用更小字体 */}
			<div className="flex items-center gap-1 mb-1">
				<div className="text-[10px] sm:text-xs truncate">{title}</div>
				{tooltipContent && (
					<TooltipProvider>
						<UITooltip delayDuration={0}> {/* 修改记录 2025-08-12 miya: 调整悬浮0延迟 */}
							<TooltipTrigger asChild>
								<span className="cursor-default">
									<InfoIcon className="h-3 w-3 text-muted-foreground hover:text-foreground transition-colors" />
								</span>
							</TooltipTrigger>
							<TooltipPortal>
								<TooltipContent
									className="bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-border dark:bg-gray-800/95 dark:border-gray-700 max-w-xs"
									side="top"
									align="end"
								>
									<div className="text-sm text-foreground">
										{tooltipContent}
									</div>
								</TooltipContent>
							</TooltipPortal>
						</UITooltip>
					</TooltipProvider>
				)}
			</div>
			<div className="flex-1 flex items-center justify-between gap-1">
				{/* 响应式数值字体：根据屏幕大小调整 */}
				{title === "持股" ? (
					// 持股特殊显示格式：分两排展示，优化为固定高度内的垂直居中布局
					<div className="flex-1 min-w-0 flex flex-col justify-center">
						{(() => {
							// 解析传入的字符串，提取四个值
							const valueStr = String(value);
							// 修改记录 2025-08-12 miya: 改进类型安全，添加更严格的类型检查
							const shareholdingMatch =
								valueStr.match(/持股:\s*([^|]+)/);
							const ratioMatch =
								valueStr.match(/比例:\s*([^|]+)/);
							const sharesChangeMatch =
								valueStr.match(/持股变化\s*([^|]+)/);
							const ratioChangeMatch =
								valueStr.match(/比例变化\s*(.+)$/);

							const shareholding = shareholdingMatch?.[1]?.trim() ?? "";
							const ratio = ratioMatch?.[1]?.trim() ?? "";
							const sharesChange = sharesChangeMatch?.[1]?.trim() ?? "";
							const ratioChange = ratioChangeMatch?.[1]?.trim() ?? "";

							// 获取变化值的数值用于颜色判断
							// 修改记录 2025-08-12 miya: 改进类型安全，添加更严格的数值检查和边界条件处理
							const getChangeColor = (changeStr: string): string => {
								// 处理null、undefined、空字符串
								if (!changeStr || typeof changeStr !== 'string') {
									return "text-foreground";
								}

								const trimmedStr = changeStr.trim();
								if (!trimmedStr) {
									return "text-foreground";
								}

								// 提取数值
								const numValue = Number.parseFloat(
									trimmedStr.replace(/[^\d.-]/g, ""),
								);

								// 处理各种边界情况
								if (Number.isNaN(numValue)) {
									return "text-foreground";
								}

								// 处理无穷大值
								if (!Number.isFinite(numValue)) {
									if (numValue === Number.POSITIVE_INFINITY) {
										return "text-red-500"; // 正无穷显示为上涨色
									}
									if (numValue === Number.NEGATIVE_INFINITY) {
										return "text-green-500"; // 负无穷显示为下跌色
									}
									return "text-foreground";
								}

								// 处理极小数值（接近0但不为0）
								if (Math.abs(numValue) > 0 && Math.abs(numValue) < 1e-10) {
									return "text-foreground"; // 极小变化视为无变化
								}

								// 标准数值判断
								if (numValue > 0) {
									return "text-red-500"; // 上涨为红色
								}
								if (numValue < 0) {
									return "text-green-500"; // 下跌为绿色
								}
								return "text-foreground"; // 无变化为默认色
							};

							return (
								<div className="space-y-0.5">
									{/* 第一排：持股数量（调整字体大小适应固定高度）和比例 */}
									<div className="flex items-end justify-between gap-1">
										<div className="text-xs sm:text-sm md:text-base lg:text-lg font-semibold truncate flex-1 min-w-0">
											{shareholding}
										</div>
										{ratio && (
											<div className="text-[8px] sm:text-[9px] md:text-[10px] flex-shrink-0">
												{Number(ratio).toFixed(2)}%
											</div>
										)}
									</div>
									{/* 第二排：两个变化值（调整字体大小和间距） */}
									<div className="flex items-end justify-between gap-1">
										{sharesChange && (
											<div
												className={cn(
													"text-[8px] sm:text-[9px] md:text-[10px] flex-shrink-0",
													getChangeColor(
														sharesChange,
													),
												)}
											>
												{formatChangeValue(
													Number(
														sharesChange,
													).toLocaleString(),
												)}
											</div>
										)}
										{ratioChange && (
											<div
												className={cn(
													"text-[8px] sm:text-[9px] md:text-[10px] flex-shrink-0",
													getChangeColor(ratioChange),
												)}
											>
												{formatChangeValue(
													Number(ratioChange).toFixed(
														2,
													),
												)}
												%
											</div>
										)}
									</div>
								</div>
							);
						})()}
					</div>
				) : (
					// 原有的显示逻辑，优化为垂直居中布局
					<div className="flex-1 min-w-0 flex items-center">
						<div
							className={cn(
								"text-sm sm:text-base md:text-lg lg:text-xl font-semibold truncate flex-1 min-w-0",
								// 当标题为"最新持仓收益"时，根据数值设置红涨绿跌颜色
								(title === "最新持仓收益" ||
									title === "预估退出收益（元）") &&
									(() => {
										const numValue =
											typeof value === "number"
												? value
												: Number.parseFloat(
														String(value).replace(
															/[^\d.-]/g,
															"",
														),
													);
										if (numValue > 0) {
											return "text-red-500";
										}
										if (numValue < 0) {
											return "text-green-500";
										}
										return "text-foreground";
									})(),
							)}
						>
							{typeof value === "number"
								? value.toLocaleString()
								: value}
						</div>
					</div>
				)}
				{change && (
					<div className="flex items-center">
						<div
							className={cn(
								"text-[9px] sm:text-[10px] md:text-xs flex-shrink-0",
								// 根据变化值确定颜色：正数红色，负数绿色
								(() => {
									const changeStr = String(change);
									const numValue = Number.parseFloat(
										changeStr.replace(/[^\d.-]/g, ""),
									);
									if (numValue > 0) {
										return "text-red-500"; // 正数显示红色
									}
									if (numValue < 0) {
										return "text-green-500"; // 负数显示绿色
									}
									return "text-foreground"; // 零值保持默认颜色
								})(),
							)}
						>
							{formatChangeValue(change)}
						</div>
					</div>
				)}
				{ratio && (
					<div className="flex items-center">
						<div className="text-[9px] sm:text-[10px] md:text-xs flex-shrink-0">
							{ratio}%
						</div>
					</div>
				)}
			</div>
		</div>
	);
}


/**
 * 持股比例分布饼图组件属性接口
 * @interface ShareholdingDistributionChartProps
 * <AUTHOR>
 * @created 2025-06-16 14:14:19
 * @modified 2025-06-19 14:01:45 - 添加持股数量字段支持，用于在图表上显示具体持股数量 - hayden
 */
interface ShareholdingDistributionChartProps {
	/** 控股股东持股比例 */
	controllingRatio?: number;
	/** 前十股东持股比例 */
	top10Ratio?: number;
	/** 前百股东持股比例 */
	top100Ratio?: number;
	/** 第一大股东持股数量（股） */
	top1ShareholdingAmount?: number;
	/** 前十大股东合计持股数量（股） */
	top10ShareholdingAmount?: number;
	/** 前一百大股东合计持股数量（股） */
	top100ShareholdingAmount?: number;
	/** 第一大股东持股比例变化 */
	top1RatioChange?: number;
	/** 前十大股东合计持股比例变化 */
	top10RatioChange?: number;
	/** 前一百大股东合计持股比例变化 */
	top100RatioChange?: number;
}

/**
 * 持股比例分布横向条形图组件
 * @param props 组件属性
 * @returns 持股比例分布横向条形图UI组件
 * <AUTHOR>
 * @modified 2025-07-16 - 从同心圆饼状图改为横向条形图展示
 */
function ShareholdingDistributionChart({
	controllingRatio = 0,
	top10Ratio = 0,
	top100Ratio = 0,
	top1ShareholdingAmount = 0,
	top10ShareholdingAmount = 0,
	top100ShareholdingAmount = 0,
	top1RatioChange = 0,
	top10RatioChange = 0,
	top100RatioChange = 0
}: ShareholdingDistributionChartProps): JSX.Element {
	// Tooltip 状态管理
	const [tooltip, setTooltip] = useState<{
		visible: boolean;
		x: number;
		y: number;
		data: {
			name: string;
			ratio: number;
			amount: number;
			change: number;
		} | null;
	}>({
		visible: false,
		x: 0,
		y: 0,
		data: null,
	});

	// 动画状态管理
	const [animatedWidths, setAnimatedWidths] = useState<number[]>([0, 0, 0]);
	const [isInitialLoad, setIsInitialLoad] = useState(true);

	// 确保所有传入的值都是数字类型，并设置默认值用于测试
	const safeControllingRatio = Number(controllingRatio) || 0;
	const safeTop10Ratio = Number(top10Ratio) || 0;
	const safeTop100Ratio = Number(top100Ratio) || 0;
	const safeTop1RatioChange = Number(top1RatioChange) || 0;
	const safeTop10RatioChange = Number(top10RatioChange) || 0;
	const safeTop100RatioChange = Number(top100RatioChange) || 0;

	// 确保持股数量字段都是数字类型
	const safeTop1ShareholdingAmount = Number(top1ShareholdingAmount) || 0;
	const safeTop10ShareholdingAmount = Number(top10ShareholdingAmount) || 0;
	const safeTop100ShareholdingAmount = Number(top100ShareholdingAmount) || 0;

	// 动画效果：组件首次加载时触发条形图动画
	useEffect(() => {
		const targetWidths = [safeControllingRatio, safeTop10Ratio, safeTop100Ratio];

		// 检查是否有有效数据
		const hasData = targetWidths.some(width => width > 0);
		if (!hasData) {
			setAnimatedWidths([0, 0, 0]);
			return;
		}

		if (isInitialLoad) {
			// 首次加载：依次动画每个条形图
			targetWidths.forEach((targetWidth, index) => {
				setTimeout(() => {
					setAnimatedWidths(prev => {
						const newWidths = [...prev];
						newWidths[index] = targetWidth;
						return newWidths;
					});
				}, index * 200); // 每个条形图延迟200ms
			});

			// 标记首次加载完成
			setTimeout(() => {
				setIsInitialLoad(false);
			}, targetWidths.length * 200 + 1000);
		} else {
			// 数据更新：快速过渡到新值
			setAnimatedWidths(targetWidths);
		}
	}, [safeControllingRatio, safeTop10Ratio, safeTop100Ratio, isInitialLoad]);

	/**
	 * 格式化持股数量显示
	 * @param amount 持股数量（股）
	 * @returns 格式化后的持股数量字符串
	 * <AUTHOR>
	 * @created 2025-06-19 14:01:45
	 * @modified 2025-06-23 14:38:46 - 简化显示格式，直接显示股数不带单位，减少文字长度
	 */
	function formatShareholdingAmount(amount: number): string {
		if (amount === 0) {
			return "0";
		}

		// 直接返回格式化的数字，不带单位
		return amount.toLocaleString();
	}

	/**
	 * 处理鼠标进入条形图区域
	 * @param event 鼠标事件
	 * @param item 条形图数据项
	 */
	const handleMouseEnter = (
		event: React.MouseEvent<HTMLDivElement>,
		item: { name: string; ratio: number; amount: number; change: number }
	) => {
		const containerRect = event.currentTarget.closest('.h-full')?.getBoundingClientRect();

		if (containerRect) {
			setTooltip({
				visible: true,
				x: event.clientX - containerRect.left,
				y: event.clientY - containerRect.top,
				data: item,
			});
		}
	};

	/**
	 * 处理鼠标离开条形图区域
	 */
	const handleMouseLeave = () => {
		setTooltip({
			visible: false,
			x: 0,
			y: 0,
			data: null,
		});
	};

	/**
	 * 处理鼠标在条形图区域移动
	 * @param event 鼠标事件
	 */
	const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
		if (tooltip.visible && tooltip.data) {
			const containerRect = event.currentTarget.closest('.h-full')?.getBoundingClientRect();

			if (containerRect) {
				setTooltip(prev => ({
					...prev,
					x: event.clientX - containerRect.left,
					y: event.clientY - containerRect.top,
				}));
			}
		}
	};

	/**
	 * 横向条形图数据配置
	 * <AUTHOR>
	 * @created 2025-07-16
	 */
	const barData = [
		{
			name: "控股股东",
			color: "#4B5D9A",
			ratio: safeControllingRatio,
			amount: safeTop1ShareholdingAmount,
			change: safeTop1RatioChange,
		},
		{
			name: "前十股东",
			color: "#ce712fff",
			ratio: safeTop10Ratio,
			amount: safeTop10ShareholdingAmount,
			change: safeTop10RatioChange,
		},
		{
			name: "前百股东",
			color: "rgba(112, 167, 207, 1)",
			ratio: safeTop100Ratio,
			amount: safeTop100ShareholdingAmount,
			change: safeTop100RatioChange,
		},
	];

	// 检查是否有有效数据 - 如果所有比例都为0，则显示空数据状态
	const hasValidData = safeControllingRatio > 0 || safeTop10Ratio > 0 || safeTop100Ratio > 0;

	// 如果没有数据，显示暂无数据状态
	if (!hasValidData) {
		return (
			<div className="h-full">
				<h3 className="mt-3 mb-3 sm:mb-5 text-xs sm:text-sm font-medium">
					持股比例分布
				</h3>
				<div className="h-[260px] sm:h-[280px] flex items-center justify-center text-muted-foreground">
					<div className="text-center">
						<div className="text-xs sm:text-sm">暂无数据</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full relative">
			<h3 className="mt-3 mb-3 sm:mb-5 text-xs sm:text-sm font-medium">
				持股比例分布
			</h3>

			{/* 横向条形图容器 */}
			<div className="h-[260px] pt-8 sm:h-[280px] flex flex-col justify-center space-y-4 sm:space-y-5 px-2 sm:px-4">
				{barData.map((item, index) => (
					<div key={index} className="space-y-1 sm:space-y-2">
						{/* 标签行：股东群体名称和数据 */}
						<div className="flex justify-between items-center text-xs sm:text-sm">
							<div className="flex items-center gap-2">
								<div
									className="w-3 h-3 rounded-sm flex-shrink-0"
									style={{ backgroundColor: item.color }}
								/>
								<span className="font-medium text-foreground">
									{item.name}
								</span>
							</div>
						</div>

						{/* 条形图 */}
						<div
							className="relative cursor-pointer"
							onMouseEnter={(e) => handleMouseEnter(e, item)}
							onMouseLeave={handleMouseLeave}
							onMouseMove={handleMouseMove}
						>
							{/* 背景条 */}
							<div className="w-full h-4 sm:h-5 bg-border rounded-full overflow-hidden">
								{/* 进度条 */}
								<div
									className={`h-full rounded-full hover:opacity-90 ${
										isInitialLoad
											? "transition-all duration-1000 ease-out"
											: "transition-all duration-300 ease-out"
									}`}
									style={{
										backgroundColor: item.color,
										width: `${Math.min(animatedWidths[index] || 0, 100)}%`,
									}}
								/>
							</div>

							{/* 悬浮提示 - 仅在条形图有足够宽度时显示内部文本 */}
							{(animatedWidths[index] || 0) > 15 && (
								<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
									<span className="text-white text-xs font-medium drop-shadow-sm">
										{item.ratio.toFixed(1)}%&nbsp;(
										{formatShareholdingAmount(item.amount)})
										{"       "}
									</span>
									&nbsp;{/* HTML 实体空格*/}
									<span
										className={cn(
											"text-white text-xs font-medium drop-shadow-sm",
											(() => {
												if (item.change > 0) {
													return "text-red-500";
												}
												if (item.change < 0) {
													return "text-green-500";
												}
												return "text-white";
											})(),
										)}
									>
										{item.change}%
									</span>
								</div>
							)}
						</div>
					</div>
				))}
			</div>

			{/* Tooltip 显示 */}
			{tooltip.visible && tooltip.data && (
				<div
					className="absolute z-50 pointer-events-none"
					style={{
						left: `${tooltip.x + 10}px`,
						top: `${tooltip.y - 10}px`,
						transform:
							tooltip.x > 200
								? "translateX(-100%)"
								: "translateX(0)",
					}}
				>
					<div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-border dark:bg-gray-800/95 dark:border-gray-700">
						<div className="text-sm font-medium text-foreground">
							{tooltip.data.name}
						</div>
						<div className="text-xs text-foreground/80 mt-1">
							持股数量：
							{formatShareholdingAmount(tooltip.data.amount)}
						</div>
						<div className="text-xs text-foreground/80">
							持股比例：{tooltip.data.ratio.toFixed(1)}%
						</div>
						<div className="text-xs text-foreground/80">
							变动幅度：
							<span
								className={cn(
									"text-xs font-medium drop-shadow-sm",
									(() => {
										if (tooltip.data.change > 0) {
											return "text-red-500";
										}
										if (tooltip.data.change < 0) {
											return "text-green-500";
										}
										return "text-foreground/80";
									})(),
								)}
							>
								{tooltip.data.change}%
							</span>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}


/**
 * 机构股东类型分布图表组件属性接口
 * @interface InstitutionalSharesDistributionChartProps
 * <AUTHOR>
 * @created 2025-06-27 18:28:05
 */
interface InstitutionalSharesDistributionChartProps {
	/** 股东类型分布数据 */
	shareholderTypes?: ShareholderTypeItem[];
	/** 显示模式：'count' 为户数分布，'shares' 为持股分布 */
	mode?: 'count' | 'shares';
}

/**
 * 机构股东类型分布饼图组件
 * @param props 组件属性
 * @returns 机构股东类型分布饼图UI组件
 * @modified 2025-06-16 18:03:45 - 添加响应式字体大小支持
 * @modified 2025-06-27 18:28:05 - 重构为真实数据驱动的图表组件，支持户数分布和持股分布两种模式 - hayden
 * <AUTHOR>
 */
function InstitutionalSharesDistributionChart({
	shareholderTypes = [],
	mode = 'count'
}: InstitutionalSharesDistributionChartProps): JSX.Element {

	/**
	 * 格式化变化百分比显示
	 * @param changePercent 变化百分比数值
	 * @returns 格式化后的显示字符串和样式类名
	 * <AUTHOR>
	 * @created 2025-07-18
	 */
	function formatChangePercent(changePercent?: number): {
		text: string;
		className: string;
	} {
		if (changePercent === undefined || changePercent === null) {
			return { text: '', className: '' };
		}

		const numValue = Number(changePercent);

		// 处理零值
		if (numValue === 0) {
			return { text: '0%', className: 'text-foreground/60' };
		}

		// 格式化显示文本，移除括号
		const sign = numValue > 0 ? '+' : '';
		const text = `${sign}${numValue.toFixed(1)}%`;

		// 应用红涨绿跌颜色规则
		const className = numValue > 0 ? 'text-red-500' : 'text-green-500';

		return { text, className };
	}

	/**
	 * 转换股东类型数据为图表数据
	 * @param data 股东类型数据
	 * @param displayMode 显示模式
	 * @returns 图表数据
	 * <AUTHOR>
	 * @created 2025-06-27 18:28:05
	 */
	function transformShareholderTypesToChartData(
		data: ShareholderTypeItem[],
		displayMode: 'count' | 'shares'
	): Array<{
		name: string;
		value: number;
		count: number;
		shares: number;
		countPercentage: number;
		sharesPercentage: number;
		countChangePercent?: number;
		sharesChangePercent?: number;
		fill: string;
	}> {
		if (!data || data.length === 0) {
			return [];
		}

		// 定义简约颜色配置 - 参考ShareholdingDistributionChart，支持暗色主题适配
		const colors = [
			"hsl(210, 70%, 60%)",   // 柔和蓝色
			"hsl(142, 60%, 50%)",   // 柔和绿色
			"hsl(38, 80%, 60%)",    // 柔和橙色
			"hsl(358, 60%, 65%)",   // 柔和红色
			"hsl(262, 70%, 65%)",   // 柔和紫色
			"hsl(173, 50%, 55%)",   // 柔和青色
			"hsl(43, 60%, 70%)",    // 柔和黄色
			"hsl(197, 60%, 70%)",   // 柔和浅蓝色
			"hsl(326, 60%, 70%)",   // 柔和粉色
			"hsl(84, 60%, 55%)"     // 柔和草绿色
		];

		return data.map((item, index) => {
			const count = Number.parseInt(item.typeCount) || 0;
			const shares = Number.parseInt(item.typeShares) || 0;
			const countPercentage = Number.parseFloat(item.typePercentage) || 0;
			const sharesPercentage = Number.parseFloat(item.sharesPercentage) || 0;

			return {
				name: item.shareholderType,
				value: displayMode === 'count' ? countPercentage : sharesPercentage,
				count,
				shares,
				countPercentage,
				sharesPercentage,
				countChangePercent: item.typeCountChangePercent,
				sharesChangePercent: item.typeSharesChangePercent,
				fill: colors[index % colors.length]
			};
		}).filter(item => item.value > 0); // 过滤掉值为0的项
	}

	// 转换数据
	const chartData = transformShareholderTypesToChartData(shareholderTypes, mode);

	/**
	 * 格式化数值显示
	 * @param value 数值
	 * @returns 格式化后的字符串
	 * <AUTHOR>
	 * @created 2025-06-27 18:28:05
	 */
	function formatNumber(value: number): string {
		if (value === 0) {
			return "0";
		}
		return value.toLocaleString();
	}



	// 如果没有数据，显示暂无数据状态
	if (chartData.length === 0) {
		return (
			<div className="h-full">
				<h3 className="mt-3 mb-3 sm:mb-5 text-xs sm:text-sm font-medium">
					股东类型{mode === "count" ? "户数" : "持股"}分布（TOP200）
				</h3>
				{/* 修改记录 2025-06-30 10:12:56 - 调整暂无数据状态高度，与图表容器保持一致 - hayden */}
				<div className="h-[260px] sm:h-[280px] flex items-center justify-center text-muted-foreground">
					<div className="text-center">
						<div className="text-xs sm:text-sm">暂无数据</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full">
			<h3 className="mt-3 mb-3 sm:mb-5 text-xs sm:text-sm font-medium">
				股东类型{mode === "count" ? "户数" : "持股"}分布（TOP200）
			</h3>

			{/* 垂直布局：扇形图占主要空间(75%)，图例占辅助空间(25%) */}
			<div className="h-[320px] sm:h-[360px] md:h-[400px] flex flex-col">
				{/* 扇形图容器 - 占据主要空间 */}
				<div className="h-[75%] w-full">
					<ResponsiveContainer width="100%" height="100%">
						{/** 修改块开始: 去除饼图焦点可聚焦属性
						  * 修改范围: 为 PieChart 根元素添加 tabIndex={-1}，避免 wrapper 获得焦点
						  * 修改时间: 2025-08-12
						  * 对应计划步骤: 焦点样式修复 - 步骤A
						  * 恢复方法: 将下方 PieChart 还原为无 tabIndex 属性
						  */}
						<PieChart tabIndex={-1}>
						{/** 修改块结束: 去除饼图焦点可聚焦属性
						  * 修改时间: 2025-08-12
						  */}
							{/* 修改记录 2025-07-16 - 从环形图改为扇形图，移除innerRadius，添加百分比标签显示 */}
							<Pie
								data={chartData}
								cx="50%"
								cy="50%"
								outerRadius="85%"
								dataKey="value"
								startAngle={90}
								endAngle={450}
								label={({
									value,
									cx,
									cy,
									midAngle,
									outerRadius,
								}) => {
									// 只显示大于2%的标签，避免小扇形区域文字重叠
									// if (value < 2) {
									// 	return null;
									// }

									// 计算标签位置（在扇形区域的中心，距离圆心约60%的位置）
									const RADIAN = Math.PI / 180;
									const radius = (outerRadius || 0) * 0.6; // 使用外半径的60%作为标签位置
									const x =
										(cx || 0) +
										radius * Math.cos(-midAngle * RADIAN);
									const y =
										(cy || 0) +
										radius * Math.sin(-midAngle * RADIAN);

									return (
										<text
											x={x}
											y={y}
											fill="white"
											textAnchor="middle"
											dominantBaseline="central"
											fontSize="11"
											fontWeight="600"
											style={{
												textShadow:
													"1px 1px 2px rgba(0,0,0,0.8)",
												filter: "drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.62))",
											}}
										>
											{`${value.toFixed(1)}%`}
										</text>
									);
								}}
								labelLine={false}
							>
								{chartData.map((entry, index) => (
									<Cell
										key={`cell-${index}`}
										fill={entry.fill}
									/>
								))}
							</Pie>
							<Tooltip
								content={({ active, payload }) => {
									if (active && payload && payload.length) {
										const data = payload[0].payload;
										return (
											<div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-border dark:bg-gray-800/95 dark:border-gray-700">
												<div className="text-sm font-medium text-foreground">
													{data.name}
												</div>
												<div className="text-xs text-muted-foreground mt-1">
													股东数量：
													{formatNumber(data.count)}
												</div>
												<div className="text-xs text-muted-foreground">
													户数占比：
													{data.countPercentage.toFixed(
														1,
													)}
													%
												</div>
												<div className="text-xs text-muted-foreground">
													持股数量：
													{formatNumber(data.shares)}
												</div>
												<div className="text-xs text-muted-foreground">
													持股占比：
													{data.sharesPercentage.toFixed(
														1,
													)}
													%
												</div>
												{/* 根据模式显示对应的变动状况 */}
												{(() => {
													const changePercent =
														mode === "count"
															? data.countChangePercent
															: data.sharesChangePercent;
													const changeInfo =
														formatChangePercent(
															changePercent,
														);
													return changeInfo.text ? (
														<div className="text-xs text-muted-foreground">
															变动状况：
															<span
																className={
																	changeInfo.className
																}
															>
																{
																	changeInfo.text
																}
															</span>
														</div>
													) : null;
												})()}
											</div>
										);
									}
									return null;
								}}
							/>
						</PieChart>
					</ResponsiveContainer>
					{/* 修改记录 2025-08-12 miya: 移除内联全局样式，改为导入独立CSS文件 */}
					{/* 原全局样式已移动到 recharts-fixes.css 文件中 */}
				</div>

				{/* 底部图例 - 紧凑的两列网格布局，占据剩余25%空间 */}
				<div className="h-[25%] mt-1 overflow-y-auto">
					<div className="grid grid-cols-2 gap-x-1 gap-y-0.5 sm:gap-x-2 sm:gap-y-1 px-1">
						{chartData.map((item, index) => (
							<div
								key={index}
								className="flex items-center gap-1 min-w-0 px-1 py-0.5 rounded-sm bg-background/30 hover:bg-background/60 transition-colors"
							>
								{/* 颜色指示器 */}
								<div
									className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-sm flex-shrink-0"
									style={{ backgroundColor: item.fill }}
								/>

								{/* 单行紧凑内容：名称 + 数据 + 变化 */}
								<div className="min-w-0 flex-1 flex items-center gap-1 text-[7px] sm:text-[8px] leading-tight">
									{/* 股东类型名称 */}
									<span className="font-medium text-foreground truncate max-w-[40%]">
										{item.name}
									</span>

									{/* 数量和比例 */}
									<span className="text-muted-foreground truncate max-w-[35%]">
										{mode === "count"
											? `${formatNumber(item.count)}(${item.countPercentage.toFixed(1)}%)`
											: `${formatNumber(item.shares)}(${item.sharesPercentage.toFixed(1)}%)`}
									</span>

									{/* 变化百分比 */}
									{(() => {
										const changePercent =
											mode === "count"
												? item.countChangePercent
												: item.sharesChangePercent;
										const changeInfo = formatChangePercent(changePercent);
										return changeInfo.text ? (
											<span
												className={`font-medium flex-shrink-0 max-w-[25%] ${changeInfo.className}`}
											>
												{changeInfo.text}
											</span>
										) : null;
									})()}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}

/**
 * 公司概览组件属性接口
 * @interface CompanyOverviewProps
 * @modified 2025-06-16 12:25:02 - 添加组织ID属性支持
 */
interface CompanyOverviewProps {
	/** 组织ID，用于获取公司概览数据 */
	organizationId?: string;
}

/**
 * 公司概览组件
 * @param props 组件属性
 * @returns 公司概览UI组件
 * @modified 2025-06-12 18:15:53 - 将生成时间移至右下角底部
 * @modified 2025-06-12 18:18:06 - 调整生成时间位置，使其与公司名称和分析范围保持在同一水平线上
 * @modified 2025-06-12 18:20:28 - 调整生成时间位置，使其再下移一点
 * @modified 2025-06-16 12:25:02 - 添加组织ID参数支持和日志打印功能
 * @modified 2025-06-16 13:47:17 - 添加真实API数据渲染支持，将API数据映射到界面指标
 */
export function CompanyOverview({ organizationId }: CompanyOverviewProps): JSX.Element {
	// 获取 queryClient 实例用于刷新所有相关查询
	const queryClient = useQueryClient();

	// 使用 useCompanyOverview 钩子获取真实数据
	const {data: apiData, isLoading, error, refetch } = useCompanyOverview(organizationId || "", {
		enabled: !!organizationId, // 只有当 organizationId 存在时才启用查询
	});

	// 使用 useStockData 钩子获取股票数据
	const {
		data: stockData,
		isLoading: stockDataLoading,
		error: stockDataError,
		refetch: refetchStockData
	} = useStockData(organizationId || "", {
		enabled: !!organizationId, // 只有当 organizationId 存在时才启用查询
	});

	/* 修改块开始: 本地UI刷新态
	 * 修改范围: 新增本地状态 isRefreshingUI，用于控制点击刷新时仅头部区域展示骨架屏，不影响其他区域
	 * 修改时间: 2025-08-12
	 * 对应计划步骤: 步骤1
	 * 恢复方法: 删除此修改块内所有代码
	 */
	/**
	 * 本地 UI 刷新态
	 * @description 当用户点击“刷新”按钮时，将该状态置为 true，仅在头部选中区域展示骨架屏；
	 *              当相关数据刷新完成后置为 false，恢复正常渲染。
	 * <AUTHOR>
	 * @date 2025-08-12
	 */
	const [isRefreshingUI, setIsRefreshingUI] = useState<boolean>(false);
	/* 修改块结束: 本地UI刷新态
	 * 修改时间: 2025-08-12
	 */


	/**
	 * 解析API数据并转换为组件所需格式
	 * @param apiResponse API响应数据
	 * @returns 解析后的公司数据
	 * <AUTHOR>
	 * @created 2025-06-16 13:47:17
	 */
	function parseApiData(apiResponse: any): any {
		if (!apiResponse?.data) {
			return null;
		}

		try {
			// 解析字符串格式的数据
			let parsedData: any;
			if (typeof apiResponse.data === 'string') {
				parsedData = JSON.parse(apiResponse.data);
			} else {
				parsedData = apiResponse.data;
			}

			// 如果是数组，取第一个元素
			const dataItem = Array.isArray(parsedData) ? parsedData[0] : parsedData;

			// 如果有嵌套的data字段，提取出来
			const companyData = dataItem?.data || dataItem;

			return companyData;
		} catch (error) {
			console.error("解析API数据失败:", error);
			return null;
		}
	}

	// 解析API数据
	const parsedApiData = parseApiData(apiData);

	const parsedStockData = parseApiData(stockData);

	


	

	/**
	 * 全面刷新所有股东分析相关数据
	 * @description 刷新公司概览、总体报告、趋势分析、股东变动分析、股票数据等所有相关数据
	 * <AUTHOR>
	 * @created 2025-06-30 10:43:08
	 * @modified 2025-07-21 - 添加股票数据刷新支持
	 */
	/* 修改块开始: 刷新逻辑支持本地UI刷新态
	 * 修改范围: 将 handleRefreshAllData 从同步改为异步，进入刷新前置 isRefreshingUI=true；
	 *           等待公司概览与股票数据两路刷新完成后在 finally 中置为 false
	 * 修改时间: 2025-08-12
	 * 对应计划步骤: 步骤2
	 * 恢复方法: 删除此修改块内所有代码，恢复下方注释中的原代码
	 */
	/* 原代码:
	 * function handleRefreshAllData(): void {
	 *   if (!organizationId) {
	 *     return;
	 *   }
	 *   // 刷新公司概览数据
	 *   refetch();
	 *   // 刷新股票数据
	 *   refetchStockData();
	 *   // 刷新所有相关的查询缓存
	 *   queryClient.invalidateQueries({ queryKey: ["companyGeneralReport", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["shareholdersTrend", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["stockData", organizationId] });
	 *   // 刷新自然人股东变动分析数据
	 *   queryClient.invalidateQueries({ queryKey: ["increaseIndividualShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["decreaseIndividualShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["newIndividualShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["exitIndividualShareholders", organizationId] });
	 *   // 刷新机构股东变动分析数据
	 *   queryClient.invalidateQueries({ queryKey: ["increaseInstitutionShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["decreaseInstitutionShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["newInstitutionShareholders", organizationId] });
	 *   queryClient.invalidateQueries({ queryKey: ["exitInstitutionShareholders", organizationId] });
	 * }
	 */
    /**
     * 刷新头部选中区域数据（公司概览 + 股票数据）
     * @description 触发两路数据的并发刷新，并在刷新期间设置本地刷新态以展示局部骨架屏。
     *              刷新完成后恢复 UI，同时保持对相关 Query 的缓存失效以驱动后续视图按需更新。
     * <AUTHOR>
     * @date 2025-08-12
     * @modified 2025-08-12 miya: 添加具体错误处理和用户反馈
     */
    async function handleRefreshAllData(): Promise<void> {
		if (!organizationId) {
			toast.error("组织ID不存在，无法刷新数据");
			return;
		}

		setIsRefreshingUI(true);
		try {
			// 执行并发刷新
			const [companyResult, stockResult] = await Promise.allSettled([
				refetch(),
				refetchStockData(),
			]);

			// 检查刷新结果并提供具体反馈
			let successCount = 0;
			const errorMessages: string[] = [];

			if (companyResult.status === 'fulfilled') {
				successCount++;
			} else {
				console.error('公司概览数据刷新失败:', companyResult.reason);
				errorMessages.push('公司概览数据刷新失败');
			}

			if (stockResult.status === 'fulfilled') {
				successCount++;
			} else {
				console.error('股票数据刷新失败:', stockResult.reason);
				errorMessages.push('股票数据刷新失败');
			}

			// 根据结果提供用户反馈
			if (successCount === 2) {
				toast.success("数据刷新成功");
			} else if (successCount === 1) {
				toast.warning(`部分数据刷新成功，${errorMessages.join('、')}`);
			} else {
				toast.error(`数据刷新失败：${errorMessages.join('、')}`);
				return; // 如果全部失败，不执行后续缓存清理
			}

			// 保持原有的相关查询缓存失效，确保依赖视图后续按需更新
			queryClient.invalidateQueries({ queryKey: ["companyGeneralReport", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["shareholdersTrend", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["stockData", organizationId] });

			// 自然人股东变动分析
			queryClient.invalidateQueries({ queryKey: ["increaseIndividualShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["decreaseIndividualShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["newIndividualShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["exitIndividualShareholders", organizationId] });

			// 机构股东变动分析
			queryClient.invalidateQueries({ queryKey: ["increaseInstitutionShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["decreaseInstitutionShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["newInstitutionShareholders", organizationId] });
			queryClient.invalidateQueries({ queryKey: ["exitInstitutionShareholders", organizationId] });
		} catch (error) {
			console.error('刷新数据时发生未预期的错误:', error);
			toast.error("刷新数据时发生未知错误，请稍后重试");
		} finally {
			setIsRefreshingUI(false);
		}
	}
	/* 修改块结束: 刷新逻辑支持本地UI刷新态
	 * 修改时间: 2025-08-12
	 */

	/**
	 * 转换API数据为渲染数据格式
	 * @param apiData 解析后的API数据
	 * @param timestamp API响应时间戳
	 * @returns 渲染数据
	 * <AUTHOR>
	 * @created 2025-06-16 13:55:57
	 * @modified 2025-06-16 14:05:52 - 添加空值检查，防止访问null对象属性导致的运行时错误
	 */
	function transformApiDataToRenderData(apiData: any, timestamp?: string): CompanyOverviewRenderData {
		// 添加空值检查，确保 apiData 不为 null 或 undefined
		if (!apiData) {

			// 返回默认的空数据结构
			return {
				companyName: "暂无数据",
				stockCode: "000000",
				analysisDateRange: "数据范围未知",
				generatedTime: new Date().toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				}),
				metrics: {
					// 第一行指标数据 - 默认值
					latestPeriod: {
						date: "未知",
					},
					latestProduct: {
						value: 0,
						change: 0
					},
					latestTotalCapital: {
						value: 0,
						changePercent: "0%"
					},
					totalPersonalShareholders: {
						value: 0,
						change: "0"
					},
					totalInstitutionalShareholders: {
						value: 0,
						change: "0"
					},
					totalShareholders: {
						value: 0,
						change: "0"
					},
					// 第二行指标数据 - 默认值
					totalPersonalShares: {
						value: 0,
						changePercent: "0%"
					},
					totalInstitutionalShares: {
						value: 0,
						changePercent: "0%"
					},
					totalProductShares: {
						value: 0,
						change: "0"
					},
					topTenConcentration: {
						value: "0%",
						changePercent: "0%"
					},
					topTwentyConcentration: {
						value: "0%",
						changePercent: "0%"
					}
				}
			};
		}

		return {
			companyName: apiData.companyName || "未知公司",
			stockCode: apiData.companyCode || "000000",
			analysisDateRange: apiData.registerDate && apiData.oldestRegisterDate ?
				`${apiData.oldestRegisterDate.split('T')[0]} 至 ${apiData.registerDate.split('T')[0]}` :
				"数据范围未知",
			generatedTime: timestamp ?
				new Date(timestamp).toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				}) :
				new Date().toLocaleString('zh-CN'),
			metrics: {
				// 第一行指标数据
				latestPeriod: {
					date: apiData.registerDate?.split('T')[0] || "未知",
				},
				latestProduct: {
					value: apiData.totalShareholders || 0,
					change: apiData.shareholdersChange || 0
				},
				latestTotalCapital: {
					value: apiData.totalShares ? Math.round(Number(apiData.totalShares) / 10000) : 0,
					changePercent: apiData.totalSharesChangePercent ? `${apiData.totalSharesChangePercent}%` : "0%"
				},
				totalPersonalShareholders: {
					value: apiData.individualShareholders || 0,
					change: apiData.individualShareholdersChange ?
						(apiData.individualShareholdersChange >= 0 ? `+${apiData.individualShareholdersChange}` : `${apiData.individualShareholdersChange}`) :
						"0"
				},
				totalInstitutionalShareholders: {
					value: apiData.totalInstitutions || 0,
					change: apiData.institutionsChange ?
						(apiData.institutionsChange >= 0 ? `+${apiData.institutionsChange}` : `${apiData.institutionsChange}`) :
						"0"
				},
				totalShareholders: {
					value: apiData.creditAccountCount || 0,
					change: apiData.creditAccountCountChange ?
						(apiData.creditAccountCountChange >= 0 ? `+${apiData.creditAccountCountChange}` : `${apiData.creditAccountCountChange}`) :
						"0"
				},
				// 第二行指标数据
				totalPersonalShares: {
					value: apiData.institutionShares ? Number(apiData.institutionShares) : 0,
					changePercent: apiData.institutionSharesChangePercent ? `${apiData.institutionSharesChangePercent}%` : "0%"
				},
				totalInstitutionalShares: {
					value: apiData.totalMarginShares ? Number(apiData.totalMarginShares) : 0,
					changePercent: apiData.totalMarginSharesChangePercent ? `${apiData.totalMarginSharesChangePercent}%` : "0%"
				},
				totalProductShares: {
					value: apiData.avgSharesPerHolder ? Math.round(Number(apiData.avgSharesPerHolder)) : 0,
					change: apiData.avgSharesPerHolderChange ?
						(apiData.avgSharesPerHolderChange >= 0 ? `+${Math.round(apiData.avgSharesPerHolderChange)}` : `${Math.round(apiData.avgSharesPerHolderChange)}`) :
						"0"
				},
				topTenConcentration: {
					value: apiData.top10ShareholdingRatio ? `${apiData.top10ShareholdingRatio}%` : "0%",
					changePercent: apiData.top10RatioChange ? `${apiData.top10RatioChange}%` : "0%"
				},
				topTwentyConcentration: {
					value: apiData.top20ShareholdingRatio ? `${apiData.top20ShareholdingRatio}%` : "0%",
					changePercent: apiData.top20RatioChange ? `${apiData.top20RatioChange}%` : "0%"
				}
			}
		};
	}


	// 如果正在加载且没有数据，显示骨架屏
	// 修复条件判断逻辑：当正在加载且没有解析到数据时显示骨架屏
	if ((isLoading && !parsedApiData) || (stockDataLoading && !stockData)) {
		return <CompanyOverviewSkeleton />;
	}

	// 如果有错误且没有数据，显示错误状态
	if (error || stockDataError) {
		const errorMessage = (error as unknown as Error)?.message || (stockDataError as unknown as Error)?.message || "未知错误";
		return (
			<div className="w-full">
				{/* 修改记录 2025-06-18 15:59:48 - 调整错误状态背景渐变颜色，与正常状态保持一致 - hayden */}
				{/* 修改记录 2025-06-18 16:02:35 - 更新错误状态背景为纯净蓝色系渐变，与正常状态保持一致 - hayden */}
				{/* 修改记录 2025/7/22 - UI修改，原样式变淡效果 - 修改人：Miya */}
				<div className="w-full bg-gradient-to-br from-slate-900 via-gray-800 to-slate-700 p-6 rounded-lg text-white shadow-lg relative overflow-hidden">
					<div className="relative z-10 flex items-center justify-center">
						<div className="flex items-center gap-2">
							<span className="text-lg">
								加载失败: {errorMessage}
							</span>
							<Button
								variant="outline"
								size="sm"
								className="text-white border-white/30 hover:bg-white/20"
								onClick={() => {
									handleRefreshAllData();
								}}
							>
								重试
							</Button>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// 转换数据为渲染格式
	const data = transformApiDataToRenderData(parsedApiData, apiData?.timestamp);

	/**
	 * 获取当前实时时间（精确到分钟）
	 * @returns 格式化的当前时间字符串
	 * <AUTHOR>
	 * @created 2025-07-21
	 */
	function getCurrentTime(): string {
		return new Date().toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	return (
		<div className="w-full">
			{/* 头部信息 - 数据分析风格渐变背景 */}
			{/* 修改记录 2025-06-18 15:59:48 - 调整背景渐变颜色，从深紫色改为柔和的蓝灰色调，更适合数据分析场景 - hayden */}
			{/* 修改记录 2025-06-18 16:02:35 - 移除灰色调，改用纯净蓝色系渐变，更契合数据分析场景 - hayden */}
			<div className="w-full bg-gradient-to-br from-slate-800 via-gray-700 to-slate-800 p-6 rounded-lg text-white shadow-lg relative overflow-hidden">
				{/* 装饰性背景元素 */}
				<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/2 to-transparent" />
				<div className="absolute top-0 right-0 w-32 h-32 bg-white/3 rounded-full -translate-y-16 translate-x-16" />
				<div className="absolute bottom-0 left-0 w-24 h-24 bg-white/2 rounded-full translate-y-12 -translate-x-12" />

				{/* 内容区域 */}
				<div className="relative z-10">
                    {/* 修改块开始: 刷新时头部区域骨架屏 */}
                    {/**
                     * 头部局部骨架渲染
                     * @description 当 isRefreshingUI 为 true 时，仅替换头部选中区域为骨架样式。
                     *              骨架子项（公司名/股票代码/分析范围/生成时间+刷新按钮）与 CompanyOverviewSkeleton 保持一致风格。
                     * <AUTHOR>
                     * @date 2025-08-12
                     */}
					{isRefreshingUI ? (
						<div className="animate-pulse">
							<div className="flex flex-col md:flex-row md:items-start md:justify-between">
								<div>
									{/* 公司名称骨架（与 CompanyOverviewSkeleton 保持一致） */}
									<Skeleton className="h-8 w-64 mb-2 bg-white/20" />
									<div className="mt-1 flex items-center gap-2">
										{/* 股票代码骨架 */}
										<Skeleton className="h-6 w-16 bg-white/20 rounded-md" />
										{/* 分析范围骨架 */}
										<Skeleton className="h-4 w-48 bg-white/20" />
									</div>
								</div>
							</div>

							{/* 生成时间区域骨架 */}
							<div className="flex justify-end mt-[-24px] md:mt-[-20px]">
								<div className="flex items-center gap-1 text-sm">
									<Skeleton className="h-4 w-32 bg-white/20" />
									<Skeleton className="size-4 bg-white/20 rounded" />
								</div>
							</div>
						</div>
					) : (
						<>
							<div className="flex flex-col md:flex-row md:items-start md:justify-between">
								<div>
									<h2 className="text-2xl font-bold text-white">
										{data.companyName}
									</h2>
									<div className="mt-1 flex items-center gap-2">
										<span className="text-sm bg-white/8 px-2 py-1 rounded-md backdrop-blur-sm border border-white/10">
											{data.stockCode}
										</span>
										<span className="text-sm text-slate-300">
											分析范围: {data.analysisDateRange}
										</span>
									</div>
								</div>
							</div>

							{/* 生成时间单独放置，与股票代码和分析范围在同一水平线上 */}
							<div className="flex justify-end mt-[-24px] md:mt-[-20px]">
								<div className="flex items-center gap-1 text-sm">
									<span className="text-slate-300">
										生成时间: {data.generatedTime}
									</span>
									<Button
										variant="outline"
										size="icon"
										className="size-4 m-0 text-white border-white/20 hover:bg-white/10 hover:border-white/30 transition-all duration-200"
										onClick={handleRefreshAllData}
										disabled={isLoading || isRefreshingUI}
										aria-label="刷新公司概览数据"
										title={isLoading || isRefreshingUI ? "正在刷新数据..." : "刷新公司概览数据"}
									>
										<RefreshCcw
											className={cn(
												"size-4",
												(isLoading || isRefreshingUI) && "animate-spin",
											)}
										/>
									</Button>
								</div>
							</div>
						</>
					)}
					{/* 修改块结束: 刷新时头部区域骨架屏 */}
				</div>
			</div>
			<div>
				{/* 指标部分 - 响应式间距和网格 */}
				<div className="mt-4 sm:mt-6 mb-4 sm:mb-6 grid grid-cols-2 gap-1.5 sm:gap-2 md:grid-cols-3 lg:grid-cols-6">
					<MetricCard
						title="最新总户数"
						value={data.metrics.latestProduct.value}
						change={data.metrics.latestProduct.change}
					/>
					<MetricCard
						title="最新总股本 (万股)"
						value={data.metrics.latestTotalCapital.value}
						change={data.metrics.latestTotalCapital.changePercent}
					/>
					<MetricCard
						title="最新个人股东数"
						value={data.metrics.totalPersonalShareholders.value}
						change={data.metrics.totalPersonalShareholders.change}
					/>
					<MetricCard
						title="最新机构数"
						value={
							data.metrics.totalInstitutionalShareholders.value
						}
						change={
							data.metrics.totalInstitutionalShareholders.change
						}
					/>
					<MetricCard
						title="最新信用户数"
						value={data.metrics.totalShareholders.value}
						change={data.metrics.totalShareholders.change}
					/>
					<MetricCard
						title="最新机构持股"
						value={data.metrics.totalPersonalShares.value.toLocaleString()}
						change={data.metrics.totalPersonalShares.changePercent}
					/>
				</div>

				{/* 第二行指标 - 响应式间距和网格 */}
				<div className="mb-4 sm:mb-6 grid grid-cols-2 gap-1.5 sm:gap-2 md:grid-cols-3 lg:grid-cols-6">
					<MetricCard
						title="最新信用户持股"
						value={data.metrics.totalInstitutionalShares.value.toLocaleString()}
						change={
							data.metrics.totalInstitutionalShares.changePercent
						}
					/>
					<MetricCard
						title="最新户均持股"
						value={data.metrics.totalProductShares.value.toLocaleString()}
						change={data.metrics.totalProductShares.change}
					/>
					<MetricCard
						title="本期股价"
						value={parsedStockData.end_date_stock_price.toFixed(2)}
						change={(
							parsedStockData.end_date_stock_price -
							parsedStockData.start_date_stock_price
						).toFixed(2)}
						tooltipContent={`${parsedStockData.enddate} 收盘价`}
					/>
					<MetricCard
						title="上期股价"
						value={parsedStockData.start_date_stock_price.toFixed(
							2,
						)}
						tooltipContent={`${parsedStockData.startdate} 收盘价`}
					/>

					{/* {保留两位小数} */}
					{parsedStockData.average_stock_price ? (
						<MetricCard
							title="本期与上期期间均价"
							value={parsedStockData.average_stock_price.toFixed(
								2,
							)}
							tooltipContent={`${parsedStockData.startdate} - ${parsedStockData.enddate}日均收盘价，交易日${parsedStockData.trading_days_count}天`}
						/>
					) : (
						<MetricCard
							title="本期与上期期间均价"
							value="期间过大"
							tooltipContent="无法进行持仓成本和盈亏状态估算，请上传最近两期间隔不超过 30 个交易日的股东名册"
						/>
					)}
					<MetricCard
						title="最新股价"
						value={parsedStockData.latest_price.toFixed(2)}
						tooltipContent={`${getCurrentTime()}`}
					/>
				</div>

				{/* 图表部分 - 响应式间距 */}
				{/* 修改记录 2025-06-30 10:12:56 - 减少图表容器底部内边距，解决底部空白过多问题 - hayden */}
				<div className="grid grid-cols-1 gap-4 sm:gap-2 md:grid-cols-3">
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<ShareholdingDistributionChart
							controllingRatio={
								parsedApiData?.top1_shareholding_ratio || 0
							}
							top10Ratio={
								parsedApiData?.top10_shareholding_ratio || 0
							}
							top100Ratio={
								parsedApiData?.top100_shareholding_ratio || 0
							}
							top1ShareholdingAmount={
								parsedApiData?.top1_shareholding_amount || 0
							}
							top10ShareholdingAmount={
								parsedApiData?.top10_shareholding_amount || 0
							}
							top100ShareholdingAmount={
								parsedApiData?.top100_shareholding_amount || 0
							}
							top1RatioChange={
								parsedApiData?.top1_ratio_change || 0
							}
							top10RatioChange={
								parsedApiData?.top10_ratio_change || 0
							}
							top100RatioChange={
								parsedApiData?.top100_ratio_change || 0
							}
						/>
					</div>
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<InstitutionalSharesDistributionChart
						// 修改记录：2025/7/22 原代码弱警告 已修复问题 Miya
							shareholderTypes={
								
								parsedApiData?.shareholderTypes || []
							
							}
							mode="count"
						/>
					</div>
					<div className="rounded-lg border p-3 pb-2 sm:p-4 sm:pb-3">
						<InstitutionalSharesDistributionChart
						// 修改记录：2025/7/22 原代码弱警告 已修复问题 Miya
							shareholderTypes={
								
								parsedApiData?.shareholderTypes || []
							
							}
							mode="shares"
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
