/**
 * 股东名册分析 - 持股变动分析骨架屏组件
 * @file ShareholdingChangesSkeleton.tsx
 * @description 用于展示持股变动分析数据加载时的骨架屏，与 ShareholdingChanges 组件布局完全一致
 * @created 2025-06-17 12:20:47
 * <AUTHOR>
 */

"use client";

import { BarChart3Icon, InfoIcon } from "lucide-react";
import { Skeleton } from "@ui/components/skeleton";
import { Button } from "@ui/components/button";

/**
 * 表格骨架屏组件
 * @param type 股东类型（个人或机构）
 * @returns 表格骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-17 12:20:47
 */
function ShareholderTableSkeleton({ type }: { type: "个人" | "机构" }): JSX.Element {
	return (
		<div className="w-full space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="text-sm font-medium">{type}</h3>
			</div>
			<div className="rounded-md border">
				<div className="h-[300px] p-4">
					{/* 表头骨架 */}
					<div className="grid grid-cols-5 gap-4 mb-4 pb-2 border-b">
						<Skeleton className="h-4 w-16" />
						<Skeleton className="h-4 w-20" />
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-4 w-20" />
						<Skeleton className="h-4 w-16" />
					</div>
					
					{/* 表格内容骨架 - 显示5行 */}
					<div className="space-y-3">
						{Array.from({ length: 5 }).map((_, i) => (
							<div key={i} className="grid grid-cols-5 gap-4 items-center">
								<Skeleton className="h-4 w-12" />
								<Skeleton className="h-4 w-24" />
								<Skeleton className="h-4 w-20" />
								<Skeleton className="h-4 w-16" />
								<Skeleton className="h-4 w-20" />
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}

/**
 * 股东变动分析部分骨架屏组件
 * @param title 部分标题
 * @returns 股东变动分析部分骨架屏UI组件
 * <AUTHOR>
 * @created 2025-06-17 12:20:47
 */
function ShareholdingChangesSectionSkeleton({ title }: { title: string }): JSX.Element {
	return (
		<div className="space-y-4">
			<h2 className="text-lg font-semibold">{title}</h2>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<ShareholderTableSkeleton type="个人" />
				<ShareholderTableSkeleton type="机构" />
			</div>
		</div>
	);
}

/**
 * 持股变动分析骨架屏组件
 * @returns 持股变动分析骨架屏UI组件
 * @description 在组织ID未获取到时显示的加载状态，与ShareholdingChanges组件布局完全一致
 * <AUTHOR>
 * @created 2025-06-17 12:20:47
 */
export function ShareholdingChangesSkeleton(): JSX.Element {
	return (
		<div className="w-full">
			<div className="flex items-center gap-2">
				<BarChart3Icon className="size-5 text-primary" />
				<h2 className="text-lg font-semibold">持股变动分析</h2>
				<Button
					variant="ghost"
					size="sm"
					className="h-6 w-6 p-0 hover:bg-muted/50"
					disabled
				>
					<InfoIcon className="size-4 text-muted-foreground" />
				</Button>
			</div>
			<div className="space-y-6 mt-4">
				{/* 新进股东骨架 */}
				<ShareholdingChangesSectionSkeleton title="新进股东" />
				
				{/* 退出股东骨架 */}
				<ShareholdingChangesSectionSkeleton title="退出股东" />
				
				{/* 增持股东骨架 */}
				<ShareholdingChangesSectionSkeleton title="增持股东" />
				
				{/* 减持股东骨架 */}
				<ShareholdingChangesSectionSkeleton title="减持股东" />
			</div>
		</div>
	);
}
