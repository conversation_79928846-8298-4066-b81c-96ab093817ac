import React from "react";
import { useSingleShareholderAnalysis} from "@saas/shareholder/hooks";
import { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
// import { Alert, AlertDescription } from "@ui/components/alert"; // 2025-08-07 hayden 移除未使用的导入
import {  User, TrendingUp, BarChart3 } from "lucide-react";
import { MetricCard } from "@saas/shareholder/components/analyse/CompanyOverview";
import { formatNumber } from "@saas/shareholder/lib/ui-utils";
import { TrendChart } from "@saas/shareholder/components/analyse/TrendChart";
import { SingleShareholderSkeleton } from "@saas/shareholder/components/analyse/SingleShareholderSkeleton";

/**
 * 独立股东分析组件属性
 */
interface SingleShareholderAnalysisProps {
	/** 组织ID */
	organizationId?: string;
	/** 基金代码 */
	fund_code?: string;
	/** 一码通账户号码 */
	unified_account_number?: string;
    /** 股东类型 */
    shareholderType?: string;
    // 股价数据
    stockData?:any;
}

/**
 * 独立股东分析组件
 * @param props 组件属性
 * @returns JSX元素
 */
export function SingleShareholderAnalysis({ organizationId, fund_code, unified_account_number, shareholderType, stockData }: SingleShareholderAnalysisProps) {

	const { data, isLoading, error, refetch, isRefetching } =
		useSingleShareholderAnalysis(organizationId, fund_code, unified_account_number, {
			enabled: !!organizationId && (!!fund_code || !!unified_account_number),
			staleTime: 5 * 60 * 1000, // 5分钟缓存
		});



	if (isLoading) {
		// 加载状态 - 使用与实际布局相同的骨架屏
		return <SingleShareholderSkeleton shareholderType={shareholderType} />;
	}

	/**
	 * 统一的状态显示组件
	 * @param title 主标题
	 * @param description 描述文字
	 * @param showRetryButton 是否显示重试按钮
	 * @returns JSX元素
	 * <AUTHOR>
	 * @created 2025-08-11 - 统一错误和无数据状态的样式结构
	 */
	const renderStatusMessage = (title: string, description?: string, showRetryButton = false) => {
		return (
			<div>
				<CardContent>
					<div className="flex items-center justify-center min-h-[300px]">
						<div className="text-center text-muted-foreground">
							<p className="text-lg font-medium mb-2">{title}</p>
							{description && (
								<p className="text-sm mb-4">{description}</p>
							)}
							{showRetryButton && (
								<div className="flex justify-center">
									<Button
										variant="secondary"
										size="md"
										onClick={() => {
											refetch();
										}}
										disabled={isRefetching}
										loading={isRefetching}
									>
										{isRefetching ? "重试中..." : "重试"}
									</Button>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</div>
		);
	};

	if (error) {
		// 错误状态 - 2025-08-11 hayden 使用统一的状态显示组件
		return renderStatusMessage(
			"获取股东数据失败",
			error.message || "请检查网络连接或稍后重试",
			true
		);
	}

	if (!data?.data) {
		// 无数据状态 - 2025-08-11 hayden 使用统一的状态显示组件
		return renderStatusMessage(
			"暂无该股东的基金数据"
		);
	}

	function parseApiData(apiResponse: any): any {
		if (!apiResponse?.data) {
			return null;
		}

		try {
			// 新API直接返回单个股东数据对象，不需要复杂的解析
			const shareholderData = apiResponse.data;

			// 将新API字段映射为组件期望的字段名
			const mappedData = {
				// 基本信息字段映射
				securitiesAccountName: shareholderData.account_name,
				shareholderId: shareholderData.certificate_number,
				unifiedAccountNumber: shareholderData.unified_account_number,
				contactNumber: shareholderData.phone_number,
				shareholderType: shareholderData.shareholder_type,
				shareholderCategory: shareholderData.shareholder_category,
				contactAddress: shareholderData.contact_address,
				cashAccount: shareholderData.cash_account,
				sharesInCashAccount: shareholderData.cash_account_shares,
				marginAccount: shareholderData.margin_account,
				sharesInMarginAccount: shareholderData.margin_account_shares,

				// 持股信息字段映射
				currentShares: shareholderData.current_shares,
				currentRatio: shareholderData.current_ratio,
				prevShares: shareholderData.prev_shares,
				prevRatio: shareholderData.prev_ratio,
				sharesChange: shareholderData.shares_change,
				ratioChange: shareholderData.ratio_change,
				ranking: shareholderData.holding_rank,
				// 排名变动分析字段映射 - 2025-08-07 hayden 新增
				prevRanking: shareholderData.previous_holding_rank,
				rankChange: shareholderData.rank_change,
				rankChangeDescription: shareholderData.rank_change_description,

				// 冻结和限售股信息
				frozenShares: shareholderData.company_frozen_shares,
				lockedUpShares: shareholderData.company_locked_shares,
				frozenRatioPercent: shareholderData.company_frozen_ratio_percent,
				lockedRatioPercent: shareholderData.company_locked_ratio_percent,

				// 日期信息
				latestDate: shareholderData.latest_period_date,
				registerDate: shareholderData.latest_period_date,

				// 趋势数据映射
				trendData: shareholderData.all_periods_holdings || [],

				// 新增的财务数据
				latestMarketValue: shareholderData.latest_market_value,
				latestHoldingProfit: shareholderData.latest_holding_profit,
				estimatedTotalCost: shareholderData.estimated_total_cost,
				latestPrice: shareholderData.latest_price,
				averageStockPrice: shareholderData.average_stock_price,
				shareholderBehavior: shareholderData.shareholder_behavior,
			};

			return mappedData;
		} catch (error) {
			console.error("解析API数据失败:", error);
			return null;
		}
	}

	const shareholderData = parseApiData(data);

	/**
	 * 将英文股东行为类型转换为中文
	 * @param type 英文股东行为类型
	 * @returns 中文股东行为类型
	 * <AUTHOR>
	 * @created 2025-08-08
	 */
	const translateShareholderType = (type: string): string => {
		const typeMap: Record<string, string> = {
			'new': '新进',
			'exit': '退出',
			'increase': '增持',
			'constant': '不动',
			'decrease': '减持' // 添加减持类型以防万一
		};
		return typeMap[type] || type;
	};

	/**
	 * 获取股东行为类型，优先使用shareholderType，其次使用shareholderBehavior
	 * @returns 股东行为类型字符串（中文）
	 * <AUTHOR>
	 * @created 2025-08-08
	 * @modified 2025-08-08 - 添加英文到中文的转换逻辑
	 */
	const getShareholderBehavior = (): string => {
		if (shareholderType) {
			return translateShareholderType(shareholderType);
		}
		return shareholderData?.shareholderBehavior || "-";
	};

	/**
	 * 转换股东趋势数据为图表数据格式
	 * @param trendData 股东趋势数据数组
	 * @returns 图表数据数组
	 * <AUTHOR>
	 * @created 2025-07-29
	 * @modified 2025-08-06 - 适配新API数据结构
	 */
	function transformTrendDataToChartData(
		trendData: any[],
	): Array<{ time: string; value: number; ratio: string; rawData: any }> {
		if (!trendData || !Array.isArray(trendData)) {
			return [];
		}

		// 先转换数据，然后按日期正序排序
		const transformedData = trendData.map((item) => ({
			time: item.registerDate ? item.registerDate.split("T")[0] : "",
			value: Number(item.numberOfShares) || 0, // 新API返回的是number类型
			ratio: String(item.shareholdingRatio) || "0", // 转换为字符串格式
			rawData: item, // 保存原始数据用于悬浮提示
		}));

		// 按日期正序排序（早到晚）
		return transformedData.sort((a, b) => {
			const dateA = new Date(a.time);
			const dateB = new Date(b.time);
			return dateA.getTime() - dateB.getTime();
		});
	}

	// 转换趋势数据
	const chartData = transformTrendDataToChartData(shareholderData?.trendData || []);

	/**
	 * 自定义悬浮提示格式化函数
	 * @param data 图表数据点
	 * @returns 格式化的悬浮提示内容
	 * <AUTHOR>
	 * @created 2025-07-29
	 */
	const customTooltipFormatter = (data: any) => {
		return (
			<div className="space-y-1">
				<div className="text-sm text-foreground">
					持股数量：{Number(data.value).toLocaleString()} 股
				</div>
				<div className="text-sm text-foreground">
					持股比例：{Number(data.ratio).toFixed(2)}%
				</div>
			</div>
		);
	};

	return (
		<div className="space-y-6">
			{/* 最新持仓分析 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<TrendingUp className="h-5 w-5" />
						最新持仓分析
					</CardTitle>
				</CardHeader>
				<div className="grid p-6 grid-cols-2 gap-1.5 sm:gap-2 md:gap-3">
					<MetricCard
						title="期数"
						value={shareholderData.latestDate.split("T")[0]}
					/>
					<MetricCard
						title="股东行为"
						value={getShareholderBehavior()}
					/>

					{getShareholderBehavior() !== "退出" && (
						<>
							<MetricCard
								title="持股"
								value={`持股: ${formatNumber(shareholderData.currentShares)} | 比例: ${shareholderData.currentRatio} | 持股变化 ${shareholderData.sharesChange} | 比例变化 ${shareholderData.ratioChange}`}
							/>
							{/* 2025-08-11 hayden 修改：新进和退出股东行为不显示持股排名 */}
							{getShareholderBehavior() !== "新进" && getShareholderBehavior() !== "退出" && (
								<MetricCard
									title="持股排名 (位)"
									value={shareholderData.ranking || "-"}
									change={
										shareholderData.rankChange <= 0
											? "0"
											: shareholderData.rankChange
									}
								/>
							)}
							<MetricCard
								title="最新持仓市值"
								value={
									shareholderData.currentShares *
									stockData.latest_price.toFixed(2)
								}
								tooltipContent="最新股价 × 本期持仓数量"
							/>
							{getShareholderBehavior() === "新进" && (
								<MetricCard
									title="最新持仓收益"
									value={formatNumber(
										shareholderData.currentShares *
											stockData.latest_price -
											shareholderData.currentShares *
												stockData.average_stock_price,
									)}
									tooltipContent="最新持仓市值 × 持仓成本"
								/>
							)}
							{getShareholderBehavior() === "增持" && (
								<MetricCard
									title="最新持仓收益"
									value={formatNumber(
										shareholderData.currentShares *
											stockData.latest_price -
											(Number.parseInt(
												shareholderData.sharesChange,
											) *
												stockData.average_stock_price +
												Number.parseInt(
													shareholderData.prevShares,
												) *
													stockData.start_date_stock_price),
									)}
									tooltipContent="最新股价 × 本期持仓数量"
								/>
							)}
							{getShareholderBehavior() === "减持" && (
								<MetricCard
									title="最新持仓收益"
									value={formatNumber(
										shareholderData.currentShares *
											(stockData.latest_price -
												stockData.start_date_stock_price),
									)}
									tooltipContent="最新股价 × 本期持仓数量"
								/>
							)}
							{getShareholderBehavior() === "不动" && (
								<MetricCard
									title="最新持仓收益"
									value={formatNumber(
										shareholderData.currentShares *
											(stockData.latest_price -
												stockData.start_date_stock_price),
									)}
									tooltipContent="最新股价 × 本期持仓数量"
								/>
							)}

							<MetricCard
								title="冻结股数"
								value={Number(
									shareholderData.frozenShares || 0,
								).toLocaleString()}
								ratio={`${Number.parseFloat(shareholderData.frozenRatioPercent || "0").toFixed(2)}`}
								tooltipContent="冻结股数与占总股本比例"
							/>
							<MetricCard
								title="限售股数"
								value={Number(
									shareholderData.lockedUpShares || 0,
								).toLocaleString()}
								ratio={`${Number.parseFloat(shareholderData.lockedRatioPercent || "0").toFixed(2)}`}
								tooltipContent="限售股数与占总股本比例"
							/>
						</>
					)}
					{getShareholderBehavior() === "退出" && (
						<>
							<MetricCard
								title="持股"
								value={`持股: ${formatNumber(shareholderData.currentShares)} | 比例: ${shareholderData.ratioChange} | 持股变化 ${shareholderData.sharesChange} | 比例变化 ${shareholderData.ratioChange}`}
							/>
							<MetricCard
								title="预估持仓成本(元)"
								value={formatNumber(
									shareholderData.prevShares *
										stockData.start_date_stock_price,
								)}
								tooltipContent="上期股价 × 退出前持股数"
							/>
							<MetricCard
								title="预估退出总额（元）"
								value={formatNumber(
									shareholderData.prevShares *
										stockData.average_stock_price,
								)}
								tooltipContent="期间均价 × 退出前持股数"
							/>
							<MetricCard
								title="预估退出收益（元）"
								value={formatNumber(
									shareholderData.prevShares *
										(stockData.average_stock_price -
											stockData.start_date_stock_price),
								)}
								tooltipContent="(期间均价 - 上期股价) × 退出前持股数"
							/>
						</>
					)}
				</div>
			</div>

			{/* 股东持股趋势分析图表 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BarChart3 className="h-5 w-5" />
						股东持股趋势分析
					</CardTitle>
				</CardHeader>
				<div className="p-6">
					{chartData.length > 0 ? (
						<TrendChart
							data={chartData}
							color="#3b82f6"
							title="持股数量变化"
							unit="股"
							height={160}
							customTooltipFormatter={customTooltipFormatter}
						/>
					) : (
						<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
							<h4 className="mb-4 font-medium text-sm text-foreground/80">
								持股数量变化
							</h4>
							<div style={{ height: "160px" }}>
								<div className="flex items-center justify-center h-full">
									<div className="text-center">
										<div className="text-muted-foreground text-sm mb-2">
											暂无趋势数据
										</div>
										<div className="text-xs text-muted-foreground/60">
											该股东暂无历史持股变化记录
										</div>
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* 基本信息表格 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<User className="h-5 w-5" />
						基本信息
					</CardTitle>
				</CardHeader>
				<div className="p-6 overflow-x-auto">
					<table className="w-full border-collapse">
						<tbody className="divide-y divide-border">
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 w-1/3 leading-none">
									账户名称
								</td>
								<td className="py-3 px-4 text-sm leading-none">
									{shareholderData.securitiesAccountName ||
										"-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									证件号码
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.shareholderId || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									一码通号码
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.unifiedAccountNumber ||
										"-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									电话号码
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.contactNumber || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									股东类型
								</td>
								<td className="py-3 px-4 text-sm leading-none">
									{shareholderData.shareholderType || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									持有人类别
								</td>
								<td className="py-3 px-4 text-sm leading-none">
									{shareholderData.shareholderCategory || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									通讯地址
								</td>
								<td className="py-3 px-4 text-sm leading-none">
									{shareholderData.contactAddress || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									普通账号
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.cashAccount || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									普通账号持股(股)
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.sharesInCashAccount
										? Number(
												shareholderData.sharesInCashAccount,
											).toLocaleString()
										: "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									信用账号
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.marginAccount || "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									信用账号持股(股)
								</td>
								<td className="py-3 px-4 text-sm font-mono leading-none">
									{shareholderData.sharesInMarginAccount
										? Number(
												shareholderData.sharesInMarginAccount,
											).toLocaleString()
										: "-"}
								</td>
							</tr>
							<tr className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
								<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 leading-none">
									更新期数
								</td>
								<td className="py-3 px-4 text-sm leading-none">
									{shareholderData.registerDate.split(
										"T",
									)[0] || "-"}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
    }


