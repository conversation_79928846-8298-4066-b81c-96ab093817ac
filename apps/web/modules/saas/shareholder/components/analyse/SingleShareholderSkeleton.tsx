/**
 * 独立股东分析组件骨架屏
 * @file SingleShareholderSkeleton.tsx
 * @description 为独立股东分析组件提供与实际布局完全相同的加载状态骨架屏
 * <AUTHOR>
 * @created 2025-08-06 20:45:47
 * @modified 2025-08-07 10:58:50 - 更新布局以匹配使用 shareholderBehavior 的新逻辑
 */

import React from "react";
import { CardHeader, CardTitle } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { TrendingUp, BarChart3, User } from "lucide-react";

/**
 * 独立股东分析骨架屏组件属性
 * @interface SingleShareholderSkeletonProps
 * <AUTHOR>
 * @created 2025-08-06 20:45:47
 * @modified 2025-08-07 10:58:50 - 更新为使用 shareholderBehavior 字段
 */
interface SingleShareholderSkeletonProps {
	/** 股东行为，用于决定显示哪些骨架屏元素 */
	shareholderBehavior?: string;
	/** 兼容旧的 shareholderType 参数 */
	shareholderType?: string;
}

/**
 * 指标卡片骨架屏组件
 * @description 模拟 MetricCard 组件的骨架屏
 * <AUTHOR>
 * @created 2025-08-06 20:45:47
 */
function MetricCardSkeleton() {
	return (
		<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
			<div className="space-y-2">
				<Skeleton className="h-4 w-16" />
				<Skeleton className="h-6 w-24" />
			</div>
		</div>
	);
}

/**
 * 独立股东分析骨架屏组件
 * @param props 组件属性
 * @returns JSX元素
 * <AUTHOR>
 * @created 2025-08-06 20:45:47
 * @modified 2025-08-07 10:58:50 - 更新为使用 shareholderBehavior 字段
 */
export function SingleShareholderSkeleton({
	shareholderBehavior,
	shareholderType = "new"
}: SingleShareholderSkeletonProps) {
	// 优先使用 shareholderBehavior，如果没有则使用 shareholderType 作为兼容
	const behavior = shareholderBehavior || (shareholderType === "exit" ? "退出" : "新增");
	return (
		<div className="space-y-6">
			{/* 最新持仓分析骨架屏 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<TrendingUp className="h-5 w-5" />
						最新持仓分析
					</CardTitle>
				</CardHeader>
				<div className="grid p-6 grid-cols-2 gap-1.5 sm:gap-2 md:gap-3">
					{/* 期数和股东行为 - 2025-08-07 hayden 更新注释 */}
					<MetricCardSkeleton />
					<MetricCardSkeleton />

					{/* 根据股东行为显示不同的骨架屏 - 2025-08-07 hayden 更新逻辑 */}
					{behavior !== "退出" && (
						<>
							{/* 持股信息 */}
							<MetricCardSkeleton />
							{/* 持股排名 */}
							<MetricCardSkeleton />
							{/* 最新持仓市值 */}
							<MetricCardSkeleton />
							{/* 最新持仓收益 - 根据不同行为显示 */}
							<MetricCardSkeleton />
							{/* 冻结股数 */}
							<MetricCardSkeleton />
							{/* 限售股数 */}
							<MetricCardSkeleton />
						</>
					)}
					{behavior === "退出" && (
						<>
							{/* 退出股东的特殊指标 */}
							<MetricCardSkeleton /> {/* 持股 */}
							<MetricCardSkeleton /> {/* 预估持仓成本 */}
							<MetricCardSkeleton /> {/* 预估退出总额 */}
							<MetricCardSkeleton /> {/* 预估退出收益 */}
						</>
					)}
				</div>
			</div>

			{/* 股东持股趋势分析图表骨架屏 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<BarChart3 className="h-5 w-5" />
						股东持股趋势分析
					</CardTitle>
				</CardHeader>
				<div className="p-6">
					<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
						<Skeleton className="h-4 w-24 mb-4" />
						<div style={{ height: "160px" }}>
							<div className="flex items-end justify-between h-full px-4 pb-4">
								{/* 模拟图表柱状图 */}
								{Array.from({ length: 8 }).map((_, index) => (
									<Skeleton
										key={index}
										className="w-8"
										style={{
											height: `${Math.random() * 80 + 40}px`,
										}}
									/>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* 基本信息表格骨架屏 */}
			<div>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<User className="h-5 w-5" />
						基本信息
					</CardTitle>
				</CardHeader>
				<div className="p-6 overflow-x-auto">
					<table className="w-full border-collapse">
						<tbody className="divide-y divide-border">
							{/* 表格行骨架屏 */}
							{[
								"账户名称",
								"证件号码", 
								"一码通号码",
								"电话号码",
								"股东类型",
								"持有人类别",
								"通讯地址",
								"普通账号",
								"普通账号持股(股)",
								"信用账号",
								"信用账号持股(股)",
								"更新期数"
							].map((label, index) => (
								<tr key={index} className="h-12 odd:bg-white even:bg-muted/30 dark:odd:bg-background dark:even:bg-muted/20">
									<td className="py-3 px-4 text-sm font-medium text-muted-foreground bg-muted/30 w-1/3 leading-none">
										{label}
									</td>
									<td className="py-3 px-4 text-sm leading-none">
										<Skeleton className="h-4 w-32" />
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
}
