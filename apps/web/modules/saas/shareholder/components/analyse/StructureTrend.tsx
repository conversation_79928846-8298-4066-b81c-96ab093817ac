/**
 * 股东名册分析 - 结构趋势分析组件
 * @file StructureTrend.tsx
 * @description 用于展示公司股东结构的趋势变化分析，每个类别包含户数和持股两个图表
 * <AUTHOR>
 * @created 2025-06-12 17:48:44
 * @updated 2025-06-16 17:16:55 - 在第五行添加细分机构股东的多柱状图显示，使用待开发占位
 * @updated 2025-06-18 16:20:06 - 将柱状图改为组合图表，在柱状图上添加线条显示
 */

"use client";
import { InfoIcon, TrendingUpIcon } from "lucide-react";
import { useMemo } from "react";
import {
	Tooltip as UITooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
	TooltipPortal,
} from "@ui/components/tooltip";
import { useShareholdersTrend } from "@saas/shareholder/hooks";
import type { ShareholdersTrendDataItem } from "@saas/shareholder/lib/shareholders-trend-api";
import { StructureTrendSkeleton } from "./StructureTrendSkeleton";
import { InstitutionalShareholdersChart } from "./InstitutionalShareholdersChart";
import { TrendChart } from "./TrendChart";

/**
 * 图表数据类型定义
 * @interface ChartDataPoint
 */
interface ChartDataPoint {
	/** 时间标签 */
	time: string;
	/** 数值 */
	value: number;
}

/**
 * 图表配置类型定义
 * @interface ChartConfig
 */
interface ChartConfig {
	/** 图表标题 */
	title: string;
	/** 图表数据 */
	data: ChartDataPoint[];
	/** 线条颜色 */
	color: string;
	/** 图表类型（户数/持股） */
	type: "户数" | "持股";
	/** 数值单位 */
	unit: string;
}

/**
 * 股东类别配置类型定义
 * @interface CategoryConfig
 * <AUTHOR>
 * @modified 2025-06-27 19:31:04 - 添加useMultiBarChart标记支持多柱状图
 */
interface CategoryConfig {
	/** 类别名称 */
	name: string;
	/** 户数图表配置 */
	accountChart: Omit<ChartConfig, "title" | "type">;
	/** 持股图表配置 */
	holdingChart: Omit<ChartConfig, "title" | "type">;
	/** 是否显示提示信息 */
	showTooltip?: boolean;
	/** 提示信息内容 */
	tooltipContent?: string;
	/** 是否使用多柱状图组件 */
	useMultiBarChart?: boolean;
}

/**
 * 格式化日期为显示格式
 * @param dateString ISO 日期字符串
 * @returns 格式化后的日期字符串
 */
function formatDateForChart(dateString: string): string {
	const date = new Date(dateString);
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	return `${year}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
}

/**
 * 转换API数据为图表数据格式
 * @param trendData API返回的趋势数据数组
 * @param dataKey 要提取的数据字段名
 * @returns 图表数据数组
 */
function transformApiDataToChartData(
	trendData: ShareholdersTrendDataItem[],
	dataKey: keyof ShareholdersTrendDataItem,
): ChartDataPoint[] {
	return trendData.map((item) => ({
		time: formatDateForChart(item.registerDate),
		value: Number(item[dataKey]) || 0,
	}));
}



/**
 * 结构趋势分析组件属性接口
 * @interface StructureTrendProps
 */
interface StructureTrendProps {
	/** 组织ID，可选参数，用于获取真实数据 */
	organizationId?: string;
}

/**
 * 结构趋势分析组件
 * @param props 组件属性
 * @returns 结构趋势分析UI组件
 */
export function StructureTrend({
	organizationId,
}: StructureTrendProps = {}): JSX.Element {
	// 获取股东结构历史趋势数据
	const {
		data: trendData,
		isLoading,
		error,
	} = useShareholdersTrend(organizationId || "", {
		enabled: !!organizationId, // 只有当 organizationId 存在时才启用查询
	});
	// 解析和处理API返回的数据
	const parsedTrendData = useMemo(() => {
		if (!trendData?.data) {
			return null;
		}

		try {
			// 如果 data 是字符串，需要解析
			let parsedData: any;
			if (typeof trendData.data === "string") {
				parsedData = JSON.parse(trendData.data);
			} else {
				parsedData = trendData.data;
			}

			// 处理数组格式的响应
			if (Array.isArray(parsedData)) {
				// 取第一个元素的 data.trendData
				const firstItem = parsedData[0];
				if (firstItem?.success && firstItem?.data?.trendData) {
					return firstItem.data.trendData;
				}
			} else if (parsedData?.trendData) {
				// 直接返回 trendData
				return parsedData.trendData;
			}

			return null;
		} catch (error) {
			console.error("解析趋势数据失败:", error);
			return null;
		}
	}, [trendData]);

	// 使用 useMemo 缓存股东类别配置数据，基于真实API数据
	const categoriesData = useMemo<CategoryConfig[]>(() => {
		// 如果没有数据，返回空数组
		if (!parsedTrendData || parsedTrendData.length === 0) {
			return [];
		}

		const apiData = parsedTrendData;

		return [
			{
				name: "全部股东",
				accountChart: {
					data: transformApiDataToChartData(
						apiData,
						"total_shareholders",
					),
					color: "#3b82f6",
					unit: "户",
				},
				holdingChart: {
					data: transformApiDataToChartData(apiData, "total_shares"),
					color: "#1e40af",
					unit: "股",
				},
			},
			{
				name: "信用股东",
				accountChart: {
					data: transformApiDataToChartData(
						apiData,
						"credit_shareholders",
					),
					color: "#10b981",
					unit: "户",
				},
				holdingChart: {
					data: transformApiDataToChartData(apiData, "credit_shares"),
					color: "#059669",
					unit: "股",
				},
				showTooltip: true,
				tooltipContent:
					"本数据基于用户上传的股东名册，未必包含全量信用股东",
			},
			{
				name: "个人股东",
				accountChart: {
					data: transformApiDataToChartData(
						apiData,
						"individual_shareholders",
					),
					color: "#8b5cf6",
					unit: "户",
				},
				holdingChart: {
					data: transformApiDataToChartData(
						apiData,
						"individual_shares",
					),
					color: "#7c3aed",
					unit: "股",
				},
			},
			{
				name: "机构股东",
				accountChart: {
					data: transformApiDataToChartData(
						apiData,
						"institutional_shareholders",
					),
					color: "#f59e0b",
					unit: "户",
				},
				holdingChart: {
					data: transformApiDataToChartData(
						apiData,
						"institutional_shares",
					),
					color: "#d97706",
					unit: "股",
				},
			},
			// {
			// 	name: "细分机构股东（TOP200）",
			// 	accountChart: {
			// 		// 使用多柱状图组件，不再使用传统图表数据
			// 		data: [],
			// 		color: "#ef4444",
			// 		unit: "户",
			// 	},
			// 	holdingChart: {
			// 		// 使用多柱状图组件，不再使用传统图表数据
			// 		data: [],
			// 		color: "#dc2626",
			// 		unit: "股",
			// 	},
			// 	// 标记为使用多柱状图
			// 	useMultiBarChart: true,
			// },
			// {
			// 	name: "持股集中度",
			// 	accountChart: {
			// 		data: transformApiDataToChartData(
			// 			apiData,
			// 			"top10_shareholding_amount_ratio",
			// 		),
			// 		color: "#06b6d4",
			// 		unit: "%",
			// 	},
			// 	holdingChart: {
			// 		data: transformApiDataToChartData(
			// 			apiData,
			// 			"top20_shareholding_amount_ratio",
			// 		),
			// 		color: "#0891b2",
			// 		unit: "%",
			// 	},
			// },
		];
	}, [parsedTrendData]);



	/**
	 * 渲染股东类别行组件
	 * @param category 股东类别配置
	 * @param index 类别索引
	 * @returns 类别行JSX元素
	 * <AUTHOR>
	 * @modified 2025-06-27 19:31:04 - 添加多柱状图支持
	 */
	const renderCategoryRow = (category: CategoryConfig, index: number) => (
		<div key={index} className="mb-6">
			<div className="flex items-center gap-2 mb-4">
				<h3 className="text-base font-semibold text-foreground">
					{category.name}
				</h3>
				{category.showTooltip && (
					<TooltipProvider delayDuration={0}> {/* 0延迟 - 修改于2025-08-05，修改人：miya */}
						<UITooltip>
							<TooltipTrigger asChild>
								<span className="cursor-default">
									<InfoIcon className="h-4 w-4 text-muted-foreground hover:text-foreground" />
								</span>
							</TooltipTrigger>
							<TooltipPortal>
								{/* 右侧弹出，白底黑字，适配暗主题 - 修改于2025-08-05，修改人：miya */}
								<TooltipContent side="right" className="max-w-auto bg-white text-black border-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600">
									{category.tooltipContent}
								</TooltipContent>
							</TooltipPortal>
						</UITooltip>
					</TooltipProvider>
				)}
			</div>
			<div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
				{category.useMultiBarChart && parsedTrendData ? (
					// 使用多柱状图组件
					<>
						<InstitutionalShareholdersChart
							trendData={parsedTrendData}
							mode="count"
							title="户数"
							height={160}
						/>
						<InstitutionalShareholdersChart
							trendData={parsedTrendData}
							mode="shares"
							title="持股数量"
							height={160}
						/>
					</>
				) : (
					// 使用传统图表
					<>
						<TrendChart
							data={category.accountChart.data}
							color={category.accountChart.color}
							title="户数"
							unit={category.accountChart.unit}
							height={160}
						/>
						<TrendChart
							data={category.holdingChart.data}
							color={category.holdingChart.color}
							title="持股"
							unit={category.holdingChart.unit}
							height={160}
						/>
					</>
				)}
			</div>
		</div>
	);

	// 加载状态
	if (isLoading) {
		return (
				<StructureTrendSkeleton />
		);
	}

	// 错误状态
	if (error) {
		return (
			<div className="w-full">
				<div className="flex items-center gap-2 mb-6">
					<TrendingUpIcon className="size-5 text-primary" />
					<h2 className="text-lg font-semibold">股东结构趋势分析</h2>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-muted-foreground">
						加载失败: {error.message}
					</div>
				</div>
			</div>
		);
	}

	// 无数据状态
	if (categoriesData.length === 0) {
		return (
			<div className="w-full">
				<div className="flex items-center gap-2 mb-6">
					<TrendingUpIcon className="size-5 text-primary" />
					<h2 className="text-lg font-semibold">股东结构趋势分析</h2>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-muted-foreground">
						暂无股东结构趋势数据
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="w-full">
			<div className="flex items-center gap-2 mb-6">
				<TrendingUpIcon className="size-5 text-primary" />
				<h2 className="text-lg font-semibold">股东结构趋势分析</h2>
			</div>
			<div className="space-y-6">
				{categoriesData.map((category, index) =>
					renderCategoryRow(category, index),
				)}
			</div>
		</div>
	);
}
