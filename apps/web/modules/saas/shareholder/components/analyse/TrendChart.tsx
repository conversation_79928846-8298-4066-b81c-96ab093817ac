/**
 * 通用趋势图表组件
 * @file TrendChart.tsx
 * @description 用于展示股东结构趋势的通用图表组件，支持组合图表（柱状图+线图）
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */

"use client";
import {
	Bar,
	ComposedChart,
	Line,
	XAxis,
	YAxis,
	Tooltip,
	ResponsiveContainer,
} from "recharts";
import { useTheme } from "next-themes";

/**
 * 图表数据类型定义
 * @interface ChartDataPoint
 */
interface ChartDataPoint {
	/** 时间标签 */
	time: string;
	/** 数值 */
	value: number;
	/** 可选的比例数据 */
	ratio?: string;
	/** 可选的原始数据，用于自定义悬浮提示 */
	rawData?: any;
}

/**
 * 通用趋势图表组件属性接口
 * @interface TrendChartProps
 */
interface TrendChartProps {
	/** 图表数据 */
	data: ChartDataPoint[];
	/** 图表颜色 */
	color: string;
	/** 图表标题 */
	title: string;
	/** 数值单位 */
	unit: string;
	/** 图表高度，默认160px */
	height?: number;
	/** 可选的自定义悬浮提示渲染函数 */
	customTooltipFormatter?: (data: ChartDataPoint) => React.ReactNode;
}

/**
 * 格式化数值显示
 * @param value 数值
 * @param unit 单位
 * @returns 格式化后的字符串
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 */
function formatValue(value: number, unit: string): string {
	if (unit === "户") {
		// 户数格式化：大于1000显示为千位
		if (value >= 1000) {
			const formatted = (value / 1000).toFixed(1);
			// 去掉不必要的.0
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}k`;
		}
		return value.toString();
	}
	if (unit === "%") {
		// 百分比保留1位小数，去掉不必要的.0
		const formatted = value.toFixed(1);
		return formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted;
	}
	if (unit === "股") {
		// 股数格式化：大于1万显示为万，大于1亿显示为亿
		if (value >= 100000000) {
			const formatted = (value / 100000000).toFixed(1);
			// 去掉不必要的.0
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}亿`;
		}
		if (value >= 10000) {
			const formatted = (value / 10000).toFixed(1);
			// 去掉不必要的.0
			return `${formatted.endsWith(".0") ? formatted.slice(0, -2) : formatted}万`;
		}
		return value.toString();
	}
	// 其他情况保留2位小数，去掉不必要的.00
	const formatted = value.toFixed(2);
	if (formatted.endsWith(".00")) {
		return formatted.slice(0, -3);
	}
	if (formatted.endsWith("0")) {
		return formatted.slice(0, -1);
	}
	return formatted;
}

/**
 * 通用趋势图表组件
 * @param props 组件属性
 * @returns 趋势图表JSX元素
 * <AUTHOR>
 * @created 2025-06-27 19:31:04
 * @modified 2025-06-30 10:30:57 - hayden - 添加主题适配，XAxis和YAxis颜色根据主题自动切换
 */
export function TrendChart({
	data,
	color,
	title,
	unit,
	height = 160,
	customTooltipFormatter,
}: TrendChartProps): JSX.Element {
	// 获取当前主题
	const { resolvedTheme } = useTheme();

	// 根据主题设置坐标轴颜色
	const axisColor = resolvedTheme === "dark" ? "#ffffff" : "#000000";
	return (
		<div className="rounded-lg bg-card p-3 pb-2 shadow-sm border border-border">
			<h4 className="mb-4 font-medium text-sm text-foreground/80">
				{title}
			</h4>
			<div style={{ height: `${height}px` }}>
				{data.length === 0 ? (
					// 待开发占位显示
					<div className="flex items-center justify-center h-full">
						<div className="text-center">
							<div className="text-muted-foreground text-sm mb-2">
								功能开发中
							</div>
							<div className="text-xs text-muted-foreground/60">
								敬请期待
							</div>
						</div>
					</div>
				) : (
					<ResponsiveContainer width="100%" height="100%">
						<ComposedChart
							data={data}
							margin={{ top: 10, right: 10, left: 10, bottom: 5 }}
						>
							<XAxis
								dataKey="time"
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: 11,
									fill: axisColor,
								}}
								className="text-sm text-foreground/80"
							/>
							<YAxis
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: 11,
									fill: axisColor,
								}}
								className="text-sm text-foreground/80"
								width={60}
								tickFormatter={(value) =>
									formatValue(value, unit)
								}
							/>
							<Tooltip
								cursor={false}
								contentStyle={{
									border: "1px solid hsl(var(--border))",
									borderRadius: "8px",
									boxShadow:
										"0 4px 6px -1px rgb(0 0 0 / 0.1)",
									fontSize: "12px",
								}}
								labelStyle={{ color: "hsl(var(--foreground))" }}
								content={customTooltipFormatter ? ({ active, payload, label }) => {
									if (active && payload && payload.length) {
										const dataPoint = payload[0].payload as ChartDataPoint;
										return (
											<div className="bg-background border border-border rounded-lg p-3 shadow-lg">
												<div className="text-sm font-medium text-foreground mb-1">
													{label}
												</div>
												{customTooltipFormatter(dataPoint)}
											</div>
										);
									}
									return null;
								} : undefined}
								formatter={!customTooltipFormatter ? (value: number) => [
									`${formatValue(value, unit)}${unit}`,
									title,
								] : undefined}
							/>
							<Bar
								dataKey="value"
								fill={color}
								radius={[2, 2, 0, 0]}
								opacity={0.8}
								barSize={20}
							/>
							<Line
								type="monotone"
								dataKey="value"
								stroke={color}
								strokeWidth={1}
								dot={{ fill: color, strokeWidth: 1, r: 2 }}
								activeDot={{ r: 5, fill: color }}
								tooltipType="none"
							/>
						</ComposedChart>
					</ResponsiveContainer>
				)}
			</div>
		</div>
	);
}
