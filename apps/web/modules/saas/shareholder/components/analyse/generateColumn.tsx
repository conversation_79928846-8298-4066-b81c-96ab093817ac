/**
 * 股东表格列配置生成器
 * @file generateColumns.tsx
 * @description 用于生成股东表格的列配置，从ShareholdingChanges.tsx中提取
 * @created 2025-07-24
 * <AUTHOR>
 */
import React, { useState } from "react";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
    TooltipPortal,
} from "@ui/components/tooltip";
import type {  ShareholderColumn } from "@saas/shareholder/components/ant-shareholder-table";
import { InvestorDialog } from "@saas/investors/components/Dialog";
import { useFundInstitution } from "@saas/shareholder/hooks/useFundInstitution";


/**
 * 股东变动类型枚举
 */
export type ShareholderChangeType = 'new' | 'exit' | 'increase' | 'decrease' | 'constant';

/**
 * 股东名称单元格组件
 * 提取到外部避免在render函数中重复创建组件导致状态重置
 */
interface ShareholderNameCellProps {
    value: string;
    record: any;
    organizationId?: string;
    changeType?: ShareholderChangeType;
    stockData?: any;
}

function ShareholderNameCell({ value, record, organizationId, changeType, stockData }: ShareholderNameCellProps) {
    const [dialogOpen, setDialogOpen] = useState(false);

    /**
     * 股东名称查询基金代码的Hook
     * <AUTHOR>
     * @created 2025-08-07 15:44:20 - 添加股东名称查询基金代码功能，用于获取基金详情
     */
    const fundInstitution = organizationId ? useFundInstitution(organizationId) : null;
    const shareholderNameQuery = fundInstitution?.shareholderNameQuery({
        onSuccess: (_data) => {
            // 修改记录 2025-08-07 16:08:19 hayden: 处理API返回的数组格式数据
            // const fundInfo = Array.isArray(data.data) ? data.data[0] : data.data;
            // console.log("股东名称查询基金代码成功:", {
            //     shareholderName: value,
            //     fundCode: fundInfo?.fund_code,
            //     fundName: fundInfo?.fund_name,
            //     queryTimestamp: fundInfo?.query_timestamp,
            //     fullData: data // 显示完整数据结构用于调试
            // });
        },
        onError: (error) => {
            console.error("股东名称查询基金代码失败:", {
                shareholderName: value,
                error: error.message
            });
        },
    });

    /**
     * 根据查询到的基金代码构造虚拟的FundCard
     * <AUTHOR>
     * @created 2025-08-07 16:16:57 - 为股东名称查询场景构造基金卡片数据，传递给InvestorDialog
     */
    const virtualFundCard = React.useMemo(() => {
        if (shareholderNameQuery?.data?.data) {
            const fundInfo = Array.isArray(shareholderNameQuery.data.data)
                ? shareholderNameQuery.data.data[0]
                : shareholderNameQuery.data.data;

            if (fundInfo?.fund_code) {
                return {
                    code: fundInfo.fund_code,
                    name: fundInfo.fund_name || value,
                    type: "",
                    date: "",
                    scale: "",
                    yield: "",
                    manager: "",
                    company: "",
                    tag: "",
                };
            }
        }
        return null;
    }, [shareholderNameQuery?.data, value]);

    const handleClick = () => {
        // 点击时触发股东名称查询基金代码
        if (value && organizationId && shareholderNameQuery) {

            shareholderNameQuery.mutate(value);
        }
        setDialogOpen(true);
    };

    const displayText = String(value).length > 10
        ? `${String(value).slice(0, 10)}...`
        : String(value);



    return (
        <div className="text-center">
            {String(value).length > 10 ? (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <button
                                type="button"
                                onClick={handleClick}
                                className="cursor-pointer hover:text-primary hover:underline transition-colors truncate inline-block max-w-[120px] text-left"
                            >
                                {displayText}
                            </button>
                        </TooltipTrigger>
                        <TooltipPortal>
                            <TooltipContent
                                side="top"
                                align="center"
                                className="max-w-[300px]"
                            >
                                {value}
                            </TooltipContent>
                        </TooltipPortal>
                    </Tooltip>
                </TooltipProvider>
            ) : (
                <button
                    type="button"
                    onClick={handleClick}
                    className="cursor-pointer hover:text-primary hover:underline transition-colors"
                >
                    {displayText}
                </button>
            )}

            <InvestorDialog
                open={dialogOpen}
                onOpenChange={setDialogOpen}
                shareholderAccount={record?.identifier || ""}
                organizationId={organizationId || ""}
                investorType={'shares'}
                shareholderName={value}
                selectedCard={virtualFundCard}
                investorCode={record?.identifier || ""}
                loading={false}
                error={''}
                isFavorite={false}
                onToggleFavorite={() => {}}
                shareholderChangeType={changeType}
                stockData={stockData}
            />
        </div>
    );
}

/**
 * 根据表头生成列配置
 * @param headers 表头数组
 * @param total 数据总数，用于控制排序功能开启条件
 * @param stockData 股票数据，用于计算新增列
 * @param organizationId 组织ID，用于股东详情弹窗
 * @param changeType 股东变动类型，用于在InvestorDialog中显示
 * @returns ShareholderColumn数组
 * <AUTHOR>
 * @created 2025-06-17 11:05:07
 * @modified 2025-06-17 11:36:18 - 添加className属性实现表头表体居中对齐
 * @modified 2025-07-24 - 提取到独立文件，添加股东名称点击交互功能
 * @modified 2025-07-25 - 添加股东变动类型参数，支持在InvestorDialog中识别股东变动类型
 */
export function generateColumns(headers: string[], total: number | undefined, stockData?: any, organizationId?: string, changeType?: ShareholderChangeType): ShareholderColumn[] {
    // 定义列配置映射表，确保每个表头对应正确的API数据字段
    const columnMappings: Record<string, ShareholderColumn> = {
        排名: {
            key: "rank",
            title: "排名",
            width: 80,
            sortable: total !== undefined && total >= 3,
            className: "text-center",
            render: (value: any) => (
                <div className="text-center">{value}</div>
            ),
        },
        股东名称: {
            key: "name",
            title: "股东名称",
            width: 160,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-medium",
            render: (value: any, record: any) => {
                if (!value) {
                    return <div className="text-center">-</div>;
                }

                return (
                    <ShareholderNameCell
                        value={value}
                        record={record}
                        organizationId={organizationId}
                        changeType={changeType}
                        stockData={stockData}
                    />
                );
            },
        },
        一码通: {
            key: "identifier",
            title: "一码通",
            width: 150,
            sortable: total !== undefined && total >= 3,
            className: "text-center",
        },
        // 新进股东持股数量 - API字段：number_of_shares
        当期持股: {
            key: "shares",
            title: "本期持仓数量",
            width: 120,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 退出股东退出前持股数 - API字段：prev_numberofshares
        退出前持股数: {
            key: "shares",
            title: "退出股数",
            width: 120,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 增持股东增持股数 - API字段：increased_shares
        增持股数: {
            key: "shares",
            title: "增持股数",
            width: 120,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 减持股东减持股数 - API字段：decreased_shares
        减持股数: {
            key: "shares",
            title: "减持股数",
            width: 120,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 增持/减持股东当前持股数 - API字段：current_numberofshares
        持股: {
            key: "currentShares",
            title: "本期持仓数量",
            width: 120,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 新进股东持股比例 - API字段：shareholder_ratio
        "当期持股比例(%)": {
            key: "percentage",
            title: "持股比例(%)",
            width: 150,
            sortable: false,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 退出股东退出前持股比例 - API字段：prev_shareholdingratio
        "退出前持股比例(%)": {
            key: "percentage",
            title: "原持股比例(%)",
            width: 150,
            sortable: false,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 增持股东本期持股比例 - API字段：increased_ratio_percent
        "本期持股比例(%)": {
            key: "percentage",
            title: "本期持股比例(%)",
            width: 150,
            sortable: false,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 减持股东减持比例 - API字段：decreased_ratio_percent
        "减持比例(%)": {
            key: "decreaseRatio",
            title: "持股减幅(%)",
            width: 150,
            sortable: false,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 增持股东持股增幅 - API字段：increased_ratio_percent
        "持股增幅(%)": {
            key: "increaseRatio",
            title: "持股增幅(%)",
            width: 150,
            sortable: false,
            className: "text-center font-mono",
            render: (value: any) => value, // 已在hooks中格式化
        },
        // 增持股东专用的三个新列
        "增持预估持仓总成本(元)": {
            key: "increaseEstimatedTotalCost",
            title: "预估持仓总成本(元)",
            width: 160,
            sortable: false,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 预估持仓总成本 = 上期成本 + 增持成本
                // 上期成本 = start_date_stock_price × 上期持股数（本期持仓数量 - 增持股数）
                // 增持成本 = average_stock_price × 增持股数
                const currentShares = Number.parseFloat(record.currentShares?.toString().replace(/,/g, '') || '0');
                const increasedShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const previousShares = currentShares - increasedShares;

                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;

                if (startPrice > 0 && averagePrice > 0 && previousShares >= 0 && increasedShares > 0) {
                    const previousCost = previousShares * startPrice;
                    const increaseCost = increasedShares * averagePrice;
                    const totalCost = previousCost + increaseCost;
                    return totalCost.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                }
                return '-';
            },
        },
        "增持最新持仓市值(元)": {
            key: "increaseCurrentValue",
            title: "最新持仓市值(元)",
            width: 160,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 最新持仓市值 = latest_price × 本期持仓数量
                const currentShares = Number.parseFloat(record.currentShares?.toString().replace(/,/g, '') || '0');
                const latestPrice = stockData?.latest_price ? Number.parseFloat(stockData.latest_price) : 0;

                if (latestPrice > 0 && currentShares > 0) {
                    const currentValue = currentShares * latestPrice;
                    return currentValue.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                }
                return '-';
            },
        },
        "增持最新持仓收益": {
            key: "increaseProfit",
            title: "最新持仓收益",
            width: 140,
            sortable: false,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 最新持仓收益 = 最新持仓市值 - 预估持仓总成本
                const currentShares = Number.parseFloat(record.currentShares?.toString().replace(/,/g, '') || '0');
                const increasedShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const previousShares = currentShares - increasedShares;

                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;
                const latestPrice = stockData?.latest_price ? Number.parseFloat(stockData.latest_price) : 0;

                if (startPrice > 0 && averagePrice > 0 && latestPrice > 0 && previousShares >= 0 && increasedShares > 0 && currentShares > 0) {
                    // 计算预估持仓总成本
                    const previousCost = previousShares * startPrice;
                    const increaseCost = increasedShares * averagePrice;
                    const totalCost = previousCost + increaseCost;

                    // 计算最新持仓市值
                    const currentValue = currentShares * latestPrice;

                    // 计算收益
                    const profit = currentValue - totalCost;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;
                }
                return '-';
            },
        },
        // 新增三个计算列 - 预估持仓成本、最新持仓市值、最新持仓收益
        预估持仓成本: {
            key: "estimatedCost",
            title: "预估持仓成本",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 获取本期持仓数量和平均股价
                const shares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;
                const cost = shares * averagePrice;
                return cost > 0 ? cost.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '-';
            },
        },
        最新持仓市值: {
            key: "currentValue",
            title: "最新持仓市值",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 获取本期持仓数量和最新股价
                const shares = Number.parseFloat(
                    record.shares?.toString().replace(/,/g, "") || "0",
                );
                const latestPrice = stockData?.latest_price
                    ? Number.parseFloat(stockData.latest_price)
                    : 0;
                const currentValue = shares * latestPrice;
                return currentValue > 0 ? currentValue.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '-';
            },
        },
        最新持仓收益: {
            key: "profit",
            title: "最新持仓收益",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 计算收益 = 最新持仓市值 - 预估持仓成本
                const shares = Number.parseFloat(
                    record.shares?.toString().replace(/,/g, "") || "0",
                );
                const averagePrice = stockData?.average_stock_price
                    ? Number.parseFloat(stockData.average_stock_price)
                    : 0;
                const latestPrice = stockData?.latest_price
                    ? Number.parseFloat(stockData.latest_price)
                    : 0;

                if (shares > 0 && averagePrice > 0 && latestPrice > 0) {
                    const cost = shares * averagePrice;
                    const currentValue = shares * latestPrice;
                    const profit = currentValue - cost;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;

                }
                return '-';
            },
        },
        // 退出股东专用的三个新列
        预估退出持仓成本: {
            key: "exitEstimatedCost",
            title: "预估持仓成本",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 预估持仓成本 = start_date_stock_price × 退出股数
                const shares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const cost = shares * startPrice;
                return cost > 0 ? cost.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '-';
            },
        },
        预估退出总额: {
            key: "exitTotalAmount",
            title: "预估退出总额",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 预估退出总额 = average_stock_price × 退出股数
                const shares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;
                const totalAmount = shares * averagePrice;
                return totalAmount > 0 ? totalAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '-';
            },
        },
        预估退出收益: {
            key: "exitProfit",
            title: "预估退出收益",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 预估退出收益 = 预估退出总额 - 预估持仓成本
                const shares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;

                if (shares > 0 && startPrice > 0 && averagePrice > 0) {
                    const cost = shares * startPrice;
                    const totalAmount = shares * averagePrice;
                    const profit = totalAmount - cost;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;
                }
                return '-';
            },
        },
        // 减持股东专用的三个新列
        "预估减持成本": {
            key: "decreaseEstimatedCost",
            title: "预估减持成本",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 预估减持成本 = start_date_stock_price × 减持股数
                const decreaseShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;

                if (startPrice > 0 && decreaseShares > 0) {
                    const cost = decreaseShares * startPrice;
                    return cost.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                }
                return '-';
            },
        },
        "减持实现收益(元)": {
            key: "decreaseRealizedProfit",
            title: "减持实现收益(元)",
            width: 160,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 减持实现收益 = (average_stock_price - start_date_stock_price) × 减持股数
                const decreaseShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const averagePrice = stockData?.average_stock_price ? Number.parseFloat(stockData.average_stock_price) : 0;

                if (startPrice > 0 && averagePrice > 0 && decreaseShares > 0) {
                    const profit = (averagePrice - startPrice) * decreaseShares;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;
                }
                return '-';
            },
        },
        "减持最新持仓收益": {
            key: "decreaseCurrentProfit",
            title: "最新持仓收益",
            width: 140,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 最新持仓收益 = (latest_price - start_date_stock_price) × 本期持仓数量
                const currentShares = Number.parseFloat(record.currentShares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const latestPrice = stockData?.latest_price ? Number.parseFloat(stockData.latest_price) : 0;

                if (startPrice > 0 && latestPrice > 0 && currentShares > 0) {
                    const profit = (latestPrice - startPrice) * currentShares;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;
                }
                return '-';
            },
        },
        // 持续持股股东专用的计算列
        "最新持仓市值(元)": {
            key: "constantCurrentValue",
            title: "最新持仓市值(元)",
            width: 160,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 最新持仓市值 = latest_price × 当前持股数量
                const currentShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const latestPrice = stockData?.latest_price ? Number.parseFloat(stockData.latest_price) : 0;

                if (latestPrice > 0 && currentShares > 0) {
                    const currentValue = currentShares * latestPrice;
                    return currentValue.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                }
                return '-';
            },
        },
        "最新持仓收益(元)": {
            key: "constantProfit",
            title: "最新持仓收益(元)",
            width: 160,
            sortable: total !== undefined && total >= 3,
            className: "text-center font-mono",
            render: (_value: any, record: any) => {
                // 最新持仓收益 = (latest_price - start_date_stock_price) × 当前持股数量
                const currentShares = Number.parseFloat(record.shares?.toString().replace(/,/g, '') || '0');
                const startPrice = stockData?.start_date_stock_price ? Number.parseFloat(stockData.start_date_stock_price) : 0;
                const latestPrice = stockData?.latest_price ? Number.parseFloat(stockData.latest_price) : 0;

                if (startPrice > 0 && latestPrice > 0 && currentShares > 0) {
                    const profit = (latestPrice - startPrice) * currentShares;
                    const profitText = profit.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                    // 根据盈亏情况设置颜色
                    if (profit > 0) {
                        return <span className="text-red-600">+{profitText}</span>;
                    }
                    if (profit < 0) {
                        return <span className="text-green-600">{profitText}</span>;
                    }
                    return <span className="text-gray-600">{profitText}</span>;
                }
                return '-';
            },
        },
    };

    // 根据传入的headers筛选对应的列配置
    return headers.map(header => columnMappings[header]).filter(Boolean);
}

export const tableHeaders = {
	// 新进股东表头 - 对应API字段：name, unified_account_number, number_of_shares, shareholder_ratio, register_date
	newIndividual: [
		"股东名称", // name
		"一码通", // unified_account_number
		"当期持股", // number_of_shares
		"当期持股比例(%)", // shareholder_ratio
		"预估持仓成本", // 新增：计算列
		"最新持仓市值", // 新增：计算列
		"最新持仓收益", // 新增：计算列
	],
	newInstitution: [
		"股东名称", // name
		"一码通", // unified_account_number
		"当期持股", // number_of_shares
		"当期持股比例(%)", // shareholder_ratio
		"预估持仓成本", // 新增：计算列
		"最新持仓市值", // 新增：计算列
		"最新持仓收益", // 新增：计算列
	],
	// 退出股东表头 - 对应API字段：name, unified_account_number, prev_numberofshares, prev_shareholdingratio, exit_date
	exitIndividual: [
		"股东名称", // name
		"一码通", // unified_account_number
		"退出前持股数", // prev_numberofshares
		"退出前持股比例(%)", // prev_shareholdingratio
		"预估退出持仓成本", // 新增：计算列
		"预估退出总额", // 新增：计算列
		"预估退出收益", // 新增：计算列
		"发生期数", // exit_date
	],
	exitInstitution: [
		"股东名称", // name
		"一码通", // unified_account_number
		"退出前持股数", // prev_numberofshares
		"退出前持股比例(%)", // prev_shareholdingratio
		"预估退出持仓成本", // 新增：计算列
		"预估退出总额", // 新增：计算列
		"预估退出收益", // 新增：计算列
		"发生期数", // exit_date
	],
	// 增持股东表头 - 对应API字段：rank, name, unified_account_number, increased_shares, increased_ratio_percent, current_numberofshares, increased_date
	increaseIndividual: [
		"排名", // rank (增持个人股东无rank字段)
		"股东名称", // name (增持个人股东无rank字段)
		"一码通", // unified_account_number
		"增持股数", // increased_shares
		"本期持股比例(%)", // current_shareholdingratio
		"持股增幅(%)", // increased_ratio_percent
		"持股", // current_numberofshares
		"增持预估持仓总成本(元)", // 新增：计算列
		"增持最新持仓市值(元)", // 新增：计算列
		"增持最新持仓收益", // 新增：计算列
		"发生期数", // increased_date
	],
	increaseInstitution: [
		"排名", // rank (增持机构股东有rank字段)
		"股东名称", // name
		"一码通", // unified_account_number
		"增持股数", // increased_shares
		"本期持股比例(%)", // current_shareholdingratio
		"持股增幅(%)", // increased_ratio_percent
		"持股", // current_numberofshares
		"增持预估持仓总成本(元)", // 新增：计算列
		"增持最新持仓市值(元)", // 新增：计算列
		"增持最新持仓收益", // 新增：计算列
		"发生期数", // increased_date
	],
	// 减持股东表头 - 对应API字段：rank, name, unified_account_number, decreased_shares, decreased_ratio_percent, current_numberofshares, current_shareholdingratio, decreased_date
	decreaseIndividual: [
		"排名", // rank
		"股东名称", // name
		"一码通", // unified_account_number
		"减持股数", // decreased_shares
		"本期持股比例(%)", // current_shareholdingratio
		"减持比例(%)", // decreased_ratio_percent
		"持股", // current_numberofshares
		"预估减持成本", // 新增：计算列
		"减持实现收益(元)", // 新增：计算列
		"减持最新持仓收益", // 新增：计算列
		"发生期数", // decreased_date
	],
	decreaseInstitution: [
		"排名", // rank
		"股东名称", // name
		"一码通", // unified_account_number
		"减持股数", // decreased_shares
		"本期持股比例(%)", // current_shareholdingratio
		"减持比例(%)", // decreased_ratio_percent
		"持股", // current_numberofshares
		"预估减持成本", // 新增：计算列
		"减持实现收益(元)", // 新增：计算列
		"减持最新持仓收益", // 新增：计算列
		"发生期数", // decreased_date
	],
	// 持续持股股东表头 - 对应API字段：rank, name, unified_account_number, current_numberofshares, current_shareholdingratio
	constantIndividual: [
		"排名", // rank
		"股东名称", // name
		"一码通", // unified_account_number
		"当期持股", // current_numberofshares
		"当期持股比例(%)", // current_shareholdingratio
		"最新持仓市值(元)", // 新增：计算列
		"最新持仓收益(元)", // 新增：计算列
	],
	constantInstitution: [
		"排名", // rank
		"股东名称", // name
		"一码通", // unified_account_number
		"当期持股", // current_numberofshares
		"当期持股比例(%)", // current_shareholdingratio
		"最新持仓市值(元)", // 新增：计算列
		"最新持仓收益(元)", // 新增：计算列
	],
};
