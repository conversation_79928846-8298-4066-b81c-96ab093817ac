"use client";

import { useCallback, useEffect, useRef } from "react";
import { ShareholderChangeToolbar } from "@saas/shareholder/components/change";
import { ShareholderChangeTableView } from "@saas/shareholder/components/change";
import { useShareholderChanges } from "@saas/shareholder/hooks/useShareholderChanges";
import { useOrganization } from "@saas/organizations/hooks/useOrganization";
import { ShareholderChangeSkeleton } from "@saas/shareholder/components/change";
/**
 * 股东持股变化分析组件属性接口
 *
 * @interface ShareholderChangeAnalysisProps
 * @property {string} organizationSlug - 组织名
 * @time 2025-06-11 15:42:59.427
 */
interface ShareholderListProps {
	organizationSlug?: string;
}

/**
 * 股东持股变化分析组件
 * 用于展示股东持股变化的分析数据和图表
 * 
 * <AUTHOR>
 * @created 2024-06-28 16:23:45.621
 * @modified 2025年06月11日 18:03:39
 * @param {ShareholderChangeAnalysisProps} props - 组件属性
 * @returns {JSX.Element} 股东持股变化分析组件
 * @time 2025年06月11日 18:03:39
 */
export function ShareholderChangeComposite({
		organizationSlug = "",
	}: ShareholderListProps) {
		// 不生成默认时间，完全依赖实际数据 - 修改于 2025-06-26 12:12:04.025
		const defaultEndDate = "";
		const defaultStartDate = "";

		// 添加用户选择日期的标记，用于防止自动设置覆盖用户选择
		const userSelectedDatesRef = useRef({
			startDate: false,
			endDate: false
		});

		// 获取组织信息
		const { data: organization, isLoading: isLoadingOrg } =
			useOrganization(organizationSlug);
		// 使用股东持股变化钩子 - 修改于 2025-06-17 16:47:20.601，添加sortType相关状态
		const {
			shareholderChanges,
			availableDates,
			registerDates,
			shareholderTypes,
			startDate,
			endDate,
			shareholderType,
			searchTerm,
			sortType, // 新增：排序类型状态 - 添加于 2025-06-17 16:47:20.601
			sortOrder,
			page, // 添加页码状态 - 添加于 2025-06-13 14:58:45.257
			pagination, // 添加分页信息 - 添加于 2025-06-13 14:58:45.257
			isLoading,

			setStartDate,
			setEndDate,
			setShareholderType,
			setSearchTerm,
			setSortType, // 新增：排序类型设置函数 - 添加于 2025-06-17 16:47:20.601
			setSortOrder,
			setPage, // 添加页码设置函数 - 添加于 2025-06-13 14:58:45.257
			resetAllFilters,
			refetch,
			forceRefreshAllData, // 新增：强制刷新所有相关数据 - 添加于 2025-06-26 11:53:14.842
		} = useShareholderChanges(
			organization?.id || "",
			defaultStartDate,
			defaultEndDate,
		);

		// 自定义日期设置处理函数，追踪用户手动选择
		const handleStartDateChange = useCallback((date: string) => {
			userSelectedDatesRef.current.startDate = true;
			setStartDate(date);
		}, [setStartDate]);

		const handleEndDateChange = useCallback((date: string) => {
			userSelectedDatesRef.current.endDate = true;
			setEndDate(date);
		}, [setEndDate]);

		// 当期数日期列表加载完成后，更新开始和结束日期
		// 修改于 2025-06-26 12:07:34.786，仅在用户未手动选择日期且有数据的情况下自动设置
		useEffect(() => {
			if (registerDates.length > 0) {
				// 按照日期从新到旧排序
				const sortedDates = [...registerDates].sort((a, b) => b.localeCompare(a));

				// 检查是否由用户设置了日期
				const isStartDateUserSet = userSelectedDatesRef.current.startDate;
				const isEndDateUserSet = userSelectedDatesRef.current.endDate;

				// 设置最新的日期为结束日期（仅当用户未手动选择结束日期时）
				if (sortedDates[0] && sortedDates[0] !== endDate && !isEndDateUserSet) {
					setEndDate(sortedDates[0]);
				}

				// 如果有多个日期，设置最早的日期为开始日期（仅当用户未手动选择开始日期时）
				if (sortedDates.length > 1 && sortedDates[sortedDates.length - 1] !== startDate && !isStartDateUserSet) {
					setStartDate(sortedDates[sortedDates.length - 1]);
				}
			} else {
				// 当没有数据时，清空日期选择 - 添加于 2025-06-26 12:07:34.786
				if (!userSelectedDatesRef.current.startDate && startDate) {
					setStartDate("");
				}
				if (!userSelectedDatesRef.current.endDate && endDate) {
					setEndDate("");
				}
			}
		}, [registerDates, setStartDate, setEndDate, startDate, endDate]);

		// 处理刷新按钮点击 - 修改于 2025-06-26 11:53:14.842，使用强制刷新功能
		const handleRefresh = useCallback(() => {
			// 重置用户选择状态，允许系统在数据刷新后重新设置日期范围
			userSelectedDatesRef.current.startDate = false;
			userSelectedDatesRef.current.endDate = false;

			// 使用强制刷新功能，确保所有相关数据都被刷新 - 修改于 2025-06-26 11:53:14.842
			forceRefreshAllData();

			// 使用全面的重置函数，重置所有状态到初始状态
			resetAllFilters(); // 替代resetFilters，更全面地重置所有状态
		}, [forceRefreshAllData, resetAllFilters]);

		// 处理排序 - 修改于 2025-06-17 17:09:31.041，更新sortType参数支持具体日期格式
		const handleSort = useCallback(
			(_sortKey: string, sortOrder: "asc" | "desc", sortType?: "rank" | "date" | string) => {
				// 如果传入了sortType，则更新排序类型（支持具体日期格式）
				if (sortType) {
					setSortType(sortType);
				}
				setSortOrder(sortOrder);
			},
			[setSortType, setSortOrder],
		);


		return (
			<div className="flex flex-col gap-2">
				{organization?.id ? (
					<>
						<ShareholderChangeToolbar
							startDate={startDate}
							endDate={endDate}
							shareholderType={shareholderType || "all"}
							searchTerm={searchTerm || ""}
							availableDates={availableDates}
							shareholderTypes={shareholderTypes}
							onStartDateChange={handleStartDateChange}
							onEndDateChange={handleEndDateChange}
							onShareholderTypeChange={setShareholderType}
							onSearchChange={setSearchTerm}
							onRefresh={handleRefresh}
							isLoading={isLoading}
						/>
						<ShareholderChangeTableView
							data={shareholderChanges}
							loading={isLoading}
							availableDates={availableDates}
							onSort={handleSort}
							searchTerm={searchTerm || ""}
							page={page} // 添加页码属性 - 添加于 2025-06-13 14:58:45.257
							onPageChange={setPage} // 添加页码变更回调 - 添加于 2025-06-13 14:58:45.257
							pagination={pagination} // 修复：添加缺失的分页信息属性 - 修复于 2025-06-13 15:06:00.611
						/>
					</>
				) : (
					<ShareholderChangeSkeleton />
				)}
			</div>
		);
	}

