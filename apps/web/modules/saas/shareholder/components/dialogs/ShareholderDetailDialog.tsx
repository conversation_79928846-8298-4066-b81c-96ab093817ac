/**
 * 股东详情弹窗组件
 * @file ShareholderDetailDialog.tsx
 * @description 用于显示股东详细信息的弹窗组件
 * @created 2025-07-24
 * <AUTHOR>
 */

import React from "react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@ui/components/dialog";

/**
 * 股东详情弹窗属性接口
 */
interface ShareholderDetailDialogProps {
    /** 弹窗开启状态 */
    open: boolean;
    /** 弹窗状态变更回调 */
    onOpenChange: (open: boolean) => void;
    /** 股东的一码通账号 */
    shareholderAccount: string;
    /** 组织ID */
    organizationId: string;
    /** 股东名称 */
    shareholderName?: string;

    /** 卡片展示对象类型 */
    type?: string;
}

/**
 * 股东详情弹窗组件
 * @param props 组件属性
 * @returns 股东详情弹窗UI组件
 */
export function ShareholderDetailDialog({
    open,
    onOpenChange,
    shareholderAccount,
    organizationId,
    shareholderName,
    type,
}: ShareholderDetailDialogProps): JSX.Element {
    return (
					<Dialog open={open} onOpenChange={onOpenChange}>
						<DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
							<DialogHeader>
								<DialogTitle className="flex items-center gap-2">
									<svg
										className="h-5 w-5"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
									>
										<title>股东详情图标</title>
										<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
										<circle cx="12" cy="7" r="4" />
									</svg>
									股东详情
								</DialogTitle>
								<DialogDescription>
									{shareholderName
										? `${shareholderName} 的详细信息`
										: "股东详细信息"}
								</DialogDescription>
							</DialogHeader>

							<div className="space-y-6">
								{/* 基本信息 */}
								<div className="space-y-4">
									<h3 className="text-lg font-semibold border-b pb-2">
										基本信息 {type}
									</h3>
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div className="space-y-2 text-sm font-medium text-muted-foreground">
											股东名称
											<div className="p-3 bg-muted/50 rounded-md">
												{shareholderName || "-"}
											</div>
										</div>
										<div className="space-y-2 text-sm font-medium text-muted-foreground">
											一码通账号
											<div className="p-3 bg-muted/50 rounded-md font-mono">
												{shareholderAccount || "-"}
											</div>
										</div>
										<div className="space-y-2 text-sm font-medium text-muted-foreground">
											组织ID
											<div className="p-3 bg-muted/50 rounded-md font-mono">
												{organizationId || "-"}
											</div>
										</div>
									</div>
								</div>

								{/* 占位符内容 - 后续可扩展更多详细信息 */}
								<div className="space-y-4">
									<h3 className="text-lg font-semibold border-b pb-2">
										持股信息
									</h3>
									<div className="p-4 bg-muted/30 rounded-md text-center text-muted-foreground">
										详细持股信息功能开发中...
									</div>
								</div>

								<div className="space-y-4">
									<h3 className="text-lg font-semibold border-b pb-2">
										变动历史
									</h3>
									<div className="p-4 bg-muted/30 rounded-md text-center text-muted-foreground">
										股东变动历史功能开发中...
									</div>
								</div>
							</div>
						</DialogContent>
					</Dialog>
				);
}
