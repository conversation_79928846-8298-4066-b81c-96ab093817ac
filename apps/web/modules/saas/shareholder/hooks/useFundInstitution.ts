/**
 * 股东名称查询基金代码和基金代码查询股东名称 Hook
 * @file useFundInstitution.ts
 * @description 用于股东基金双向查询的 React Hook，支持基金代码查股东名称和股东名称查基金代码
 * <AUTHOR>
 * @created 2025-08-07 14:34:20 - 基于fundInstitutionApi封装的股东基金查询Hook
 * @modified 2025-08-07 14:34:20 - 初始创建，支持双向查询功能
 */

import { useMutation, useQuery } from "@tanstack/react-query";
import { 
  fundInstitutionApi, 
  type FundCodeQueryRequest,
  type ShareholderNameQueryRequest,
  type FundCodeQueryResponse,
  type ShareholderNameQueryResponse
} from "@saas/shareholder/lib/fund-institution-api";
import type { BaseApiResponse } from "@saas/shareholder/lib/base-shareholder-api";
import { toast } from "sonner";

/**
 * 基金代码查询股东名称 Hook 配置选项
 * @interface UseFundCodeQueryOptions
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，提高代码可维护性 - hayden
 */
export interface UseFundCodeQueryOptions {
  /**
   * 是否启用查询，默认为 false（需要手动触发）
   * @type {boolean}
   * @default false
   * @description 控制查询是否自动执行，当为false时查询将被暂停，需要手动调用refetch触发
   */
  enabled?: boolean;
  /**
   * 数据刷新间隔（毫秒），默认不自动刷新
   * @type {number}
   * @default undefined
   * @description 设置查询自动刷新的时间间隔，单位毫秒，undefined表示不自动刷新
   */
  refetchInterval?: number;
  /**
   * 是否在窗口重新获得焦点时重新获取数据，默认为 false
   * @type {boolean}
   * @default false
   * @description 当浏览器窗口重新获得焦点时是否自动重新执行查询
   */
  refetchOnWindowFocus?: boolean;
  /**
   * 缓存时间（毫秒），默认 5 分钟
   * @type {number}
   * @default 300000
   * @description 数据被认为是新鲜的时间长度，在此时间内不会重新请求数据
   */
  staleTime?: number;
  /**
   * 垃圾回收时间（毫秒），默认 10 分钟
   * @type {number}
   * @default 600000
   * @description 查询结果在内存中保留的最长时间，超过此时间将被垃圾回收
   */
  gcTime?: number;
}

/**
 * 基金代码查询股东名称 Hook 返回值
 * @interface UseFundCodeQueryReturn
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括返回值类型说明 - hayden
 */
export interface UseFundCodeQueryReturn {
  /**
   * 股东信息数据，包含查询到的股东列表
   * @type {BaseApiResponse<FundCodeQueryResponse> | undefined}
   * @description 查询成功时包含股东信息数组，查询失败或未执行时为undefined
   */
  data: BaseApiResponse<FundCodeQueryResponse> | undefined;
  /**
   * 是否正在初次加载数据
   * @type {boolean}
   * @description 仅在首次查询时为true，后续的重新获取不会影响此状态
   */
  isLoading: boolean;
  /**
   * 是否加载中（包括后台刷新和重新获取）
   * @type {boolean}
   * @description 任何查询活动（包括初次加载、重新获取、后台刷新）都会使此状态为true
   */
  isFetching: boolean;
  /**
   * 错误信息，当查询失败时包含具体错误详情
   * @type {Error | null}
   * @description 查询失败时包含Error对象，成功时为null
   */
  error: Error | null;
  /**
   * 是否有错误发生
   * @type {boolean}
   * @description 当error不为null时为true
   */
  isError: boolean;
  /**
   * 是否成功获取数据
   * @type {boolean}
   * @description 当data不为undefined且无错误时为true
   */
  isSuccess: boolean;
  /**
   * 手动刷新数据的函数，返回Promise
   * @type {() => Promise<any>}
   * @description 手动触发查询重新执行，返回Promise以便处理异步结果
   */
  refetch: () => Promise<any>;
  /**
   * 是否正在执行手动刷新操作
   * @type {boolean}
   * @description 当通过refetch函数触发的查询正在执行时为true
   */
  isRefetching: boolean;
}

/**
 * 股东名称查询基金代码 Mutation 返回值
 * @interface UseShareholderNameQueryReturn
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括参数和返回值详细说明 - hayden
 */
export interface UseShareholderNameQueryReturn {
  /**
   * 执行查询的函数，接收股东名称查询请求参数
   * @type {(request: ShareholderNameQueryRequest) => void}
   * @description 触发股东名称查询，不返回Promise，适用于不需要处理异步结果的场景
   */
  mutate: (request: ShareholderNameQueryRequest) => void;
  /**
   * 异步执行查询的函数，返回Promise包装的基金信息响应
   * @type {(request: ShareholderNameQueryRequest) => Promise<BaseApiResponse<ShareholderNameQueryResponse>>}
   * @description 触发股东名称查询并返回Promise，适用于需要处理异步结果的场景
   */
  mutateAsync: (request: ShareholderNameQueryRequest) => Promise<BaseApiResponse<ShareholderNameQueryResponse>>;
  /**
   * 基金信息数据，包含查询到的基金详情
   * @type {BaseApiResponse<ShareholderNameQueryResponse> | undefined}
   * @description 查询成功时包含基金信息对象，查询失败或未执行时为undefined
   */
  data: BaseApiResponse<ShareholderNameQueryResponse> | undefined;
  /**
   * 是否正在执行查询操作
   * @type {boolean}
   * @description 当查询正在执行时为true，完成或失败后为false
   */
  isPending: boolean;
  /**
   * 错误信息，当查询失败时包含具体错误详情
   * @type {Error | null}
   * @description 查询失败时包含Error对象，成功时为null
   */
  error: Error | null;
  /**
   * 是否有错误发生
   * @type {boolean}
   * @description 当error不为null时为true
   */
  isError: boolean;
  /**
   * 是否成功获取数据
   * @type {boolean}
   * @description 当data不为undefined且无错误时为true
   */
  isSuccess: boolean;
  /**
   * 重置查询状态的函数，清除数据、错误和状态
   * @type {() => void}
   * @description 将mutation状态重置为初始状态，清除data、error等所有状态
   */
  reset: () => void;
}

/**
 * 基金代码查询股东名称 Hook
 * @description 根据基金代码查询持有该基金的股东名称信息，支持缓存、重试和错误处理
 *
 * @param {string} organizationId - 组织ID，必填参数，长度5-50个字符，用于数据隔离和权限控制
 * @param {string} fundCode - 基金代码，必填参数，格式为6位数字.后缀（如：001384.OF），用于标识具体基金
 * @param {UseFundCodeQueryOptions} [options={}] - Hook 配置选项，包含查询行为控制参数
 * @param {boolean} [options.enabled=false] - 是否启用自动查询
 * @param {number} [options.refetchInterval] - 自动刷新间隔（毫秒）
 * @param {boolean} [options.refetchOnWindowFocus=false] - 窗口获得焦点时是否重新查询
 * @param {number} [options.staleTime=300000] - 数据过期时间（毫秒）
 * @param {number} [options.gcTime=600000] - 垃圾回收时间（毫秒）
 *
 * @returns {UseFundCodeQueryReturn} 包含查询结果、状态和控制函数的对象
 * @returns {BaseApiResponse<FundCodeQueryResponse> | undefined} returns.data - 股东信息数据
 * @returns {boolean} returns.isLoading - 是否正在初次加载
 * @returns {boolean} returns.isFetching - 是否正在获取数据
 * @returns {Error | null} returns.error - 错误信息
 * @returns {boolean} returns.isError - 是否有错误
 * @returns {boolean} returns.isSuccess - 是否查询成功
 * @returns {() => Promise<any>} returns.refetch - 手动刷新函数
 * @returns {boolean} returns.isRefetching - 是否正在手动刷新
 *
 * @throws {Error} 当organizationId为空或非字符串时抛出INVALID_ORGANIZATION_ID错误
 * @throws {Error} 当fundCode为空或非字符串时抛出MISSING_FUND_CODE错误
 * @throws {Error} 当fundCode格式不正确时抛出INVALID_FUND_CODE_FORMAT错误
 *
 * @example
 * ```typescript
 * // 基本用法
 * const { data, isLoading, error, refetch } = useFundCodeQuery("12345", "001384.OF", {
 *   enabled: true,
 * });
 *
 * // 条件渲染
 * if (isLoading) return <div>查询中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) return <div>找到 {data.data.length} 条股东记录</div>;
 *
 * // 手动刷新
 * const handleRefresh = () => {
 *   refetch();
 * };
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数、返回值和异常说明 - hayden
 */
export function useFundCodeQuery(
  organizationId: string,
  fundCode: string,
  options: UseFundCodeQueryOptions = {}
): UseFundCodeQueryReturn {
  // 严格验证组织ID是必须的
  if (!organizationId || typeof organizationId !== "string") {
    throw new Error("INVALID_ORGANIZATION_ID: 组织ID是必须参数，必须是非空字符串");
  }

  const {
    enabled = false,
    refetchInterval,
    refetchOnWindowFocus = false,
    staleTime = 5 * 60 * 1000, // 5分钟
    gcTime = 10 * 60 * 1000, // 10分钟
  } = options;

  const query = useQuery({
    queryKey: ["fund-code-query", organizationId, fundCode],
    queryFn: async () => {
      // 双重验证确保参数完整性
      if (!organizationId || typeof organizationId !== "string") {
        throw new Error("MISSING_ORGANIZATION_ID: 组织ID是必须参数，不能为空");
      }
      if (!fundCode || typeof fundCode !== "string") {
        throw new Error("MISSING_FUND_CODE: 基金代码是必须参数，不能为空");
      }

      const request: FundCodeQueryRequest = {
        organizationId,
        fund_code: fundCode,
      };

      // 直接返回API结果，错误检查已在base-shareholder-api.ts中处理
      return await fundInstitutionApi.queryShareholdersByFundCode(request);
    },
    enabled: enabled && !!organizationId && !!fundCode,
    refetchInterval,
    refetchOnWindowFocus,
    staleTime,
    gcTime,
    retry: (failureCount, error) => {
      // 参数验证错误不重试
      if (error.message.includes("INVALID_")) {
        return false;
      }
      // 最多重试2次
      return failureCount < 2;
    },
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    refetch: query.refetch,
    isRefetching: query.isRefetching,
  };
}

/**
 * 股东名称查询基金代码 Hook
 * @description 根据股东名称查询对应的基金代码信息，使用Mutation模式支持手动触发查询
 *
 * @param {Object} [options={}] - Mutation 配置选项，包含成功和失败回调函数
 * @param {(data: BaseApiResponse<ShareholderNameQueryResponse>) => void} [options.onSuccess] - 查询成功时的回调函数
 * @param {(error: Error) => void} [options.onError] - 查询失败时的回调函数
 *
 * @returns {UseShareholderNameQueryReturn} 包含查询函数、结果和状态的对象
 * @returns {(request: ShareholderNameQueryRequest) => void} returns.mutate - 执行查询的函数
 * @returns {(request: ShareholderNameQueryRequest) => Promise<BaseApiResponse<ShareholderNameQueryResponse>>} returns.mutateAsync - 异步执行查询的函数
 * @returns {BaseApiResponse<ShareholderNameQueryResponse> | undefined} returns.data - 基金信息数据
 * @returns {boolean} returns.isPending - 是否正在执行查询
 * @returns {Error | null} returns.error - 错误信息
 * @returns {boolean} returns.isError - 是否有错误
 * @returns {boolean} returns.isSuccess - 是否查询成功
 * @returns {() => void} returns.reset - 重置查询状态的函数
 *
 * @throws {Error} 当organizationId为空或非字符串时抛出MISSING_ORGANIZATION_ID错误
 * @throws {Error} 当shareholder_name为空或非字符串时抛出MISSING_SHAREHOLDER_NAME错误
 * @throws {Error} 当shareholder_name长度超过限制时抛出INVALID_SHAREHOLDER_NAME_LENGTH错误
 *
 * @example
 * ```typescript
 * // 基本用法
 * const { mutate, data, isPending, error } = useShareholderNameQuery({
 *   onSuccess: (data) => {
 *     console.log("查询成功:", data.data.fund_name);
 *   },
 *   onError: (error) => {
 *     console.error("查询失败:", error.message);
 *   },
 * });
 *
 * // 执行查询
 * mutate({
 *   organizationId: "12345",
 *   shareholder_name: "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"
 * });
 *
 * // 异步查询
 * const handleAsyncQuery = async () => {
 *   try {
 *     const result = await mutateAsync({
 *       organizationId: "12345",
 *       shareholder_name: "股东名称"
 *     });
 *     console.log("查询结果:", result);
 *   } catch (error) {
 *     console.error("查询失败:", error);
 *   }
 * };
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数、返回值和异常说明 - hayden
 */
export function useShareholderNameQuery(options: {
  onSuccess?: (data: BaseApiResponse<ShareholderNameQueryResponse>) => void;
  onError?: (error: Error) => void;
} = {}): UseShareholderNameQueryReturn {
  const mutation = useMutation({
    mutationFn: async (request: ShareholderNameQueryRequest) => {
      // 直接返回API结果，错误检查已在base-shareholder-api.ts中处理
      return await fundInstitutionApi.queryFundCodeByShareholderName(request);
    },
    onSuccess: (data) => {
      options.onSuccess?.(data);
    },
    onError: (error: Error) => {
      console.error('股东名称查询失败:', error);
      toast.error(`股东名称查询失败: ${error.message}`);
      options.onError?.(error);
    },
  });

  return {
    mutate: mutation.mutate,
    mutateAsync: mutation.mutateAsync,
    data: mutation.data,
    isPending: mutation.isPending,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    reset: mutation.reset,
  };
}

/**
 * 组合 Hook：同时提供基金代码查询和股东名称查询功能
 * @description 提供统一的股东基金双向查询接口，自动管理组织ID，简化调用方式
 *
 * @param {string} organizationId - 组织ID，必填参数，长度5-50个字符，用于数据隔离和权限控制
 *
 * @returns {Object} 包含两种查询功能的对象
 * @returns {(fundCode: string, options?: UseFundCodeQueryOptions) => UseFundCodeQueryReturn} returns.fundCodeQuery - 基金代码查询股东名称的函数
 * @returns {(options?: {onSuccess?: Function, onError?: Function}) => UseShareholderNameQueryReturn} returns.shareholderNameQuery - 股东名称查询基金代码的函数
 *
 * @throws {Error} 当organizationId为空或非字符串时抛出INVALID_ORGANIZATION_ID错误
 *
 * @example
 * ```typescript
 * // 基本用法
 * const { fundCodeQuery, shareholderNameQuery } = useFundInstitution("12345");
 *
 * // 基金代码查询股东名称
 * const { data: shareholderData, refetch: refetchShareholders } = fundCodeQuery("001384.OF", {
 *   enabled: true
 * });
 *
 * // 股东名称查询基金代码
 * const { mutate: queryFundCode, data: fundData } = shareholderNameQuery({
 *   onSuccess: (data) => console.log("查询成功:", data),
 *   onError: (error) => console.error("查询失败:", error)
 * });
 *
 * // 执行股东名称查询（组织ID已自动传入）
 * queryFundCode("中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金");
 *
 * // 条件渲染示例
 * if (shareholderData?.data) {
 *   return <div>找到 {shareholderData.data.length} 条股东记录</div>;
 * }
 *
 * if (fundData?.data) {
 *   return <div>基金名称: {fundData.data.fund_name}</div>;
 * }
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-07 14:43:59 - 修复股东名称查询中组织ID必须传递的问题
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数、返回值和使用示例 - hayden
 */
export function useFundInstitution(organizationId: string) {
  // 验证组织ID是必须的
  if (!organizationId || typeof organizationId !== "string") {
    throw new Error("INVALID_ORGANIZATION_ID: 组织ID是必须参数，必须是非空字符串");
  }

  return {
    /**
     * 基金代码查询股东名称
     * @param fundCode 基金代码
     * @param options 查询选项
     * @returns 查询结果和状态
     */
    fundCodeQuery: (fundCode: string, options?: UseFundCodeQueryOptions) =>
      useFundCodeQuery(organizationId, fundCode, options),

    /**
     * 股东名称查询基金代码
     * @param options Mutation 选项
     * @returns 查询函数和状态，mutate函数只需要传入股东名称
     */
    shareholderNameQuery: (options?: {
      onSuccess?: (data: BaseApiResponse<ShareholderNameQueryResponse>) => void;
      onError?: (error: Error) => void;
    }) => {
      const mutation = useShareholderNameQuery(options);

      return {
        ...mutation,
        /**
         * 执行股东名称查询，自动使用组织ID
         * @param shareholderName 股东名称
         */
        mutate: (shareholderName: string) => {
          mutation.mutate({
            organizationId,
            shareholder_name: shareholderName,
          });
        },
        /**
         * 异步执行股东名称查询，自动使用组织ID
         * @param shareholderName 股东名称
         * @returns Promise<BaseApiResponse<ShareholderNameQueryResponse>>
         */
        mutateAsync: (shareholderName: string) => {
          return mutation.mutateAsync({
            organizationId,
            shareholder_name: shareholderName,
          });
        },
      };
    },
  };
}
