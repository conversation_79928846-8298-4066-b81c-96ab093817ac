"use client";

/**
 * 键盘按键钩子
 * @file useKeyPress.ts
 * @description 用于监听键盘按键事件的 React Hook
 * <AUTHOR>
 * @created 2025-06-18 13:41:01
 * @modified 2025-06-18 13:41:01 - 修复：将 DependencyList 改为 type 导入，符合 Biome 代码规范
 */

import { useEffect, useCallback, type DependencyList } from "react";

/**
 * 键盘按键钩子
 * @param targetKey 目标按键
 * @param callback 按键触发的回调函数
 * @param deps 依赖数组
 * <AUTHOR>
 * @created 2025-06-18 13:41:01
 */
export function useKeyPress(
  targetKey: string,
  callback: () => void,
  deps: DependencyList = []
) {
  const handler = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === targetKey) {
        callback();
      }
    },
    [targetKey, callback, ...deps]
  );

  useEffect(() => {
    window.addEventListener("keydown", handler);
    return () => {
      window.removeEventListener("keydown", handler);
    };
  }, [handler]);
} 