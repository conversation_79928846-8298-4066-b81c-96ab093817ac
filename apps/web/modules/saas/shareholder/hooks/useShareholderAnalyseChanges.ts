/**
 * 自然人股东变动分析数据 Hook
 * @file useShareholderChanges.ts
 * @description 用于获取和管理自然人股东变动分析数据的 React Hook，包含增持、减持、新进、退出自然人股东的接口
 * <AUTHOR>
 * @created 2025-06-16 18:35:23
 * @modified 2025-06-17 10:13:14 - 重新组织为专门处理自然人股东的hooks
 */

import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import {
	shareholderChangesApi,
	type IncreaseIndividualShareholdersData,
	type DecreaseIndividualShareholdersData,
	type NewIndividualShareholdersData,
	type ExitIndividualShareholdersData,
	type ConstantIndividualShareholdersData,
	type IncreaseIndividualShareholder,
	type DecreaseIndividualShareholder,
	type NewIndividualShareholder,
	type ExitIndividualShareholder,
	type ConstantIndividualShareholder
} from "@saas/shareholder/lib/shareholder-changes-api";
import type { BaseApiResponse } from "@saas/shareholder/lib/base-shareholder-api";
import { toast } from "sonner";

/**
 * 股东数据项接口
 * @interface ShareholderItem
 * @description 定义股东表格数据项的基本结构，使用API实际字段名
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 * @modified 2025-06-19 12:06:54 - 更新字段定义，使用API实际字段名
 */
export interface ShareholderItem {
	id: string;
	// 基础字段
	name: string; // 股东名称
	identifier: string; // 一码通 (unified_account_number)
	date: string; // 发生期数/日期
	rank?: string; // 排名

	// 持股相关字段 - 根据不同表格类型使用不同字段
	shares: string; // 通用持股数量字段
	percentage: string; // 通用持股比例字段
	currentShares?: string; // 当前持股数量

	// 扩展字段
	reason?: string;
	[key: string]: any; // 允许动态字段
}

/**
 * 分页信息接口
 * @interface PaginationInfo
 * @description 定义分页相关信息
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
export interface PaginationInfo {
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

/**
 * 数据处理工具函数
 * @description 用于解析和转换API数据的工具函数
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */

/**
 * 解析API响应数据
 * @param apiResponse API响应对象
 * @returns 解析后的数据数组
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function parseApiData<T>(apiResponse: any): T[] {
	try {
		if (!apiResponse?.data) {
			return [];
		}

		// 如果data是字符串，需要解析
		let parsedData: any;
		if (typeof apiResponse.data === 'string') {
			parsedData = JSON.parse(apiResponse.data);
		} else {
			parsedData = apiResponse.data;
		}

		// 处理数组格式的响应
		if (Array.isArray(parsedData)) {
			// 取第一个元素的data字段
			const firstItem = parsedData[0];
			if (firstItem?.data) {
				// 检查是否只有success标志，没有实际数据
				const dataValues = Object.values(firstItem.data);
				const firstValue = dataValues[0];

				// 如果第一个值是数组，返回该数组
				if (Array.isArray(firstValue)) {
					return firstValue as T[];
				}

				// 如果只有success标志或空数据，返回空数组
				if (dataValues.length === 1 && (firstValue === true || typeof firstValue === 'boolean')) {
					return [];
				}

				return Object.values(firstItem.data)[0] as T[] || [];
			}
		}

		// 处理直接对象格式的响应
		if (parsedData && typeof parsedData === 'object') {
			// 获取第一个数组字段的值
			const firstArrayValue = Object.values(parsedData).find(value => Array.isArray(value));
			return firstArrayValue as T[] || [];
		}

		return [];
	} catch (error) {
		console.error('解析API数据失败:', error);
		return [];
	}
}

/**
 * 格式化日期字符串
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function formatDate(dateString: string): string {
	try {
		if (!dateString) {
			return "";
		}

		// 处理ISO日期格式
		if (dateString.includes('T')) {
			return dateString.split('T')[0];
		}

		return dateString;
	} catch (error) {
		console.error('格式化日期失败:', error);
		return dateString;
	}
}

/**
 * 格式化数字为千分位格式
 * @param value 数字值
 * @returns 格式化后的字符串
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function formatNumber(value: string | number | undefined | null): string {
	try {
		// 处理undefined、null或空值
		if (value === undefined || value === null || value === '') {
			return '-';
		}

		const num = typeof value === 'string' ? Number.parseFloat(value) : value;

		if (Number.isNaN(num)) {
			return String(value);
		}

		return num.toLocaleString('zh-CN', {
			minimumFractionDigits: 0,
			maximumFractionDigits: 2
		});
	} catch (error) {
		console.error('格式化数字失败:', error);
		return String(value || '-');
	}
}

/**
 * 格式化百分比数字
 * @param value 数字值
 * @returns 格式化后的百分比字符串
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function formatPercentage(value: string | number | undefined | null): string {
	
	try {
		// 处理undefined、null或空值
		if (value === undefined || value === null || value === '') {
			return '-';
		}
	
		const formattedNumber = formatNumber(value);
	
		// 如果格式化后的数字为空，返回空字符串
		if (!formattedNumber) {
			return '-';
		}
		
		return `${formattedNumber}%`;
	} catch (error) {
		console.error('格式化百分比失败:', error);
		return '-';
	}
}

/**
 * 数据转换函数
 * @description 将API数据转换为组件需要的格式
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */

/**
 * 转换增持自然人股东数据
 * @param apiData API返回的增持自然人股东数据
 * @param startIndex 起始索引，用于生成唯一ID
 * @returns 转换后的表格数据
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 * @modified 2025-06-19 12:03:23 - 修复字段映射，确保数据正确对应表格列
 */
function transformIncreaseIndividualData(apiData: IncreaseIndividualShareholder[], startIndex = 0): ShareholderItem[] {
	// 过滤掉空的或无效的数据项
	const validData = apiData.filter(item =>
		item &&
		(item.name || item.unified_account_number || item.increased_shares || item.current_numberofshares)
	);

	return validData.map((item, index) => ({
		id: `increase-individual-${startIndex + index + 1}`,
		rank: item?.rank || "",
		name: item?.name || "",
		identifier: item?.unified_account_number || "",
		shares: formatNumber(item?.increased_shares), // 增持股数
		percentage: formatPercentage(item?.current_shareholdingratio), // 本期持股比例(%)
		increaseRatio: formatPercentage(item?.increased_ratio_percent), // 持股增幅(%)
		reason: item?.unified_account_number || "",
		date: formatDate(item?.increased_date), // 发生期数
		currentShares: formatNumber(item?.current_numberofshares), // 持股
	}));
}

/**
 * 转换减持自然人股东数据
 * @param apiData API返回的减持自然人股东数据
 * @param startIndex 起始索引，用于生成唯一ID
 * @returns 转换后的表格数据
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function transformDecreaseIndividualData(apiData: DecreaseIndividualShareholder[], startIndex = 0): ShareholderItem[] {
	// 过滤掉空的或无效的数据项
	const validData = apiData.filter(item =>
		item &&
		(item.name || item.unified_account_number || item.decreased_shares || item.current_numberofshares)
	);

	return validData.map((item, index) => ({
		id: `decrease-individual-${startIndex + index + 1}`,
		rank: item?.rank || "",
		name: item?.name || "",
		identifier: item?.unified_account_number || "",
		shares: formatNumber(item?.decreased_shares), // 减持股数
		percentage: formatPercentage(item?.current_shareholdingratio), // 本期持股比例(%)
		decreaseRatio: formatPercentage(item?.decreased_ratio_percent), // 持股减幅(%)
		reason: item?.unified_account_number || "",
		date: formatDate(item?.decreased_date), // 发生期数
		currentShares: formatNumber(item?.current_numberofshares), // 持股
	}));
}

/**
 * 转换新进自然人股东数据
 * @param apiData API返回的新进自然人股东数据
 * @param startIndex 起始索引，用于生成唯一ID
 * @returns 转换后的表格数据
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 * @modified 2025-06-23 17:30:02 - 修复字段名映射错误，API实际返回的是shareholding_ratio而不是shareholder_ratio
 */
function transformNewIndividualData(apiData: NewIndividualShareholder[], startIndex = 0): ShareholderItem[] {
	// 过滤掉空的或无效的数据项
	const validData = apiData.filter(item =>
		item &&
		(item.name || item.unified_account_number || item.number_of_shares || item.shareholding_ratio)
	);

	const list = validData.map((item, index) => ({
		id: `new-individual-${startIndex + index + 1}`,
		name: item?.name || "",
		identifier: item?.unified_account_number || "",
		shares: formatNumber(item?.number_of_shares), // 当期持股数量
		percentage: formatPercentage(item?.shareholding_ratio), // 当期持股比例 - 修复字段名
		reason: item?.unified_account_number || "",
		date: formatDate(item?.register_date) // 发生期数
	}));

	return list;
}

/**
 * 转换退出自然人股东数据
 * @param apiData API返回的退出自然人股东数据
 * @param startIndex 起始索引，用于生成唯一ID
 * @returns 转换后的表格数据
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function transformExitIndividualData(apiData: ExitIndividualShareholder[], startIndex = 0): ShareholderItem[] {
	// 过滤掉空的或无效的数据项
	const validData = apiData.filter(item =>
		item &&
		(item.name || item.unified_account_number || item.prev_numberofshares || item.prev_shareholdingratio)
	);

	return validData.map((item, index) => ({
		id: `exit-individual-${startIndex + index + 1}`,
		name: item.name,
		identifier: item.unified_account_number,
		shares: formatNumber(item.prev_numberofshares), // 退出前持股
		percentage: formatPercentage(item.prev_shareholdingratio), // 退出前持股比例
		reason: item.unified_account_number,
		date: formatDate(item.exit_date) // 发生期数
	}));
}

/**
 * 转换持续持股个人股东数据
 * @param apiData API返回的持续持股个人股东数据
 * @param startIndex 起始索引，用于生成唯一ID
 * @returns 转换后的表格数据
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股个人股东数据转换函数
 */
function transformConstantIndividualData(apiData: ConstantIndividualShareholder[], startIndex = 0): ShareholderItem[] {
	// 过滤掉空的或无效的数据项
	const validData = apiData.filter(item =>
		item &&
		(item.name || item.unified_account_number || item.current_numberofshares || item.current_shareholdingratio)
	);

	return validData.map((item, index) => ({
		id: `constant-individual-${startIndex + index + 1}`,
		rank: item?.rank || "",
		name: item?.name || "",
		identifier: item?.unified_account_number || "",
		shares: formatNumber(item?.current_numberofshares), // 当前持股数量
		percentage: formatPercentage(item?.current_shareholdingratio), // 当前持股比例(%)
		reason: item?.unified_account_number || "",
		date: "", // 持续持股数据没有特定日期
		currentShares: formatNumber(item?.current_numberofshares), // 持股
	}));
}

/**
 * 创建分页信息
 * @param apiData API响应数据
 * @param currentPage 当前页码
 * @returns 分页信息对象
 * <AUTHOR>
 * @created 2025-06-19 11:01:28
 */
function createPaginationInfo(apiData: any, currentPage: number): PaginationInfo {
	try {
		// 从API响应中提取总数信息，支持多种数据结构
		let total = 0;

		if (apiData?.data) {
			// 尝试从不同的数据结构中获取total
			if (Array.isArray(apiData.data) && apiData.data.length > 0) {
				// 数组格式：data[0].data.total
				total = apiData.data[0]?.data?.total || 0;
			} else if (apiData.data.total !== undefined) {
				// 直接对象格式：data.total
				total = apiData.data.total;
			}
		}

		const pageSize = 10;
		const totalPages = Math.ceil(total / pageSize);

		return {
			total,
			page: currentPage,
			limit: pageSize,
			totalPages
		};
	} catch (error) {
		console.error('创建分页信息失败:', error);
		return {
			total: 0,
			page: currentPage,
			limit: 10,
			totalPages: 1
		};
	}
}

/**
 * 股东变动分析 Hook 配置选项
 * @interface UseShareholderChangesOptions
 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
 * @modified 2025-06-19 11:01:28 - 添加数据处理相关配置选项
 */
export interface UseShareholderChangesOptions {
	/** 是否启用查询，默认为 true */
	enabled?: boolean;
	/** 数据刷新间隔（毫秒），默认不自动刷新 */
	refetchInterval?: number;
	/** 是否在窗口重新获得焦点时重新获取数据，默认为 false */
	refetchOnWindowFocus?: boolean;
	/** 缓存时间（毫秒），默认 5 分钟 */
	staleTime?: number;
	/** 垃圾回收时间（毫秒），默认 10 分钟 */
	gcTime?: number;
	/** 页码，默认为 1 */
	page?: number;
	/** 每页条数，默认为 5 */
	pageSize?: number;
	/** 排序方向，可选参数，Desc或Asc */
	order?: "Desc" | "Asc";
	/** 排序字段，可选参数，具体字段名参考响应参数 */
	orderBase?: string;
}

/**
 * 股东变动分析 Hook 返回值
 * @interface UseShareholderChangesReturn
 * @modified 2025-06-19 11:01:28 - 添加处理后的数据和分页信息
 */
export interface UseShareholderChangesReturn<T> {
	/** 原始股东变动分析数据 */
	data: BaseApiResponse<T> | undefined;
	/** 处理后的股东数据 */
	processedData: ShareholderItem[];
	/** 分页信息 */
	pagination: PaginationInfo;
	/** 是否正在加载 */
	isLoading: boolean;
	/** 是否加载中（包括后台刷新） */
	isFetching: boolean;
	/** 错误信息 */
	error: Error | null;
	/** 是否有错误 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动刷新数据 */
	refetch: () => Promise<any>;
	/** 是否正在刷新 */
	isRefetching: boolean;
}

/**
 * 增持自然人股东数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 增持自然人股东数据和相关状态
 * 
 * @example
 * ```typescript
 * const { data, isLoading, error } = useIncreaseIndividualShareholders("12345", {
 *   page: 1,
 *   pageSize: 5
 * });
 * 
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) console.log(data.data.increaseIndividuals);
 * ```
 * 
 * <AUTHOR>
 * @created 2025-06-16 18:35:23
 */
export function useIncreaseIndividualShareholders(
	organizationId: string,
	options: UseShareholderChangesOptions = {}
): UseShareholderChangesReturn<IncreaseIndividualShareholdersData> {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
		page = 1,
		pageSize = 5,
		order,
		orderBase,
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<IncreaseIndividualShareholdersData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["increaseIndividualShareholders", organizationId, page, pageSize, order, orderBase],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				const result = await shareholderChangesApi.getIncreaseIndividualShareholders(organizationId, page, pageSize, order, orderBase);
				return result;
			} catch (error: any) {
				// 显示用户友好的错误提示
				let errorMessage = "获取增持自然人股东数据失败";

				if (error.message.includes('INVALID_ORGANIZATION_ID')) {
					errorMessage = "无效的组织ID";
				} else if (error.message.includes('ORGANIZATION_ID_LENGTH_INVALID')) {
					errorMessage = "组织ID长度无效";
				} else if (error.message.includes('NO_DATA_FOUND')) {
					errorMessage = "未找到该组织的数据";
				} else if (error.message.includes('INTERNAL_ERROR')) {
					errorMessage = "服务器内部错误，请稍后重试";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,

		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 1;
		},

		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	// 处理数据和分页信息 - 添加于2025年06月19日11:01:28
	const processedData = useMemo(() => {
		if (!query.data) {
			return [];
		}

		const parsedData = parseApiData<IncreaseIndividualShareholder>(query.data);
		const startIndex = (page - 1) * pageSize;
		return transformIncreaseIndividualData(parsedData, startIndex);
	}, [query.data, page, pageSize]);

	const pagination = useMemo(() => {
		return createPaginationInfo(query.data, page);
	}, [query.data, page]);

	return {
		data: query.data,
		processedData,
		pagination,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}



/**
 * 减持自然人股东数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 减持自然人股东数据和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useDecreaseIndividualShareholders("12345", {
 *   page: 1,
 *   pageSize: 5
 * });
 *
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) console.log(data.data.decreaseIndividuals);
 * ```
 *
 * <AUTHOR>
 * @created 2025-06-16 18:35:23
 */
export function useDecreaseIndividualShareholders(
	organizationId: string,
	options: UseShareholderChangesOptions = {}
): UseShareholderChangesReturn<DecreaseIndividualShareholdersData> {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
		page = 1,
		pageSize = 5,
		order,
		orderBase,
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<DecreaseIndividualShareholdersData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["decreaseIndividualShareholders", organizationId, page, pageSize, order, orderBase],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				const result = await shareholderChangesApi.getDecreaseIndividualShareholders(organizationId, page, pageSize, order, orderBase);
				return result;
			} catch (error: any) {
				// 显示用户友好的错误提示
				let errorMessage = "获取减持自然人股东数据失败";

				if (error.message.includes('INVALID_ORGANIZATION_ID')) {
					errorMessage = "无效的组织ID";
				} else if (error.message.includes('ORGANIZATION_ID_LENGTH_INVALID')) {
					errorMessage = "组织ID长度无效";
				} else if (error.message.includes('NO_DATA_FOUND')) {
					errorMessage = "未找到该组织的数据";
				} else if (error.message.includes('INTERNAL_ERROR')) {
					errorMessage = "服务器内部错误，请稍后重试";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,

		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 1;
		},

		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	// 处理数据和分页信息 - 添加于2025年06月19日11:01:28
	const processedData = useMemo(() => {
		if (!query.data) {
			return [];
		}

		const parsedData = parseApiData<DecreaseIndividualShareholder>(query.data);
		const startIndex = (page - 1) * pageSize;
		return transformDecreaseIndividualData(parsedData, startIndex);
	}, [query.data, page, pageSize]);

	const pagination = useMemo(() => {
		return createPaginationInfo(query.data, page);
	}, [query.data, page]);

	return {
		data: query.data,
		processedData,
		pagination,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}

/**
 * 新进自然人股东数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 新进自然人股东数据和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useNewIndividualShareholders("12345", {
 *   page: 1,
 *   pageSize: 5
 * });
 *
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) console.log(data.data.newIndividuals);
 * ```
 *
 * <AUTHOR>
 * @created 2025-06-17 10:13:14
 */
export function useNewIndividualShareholders(
	organizationId: string,
	options: UseShareholderChangesOptions = {}
): UseShareholderChangesReturn<NewIndividualShareholdersData> {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
		page = 1,
		pageSize = 5,
		order,
		orderBase,
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<NewIndividualShareholdersData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["newIndividualShareholders", organizationId, page, pageSize, order, orderBase],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				const result = await shareholderChangesApi.getNewIndividualShareholders(organizationId, page, pageSize, order, orderBase);
				return result;
			} catch (error: any) {
				// 显示用户友好的错误提示
				let errorMessage = "获取新进自然人股东数据失败";

				if (error.message.includes('INVALID_ORGANIZATION_ID')) {
					errorMessage = "无效的组织ID";
				} else if (error.message.includes('ORGANIZATION_ID_LENGTH_INVALID')) {
					errorMessage = "组织ID长度无效";
				} else if (error.message.includes('NO_DATA_FOUND')) {
					errorMessage = "未找到该组织的数据";
				} else if (error.message.includes('INTERNAL_ERROR')) {
					errorMessage = "服务器内部错误，请稍后重试";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,

		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 1;
		},

		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	// 处理数据和分页信息 - 添加于2025年06月19日11:01:28
	const processedData = useMemo(() => {
		if (!query.data) {
			return [];
		}

		const parsedData = parseApiData<NewIndividualShareholder>(query.data);
		const startIndex = (page - 1) * pageSize;
		return transformNewIndividualData(parsedData, startIndex);
	}, [query.data, page, pageSize]);

	const pagination = useMemo(() => {
		return createPaginationInfo(query.data, page);
	}, [query.data, page]);

	return {
		data: query.data,
		processedData,
		pagination,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}

/**
 * 退出自然人股东数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 退出自然人股东数据和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useExitIndividualShareholders("12345", {
 *   page: 1,
 *   pageSize: 5
 * });
 *
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) console.log(data.data.exitIndividuals);
 * ```
 *
 * <AUTHOR>
 * @created 2025-06-17 10:13:14
 */
export function useExitIndividualShareholders(
	organizationId: string,
	options: UseShareholderChangesOptions = {}
): UseShareholderChangesReturn<ExitIndividualShareholdersData> {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
		page = 1,
		pageSize = 5,
		order,
		orderBase,
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<ExitIndividualShareholdersData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["exitIndividualShareholders", organizationId, page, pageSize, order, orderBase],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				const result = await shareholderChangesApi.getExitIndividualShareholders(organizationId, page, pageSize, order, orderBase);
				return result;
			} catch (error: any) {
				// 显示用户友好的错误提示
				let errorMessage = "获取退出自然人股东数据失败";

				if (error.message.includes('INVALID_ORGANIZATION_ID')) {
					errorMessage = "无效的组织ID";
				} else if (error.message.includes('ORGANIZATION_ID_LENGTH_INVALID')) {
					errorMessage = "组织ID长度无效";
				} else if (error.message.includes('NO_DATA_FOUND')) {
					errorMessage = "未找到该组织的数据";
				} else if (error.message.includes('INTERNAL_ERROR')) {
					errorMessage = "服务器内部错误，请稍后重试";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,

		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 1;
		},

		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	// 处理数据和分页信息 - 添加于2025年06月19日11:01:28
	const processedData = useMemo(() => {
		if (!query.data) {
			return [];
		}

		const parsedData = parseApiData<ExitIndividualShareholder>(query.data);
		const startIndex = (page - 1) * pageSize;
		return transformExitIndividualData(parsedData, startIndex);
	}, [query.data, page, pageSize]);

	const pagination = useMemo(() => {
		return createPaginationInfo(query.data, page);
	}, [query.data, page]);

	return {
		data: query.data,
		processedData,
		pagination,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}

/**
 * 持续持股个人股东数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 持续持股个人股东数据和相关状态
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useConstantIndividualShareholders("12345", {
 *   page: 1,
 *   pageSize: 5
 * });
 *
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) console.log(data.data.constantIndividuals);
 * ```
 *
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股个人股东API支持
 */
export function useConstantIndividualShareholders(
	organizationId: string,
	options: UseShareholderChangesOptions = {}
): UseShareholderChangesReturn<ConstantIndividualShareholdersData> {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
		page = 1,
		pageSize = 5,
		order,
		orderBase,
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<ConstantIndividualShareholdersData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["constantIndividualShareholders", organizationId, page, pageSize, order, orderBase],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				return await shareholderChangesApi.getConstantIndividualShareholders(
					organizationId,
					page,
					pageSize,
					order,
					orderBase,
				);
			} catch (error) {
				// 统一错误处理和用户提示
				const errorMessage = error instanceof Error ? error.message : "获取持续持股个人股东信息失败";
				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,

		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 1;
		},

		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	// 处理数据和分页信息
	const processedData = useMemo(() => {
		if (!query.data) {
			return [];
		}

		const parsedData = parseApiData<ConstantIndividualShareholder>(query.data);
		const startIndex = (page - 1) * pageSize;
		return transformConstantIndividualData(parsedData, startIndex);
	}, [query.data, page, pageSize]);

	const pagination = useMemo(() => {
		return createPaginationInfo(query.data, page);
	}, [query.data, page]);

	return {
		data: query.data,
		processedData,
		pagination,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}
