import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { shareholderApi } from "@saas/shareholder/lib/api";
import { shareholderRegistryApi } from "@saas/shareholder/lib/registry-api";
import type {
	ShareholderChangeItem,
	SortOrder,
	SortType, // 新增：排序类型 - 添加于 2025-06-17 16:47:20.601
	RegisterDateItem,
	PaginationInfo,
} from "@saas/shareholder/lib/types";

/**
 * 股东持股变化分析钩子
 * 用于获取和管理股东持股变化分析数据
 *
 * <AUTHOR>
 * @created 2024-07-01 15:30:45.621
 * @modified 2025年06月17日 17:09:31.041 - 更新sortType支持具体期数日期排序，支持YYYY-MM-DD格式的日期作为排序字段
 * @param organizationId 组织ID
 * @param initialStartDate 初始开始日期（YYYY-MM-DD格式）
 * @param initialEndDate 初始结束日期（YYYY-MM-DD格式）
 * @returns 股东持股变化分析数据和控制函数
 * @time 2025年06月17日 17:09:31.041
 */
export function useShareholderChanges(
  organizationId: string,
  initialStartDate: string,
  initialEndDate: string
) {
  // 获取 React Query 的 queryClient 用于手动刷新缓存 - 添加于 2025-06-26 11:53:14.842
  const queryClient = useQueryClient();

  // 基础状态
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [shareholderType, setShareholderType] = useState<string | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState<string | undefined>(undefined);
  const [sortType, setSortType] = useState<SortType>('rank'); // 修改：排序类型，支持'rank'、'date'或具体日期(YYYY-MM-DD)，默认按排名排序 - 修改于 2025-06-17 17:09:31.041
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // 分页状态
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // 数据缓存
  const [shareholderChanges, setShareholderChanges] = useState<ShareholderChangeItem[]>([]);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [totalPeriods, setTotalPeriods] = useState(0);

  // 使用 useRef 跟踪是否应该重置数据
  const shouldResetData = useRef(false);

  // 跟踪日期设置的来源
  const dateSourceRef = useRef({
    startDateSetByUser: false,
    endDateSetByUser: false
  });

  // 分页信息状态 - 添加于 2025-06-13 14:58:45.257
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
  });

  // 获取期数日期列表
  const registerDatesQuery = useQuery({
    queryKey: ["shareholder-register-dates", organizationId],
    queryFn: async () => {
      if (!organizationId) {
        return { registerDates: [] };
      }

      try {
        const result = await shareholderRegistryApi.getRegisterDates(organizationId);
        return result;
      } catch (error) {
        toast.error("获取股东名册日期列表失败");
        return { registerDates: [] };
      }
    },
    enabled: !!organizationId,
    staleTime: 30 * 1000, // 30秒内不重新获取
  });

  // 获取股东类型列表
  const shareholderTypesQuery = useQuery({
    queryKey: ["shareholder-types", organizationId],
    queryFn: async () => {
      if (!organizationId) {
        return [];
      }

      try {
        const types = await shareholderApi.getShareholderTypes(organizationId);
        return types;
      } catch (error) {
        toast.error("获取股东类型列表失败");
        return [];
      }
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
  });

  // 获取股东持股变化分析数据
  const query = useQuery({
    queryKey: [
      "shareholder-changes",
      organizationId,
      startDate,
      endDate,
      shareholderType,
      searchTerm,
      sortType,
      sortOrder,
      page,
      limit,
    ],
    queryFn: async () => {
      if (!organizationId || !startDate || !endDate) {
        return {
          analysisRange: {
            startDate: startDate || "",
            endDate: endDate || "",
            totalPeriods: 0,
          },
          availableDates: [],
          shareholderChanges: [],
          pagination: {
            total: 0,
            page,
            limit,
            totalPages: 0,
          },
        };
      }

      try {
        const result = await shareholderApi.getShareholdingChanges(
          organizationId,
          {
            startDate,
            endDate,
            shareholderType,
            searchTerm,
            sortType, // 新增：传递排序类型参数 - 添加于 2025-06-17 16:47:20.601
            sortOrder,
            page,
            limit,
          },
        );
        return result;
      } catch (error) {
        toast.error("获取股东持股变化分析数据失败");
        return {
          analysisRange: {
            startDate,
            endDate,
            totalPeriods: 0,
          },
          availableDates: [],
          shareholderChanges: [],
          pagination: {
            total: 0,
            page,
            limit,
            totalPages: 0,
          },
        };
      }
    },
    enabled: !!organizationId && !!startDate && !!endDate,
    staleTime: 30 * 1000, // 30秒内不重新获取
  });

  // 当查询参数变化时，标记需要重置数据 - 修改于 2025-06-17 17:09:31.041，更新sortType判断逻辑
  useEffect(() => {
    if (
      startDate !== initialStartDate ||
      endDate !== initialEndDate ||
      shareholderType !== undefined ||
      searchTerm !== undefined ||
      sortType !== "rank" || // 修改：sortType变化时也需要重置数据，支持具体日期格式
      sortOrder !== "desc"
    ) {
      // 修改：默认sortOrder改为desc，与API文档保持一致
      shouldResetData.current = true;
    }
  }, [
    startDate,
    endDate,
    shareholderType,
    searchTerm,
    sortType,
    sortOrder,
    initialStartDate,
    initialEndDate,
  ]);

  // 处理查询数据变化 - 修改于 2025-07-01 10:06:27.451，修复数据处理逻辑
  useEffect(() => {
    if (query.data) {
      // 更新可用日期和总期数
      setAvailableDates(query.data.availableDates || []);
      setTotalPeriods(query.data.analysisRange?.totalPeriods || 0);

      // 处理分页状态
      const paginationData = query.data.pagination || {
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };

      // 更新分页信息状态 - 添加于 2025-06-13 14:58:45.257
      setPagination(paginationData);

      // 处理股东变化数据
      const newShareholderChanges = query.data.shareholderChanges || [];

      // 如果需要重置数据或者是第一页，直接替换
      if (shouldResetData.current || paginationData.page === 1) {
        setShareholderChanges(newShareholderChanges);
        shouldResetData.current = false;
      }
      // 否则追加数据（用于滚动加载更多）
      else {
        setShareholderChanges((prev) => [
          ...prev,
          ...newShareholderChanges,
        ]);
      }

      // 更新分页状态
      setHasNextPage(paginationData.page < paginationData.totalPages);

      // 结束加载更多状态
      setIsLoadingMore(false);
    }
  }, [query.data]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (hasNextPage && !query.isLoading && !isLoadingMore) {
      setIsLoadingMore(true);
      setPage(prevPage => prevPage + 1);
    }
  }, [hasNextPage, query.isLoading, isLoadingMore]);

  // 重置搜索和筛选条件 - 修改于 2025-06-17 16:47:20.601，添加sortType重置
  const resetFilters = useCallback(() => {
    setShareholderType(undefined);
    setSearchTerm(undefined);
    setSortType('rank'); // 新增：重置排序类型为默认值
    setSortOrder('desc'); // 修改：重置排序方向为默认值desc
    setPage(1);
    shouldResetData.current = true;
  }, []);

  // 处理日期范围变更，确保开始日期不大于结束日期，结束日期不小于开始日期
  const handleDateRangeChange = useCallback((start: string, end: string) => {
    // 验证日期格式和有效性
    const startDateObj = new Date(start);
    const endDateObj = new Date(end);

    if (Number.isNaN(startDateObj.getTime()) || Number.isNaN(endDateObj.getTime())) {
      toast.error("日期格式无效");
      return;
    }

    // 确保开始日期不大于结束日期
    if (startDateObj > endDateObj) {
      // 如果用户选择的开始日期大于结束日期，则两者都设为开始日期
      setStartDate(start);
      setEndDate(start);
    } else {
      setStartDate(start);
      setEndDate(end);
    }

    setPage(1);
    shouldResetData.current = true;
  }, []);

  // 安全设置开始日期（保证不大于结束日期）
  const safeSetStartDate = useCallback((date: string) => {
    const newStartDate = new Date(date);
    const currentEndDate = new Date(endDate);

    // 标记为用户设置的日期
    dateSourceRef.current.startDateSetByUser = true;

    if (newStartDate > currentEndDate) {
      // 如果新开始日期大于当前结束日期，同时更新结束日期
      setEndDate(date);
      dateSourceRef.current.endDateSetByUser = true;
    }

    setStartDate(date);
    setPage(1);
    shouldResetData.current = true;
  }, [endDate]);

  // 安全设置结束日期（保证不小于开始日期）
  const safeSetEndDate = useCallback((date: string) => {
    const newEndDate = new Date(date);
    const currentStartDate = new Date(startDate);

    // 标记为用户设置的日期
    dateSourceRef.current.endDateSetByUser = true;

    if (newEndDate < currentStartDate) {
      // 如果新结束日期小于当前开始日期，同时更新开始日期
      setStartDate(date);
      dateSourceRef.current.startDateSetByUser = true;
    }

    setEndDate(date);
    setPage(1);
    shouldResetData.current = true;
  }, [startDate]);

  // 重置用户的日期选择状态
  const resetUserDateSelection = useCallback(() => {
    dateSourceRef.current.startDateSetByUser = false;
    dateSourceRef.current.endDateSetByUser = false;
  }, []);

  /**
   * 重置所有状态到初始状态
   * 包括日期、筛选条件、分页等所有筛选和排序状态
   * 并刷新基础数据
   *
   * <AUTHOR>
   * @created 2025年06月11日 18:11:24
   * @modified 2025-06-26 11:53:14.842 - 添加相关缓存刷新，确保数据同步
   * @modified 2025-07-01 10:06:27.451 - 修复函数结构和逻辑
   * @returns void
   * @time 2025-07-01 10:06:27.451
   */
  const resetAllFilters = useCallback(() => {
    // 重置日期到初始值（但不设置具体值，留给useEffect根据registerDates设置）
    resetUserDateSelection(); // 重置用户日期选择状态标记

    // 重置所有筛选条件
    setShareholderType(undefined);
    setSearchTerm(undefined);
    setSortType('rank');
    setSortOrder('desc');
    setPage(1);
    shouldResetData.current = true;

    // 刷新基础数据
    shareholderTypesQuery.refetch(); // 刷新股东类型列表
    registerDatesQuery.refetch(); // 刷新期数日期列表

    // 刷新相关缓存，确保数据同步 - 添加于 2025-06-26 11:53:14.842
    queryClient.invalidateQueries({
      queryKey: ["shareholder-changes", organizationId]
    });
    queryClient.invalidateQueries({
      queryKey: ["shareholders", organizationId]
    });
    queryClient.invalidateQueries({
      queryKey: ["shareholder-registry-list", organizationId]
    });
  }, [resetUserDateSelection, shareholderTypesQuery, registerDatesQuery, queryClient, organizationId]);

  // 增强版setLimit函数，改变每页条数时重置页码
  const handleLimitChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
    shouldResetData.current = true;
  }, []);

  /**
   * 处理股东类型变更，重置分页和数据
   *
   * <AUTHOR>
   * @created 2025年06月13日 15:21:43
   * @param {string | undefined} type - 股东类型
   * @returns void
   * @time 2025年06月13日 15:21:43
   */
  const handleShareholderTypeChange = useCallback((type: string | undefined) => {
    setShareholderType(type);
    setPage(1);
    shouldResetData.current = true;
  }, []);

  // 从期数日期列表中提取日期字符串
  const registerDates = registerDatesQuery.data?.registerDates?.map(
    (item: RegisterDateItem | string) => {
      if (typeof item === 'string') {
        return item;
      }
      return item.registerDate;
    }
  ) || [];

  /**
   * 强制刷新所有相关数据
   * 用于解决缓存数据不同步的问题
   *
   * <AUTHOR>
   * @created 2025-06-26 11:53:14.842
   * @returns void
   * @time 2025-06-26 11:53:14.842
   */
  const forceRefreshAllData = useCallback(() => {
    // 刷新当前组件的所有查询
    query.refetch();
    shareholderTypesQuery.refetch();
    registerDatesQuery.refetch();

    // 刷新相关模块的缓存
    queryClient.invalidateQueries({
      queryKey: ["shareholders", organizationId]
    });
    queryClient.invalidateQueries({
      queryKey: ["shareholder-registry-list", organizationId]
    });
    queryClient.invalidateQueries({
      queryKey: ["shareholder-registry-register-dates", organizationId]
    });

    // 标记需要重置数据
    shouldResetData.current = true;
  }, [query, shareholderTypesQuery, registerDatesQuery, queryClient, organizationId]);

  return {
    // 数据
    shareholderChanges,
    availableDates: registerDates.length > 0 ? registerDates : availableDates,
    totalPeriods,
    shareholderTypes: shareholderTypesQuery.data || [],
    registerDates,
    pagination, // 添加分页信息 - 添加于 2025-06-13 14:58:45.257

    // 状态
    isLoading: query.isLoading,
    isLoadingMore,
    hasNextPage,
    error: query.error,

    // 筛选和排序状态
    startDate,
    endDate,
    shareholderType,
    searchTerm,
    sortType,
    sortOrder,
    page,
    limit,

    // 控制函数
    setStartDate: safeSetStartDate,
    setEndDate: safeSetEndDate,
    setShareholderType: handleShareholderTypeChange,
    setSearchTerm: (term: string | undefined) => {
      setSearchTerm(term);
      setPage(1);
      shouldResetData.current = true;
    },
    setSortType: (type: SortType) => {
      setSortType(type);
      setPage(1);
      shouldResetData.current = true;
    },
    setSortOrder: (order: SortOrder) => {
      setSortOrder(order);
      setPage(1);
      shouldResetData.current = true;
    },
    setPage,
    setLimit: handleLimitChange,
    handleDateRangeChange,
    loadMore,
    resetFilters,
    resetAllFilters,

    // 刷新数据
    refetch: query.refetch,
    refetchShareholderTypes: shareholderTypesQuery.refetch,
    refetchRegisterDates: registerDatesQuery.refetch,
    forceRefreshAllData, // 新增：强制刷新所有相关数据 - 添加于 2025-06-26 11:53:14.842
  };
}
