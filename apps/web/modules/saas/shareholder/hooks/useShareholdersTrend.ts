/**
 * 股东结构历史趋势数据 Hook
 * @file useShareholdersTrend.ts
 * @description 用于获取和管理股东结构历史趋势数据的 React Hook
 * <AUTHOR>
 * @created 2025-06-16 16:44:16
 * @modified 2025-06-16 17:41:17 - 修复 React Query v5 兼容性问题：
 *   1. 将 cacheTime 替换为 gcTime
 *   2. 移除已弃用的 onError 和 onSuccess 回调
 *   3. 在 queryFn 中直接处理错误和成功逻辑
 *   4. 简化 useQuery 泛型参数
 */

import { useQuery } from "@tanstack/react-query";
import { shareholdersTrendApi, type ShareholdersTrendData } from "@saas/shareholder/lib/shareholders-trend-api";
import type { BaseApiResponse } from "@saas/shareholder/lib/base-shareholder-api";
import { toast } from "sonner";

/**
 * 股东结构历史趋势 Hook 配置选项
 * @interface UseShareholdersTrendOptions
 */
export interface UseShareholdersTrendOptions {
	/** 是否启用查询，默认为 true */
	enabled?: boolean;
	/** 数据刷新间隔（毫秒），默认不自动刷新 */
	refetchInterval?: number;
	/** 是否在窗口重新获得焦点时重新获取数据，默认为 false */
	refetchOnWindowFocus?: boolean;
	/** 缓存时间（毫秒），默认 5 分钟 */
	staleTime?: number;
	/** 垃圾回收时间（毫秒），默认 10 分钟 */
	gcTime?: number;
}

/**
 * 股东结构历史趋势 Hook 返回值
 * @interface UseShareholdersTrendReturn
 */
export interface UseShareholdersTrendReturn {
	/** 股东结构历史趋势数据 */
	data: BaseApiResponse<ShareholdersTrendData> | undefined;
	/** 是否正在加载 */
	isLoading: boolean;
	/** 是否加载中（包括后台刷新） */
	isFetching: boolean;
	/** 错误信息 */
	error: Error | null;
	/** 是否有错误 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动刷新数据 */
	refetch: () => Promise<any>;
	/** 是否正在刷新 */
	isRefetching: boolean;
}

/**
 * 股东结构历史趋势数据 Hook
 * @param organizationId 组织ID，必填参数，长度5-50个字符
 * @param options Hook 配置选项
 * @returns 股东结构历史趋势数据和相关状态
 * 
 * @example
 * ```typescript
 * const { data, isLoading, error, refetch } = useShareholdersTrend("12345", {
 *   enabled: true,
 *   refetchInterval: 30000, // 30秒自动刷新
 * });
 * 
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) return <div>{data.data.trendData.length} 条趋势数据</div>;
 * ```
 * 
 * <AUTHOR>
 * @created 2025-06-16 16:44:16
 */
export function useShareholdersTrend(
	organizationId: string,
	options: UseShareholdersTrendOptions = {}
): UseShareholdersTrendReturn {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<ShareholdersTrendData>>({
		// 查询键，用于缓存和重新验证
		queryKey: ["shareholdersTrend", organizationId],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织ID不能为空");
			}

			try {
				const result = await shareholdersTrendApi.getShareholdersTrend(organizationId);
				return result;
			} catch (error: any) {
				// 显示用户友好的错误提示
				let errorMessage = "获取股东结构历史趋势数据失败";

				if (error.message.includes('INVALID_ORGANIZATION_ID')) {
					errorMessage = "无效的组织ID";
				} else if (error.message.includes('ORGANIZATION_ID_LENGTH_INVALID')) {
					errorMessage = "组织ID长度无效";
				} else if (error.message.includes('NO_DATA_FOUND')) {
					errorMessage = "未找到该组织的数据";
				} else if (error.message.includes('INTERNAL_ERROR')) {
					errorMessage = "服务器内部错误，请稍后重试";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},
		
		// 配置选项
		enabled: enabled && !!organizationId,
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,
		

		
		// 重试配置
		retry: (failureCount, error) => {
			// 对于客户端错误（4xx）不重试
			if (error.message.includes('400') || error.message.includes('INVALID_')) {
				return false;
			}
			// 最多重试 3 次
			return failureCount < 3;
		},
		
		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	return {
		data: query.data,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}

/**
 * 获取股东结构历史趋势查询键
 * @param organizationId 组织ID
 * @returns 查询键数组
 * 
 * @example
 * ```typescript
 * const queryKey = getShareholdersTrendQueryKey("12345");
 * queryClient.invalidateQueries(queryKey);
 * ```
 */
export function getShareholdersTrendQueryKey(organizationId: string): readonly string[] {
	return ["shareholdersTrend", organizationId] as const;
}

/**
 * 预取股东结构历史趋势数据
 * @param queryClient React Query 客户端实例
 * @param organizationId 组织ID
 * @returns Promise
 * 
 * @example
 * ```typescript
 * import { useQueryClient } from "@tanstack/react-query";
 * 
 * const queryClient = useQueryClient();
 * await prefetchShareholdersTrend(queryClient, "12345");
 * ```
 */
export async function prefetchShareholdersTrend(
	queryClient: any,
	organizationId: string
): Promise<void> {
	await queryClient.prefetchQuery({
		queryKey: getShareholdersTrendQueryKey(organizationId),
		queryFn: () => shareholdersTrendApi.getShareholdersTrend(organizationId),
		staleTime: 5 * 60 * 1000, // 5分钟
	});
}
