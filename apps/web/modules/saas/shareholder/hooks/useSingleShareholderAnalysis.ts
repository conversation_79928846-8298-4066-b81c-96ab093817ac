/**
 * 独立股东详情分析数据 Hook
 * @file useSingleShareholderAnalysis.ts
 * @description 用于获取和管理独立股东详情分析数据的 React Hook
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 */

import { useQuery } from "@tanstack/react-query";
import { singleShareholderAnalysisApi, type SingleShareholderAnalysisData } from "@saas/shareholder/lib/single-shareholder-analysis-api";
import type { BaseApiResponse } from "@saas/shareholder/lib/base-shareholder-api";
import { toast } from "sonner";

/**
 * 独立股东详情分析 Hook 配置选项
 * @interface UseSingleShareholderAnalysisOptions
 */
export interface UseSingleShareholderAnalysisOptions {
	/** 是否启用查询，默认为 true */
	enabled?: boolean;
	/** 数据刷新间隔（毫秒），默认不自动刷新 */
	refetchInterval?: number;
	/** 是否在窗口重新获得焦点时重新获取数据，默认为 false */
	refetchOnWindowFocus?: boolean;
	/** 缓存时间（毫秒），默认 5 分钟 */
	staleTime?: number;
	/** 垃圾回收时间（毫秒），默认 10 分钟 */
	gcTime?: number;
}

/**
 * 独立股东详情分析 Hook 返回值
 * @interface UseSingleShareholderAnalysisReturn
 */
export interface UseSingleShareholderAnalysisReturn {
	/** 独立股东详情分析数据 */
	data: BaseApiResponse<SingleShareholderAnalysisData> | undefined;
	/** 是否正在加载 */
	isLoading: boolean;
	/** 是否加载中（包括后台刷新） */
	isFetching: boolean;
	/** 错误信息 */
	error: Error | null;
	/** 是否有错误 */
	isError: boolean;
	/** 是否成功获取数据 */
	isSuccess: boolean;
	/** 手动刷新数据 */
	refetch: () => Promise<any>;
	/** 是否正在刷新 */
	isRefetching: boolean;
}

/**
 * 独立股东详情分析数据 Hook（基金代码查股东名册）
 * @param organizationId 组织机构ID，必填参数
 * @param fund_code 基金代码，与unified_account_number二选一
 * @param unified_account_number 一码通账户号码，与fund_code二选一
 * @param options Hook 配置选项
 * @returns 独立股东详情分析数据和相关状态
 *
 * @example
 * ```typescript
 * // 基金代码查询
 * const { data, isLoading, error, refetch } = useSingleShareholderAnalysis(
 *   "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
 *   "001384.OF",
 *   undefined,
 *   {
 *     enabled: true,
 *     refetchInterval: 30000, // 30秒自动刷新
 *   }
 * );
 *
 * // 一码通查询
 * const { data, isLoading, error, refetch } = useSingleShareholderAnalysis(
 *   "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
 *   undefined,
 *   "A123456789",
 *   {
 *     enabled: true,
 *   }
 * );
 *
 * if (isLoading) return <div>加载中...</div>;
 * if (error) return <div>错误: {error.message}</div>;
 * if (data) return <div>{data.data.account_name}</div>;
 * ```
 *
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 - 重构为支持基金代码和一码通查询
 */
export function useSingleShareholderAnalysis(
	organizationId?: string,
	fund_code?: string,
	unified_account_number?: string,
	options: UseSingleShareholderAnalysisOptions = {}
): UseSingleShareholderAnalysisReturn {
	const {
		enabled = true,
		refetchInterval,
		refetchOnWindowFocus = false,
		staleTime = 5 * 60 * 1000, // 5分钟
		gcTime = 10 * 60 * 1000, // 10分钟
	} = options;

	// 使用 React Query 管理数据获取
	const query = useQuery<BaseApiResponse<SingleShareholderAnalysisData>, Error>({
		// 查询键，用于缓存和重新验证
		queryKey: ["singleShareholderAnalysis", organizationId, fund_code, unified_account_number],

		// 查询函数
		queryFn: async () => {
			if (!organizationId) {
				throw new Error("组织机构ID不能为空");
			}

			if (!fund_code && !unified_account_number) {
				throw new Error("基金代码和一码通账号至少需要提供一个");
			}

			try {
				const result = await singleShareholderAnalysisApi.getSingleShareholderAnalysis(organizationId, fund_code, unified_account_number);

				return result;
			} catch (error: any) {
				// 错误处理
				// console.error("获取独立股东详情分析数据失败:", error);

				// 显示用户友好的错误提示
				let errorMessage = "获取独立股东详情分析数据失败";

				if (error.message.includes('MISSING_ORGANIZATIONID')) {
					errorMessage = "缺少必需参数：组织机构ID";
				} else if (error.message.includes('INSUFFICIENT_QUERY_PARAMS')) {
					errorMessage = "基金代码和一码通账号至少需要提供一个";
				} else if (error.message.includes('ORGANIZATION_NOT_FOUND')) {
					errorMessage = "未找到对应的组织ID";
				} else if (error.message.includes('没有找到该基金')) {
					errorMessage = "没有找到该基金，请检查基金代码";
				} else if (error.message.includes('未找到匹配的股东数据')) {
					errorMessage = "未找到匹配的股东数据";
				} else if (error.message.includes('网络')) {
					errorMessage = "网络连接失败，请检查网络";
				}

				toast.error(errorMessage);
				throw error;
			}
		},

		// 配置选项
		enabled: enabled && !!organizationId && (!!fund_code || !!unified_account_number),
		refetchInterval,
		refetchOnWindowFocus,
		staleTime,
		gcTime,
		// 重试延迟（指数退避）
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});

	return {
		data: query.data,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		isError: query.isError,
		isSuccess: query.isSuccess,
		refetch: query.refetch,
		isRefetching: query.isRefetching,
	};
}

/**
 * 获取独立股东详情分析查询键
 * @param organizationId 组织机构ID
 * @param fund_code 基金代码
 * @param unified_account_number 一码通账户号码
 * @returns 查询键数组
 *
 * @example
 * ```typescript
 * const queryKey = getSingleShareholderAnalysisQueryKey("Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36", "001384.OF");
 * queryClient.invalidateQueries(queryKey);
 * ```
 */
export function getSingleShareholderAnalysisQueryKey(organizationId: string, fund_code?: string, unified_account_number?: string): readonly (string | undefined)[] {
	return ["singleShareholderAnalysis", organizationId, fund_code, unified_account_number] as const;
}

/**
 * 预取独立股东详情分析数据
 * @param queryClient React Query 客户端实例
 * @param organizationId 组织机构ID
 * @param fund_code 基金代码
 * @param unified_account_number 一码通账户号码
 * @returns Promise
 *
 * @example
 * ```typescript
 * import { useQueryClient } from "@tanstack/react-query";
 *
 * const queryClient = useQueryClient();
 * await prefetchSingleShareholderAnalysis(queryClient, "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36", "001384.OF");
 * ```
 */
export async function prefetchSingleShareholderAnalysis(
	queryClient: any,
	organizationId: string,
	fund_code?: string,
	unified_account_number?: string
): Promise<void> {
	await queryClient.prefetchQuery({
		queryKey: getSingleShareholderAnalysisQueryKey(organizationId, fund_code, unified_account_number),
		queryFn: () => singleShareholderAnalysisApi.getSingleShareholderAnalysis(organizationId, fund_code, unified_account_number),
		staleTime: 5 * 60 * 1000, // 5分钟
	});
}
