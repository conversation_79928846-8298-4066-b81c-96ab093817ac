/**
 * 股东模块基础 API 客户端
 * @file base-shareholder-api.ts
 * @description 股东模块所有 API 的共用基础功能，包含参数验证、请求处理、错误处理等通用逻辑
 * <AUTHOR>
 * @created 2025-06-18 09:40:56
 * @modified 2025-06-18 13:52:14 - 添加公司总体报告 API 支持
 */

import { decryptData, createEncryptedRequestAsync, getBaseUrl } from "@repo/utils";

/**
 * 基础请求参数接口
 * @interface BaseRequest
 */
export interface BaseRequest {
	/** 组织ID，长度5-50个字符 */
	id: string;
}

/**
 * 分页请求参数接口
 * @interface PaginationRequest
 */
export interface PaginationRequest extends BaseRequest {
	/** 页码，正整数 */
	page: number;
	/** 每页条数，正整数 */
	pageSize: number;
}

/**
 * 带排序的分页请求参数接口
 * @interface SortablePaginationRequest
 * <AUTHOR>
 * @created 2025-06-18 18:01:01 - 添加排序字段支持，用于模块三API更新
 */
export interface SortablePaginationRequest extends PaginationRequest {
	/** 降序或升序排列(Desc or Asc)，可选参数 */
	order?: "Desc" | "Asc";
	/** 排序依据的字段名，可选参数，具体字段名参考响应参数 */
	order_base?: string;
}

/**
 * 基础 API 响应接口
 * @interface BaseApiResponse
 */
export interface BaseApiResponse<T> {
	/** 请求是否成功 */
	success: boolean;
	/** 响应时间戳（ISO 日期时间字符串） */
	timestamp: string;
	/** 数据内容 */
	data: T;
}

/**
 * API 响应包装接口
 * @interface ApiResponse
 */
interface ApiResponse {
	code: number;
	message: string;
	data: string | any;
}

/**
 * 参数验证错误类型
 * @type ValidationErrorType
 */
type ValidationErrorType = 
	| "INVALID_ORGANIZATION_ID"
	| "ORGANIZATION_ID_LENGTH_INVALID"
	| "INVALID_PAGE"
	| "INVALID_PAGE_SIZE";

/**
 * 验证组织ID参数
 * @param organizationId 组织ID
 * @throws {Error} 当参数无效时抛出错误
 */
export function validateOrganizationId(organizationId: string): void {
	if (!organizationId || typeof organizationId !== "string") {
		throw new Error(
			"INVALID_ORGANIZATION_ID: 无效的organizationId：参数必须是非空字符串",
		);
	}

	if (organizationId.length < 5 || organizationId.length > 50) {
		throw new Error(
			"ORGANIZATION_ID_LENGTH_INVALID: 无效的organizationId长度：参数长度必须在5-50个字符之间",
		);
	}
}

/**
 * 验证分页参数
 * @param page 页码
 * @param pageSize 每页条数
 * @throws {Error} 当参数无效时抛出错误
 */
export function validatePaginationParams(page: number, pageSize: number): void {
	if (!Number.isInteger(page) || page < 1) {
		throw new Error("INVALID_PAGE: 页码必须是正整数");
	}

	if (!Number.isInteger(pageSize) || pageSize < 1) {
		throw new Error("INVALID_PAGE_SIZE: 每页条数必须是正整数");
	}
}

/**
 * 验证排序参数
 * @param order 排序方向，可选参数
 * @param orderBase 排序字段，可选参数
 * @throws {Error} 当参数无效时抛出错误
 * <AUTHOR>
 * @created 2025-06-18 18:01:01 - 添加排序参数验证，用于模块三API更新
 */
export function validateSortParams(order?: string, orderBase?: string): void {
	if (order && !["Desc", "Asc"].includes(order)) {
		throw new Error("INVALID_ORDER: 排序方向必须是 'Desc' 或 'Asc'");
	}

	if (orderBase && typeof orderBase !== "string") {
		throw new Error("INVALID_ORDER_BASE: 排序字段必须是字符串");
	}
}

/**
 * 处理 API 响应错误
 * @param result API 响应结果
 * @throws {Error} 当响应包含错误时抛出错误
 */
export function handleApiResponseError(result: ApiResponse): void {
	if (result.code !== 200) {
		// 处理业务错误
		switch (result.code) {
			case 400:
				if (result.message.includes("MISSING_ORGANIZATION_ID")) {
					throw new Error("缺少必需参数：organizationId");
				}
				if (result.message.includes("ORGANIZATION_ID_LENGTH_INVALID")) {
					throw new Error("organizationId长度必须在5-50个字符之间");
				}
				if (result.message.includes("INVALID_ORGANIZATION_ID")) {
					throw new Error("无效的organizationId：参数必须是非空字符串");
				}
				break;
			case 500:
				if (result.message.includes("NO_DATA_FOUND")) {
					throw new Error("未找到指定组织的数据，请检查organizationId是否正确");
				}
				if (result.message.includes("INTERNAL_ERROR")) {
					throw new Error("服务内部错误，请稍后重试");
				}
				break;
		}
		throw new Error(result.message || "请求失败");
	}

	// 修改记录 2025-08-07 16:21:27 hayden: 添加对解密后数据中错误信息的检查
	// 检查解密后的数据是否包含错误信息（针对n8n返回的特殊错误格式）
	if (result.data && typeof result.data === 'string') {
		try {
			const decryptedData = JSON.parse(result.data);
			if (decryptedData && typeof decryptedData === 'object' && 'code' in decryptedData && 'type' in decryptedData) {
				// 如果解密后的数据包含错误信息，抛出错误
				const errorData = decryptedData as any;
				throw new Error(`${errorData.message || '查询失败'} (${errorData.code})`);
			}
		} catch (parseError) {
			// 如果不是JSON格式，忽略解析错误，继续正常流程
		}
	}
}

/**
 * 解密 API 响应数据
 * @param data 响应数据
 * @returns 解密后的数据
 * <AUTHOR>
 * @updated 2025-06-18 10:31:10 - 修复类型错误：decryptData返回string需要JSON.parse解析为对象
 */
export function decryptApiResponseData<T>(data: string | any): T {
	if (typeof data === "string") {
		// 数据已加密，需要解密
		const decryptedString = decryptData(data);
		try {
			// 解析JSON字符串为对象
			return JSON.parse(decryptedString) as T;
		} catch (error) {
			throw new Error(`解密数据JSON解析失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	} else {
		// 数据未加密，直接使用
		return data as T;
	}
}

/**
 * 创建标准化的 API 响应
 * @param data 响应数据
 * @param timestamp 可选的时间戳，默认使用当前时间
 * @returns 标准化的 API 响应
 */
export function createApiResponse<T>(
	data: T,
	timestamp?: string,
): BaseApiResponse<T> {
	return {
		success: true,
		timestamp: timestamp || new Date().toISOString(),
		data,
	};
}

/**
 * 股东 API 客户端工厂函数
 * @description 创建股东模块的 API 客户端，提供通用的请求处理逻辑
 * <AUTHOR>
 * @created 2025-06-18 09:40:56
 */
export function createShareholderApiClient() {
	return {
		/**
		 * 发送基础请求（不分页）
		 * @param endpoint API 端点名称
		 * @param organizationId 组织ID
		 * @param errorPrefix 错误信息前缀
		 * @returns API 响应数据
		 */
		async sendBaseRequest<T>(
			endpoint: string,
			organizationId: string,
			errorPrefix: string,
		): Promise<BaseApiResponse<T>> {
			try {
				// 参数验证
				validateOrganizationId(organizationId);

				// 创建加密且带签名的请求参数
				const requestData = await createEncryptedRequestAsync({
					id: organizationId,
				});

				// 发送请求到 n8n 代理接口
				// 使用动态路径调用 n8n 代理
				const response = await fetch(`${getBaseUrl()}/api/n8n_proxy/${endpoint}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify(requestData),
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: 请求失败`);
				}

				const result: ApiResponse = await response.json();

				// 检查响应状态
				handleApiResponseError(result);

				// 解密响应数据
				const decryptedData = decryptApiResponseData<T>(result.data);

				// 返回标准化的响应格式
				return createApiResponse(decryptedData);
			} catch (error) {
				// 统一错误处理
				if (error instanceof Error) {
					throw error;
				}
				throw new Error(`${errorPrefix}: ${String(error)}`);
			}
		},

		/**
		 * 发送分页请求
		 * @param endpoint API 端点名称
		 * @param organizationId 组织ID
		 * @param page 页码
		 * @param pageSize 每页条数
		 * @param errorPrefix 错误信息前缀
		 * @returns API 响应数据
		 */
		async sendPaginatedRequest<T>(
			endpoint: string,
			organizationId: string,
			page: number,
			pageSize: number,
			errorPrefix: string,
		): Promise<BaseApiResponse<T>> {
			try {
				// 参数验证
				validateOrganizationId(organizationId);
				validatePaginationParams(page, pageSize);

				// 创建加密且带签名的请求参数
				const requestData = await createEncryptedRequestAsync({
					id: organizationId,
					page,
					pageSize,
				});

				// 发送请求到 n8n 代理接口
				// 使用动态路径调用 n8n 代理
				const response = await fetch(`${getBaseUrl()}/api/n8n_proxy/${endpoint}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify(requestData),
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: 请求失败`);
				}

				const result: ApiResponse = await response.json();

				// 检查响应状态
				handleApiResponseError(result);

				// 解密响应数据
				const decryptedData = decryptApiResponseData<T>(result.data);

				// 返回标准化的响应格式
				return createApiResponse(decryptedData);
			} catch (error) {
				// 统一错误处理
				if (error instanceof Error) {
					throw error;
				}
				throw new Error(`${errorPrefix}: ${String(error)}`);
			}
		},

		/**
		 * 发送带排序的分页请求
		 * @param endpoint API 端点名称
		 * @param organizationId 组织ID
		 * @param page 页码
		 * @param pageSize 每页条数
		 * @param errorPrefix 错误信息前缀
		 * @param order 排序方向，可选参数
		 * @param orderBase 排序字段，可选参数
		 * @returns API 响应数据
		 * <AUTHOR>
		 * @created 2025-06-18 18:01:01 - 添加带排序的分页请求方法，用于模块三API更新
		 */
		async sendSortablePaginatedRequest<T>(
			endpoint: string,
			organizationId: string,
			page: number,
			pageSize: number,
			errorPrefix: string,
			order?: "Desc" | "Asc",
			orderBase?: string,
		): Promise<BaseApiResponse<T>> {
			try {
				// 参数验证
				validateOrganizationId(organizationId);
				validatePaginationParams(page, pageSize);
				validateSortParams(order, orderBase);

				// 创建请求参数对象 - 修改记录 2025-08-11 - 使用具体类型替代any，提高类型安全性 - hayden
				const requestParams: {
					id: string;
					page: number;
					pageSize: number;
					order?: "Desc" | "Asc";
					order_base?: string;
				} = {
					id: organizationId,
					page,
					pageSize,
				};

				// 添加排序参数（如果提供）
				if (order) {
					requestParams.order = order;
				}
				if (orderBase) {
					requestParams.order_base = orderBase;
				}

				// 创建加密且带签名的请求参数
				const requestData = await createEncryptedRequestAsync(requestParams);

				// 发送请求到 n8n 代理接口
				const response = await fetch(`${getBaseUrl()}/api/n8n_proxy/${endpoint}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify(requestData),
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: 请求失败`);
				}

				const result: ApiResponse = await response.json();

				// 检查响应状态
				handleApiResponseError(result);

				// 解密响应数据
				const decryptedData = decryptApiResponseData<T>(result.data);

				// 返回标准化的响应格式
				return createApiResponse(decryptedData);
			} catch (error) {
				// 统一错误处理
				if (error instanceof Error) {
					throw error;
				}
				throw new Error(`${errorPrefix}: ${String(error)}`);
			}
		},

		/**
		 * 发送自定义参数请求
		 * @param endpoint API 端点名称
		 * @param requestParams 自定义请求参数对象
		 * @param errorPrefix 错误信息前缀
		 * @returns API 响应数据
		 * <AUTHOR>
		 * @created 2025-08-07 14:34:20 - 添加自定义参数请求方法，用于股东基金查询API
		 */
		async sendCustomRequest<T>(
			endpoint: string,
			requestParams: Record<string, any>,
			errorPrefix: string,
		): Promise<BaseApiResponse<T>> {
			try {
				// 验证organizationId（支持id和organizationId两种字段名）
				// 修改记录 2025-08-07 15:50:02 hayden: 支持organizationId字段名验证
				if (requestParams.id) {
					validateOrganizationId(requestParams.id);
				}
				if (requestParams.organizationId) {
					validateOrganizationId(requestParams.organizationId);
				}

				// 创建加密且带签名的请求参数
				const requestData = await createEncryptedRequestAsync(requestParams);

				// 发送请求到 n8n 代理接口
				const response = await fetch(`${getBaseUrl()}/api/n8n_proxy/${endpoint}`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify(requestData),
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: 请求失败`);
				}

				const result: ApiResponse = await response.json();

				// 检查响应状态
				handleApiResponseError(result);

				// 解密响应数据
				const decryptedData = decryptApiResponseData<T>(result.data);

				// 返回标准化的响应格式
				return createApiResponse(decryptedData);
			} catch (error) {
				// 统一错误处理
				if (error instanceof Error) {
					throw error;
				}
				throw new Error(`${errorPrefix}: ${String(error)}`);
			}
		},
	};
}
