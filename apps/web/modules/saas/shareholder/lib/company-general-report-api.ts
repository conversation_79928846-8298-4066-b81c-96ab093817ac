/**
 * 公司总体报告 API 接口
 * @file company-general-report-api.ts
 * @description 公司总体报告相关的 API 接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-06-18 13:52:14
 */

import { createShareholderApiClient, type BaseApiResponse } from "./base-shareholder-api";

/**
 * 公司总体报告数据项接口
 * @interface CompanyGeneralReportDataItem
 * @description 单个公司总体报告数据项的结构
 */
export interface CompanyGeneralReportDataItem {
	/** 请求是否成功 */
	success: boolean;
	/** 响应时间戳（ISO 日期时间字符串） */
	timestamp: string;
	/** 报告数据 */
	data: {
		/** AI生成的股东结构智能分析报告内容（Markdown格式） */
		output: string;
	};
}

/**
 * 公司总体报告 API 响应数据类型
 * @description 公司总体报告的完整响应数据，API返回数组格式
 * @modified 2025-06-18 13:59:50 - 修复数据结构，API返回的是数组格式
 */
export type CompanyGeneralReportApiData = CompanyGeneralReportDataItem[];

// 创建 API 客户端实例
const apiClientInstance = createShareholderApiClient();

/**
 * 公司总体报告 API 客户端
 * @description 提供公司总体报告相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-06-18 13:52:14
 */
export const companyGeneralReportApi = {
	/**
	 * 获取公司总体报告
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @returns 公司总体报告数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 *
	 * @example
	 * ```typescript
	 * const report = await companyGeneralReportApi.getCompanyGeneralReport("HQjTJH3Y8AIWHsMwsh5SJAIamiIlgoQ6");
	 * console.log(report.data.output);
	 * ```
	 */
	getCompanyGeneralReport: async (organizationId: string): Promise<BaseApiResponse<CompanyGeneralReportApiData>> => {
		const response = await apiClientInstance.sendBaseRequest<CompanyGeneralReportApiData>(
			"company-general-report",
			organizationId,
			"获取公司总体报告失败",
		);

		// 返回响应数据
		return response;
	}
};
