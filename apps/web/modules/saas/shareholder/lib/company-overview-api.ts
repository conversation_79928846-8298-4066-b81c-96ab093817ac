/**
 * 公司概览 API 接口
 * @file company-overview-api.ts
 * @description 公司概览相关的 API 接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-06-16 11:38:47
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */

import { createShareholderApiClient, type BaseApiResponse } from "./base-shareholder-api";

/**
 * 股东类型分布数据项接口
 * @interface ShareholderTypeItem
 * @description 单个股东类型分布数据项的结构
 * <AUTHOR>
 * @created 2025-06-27 18:28:05
 */
export interface ShareholderTypeItem {
	/** 股东类型名称 */
	shareholderType: string;
	/** 该类型股东数量 */
	typeCount: string;
	/** 该类型股东持股总数（股） */
	typeShares: string;
	/** 该类型股东数量占总股东数比例（%） */
	typePercentage: string;
	/** 该类型股东持股占总股本比例（%） */
	sharesPercentage: string;
	/** 该类型股东户数占比变化百分比（%） */
	typeCountChangePercent?: number;
	/** 该类型股东持股占比变化百分比（%） */
	typeSharesChangePercent?: number;
}

/**
 * 公司概览数据项接口
 * @interface CompanyOverviewDataItem
 * @description 单个公司概览数据项的结构
 * @modified 2025-06-16 12:10:15 - 重构数据结构，支持数组和单个对象两种格式
 * @modified 2025-06-27 18:28:05 - 添加shareholderTypes字段支持股东类型分布数据 - hayden
 */
export interface CompanyOverviewDataItem {
	/** 公司全称 */
	companyName: string;
	/** 公司股票代码 */
	companyCode: string;
	/** 最新一期登记日期，数据基于此日期 */
	registerDate: string;
	/** 该公司最早有数据的登记日期 */
	oldestRegisterDate: string;
	/** 最新一期公司总股本（单位：股） */
	totalShares: number;
	/** 最新一期公司全部股东数量 */
	totalShareholders: number;
	/** 最新一期机构投资者数量 */
	totalInstitutions: number;
	/** 最新一期机构投资者合计持股数（单位：股） */
	institutionShares: number;
	/** 最新一期个人股东数量（=总股东数-机构投资者数） */
	individualShareholders: number;
	/** 上一期公司总股本（单位：股） */
	prevTotalShares: number;
	/** 上一期公司全部股东数量 */
	prevTotalShareholders: number;
	/** 上一期机构投资者数量 */
	prevTotalInstitutions: number;
	/** 上一期机构投资者合计持股数（单位：股） */
	prevInstitutionShares: number;
	/** 上一期个人股东数量 */
	prevIndividualShareholders: number;
	/** 股东总人数变化（最新一期 - 上一期） */
	shareholdersChange: number;
	/** 机构投资者数量变化（最新一期 - 上一期） */
	institutionsChange: number;
	/** 个人股东数量变化（最新一期 - 上一期） */
	individualShareholdersChange: number;
	/** 总股本变动百分比（%），正数为增长，负数为减少 */
	totalSharesChangePercent: number;
	/** 机构持股变动百分比（%），正数为增长，负数为减少 */
	institutionSharesChangePercent: number;
	/** 最新一期信用账户数量 */
	creditAccountCount: number;
	/** 最新一期信用账户对应的总持股数（单位：股） */
	totalMarginShares: number;
	/** 上一期信用账户数量 */
	prevCreditAccountCount: number;
	/** 上一期信用账户对应的总持股数（单位：股） */
	prevTotalMarginShares: number;
	/** 信用账户数量变化（最新一期 - 上一期） */
	creditAccountCountChange: number;
	/** 信用账户持股数量变化百分比（%），正数为增长，负数为减少 */
	totalMarginSharesChangePercent: number;
	/** 最新一期前十大股东合计持股比例（%） */
	top10ShareholdingRatio: number;
	/** 最新一期前二十大股东合计持股比例（%） */
	top20ShareholdingRatio: number;
	/** 最新一期户均持股数（股） */
	avgSharesPerHolder: number;
	/** 上一期前十大股东合计持股比例（%） */
	prevTop10ShareholdingRatio: number;
	/** 上一期前二十大股东合计持股比例（%） */
	prevTop20ShareholdingRatio: number;
	/** 上一期户均持股数（股） */
	prevAvgSharesPerHolder: number;
	/** 前十大股东合计持股比例变化（最新一期 - 上一期，单位：%） */
	top10RatioChange: number;
	/** 前二十大股东合计持股比例变化（最新一期 - 上一期，单位：%） */
	top20RatioChange: number;
	/** 户均持股数变化（最新一期 - 上一期，单位：股） */
	avgSharesPerHolderChange: number;
	/** 最新一期第一大股东持股数量（股） */
	top1_shareholding_amount: number;
	/** 最新一期第一大股东持股比例（%） */
	top1_shareholding_ratio: number;
	/** 最新一期前十大股东合计持股数量（股） */
	top10_shareholding_amount: number;
	/** 最新一期前十大股东合计持股比例（%） */
	top10_shareholding_ratio: number;
	/** 最新一期前一百大股东合计持股数量（股） */
	top100_shareholding_amount: number;
	/** 最新一期前一百大股东合计持股比例（%） */
	top100_shareholding_ratio: number;
	/** 股东类型分布数据 */
	shareholderTypes: ShareholderTypeItem[];
}

/**
 * 公司概览 API 响应数据类型
 * @description 支持数组和单个对象两种格式
 */
export type CompanyOverviewApiData = CompanyOverviewDataItem[] | CompanyOverviewDataItem;

// API 响应接口已移至 base-shareholder-api.ts，使用 BaseApiResponse<CompanyOverviewApiData>

// 创建 API 客户端实例
const apiClientInstance = createShareholderApiClient();

/**
 * 公司概览 API 客户端
 * @description 提供公司概览相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-06-16 11:38:47
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */
export const companyOverviewApi = {
	/**
	 * 获取公司概览信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @returns 公司概览数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 *
	 * @example
	 * ```typescript
	 * const overview = await companyOverviewApi.getCompanyOverview("12345");
	 * console.log(overview.data.companyName);
	 * ```
	 */
	getCompanyOverview: async (organizationId: string): Promise<BaseApiResponse<CompanyOverviewApiData>> => {
		const response = await apiClientInstance.sendBaseRequest<CompanyOverviewApiData>(
			"company-info",
			organizationId,
			"获取公司概览信息失败",
		);

		// 获取时间戳，优先使用数据中的时间
		let timestamp: string;
		if (Array.isArray(response.data) && response.data.length > 0) {
			timestamp = response.data[0].registerDate || response.timestamp;
		} else if (!Array.isArray(response.data)) {
			timestamp = response.data.registerDate || response.timestamp;
		} else {
			timestamp = response.timestamp;
		}

		// 返回带有正确时间戳的响应
		return {
			...response,
			timestamp,
		};
	}
};
