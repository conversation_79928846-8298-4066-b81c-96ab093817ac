/**
 * 股东名册解析配置文件
 * 定义了解析过程中使用的常量和配置项
 *
 * @version 1.0.0 (2025-06-05)
 * <AUTHOR> 2025-06-05
 */

/**
 * 股东记录最大数量
 * 解析出的股东记录将按持股数量排序，只保留前MAX_RECORDS_COUNT条记录
 *
 * <AUTHOR>
 * @created 2025-06-05
 * @update 2025-06-17 19:15:34 修正环境变量获取方式，使用正确的语法和类型转换
 */
export const MAX_RECORDS_COUNT = process.env.NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE
	? Number(process.env.NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE)
	: 200;

/**
 * 名册类型定义
 */
export enum RegistryType {
  TYPE_01 = '01',
  TYPE_05 = '05',
  TYPE_T1 = 't1',
  TYPE_T2 = 't2',
  TYPE_T3 = 't3',
  UNKNOWN = 'unknown'
}

/**
 * 名册文件格式定义
 */
export enum FileFormat {
  DBF = 'dbf',
  EXCEL = 'excel',
  ZIP = 'zip',
  UNKNOWN = 'unknown'
}

/**
 * 名册解析阶段定义
 */
export enum ParsePhase {
  READING = 'reading',
  PARSING = 'parsing',
  PREPROCESSING = 'preprocessing',
  COMPLETE = 'complete'
}

/**
 * 文件名匹配规则
 * 定义各类型股东名册文件的灵活文件名格式
 * 验证前缀标识、公司代码和日期，对后续文件名变化部分放宽要求
 *
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 * @updated 2025-06-24 19:26:28 保留公司代码和日期验证，对后续文件名变化部分放宽要求
 */
export const FILE_NAME_PATTERNS = {
  // DBF文件名规则 - 验证DQMC01前缀、公司代码(6位)、日期(8位)，对后续内容放宽
  // 支持示例：DQMC01_001339_20240930.DBF, DQMC01_001339_20241231 (1).DBF
  DBF_C01: /^DQMC01_\d{6}_\d{8}.*\.DBF$/i,
  DBF_C05: /^DQMC05_\d{6}_\d{8}.*\.DBF$/i,

  // Excel文件名规则 - 验证t1/t2/t3前缀、公司代码(6-8位)、日期(8位)，对后续内容放宽
  // t1格式：t1 + 公司代码(6-8位) + 日期(8位) + 任意后缀 + .xls
  // 支持示例：t16050800320241231t200.xls, t16050800320240508all.508.xls
  EXCEL_T1: /^t1\d{6,8}\d{8}.*\.xls$/i,

  // t2格式：t2 + 公司代码(6-8位) + 日期(8位) + 任意后缀 + .xls
  // 支持示例：t26053380320241231t200.c31.xls
  EXCEL_T2: /^t2\d{6,8}\d{8}.*\.xls$/i,

  // t3格式：t3 + 公司代码(6-8位) + 日期(8位) + 任意后缀 + .xls
  // 支持示例：t36053380320241231t200.c31.xls, t36053380320241231t200.xls
  EXCEL_T3: /^t3\d{6,8}\d{8}.*\.xls$/i,

  // ZIP文件名规则 - 对应DBF和Excel的放宽规则
  // 例如：DQMC05_300723_20240430.zip, t36053380320241231t200.c31.zip
  ZIP_DBF_C01: /^DQMC01_\d{6}_\d{8}.*\.zip$/i,
  ZIP_DBF_C05: /^DQMC05_\d{6}_\d{8}.*\.zip$/i,
  ZIP_EXCEL_T1: /^t1\d{6,8}\d{8}.*\.zip$/i,
  ZIP_EXCEL_T2: /^t2\d{6,8}\d{8}.*\.zip$/i,
  ZIP_EXCEL_T3: /^t3\d{6,8}\d{8}.*\.zip$/i,
};

/**
 * 严格文件名验证开关
 * 控制是否启用严格的文件名格式验证
 *
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export const STRICT_FILENAME_VALIDATION =  true;

/**
 * 支持的文件名示例
 * 用于错误提示中显示正确的文件名格式
 *
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 * @updated 2025-06-24 19:26:28 更新示例以反映保留公司代码和日期但放宽后续内容的规则
 */
export const FILE_NAME_EXAMPLES = {
  DBF_C01: 'DQMC01_001339_20240930.DBF 或 DQMC01_001339_20241231 (1).DBF',
  DBF_C05: 'DQMC05_300723_20240430.DBF',
  EXCEL_T1: 't16050800320241231t200.xls 或 t16050800320240508all.508.xls',
  EXCEL_T2: 't26053380320241231t200.c31.xls',
  EXCEL_T3: 't36053380320241231t200.c31.xls 或 t36053380320241231t200.xls',
  ZIP_DBF_C01: 'DQMC01_001339_20240930.zip',
  ZIP_DBF_C05: 'DQMC05_300723_20240430.zip',
  ZIP_EXCEL_T1: 't16050800320241231t200.zip',
  ZIP_EXCEL_T2: 't26053380320241231t200.c31.zip',
  ZIP_EXCEL_T3: 't36053380320241231t200.c31.zip',
};

/**
 * 文件名验证错误信息模板
 *
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 * @updated 2025-06-24 19:26:28 更新错误信息以反映保留公司代码和日期但放宽后续内容的规则
 */
//   目前仅支持导入C01、C05、T1、T2及T3类型的股东名册文件。
export const FILENAME_ERROR_MESSAGES = {
	INVALID_ZIP_FORMAT:
		"文件名格式不正确，ZIP文件名应包含对应的前缀标识、公司代码和日期",
	ZIP_CONTENT_MISMATCH:
		"ZIP文件内容与文件名不匹配，内部文件名应与ZIP文件名对应",
	UNSUPPORTED_TYPE: "目前仅支持导入C01、C05、T1、T2及T3类型的股东名册文件。",
};

/**
 * 验证DBF文件名格式
 *
 * @param fileName DBF文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateDBFFileName(fileName: string): {
  isValid: boolean;
  registryType?: string;
  error?: string;
  suggestion?: string;
} {
  // 检查c01格式
  if (FILE_NAME_PATTERNS.DBF_C01.test(fileName)) {
    return {
      isValid: true,
      registryType: 'c01'
    };
  }

  // 检查c05格式
  if (FILE_NAME_PATTERNS.DBF_C05.test(fileName)) {
    return {
      isValid: true,
      registryType: 'c05'
    };
  }

  return {
		isValid: false,
		error: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE,
  };
}

/**
 * 验证Excel文件名格式
 *
 * @param fileName Excel文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateExcelFileName(fileName: string): {
  isValid: boolean;
  registryType?: string;
  error?: string;
} {
  // 检查t1格式
  if (FILE_NAME_PATTERNS.EXCEL_T1.test(fileName)) {
    return {
      isValid: true,
      registryType: 't1'
    };
  }

  // 检查t2格式
  if (FILE_NAME_PATTERNS.EXCEL_T2.test(fileName)) {
    return {
      isValid: true,
      registryType: 't2'
    };
  }


  // 检查t3格式
  if (FILE_NAME_PATTERNS.EXCEL_T3.test(fileName)) {
    return {
      isValid: true,
      registryType: 't3'
    };
  }

  return {
		isValid: false,
		error: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE
  };
}

/**
 * 验证ZIP文件名格式
 *
 * @param fileName ZIP文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 16:01:13
 */
export function validateZipFileName(fileName: string): {
  isValid: boolean;
  expectedInternalFileName?: string;
  registryType?: string;
  error?: string;
} {
  // 检查各种ZIP文件名格式
  if (FILE_NAME_PATTERNS.ZIP_DBF_C01.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.DBF'),
      registryType: 'c01'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_DBF_C05.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.DBF'),
      registryType: 'c05'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T1.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't1'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T2.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't2'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T3.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't3'
    };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.INVALID_ZIP_FORMAT
  };
}