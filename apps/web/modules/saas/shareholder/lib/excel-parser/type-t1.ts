/**
 * t1名册Excel文件解析器
 * 专门用于处理t1类型股东名册的Excel文件解析
 * 
 * 主要功能:
 * 1. 解析t1名册特有的字段结构
 * 2. 提取t1名册中的公司信息和股东信息
 * 3. 处理t1名册特有的数据格式和规则
 * 
 * t1名册特点:
 * - 文件名格式为t1XXXXXXxxyyyymmddall.mdd.xls
 * - 包含普通股东信息
 * - 包含流通类型(LTLX)和权益类别(YQLB)等特有字段
 * 
 * @version 1.1.0 (2025-06-04)
 * <AUTHOR> 2025-06-03
 * @update 2025-06-03 修复字段映射问题，确保正确识别"证券账户名称"、"股东类别"、"权益类别"字段
 * @update 2025-06-03 15:17:32 增强字段映射逻辑，添加ZQZHHM->ZQZHMC, CYRMC->ZQZHMC, CYRLB->GDLB, ZQLB->YQLB的映射关系
 * @update 2025-06-03 15:23:22 增强字段处理逻辑，清理字符串值中的空格，手动确保所有必填字段都存在
 * @update 2025-06-03 15:28:30 增强空值检测逻辑，确保所有必填字段都有有效值
 * @update 2025-06-03 15:58:33 确保保留所有原始字段，包括CYRMC字段，以便后端可以正确处理
 * @update 2025-06-03 16:00:40 添加字段完整性检查函数，确保所有参照表中的t1名册字段都被正确处理
 * @update 2025-06-03 16:14:00 添加对SJLX=802记录的处理，提取公司代码、公司简称、证券总数量、总户数、机构户数合计和机构持有合计等信息
 * @update 2025-06-03 16:21:00 增强数据校验，使用SJLX=802记录中的信息对文件名中的公司代码和期数时间进行校验
 * @update 2025-06-03 17:10:11 修改字段映射逻辑，将 securitiesAccountName 直接映射到 CYRMC，而不是 ZQZHMC，以适配后端变更
 * @update 2025-06-03 17:44:42 修改为绝对使用SJLX=802的信息，而不是优先使用
 * @update 2025-06-04 12:04:15 添加对SJLX=801（股东数据）的识别和提取功能
 * @update 2025-06-04 12:14:27 使用统一提取函数extractUnifiedCompanyInfo替换原有的extractCompanyBasicInfo函数
 */

import * as XLSX from "xlsx";
import { 
  checkRequiredFields,
  cleanString,
  extractCompanyInfo,
  extractRegularRecords,
  identifySpecialRecords,
  parseExcelFileName,
  readExcelWorksheet,
  validateRequiredFieldsContent,
  extractUnifiedCompanyInfo
} from "./common";
import { MAX_RECORDS_COUNT } from "../config";

/**
 * t1名册必填字段列表 - 修正必填字段名称和标签
 * @update 2025-06-24 19:01:59 hayden 修复字段名不一致问题
 * 将GDLB改为CYRLB，与后端验证保持一致
 */
const REQUIRED_FIELDS_T1 = [
	{ name: "YMTZHHM", label: "YMTZHHM" },
	{ name: "CYRMC", label: "CYRMC" }, // 直接使用CYRMC作为证券账户名称
	{ name: "ZJHM", label: "ZJHM" },
	{ name: "CYRLB", label: "CYRLB" }, // 修复：使用CYRLB与后端保持一致
	{ name: "LTLX", label: "LTLX" },
];

// 根据参照表中的字段列表，定义t1名册所有可能的字段
const T1_ALL_FIELDS = [
  "YMTZHHM",   // 一码通账户号码 (unifiedAccountNumber)
  "CYRMC",     // 持有人名称 (securitiesAccountName)
  "ZJHM",      // 证件号码 (shareholderId)
  "CYRLB",     // 持有人类别 (shareholderCategory)
  "QYDJR",     // 权益登记日 (reportDate)
  "CYSL",      // 持有数量 (sharesInCashAccount)
  "ZYDJZS",    // 质押冻结总数 (frozenShares)
  "TXDZ",      // 通讯地址 (contactAddress)
  "LXDH",      // 联系电话 (contactNumber)
  "YZBM",      // 邮政编码 (zipCode)
  "ZQLB",      // 证券类别 (natureOfShares)
  "GLGXSFQR",  // 关联关系确认标识 (relatedPartyIndicator)
  "ZQZHHM",    // 证券账户号码 (cashAccount)
  "BZ",        // 备注 (remarks)
  "LTLX",      // 流通类型 (shareTradingCategory)
  "QYLB"       // 权益类别 (rightsCategory)
];

/**
 * t1名册字段映射 - 添加字段映射表，确保正确识别字段
 * @update 2025-06-24 19:01:59 hayden 添加GDLB到CYRLB的映射，处理字段名不一致问题
 */
const T1_FIELD_MAPPING: { [key: string]: string } = {
  // 根据日志中实际字段添加的映射
  "ZQZHHM": "ZQZHHM", // 保留原始字段
  "CYRMC": "CYRMC",   // 保留原始字段，直接作为securitiesAccountName
  "CYRLB": "CYRLB",   // 保留原始字段
  "GDLB": "CYRLB",    // 映射股东类别到持有人类别
  "ZQLB": "ZQLB",     // 保留原始字段
  "QYDJR": "QYDJR",   // 保留原始字段
  "ZYDJZS": "ZYDJZS", // 保留原始字段
  "TXDZ": "TXDZ",     // 保留原始字段
  "LXDH": "LXDH",     // 保留原始字段
  "YZBM": "YZBM",     // 保留原始字段
  "GLGXSFQR": "GLGXSFQR", // 保留原始字段
  "BZ": "BZ",         // 保留原始字段
};

/**
 * 检查记录是否包含参照表中列出的所有t1名册字段
 * 
 * @param record 记录对象
 * @returns 缺失的字段列表
 */
function checkT1FieldsCompleteness(record: Record<string, any>): string[] {
  const missingFields: string[] = [];
  
  // 检查每个字段是否存在
  for (const field of T1_ALL_FIELDS) {
    if (!Object.prototype.hasOwnProperty.call(record, field)) {
      missingFields.push(field);
    }
  }
  
  return missingFields;
}

/**
 * 从原始数据中提取SJLX=802的记录，获取公司基本信息
 * 
 * @param data 原始数据
 * @returns 公司基本信息
 */
function extractCompanyBasicInfo(data: Record<string, any>[]): {
  companyCode: string;
  companyName: string;
  totalShares: string;
  totalShareholders: number;
  totalInstitutions: number;
  institutionShares: string;
  registerDate: string;
} {
  // 默认值
  const defaultInfo = {
    companyCode: "",
    companyName: "",
    totalShares: "0",
    totalShareholders: 0,
    totalInstitutions: 0,
    institutionShares: "0",
    registerDate: "",
  };
  
  try {
    // 查找SJLX=802的记录
    const infoRecord = data.find(record => record.SJLX === '802');
    
    if (!infoRecord) {
      return defaultInfo;
    }
    
    // 提取公司代码
    if (infoRecord.ZQDM) {
      defaultInfo.companyCode = typeof infoRecord.ZQDM === 'string' ? infoRecord.ZQDM.trim() : infoRecord.ZQDM.toString();
    }
    
    // 提取公司名称
    if (infoRecord.ZQJC) {
      defaultInfo.companyName = typeof infoRecord.ZQJC === 'string' ? infoRecord.ZQJC.trim() : infoRecord.ZQJC.toString();
    }
    
    // 提取证券总数量
    if (infoRecord.ZQZSL) {
      defaultInfo.totalShares = typeof infoRecord.ZQZSL === 'string' ? 
        infoRecord.ZQZSL.trim().replace(/[,，]/g, '') : 
        infoRecord.ZQZSL.toString();
    }
    
    // 提取总户数
    if (infoRecord.ZHS) {
      defaultInfo.totalShareholders = typeof infoRecord.ZHS === 'string' ? 
        Number.parseInt(infoRecord.ZHS.trim(), 10) : 
        Number(infoRecord.ZHS);
    }
    
    // 提取机构户数合计
    if (infoRecord.JGHSHJ) {
      defaultInfo.totalInstitutions = typeof infoRecord.JGHSHJ === 'string' ? 
        Number.parseInt(infoRecord.JGHSHJ.trim(), 10) : 
        Number(infoRecord.JGHSHJ);
    }
    
    // 提取机构持有合计
    if (infoRecord.JGCYHJ) {
      defaultInfo.institutionShares = typeof infoRecord.JGCYHJ === 'string' ? 
        infoRecord.JGCYHJ.trim().replace(/[,，]/g, '') : 
        infoRecord.JGCYHJ.toString();
    }
    
    // 提取权益登记日
    if (infoRecord.QYDJR) {
      // 处理不同格式的日期
      let registerDate = infoRecord.QYDJR;
      if (typeof registerDate === 'string') {
        registerDate = registerDate.trim();
        // 如果是YYYYMMDD格式，转换为YYYY-MM-DD
        if (/^\d{8}$/.test(registerDate)) {
          registerDate = `${registerDate.substring(0, 4)}-${registerDate.substring(4, 6)}-${registerDate.substring(6, 8)}`;
        }
        // 如果是YYYY/MM/DD格式，转换为YYYY-MM-DD
        else if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(registerDate)) {
          const parts = registerDate.split('/');
          registerDate = `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
        }
        // 如果是YYYY年MM月DD日格式，转换为YYYY-MM-DD
        else if (/^\d{4}年\d{1,2}月\d{1,2}日$/.test(registerDate)) {
          const year = registerDate.match(/(\d{4})年/)?.[1] || '';
          const month = registerDate.match(/年(\d{1,2})月/)?.[1].padStart(2, '0') || '';
          const day = registerDate.match(/月(\d{1,2})日/)?.[1].padStart(2, '0') || '';
          registerDate = `${year}-${month}-${day}`;
        }
      } else if (registerDate instanceof Date) {
        // 如果是Date对象，转换为YYYY-MM-DD格式
        const year = registerDate.getFullYear();
        const month = (registerDate.getMonth() + 1).toString().padStart(2, '0');
        const day = registerDate.getDate().toString().padStart(2, '0');
        registerDate = `${year}-${month}-${day}`;
      }
      defaultInfo.registerDate = registerDate;
    }
    
    return defaultInfo;
  } catch (error) {

    return defaultInfo;
  }
}

/**
 * 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
 * 
 * @param fileNameInfo 从文件名中提取的信息
 * @param basicInfo 从SJLX=802记录中提取的基本信息
 * @returns 校验结果
 */
function validateFileNameWithBasicInfo(
  fileNameInfo: { companyCode?: string; registerDate?: string },
  basicInfo: { companyCode: string; registerDate: string }
): {
  isValid: boolean;
  error?: {
    type: "COMPANY_MISMATCH" | "DATE_MISMATCH";
    message: string;
  };
} {
  // 默认校验通过
  const result = {
    isValid: true
  };
  
  // 如果文件名和基本信息中都有公司代码，进行校验
  if (fileNameInfo.companyCode && basicInfo.companyCode && 
      // 修复：在比较前对公司代码进行格式化处理，去除空格和不可见字符
      String(fileNameInfo.companyCode).trim() !== String(basicInfo.companyCode).trim()) {

    return {
      isValid: false,
      error: {
        type: "COMPANY_MISMATCH",
        message: `文件名中的公司代码(${fileNameInfo.companyCode})与名册中的公司代码(${basicInfo.companyCode})不一致`
      }
    };
  }
  
  // 如果文件名和基本信息中都有期数时间，进行校验
  if (fileNameInfo.registerDate && basicInfo.registerDate) {
    // 标准化日期格式为YYYY-MM-DD
    const fileNameDate = fileNameInfo.registerDate.replace(/\//g, '-');
    const basicInfoDate = basicInfo.registerDate.replace(/\//g, '-');
    
    // 比较日期，忽略格式差异
    const fileNameDateObj = new Date(fileNameDate);
    const basicInfoDateObj = new Date(basicInfoDate);
    
    if (!Number.isNaN(fileNameDateObj.getTime()) && !Number.isNaN(basicInfoDateObj.getTime()) && 
        fileNameDateObj.getTime() !== basicInfoDateObj.getTime()) {
      return {
        isValid: false,
        error: {
          type: "DATE_MISMATCH",
          message: `文件名中的期数时间(${fileNameInfo.registerDate})与名册中的权益登记日(${basicInfo.registerDate})不一致`
        }
      };
    }
  }
  
  return result;
}

/**
 * 从原始数据中提取SJLX=801的记录，获取股东数据
 * 
 * @param data 原始数据
 * @returns 股东数据记录数组
 * @version 1.0.0 (2025-06-04)
 * <AUTHOR> 2025-06-04 12:04:15
 */
function extractShareholderRecords(data: Record<string, any>[]): Record<string, any>[] {
  // 寻找SJLX=801的记录，这通常是股东数据
  const shareholderRecords = data.filter((record) => record.SJLX === 801 || record.SJLX === "801");
  
  // 如果找不到SJLX=801的记录，返回空数组
  if (!shareholderRecords || shareholderRecords.length === 0) {
    // 调试日志已移除 - 2025-06-24 19:17:24 hayden 问题已解决
    return [];
  }
  
  return shareholderRecords;
}

/**
 * 解析t1名册Excel文件
 * 
 * @param file t1名册Excel文件
 * @returns 解析结果
 * @update 2025-06-04 12:04:15 添加对SJLX=801（股东数据）的识别和提取功能
 * @update 2025-06-04 12:14:27 使用统一提取函数extractUnifiedCompanyInfo替换原有的extractCompanyBasicInfo函数
 */
export async function parseExcelFileT1(file: File): Promise<{
  success: boolean;
  fileName: string;
  companyCode?: string;
  registerDate?: string;
  records?: any[];
  recordCount?: number;
  companyInfo?: {
    companyName: string;
    totalShares: string;
    totalShareholders: number;
    totalInstitutions: number;
    largeSharesCount: string;
    largeShareholdersCount: number;
    institutionShares: string;
  };
  error?: {
    type: "FILE_ERROR" | "COMPANY_MISMATCH" | "DATE_MISMATCH" | "MISSING_FIELDS" | "EMPTY_FIELDS";
    message: string;
    details?: string[];
  };
}> {
  try {
    // 读取Excel文件
    const { worksheet } = await readExcelWorksheet(file);
    
    // 将工作表转换为JSON对象数组
    const rawData = XLSX.utils.sheet_to_json(worksheet);
    // 如果没有数据，返回错误
    if (!rawData || rawData.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "Excel文件不包含有效数据",
        },
      };
    }
    
    // 分析文件名，提取公司代码和报告日期
    const fileNameInfo = parseExcelFileName(file.name);
    
    // 从原始数据中提取SJLX=802的公司基本信息
    // 使用统一提取函数，指定名册类型为TYPE_T1
    const companyBasicInfo = extractUnifiedCompanyInfo(
      rawData as Record<string, any>[],
      'TYPE_T1'
    );
    
    // 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
    const validationResult = validateFileNameWithBasicInfo(fileNameInfo, companyBasicInfo);
    if (!validationResult.isValid && validationResult.error) {
      return {
        success: false,
        fileName: file.name,
        error: validationResult.error,
      };
    }
    
    // 从原始数据中提取SJLX=801的股东数据记录
    const shareholderRecordsFrom801 = extractShareholderRecords(
      rawData as Record<string, any>[],
    );
    
    // 尝试识别特殊记录（通常是表头或汇总行）
    const specialRecords = identifySpecialRecords(rawData as Record<string, any>[]);
    
    // 如果存在SJLX=801的股东数据记录，优先使用这些记录
    let regularRecords: Record<string, any>[] = [];
    if (shareholderRecordsFrom801.length > 0) {

      regularRecords = shareholderRecordsFrom801;
    } else {
      // 如果没有SJLX=801的记录，则使用常规方法提取记录

      regularRecords = extractRegularRecords(rawData, specialRecords);
    }

    /**
     * 检查是否有有效的记录数据
     * <AUTHOR>
     * @created 2025-07-01 10:29:07
     * @description 在进行字段校验之前，确保有有效的记录数据
     */
    if (!regularRecords || regularRecords.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "Excel文件中没有找到有效的股东记录数据",
        },
      };
    }

    // 第一步：进行基础字段映射，但不设置默认值
    const mappedRecords = regularRecords.map(record => {
      const mappedRecord: Record<string, any> = {};

      // 复制原始记录的所有字段
      for (const key in record) {
        if (Object.prototype.hasOwnProperty.call(record, key)) {
          // 保留原始字段
          mappedRecord[key] = typeof record[key] === 'string' ? record[key].trim() : record[key];

          // 如果字段名在映射表中，同时添加标准字段名
          const standardKey = T1_FIELD_MAPPING[key];
          if (standardKey && standardKey !== key) {
            mappedRecord[standardKey] = typeof record[key] === 'string' ? record[key].trim() : record[key];
          }
        }
      }

      /**
       * 处理字段映射，但不设置默认值
       * @update 2025-07-01 10:29:07 hayden 修复字段校验问题
       * 将字段映射和默认值设置分离，确保字段校验能正确工作
       */
      // 强制确保GDLB映射到CYRLB（防止映射逻辑失效）
      if (mappedRecord.GDLB && !mappedRecord.CYRLB) {
        mappedRecord.CYRLB = typeof mappedRecord.GDLB === 'string' ? mappedRecord.GDLB.trim() : mappedRecord.GDLB;
      }

      if (!mappedRecord.YQLB && mappedRecord.ZQLB) {
        mappedRecord.YQLB = typeof mappedRecord.ZQLB === 'string' ? mappedRecord.ZQLB.trim() : mappedRecord.ZQLB;
      }

      return mappedRecord;
    });

    /**
     * 检查映射后的记录是否为空
     * <AUTHOR>
     * @created 2025-07-01 10:29:07
     * @description 确保映射处理后仍有有效记录
     */
    if (!mappedRecords || mappedRecords.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "Excel文件字段映射后没有有效的股东记录数据",
        },
      };
    }

    // 第二步：检查是否包含必要字段（在设置默认值之前）
    const missingFields = checkRequiredFields(mappedRecords[0], REQUIRED_FIELDS_T1);

    if (missingFields.length > 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "MISSING_FIELDS",
          message: `名册中缺少字段"${missingFields.join('", "')}"`,
          details: missingFields,
        },
      };
    }

    // 第三步：设置默认值并进行最终标准化
    const standardizedRecords = mappedRecords.map(record => {
      const standardRecord = { ...record };

      // 确保必填字段有默认值（仅在字段存在但值为空时设置）
      if (standardRecord.YMTZHHM === undefined || standardRecord.YMTZHHM === null || standardRecord.YMTZHHM === "") {
        standardRecord.YMTZHHM = "未提供";
      }

      if (standardRecord.CYRMC === undefined || standardRecord.CYRMC === null || standardRecord.CYRMC === "") {
        standardRecord.CYRMC = "未提供";
      }

      if (standardRecord.ZJHM === undefined || standardRecord.ZJHM === null || standardRecord.ZJHM === "") {
        standardRecord.ZJHM = "未提供";
      }

      if (standardRecord.CYRLB === undefined || standardRecord.CYRLB === null || standardRecord.CYRLB === "") {
        standardRecord.CYRLB = "未提供";
      }

      if (standardRecord.LTLX === undefined || standardRecord.LTLX === null || standardRecord.LTLX === "") {
        standardRecord.LTLX = "未提供";
      }

      return standardRecord;
    });
    
    // 检查必填字段是否有空值
    const emptyFields = validateRequiredFieldsContent(standardizedRecords, REQUIRED_FIELDS_T1);
    if (emptyFields.length > 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "EMPTY_FIELDS",
          message: `名册中字段"${emptyFields.join('", "')}"缺少内容`,
          details: emptyFields,
        },
      };
    }
    
    // 检查是否包含参照表中的所有字段
    const missingReferenceFields = checkT1FieldsCompleteness(standardizedRecords[0]);
    // 提取公司信息
    const companyInfoFromSpecial = extractCompanyInfo(specialRecords);
    
    // 合并公司信息，绝对使用SJLX=802的信息
    const companyInfo = {
      // 绝对使用SJLX=802的信息，只有在SJLX=802没有提供信息时才使用其他来源
      companyName: companyBasicInfo.companyName || companyInfoFromSpecial.companyName,
      companyCode: companyBasicInfo.companyCode || companyInfoFromSpecial.companyCode,
      registerDate: companyBasicInfo.registerDate || fileNameInfo.registerDate || companyInfoFromSpecial.registerDate,
      totalShares: companyBasicInfo.totalShares || companyInfoFromSpecial.totalShares,
      totalShareholders: companyBasicInfo.totalShareholders || companyInfoFromSpecial.totalShareholders,
      totalInstitutions: companyBasicInfo.totalInstitutions || companyInfoFromSpecial.totalInstitutions,
      institutionShares: companyBasicInfo.institutionShares || companyInfoFromSpecial.institutionShares,
      largeSharesCount: companyInfoFromSpecial.largeSharesCount,
      largeShareholdersCount: companyInfoFromSpecial.largeShareholdersCount,
    };
    
    // 处理记录，清理字段值，但保留所有原始字段
    const processedRecords = standardizedRecords.map(record => {
      const processedRecord: Record<string, any> = {};

      /**
       * 数值字段列表 - 需要转换为字符串类型
       *
       * <AUTHOR>
       * @created 2025-06-24 15:34:59
       * @description 确保所有数值字段都转换为字符串类型，避免后端验证失败
       */
      const numericFields = [
        'CYSL', 'CYBL', 'ZYDJZS', 'GPNF', 'XH'
      ];

      // 处理每个字段
      for (const key in record) {
        if (Object.prototype.hasOwnProperty.call(record, key)) {
          // 如果是字符串类型，清理字符串
          if (typeof record[key] === 'string') {
            processedRecord[key] = cleanString(record[key]);
          } else if (numericFields.includes(key) && record[key] !== undefined && record[key] !== null) {
            // 数值字段转换为字符串
            processedRecord[key] = String(record[key]);
          } else {
            processedRecord[key] = record[key];
          }
        }
      }

      /**
       * 再次确保GDLB映射到CYRLB（防止在处理过程中丢失）
       * @update 2025-06-24 19:05:56 hayden 修复字段映射在处理过程中丢失的问题
       */
      if (processedRecord.GDLB && !processedRecord.CYRLB) {
        processedRecord.CYRLB = processedRecord.GDLB;
      }

      // 确保所有参照表字段都有值（即使是空值）
      for (const field of T1_ALL_FIELDS) {
        if (!Object.prototype.hasOwnProperty.call(processedRecord, field)) {
          processedRecord[field] = "";
        }
      }

      return processedRecord;
    });
    
    // 预处理：合并重复记录
    const mergedRecords = preprocessT1Records(processedRecords);
    
    // 按持股数量排序
    const sortedRecords = [...mergedRecords].sort((a, b) => {
      const sharesA = Number(a.CYSL || 0);
      const sharesB = Number(b.CYSL || 0);
      return sharesB - sharesA; // 降序排序
    });
    
    // 只保留前MAX_RECORDS_COUNT条记录
    const limitedRecords = sortedRecords.slice(0, MAX_RECORDS_COUNT);
    
    return {
      success: true,
      fileName: file.name,
      companyCode: companyInfo.companyCode,
      registerDate: companyInfo.registerDate,
      records: limitedRecords,
      recordCount: limitedRecords.length,
      companyInfo,
    };
  } catch (error) {

    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析Excel文件失败",
      },
    };
  }
}

/**
 * 预处理t1名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t1名册记录
 * @returns 预处理后的记录
 */
export function preprocessT1Records(records: any[]): any[] {
  if (!records || records.length === 0) {
    return [];
  }
  
  // 使用Map存储合并后的记录，键为"ZJHM+YMTZHHM"
  const mergedRecordsMap = new Map<string, any>();
  
  // 遍历所有记录
  for (const record of records) {
    // 生成组合键
    const zjhm = record.ZJHM || '';
    const ymtzhhm = record.YMTZHHM || '';
    const key = `${zjhm}|${ymtzhhm}`;
    
    // 如果Map中已存在该键，合并记录
    if (mergedRecordsMap.has(key)) {
      const existingRecord = mergedRecordsMap.get(key);
      
      // 叠加持股数量
      const existingShares = Number(existingRecord.CYSL || 0);
      const newShares = Number(record.CYSL || 0);
      existingRecord.CYSL = (existingShares + newShares).toString();
      
      // 其他字段保留先到先得的值
    } else {
      // 如果Map中不存在该键，添加记录
      mergedRecordsMap.set(key, { ...record });
    }
  }
  
  // 将Map转换为数组
  return Array.from(mergedRecordsMap.values());
}

/**
 * 合并多个t1名册的记录
 * 
 * @param recordsArray 多个t1名册记录数组
 * @returns 合并后的记录
 */
export function mergeT1Records(recordsArray: any[][]): any[] {
  // 将所有记录合并为一个数组
  const allRecords = recordsArray.flat();
  
  // 使用预处理函数合并记录
  return preprocessT1Records(allRecords);
} 