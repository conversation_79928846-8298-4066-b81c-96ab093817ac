/**
 * 股东名称查询基金代码和基金代码查询股东名称 API
 * @file fund-institution-api.ts
 * @description 提供双向查询功能：根据基金代码查询股东名称信息，根据股东名称查询基金代码信息
 * <AUTHOR>
 * @created 2025-08-07 14:34:20 - 基于base-shareholder-api.ts封装的股东基金查询API
 * @modified 2025-08-07 14:43:59 - 加强组织ID必须性验证，确保所有API调用都需要有效的组织ID
 */

import { 
  createShareholderApiClient, 
 type BaseApiResponse, 
  validateOrganizationId 
} from "./base-shareholder-api";

/**
 * 基金代码查询股东名称请求参数接口
 * @interface FundCodeQueryRequest
 * @modified 2025-08-11 hayden - 加强类型安全性，明确参数类型定义
 */
export interface FundCodeQueryRequest {
  /** 组织ID，必须参数，长度5-50个字符，用于数据隔离 */
  organizationId: string;
  /** 基金代码，必须参数，格式为6位数字.后缀（如：001384.OF） */
  fund_code: string;
}

/**
 * API请求参数接口 - 替代any类型
 * @interface ApiRequestParams
 * <AUTHOR>
 * @created 2025-08-11 - 提高类型安全性，避免使用any类型
 */
export interface ApiRequestParams {
  /** 组织ID，必须参数 */
  organizationId: string;
  /** 基金代码，可选参数 */
  fund_code?: string;
  /** 股东名称，可选参数 */
  shareholder_name?: string;
}

/**
 * 股东名称查询基金代码请求参数接口
 * @interface ShareholderNameQueryRequest
 */
export interface ShareholderNameQueryRequest {
  /** 组织ID，必须参数，长度5-50个字符，用于数据隔离 */
  organizationId: string;
  /** 股东名称（证券账户名称），必须参数，不能为空 */
  shareholder_name: string;
}

/**
 * 股东信息响应数据接口
 * @interface ShareholderInfo
 */
export interface ShareholderInfo {
  /** 记录ID */
  id: string;
  /** 股东ID */
  shareholderId: string;
  /** 统一账户号码 */
  unifiedAccountNumber: string;
  /** 证券账户名称 */
  securitiesAccountName: string;
  /** 持股数量 */
  numberOfShares: string;
  /** 持股比例 */
  shareholdingRatio: string;
  /** 登记日期 */
  registerDate: string;
  /** 联系电话 */
  contactNumber: string;
  /** 联系地址 */
  contactAddress: string;
  /** 托管人部分 */
  custodian_part: string;
  /** 基金名称部分 */
  fund_name_part: string;
  /** 匹配状态 */
  match_status: string;
}

/**
 * 基金信息响应数据接口
 * @interface FundInfo
 */
export interface FundInfo {
  /** 基金代码 */
  fund_code: string;
  /** 基金名称 */
  fund_name: string;
  /** 基金全称 */
  fund_full_name: string;
  /** 查询时间戳 */
  query_timestamp: string;
}

/**
 * 基金代码查询股东名称响应类型
 */
export type FundCodeQueryResponse = ShareholderInfo[] | FundInfo[];

/**
 * 股东名称查询基金代码响应类型
 */
export type ShareholderNameQueryResponse = FundInfo;

/**
 * 验证基金代码格式
 * @description 验证基金代码是否符合标准格式要求，确保API调用参数的正确性
 *
 * @param {string} fundCode - 基金代码，必须是非空字符串
 *
 * @throws {Error} INVALID_FUND_CODE - 当fundCode为空、null、undefined或非字符串时抛出
 * @throws {Error} INVALID_FUND_CODE_FORMAT - 当fundCode格式不符合6位数字.后缀格式时抛出
 *
 * @example
 * ```typescript
 * // 正确格式
 * validateFundCode("001384.OF"); // 通过验证
 * validateFundCode("000001.SH"); // 通过验证
 *
 * // 错误格式
 * validateFundCode(""); // 抛出 INVALID_FUND_CODE
 * validateFundCode("123456"); // 抛出 INVALID_FUND_CODE_FORMAT
 * validateFundCode("001384.of"); // 抛出 INVALID_FUND_CODE_FORMAT (小写)
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数验证规则和示例 - hayden
 */
export function validateFundCode(fundCode: string): void {
  if (!fundCode || typeof fundCode !== "string") {
    throw new Error("INVALID_FUND_CODE: 基金代码必须是非空字符串");
  }

  // 基金代码格式：6位数字.后缀
  const fundCodePattern = /^\d{6}\.[A-Z]{2}$/;
  if (!fundCodePattern.test(fundCode)) {
    throw new Error("INVALID_FUND_CODE_FORMAT: 基金代码格式必须是6位数字.后缀（如：001384.OF）");
  }
}

/**
 * 验证股东名称
 * @description 验证股东名称是否符合要求，确保API调用参数的正确性
 *
 * @param {string} shareholderName - 股东名称，必须是非空字符串
 *
 * @throws {Error} INVALID_SHAREHOLDER_NAME - 当shareholderName为空、null、undefined、非字符串或仅包含空白字符时抛出
 *
 * @example
 * ```typescript
 * // 正确格式
 * validateShareholderName("中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"); // 通过验证
 * validateShareholderName("招商银行股份有限公司"); // 通过验证
 *
 * // 错误格式
 * validateShareholderName(""); // 抛出 INVALID_SHAREHOLDER_NAME
 * validateShareholderName("   "); // 抛出 INVALID_SHAREHOLDER_NAME (仅空白字符)
 * validateShareholderName(null); // 抛出 INVALID_SHAREHOLDER_NAME
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数验证规则和示例 - hayden
 */
export function validateShareholderName(shareholderName: string): void {
  if (!shareholderName || typeof shareholderName !== "string") {
    throw new Error("INVALID_SHAREHOLDER_NAME: 股东名称必须是非空字符串");
  }

  if (shareholderName.trim().length === 0) {
    throw new Error("INVALID_SHAREHOLDER_NAME: 股东名称不能为空");
  }
}

/**
 * 创建股东基金查询 API 客户端
 * @description 基于 base-shareholder-api 封装的股东基金双向查询功能，提供基金代码查股东和股东名称查基金的API接口
 *
 * @returns {Object} API客户端对象，包含两个查询方法
 * @returns {Function} returns.queryShareholdersByFundCode - 根据基金代码查询股东名称信息的方法
 * @returns {Function} returns.queryFundCodeByShareholderName - 根据股东名称查询基金代码信息的方法
 *
 * @example
 * ```typescript
 * // 创建API客户端
 * const apiClient = createFundInstitutionApiClient();
 *
 * // 基金代码查询股东
 * const shareholderResult = await apiClient.queryShareholdersByFundCode({
 *   organizationId: "12345",
 *   fund_code: "001384.OF"
 * });
 *
 * // 股东名称查询基金
 * const fundResult = await apiClient.queryFundCodeByShareholderName({
 *   organizationId: "12345",
 *   shareholder_name: "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"
 * });
 * ```
 *
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 * @modified 2025-08-11 - 补充完整的JSDoc注释，包括返回值类型和使用示例 - hayden
 */
export function createFundInstitutionApiClient() {
  const baseClient = createShareholderApiClient();

  return {
    /**
     * 根据基金代码查询股东名称信息
     * @description 通过基金代码查询持有该基金的股东名称列表，支持参数验证和错误处理
     *
     * @param {FundCodeQueryRequest} request - 查询请求参数对象
     * @param {string} request.organizationId - 组织ID，必填，长度5-50个字符
     * @param {string} request.fund_code - 基金代码，必填，格式为6位数字.后缀（如：001384.OF）
     *
     * @returns {Promise<BaseApiResponse<FundCodeQueryResponse>>} 包含股东信息列表的API响应
     *
     * @throws {Error} MISSING_ORGANIZATION_ID - 当organizationId为空时抛出
     * @throws {Error} INVALID_ORGANIZATION_ID - 当organizationId格式不正确时抛出
     * @throws {Error} MISSING_FUND_CODE - 当fund_code为空时抛出
     * @throws {Error} INVALID_FUND_CODE_FORMAT - 当fund_code格式不正确时抛出
     *
     * @example
     * ```typescript
     * try {
     *   const result = await queryShareholdersByFundCode({
     *     organizationId: "12345",
     *     fund_code: "001384.OF"
     *   });
     *   console.log("股东列表:", result.data);
     * } catch (error) {
     *   console.error("查询失败:", error.message);
     * }
     * ```
     *
     * <AUTHOR>
     * @created 2025-08-07 14:34:20
     * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数、返回值和异常说明 - hayden
     */
    async queryShareholdersByFundCode(
      request: FundCodeQueryRequest
    ): Promise<BaseApiResponse<FundCodeQueryResponse>> {
      try {
        // 严格参数验证 - 组织ID是必须的
        if (!request.organizationId) {
          throw new Error("MISSING_ORGANIZATION_ID: 组织ID是必须参数，不能为空");
        }
        validateOrganizationId(request.organizationId);

        if (!request.fund_code) {
          throw new Error("MISSING_FUND_CODE: 基金代码是必须参数，不能为空");
        }
        validateFundCode(request.fund_code);

        // 使用基础客户端发送自定义请求，包含fund_code参数
        return await baseClient.sendCustomRequest<FundCodeQueryResponse>(
          "fund_institution",
          {
            organizationId: request.organizationId,
            fund_code: request.fund_code
          },
          "基金代码查询股东名称失败"
        );
      } catch (error) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error(`基金代码查询股东名称失败: ${String(error)}`);
      }
    },

    /**
     * 根据股东名称查询基金代码信息
     * @description 通过股东名称查询对应的基金代码和基金详细信息，支持参数验证和错误处理
     *
     * @param {ShareholderNameQueryRequest} request - 查询请求参数对象
     * @param {string} request.organizationId - 组织ID，必填，长度5-50个字符
     * @param {string} request.shareholder_name - 股东名称，必填，不能为空或仅包含空白字符
     *
     * @returns {Promise<BaseApiResponse<ShareholderNameQueryResponse>>} 包含基金信息的API响应
     *
     * @throws {Error} MISSING_ORGANIZATION_ID - 当organizationId为空时抛出
     * @throws {Error} INVALID_ORGANIZATION_ID - 当organizationId格式不正确时抛出
     * @throws {Error} MISSING_SHAREHOLDER_NAME - 当shareholder_name为空时抛出
     * @throws {Error} INVALID_SHAREHOLDER_NAME - 当shareholder_name格式不正确时抛出
     *
     * @example
     * ```typescript
     * try {
     *   const result = await queryFundCodeByShareholderName({
     *     organizationId: "12345",
     *     shareholder_name: "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"
     *   });
     *   console.log("基金信息:", result.data);
     *   console.log("基金代码:", result.data.fund_code);
     *   console.log("基金名称:", result.data.fund_name);
     * } catch (error) {
     *   console.error("查询失败:", error.message);
     * }
     * ```
     *
     * <AUTHOR>
     * @created 2025-08-07 14:34:20
     * @modified 2025-08-11 - 补充完整的JSDoc注释，包括详细的参数、返回值和异常说明 - hayden
     */
    async queryFundCodeByShareholderName(
      request: ShareholderNameQueryRequest
    ): Promise<BaseApiResponse<ShareholderNameQueryResponse>> {
      try {
        // 严格参数验证 - 组织ID是必须的
        if (!request.organizationId) {
          throw new Error("MISSING_ORGANIZATION_ID: 组织ID是必须参数，不能为空");
        }
        validateOrganizationId(request.organizationId);

        if (!request.shareholder_name) {
          throw new Error("MISSING_SHAREHOLDER_NAME: 股东名称是必须参数，不能为空");
        }
        validateShareholderName(request.shareholder_name);

        // 使用基础客户端发送自定义请求，包含shareholder_name参数
        // 修改记录 2025-08-07 15:50:02 hayden: 修复参数名称，确保与n8n流程期望的参数名一致
        return await baseClient.sendCustomRequest<ShareholderNameQueryResponse>(
          "fund_institution",
          {
            organizationId: request.organizationId, // 修改：使用organizationId而不是id
            shareholder_name: request.shareholder_name
          },
          "股东名称查询基金代码失败"
        );
      } catch (error) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error(`股东名称查询基金代码失败: ${String(error)}`);
      }
    },
  };
}

/**
 * 默认导出的股东基金查询 API 客户端实例
 * <AUTHOR>
 * @created 2025-08-07 14:34:20
 */
export const fundInstitutionApi = createFundInstitutionApiClient();
