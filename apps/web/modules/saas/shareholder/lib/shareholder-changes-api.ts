/**
 * 股东变动分析 API 接口
 * @file shareholder-changes-api.ts
 * @description 股东变动分析相关的 API 接口，包含增持、减持、新进、退出股东的8个接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-06-16 18:35:23
 * @modified 2025-06-17 09:55:17 - 修复类型错误：移除Number类型使用number类型，移除不必要的类型注解
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */

import { createShareholderApiClient, type BaseApiResponse} from "./base-shareholder-api";

/**
 * 增持自然人股东数据项接口
 * @interface IncreaseIndividualShareholder
 * @modified 2025-06-17 11:58:40 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface IncreaseIndividualShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: number;
	/** 较上期增持股数 */
	increased_shares: number;
	/** 较上期增持比例（%） */
	increased_ratio_percent: number;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: number;
	/** 增持发生日期 */
	increased_date: string;
}

/**
 * 增持机构股东数据项接口
 * @interface IncreaseInstitutionShareholder
 * @modified 2025-06-17 11:58:40 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface IncreaseInstitutionShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: number;
	/** 较上期增持股数 */
	increased_shares: number;
	/** 较上期增持比例（%） */
	increased_ratio_percent: number;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: number;
	/** 增持发生日期 */
	increased_date: string;
}

/**
 * 减持自然人股东数据项接口
 * @interface DecreaseIndividualShareholder
 * @modified 2025-06-17 11:58:40 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface DecreaseIndividualShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: number;
	/** 较上期减持股数 */
	decreased_shares: number;
	/** 较上期减持比例（%） */
	decreased_ratio_percent: number;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: number;
	/** 减持发生日期 */
	decreased_date: string;
}

/**
 * 减持机构股东数据项接口
 * @interface DecreaseInstitutionShareholder
 * @modified 2025-06-17 11:58:40 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface DecreaseInstitutionShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: number;
	/** 较上期减持股数 */
	decreased_shares: number;
	/** 较上期减持比例（%） */
	decreased_ratio_percent: number;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: number;
	/** 减持发生日期 */
	decreased_date: string;
}

/**
 * 新进自然人股东数据项接口
 * @interface NewIndividualShareholder
 * @modified 2025-06-18 18:38:49 - 更新字段名为下划线格式，与API文档保持一致
 * @modified 2025-06-23 17:30:02 - 修复字段名映射错误，API实际返回的是shareholding_ratio而不是shareholder_ratio
 */
export interface NewIndividualShareholder {
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为下划线格式 */
	number_of_shares: number;
	/** 当前期持股比例（%） - API实际返回字段名为下划线格式 */
	shareholding_ratio: number;
	/** 新进日期 - API实际返回字段名为下划线格式 */
	register_date: string;
}

/**
 * 新进机构股东数据项接口
 * @interface NewInstitutionShareholder
 * @modified 2025-06-17 10:09:47 - 根据API文档更新字段映射：numberOfShares → current_numberOfShares，shareholdingRatio → current_shareholdingRatio，registerDate → new_in_date
 * @modified 2025-06-18 18:38:49 - 更新字段名为下划线格式，与API文档保持一致
 * @modified 2025-06-23 17:30:02 - 修复字段名映射错误，API实际返回的是shareholding_ratio而不是shareholder_ratio
 */
export interface NewInstitutionShareholder {
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为下划线格式 */
	number_of_shares: number;
	/** 当前期持股比例（%） - API实际返回字段名为下划线格式 */
	shareholding_ratio: number;
	/** 新进日期 - API实际返回字段名为下划线格式 */
	register_date: string;
}

/**
 * 退出自然人股东数据项接口
 * @interface ExitIndividualShareholder
 * @modified 2025-06-17 10:09:47 - 根据API文档更新字段映射：添加exit_date字段，移除last_top20_date和cleared_date字段
 * @modified 2025-06-17 11:54:53 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface ExitIndividualShareholder {
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 退出发生日期 */
	exit_date: string;
	/** 上期持股数量 - API实际返回字段名为全小写 */
	prev_numberofshares: number;
	/** 上期持股比例（%） - API实际返回字段名为全小写 */
	prev_shareholdingratio: number;
}

/**
 * 退出机构股东数据项接口
 * @interface ExitInstitutionShareholder
 * @modified 2025-06-17 10:09:47 - 根据API文档更新字段映射：last_numberOfShares → prev_numberOfShares，last_shareholdingRatio → prev_shareholdingRatio
 * @modified 2025-06-17 11:54:53 - 修复字段名映射，API实际返回的是全小写字段名
 * @modified 2025-06-18 18:38:49 - 更新一码通字段名为下划线格式，与API文档保持一致
 */
export interface ExitInstitutionShareholder {
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 退出发生日期 */
	exit_date: string;
	/** 上期持股数量 - API实际返回字段名为全小写 */
	prev_numberofshares: number;
	/** 上期持股比例（%） - API实际返回字段名为全小写 */
	prev_shareholdingratio: number;
}

/**
 * 持续持股个人股东数据项接口
 * @interface ConstantIndividualShareholder
 * @description 个人股东持续持股趋势数据项，字段较减持股东API有所减少
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股个人股东API支持
 */
export interface ConstantIndividualShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: string;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: string;
}

/**
 * 持续持股机构股东数据项接口
 * @interface ConstantInstitutionShareholder
 * @description 机构股东持续持股趋势数据项，字段较减持股东API有所减少
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股机构股东API支持
 */
export interface ConstantInstitutionShareholder {
	/** 排名 */
	rank: string;
	/** 股东名称 */
	name: string;
	/** 一码通 - API实际返回字段名为下划线格式 */
	unified_account_number: string;
	/** 当前期持股数量 - API实际返回字段名为全小写 */
	current_numberofshares: string;
	/** 当前期持股比例（%） - API实际返回字段名为全小写 */
	current_shareholdingratio: string;
}

// 基础接口已移至 base-shareholder-api.ts

/**
 * 增持自然人股东 API 响应数据接口
 * @interface IncreaseIndividualShareholdersData
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface IncreaseIndividualShareholdersData {
	/** 增持自然人股东明细 */
	increaseIndividuals: IncreaseIndividualShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 增持机构股东 API 响应数据接口
 * @interface IncreaseInstitutionShareholdersData
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface IncreaseInstitutionShareholdersData {
	/** 增持机构股东明细 */
	increaseInstitutions: IncreaseInstitutionShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 减持自然人股东 API 响应数据接口
 * @interface DecreaseIndividualShareholdersData
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface DecreaseIndividualShareholdersData {
	/** 减持自然人股东明细 */
	decreaseIndividuals: DecreaseIndividualShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 减持机构股东 API 响应数据接口
 * @interface DecreaseInstitutionShareholdersData
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface DecreaseInstitutionShareholdersData {
	/** 减持机构股东明细 */
	decreaseInstitutions: DecreaseInstitutionShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 新进自然人股东 API 响应数据接口
 * @interface NewIndividualShareholdersData
 * @modified 2025-06-17 10:09:47 - 根据API文档拆分为独立接口
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface NewIndividualShareholdersData {
	/** 新进自然人股东明细 */
	newIndividuals: NewIndividualShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 新进机构股东 API 响应数据接口
 * @interface NewInstitutionShareholdersData
 * @modified 2025-06-17 10:09:47 - 根据API文档拆分为独立接口
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface NewInstitutionShareholdersData {
	/** 新进机构股东明细 */
	newInstitutions: NewInstitutionShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 退出自然人股东 API 响应数据接口
 * @interface ExitIndividualShareholdersData
 * @modified 2025-06-17 10:09:47 - 根据API文档拆分为独立接口
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface ExitIndividualShareholdersData {
	/** 退出自然人股东明细 */
	exitIndividuals: ExitIndividualShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 退出机构股东 API 响应数据接口
 * @interface ExitInstitutionShareholdersData
 * @modified 2025-06-17 10:09:47 - 根据API文档拆分为独立接口
 * @modified 2025-06-18 18:01:01 - 添加总数字段，用于模块三API更新
 */
export interface ExitInstitutionShareholdersData {
	/** 退出机构股东明细 */
	exitInstitutions: ExitInstitutionShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 持续持股个人股东 API 响应数据接口
 * @interface ConstantIndividualShareholdersData
 * @description 个人股东持续持股趋势API响应数据结构
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股个人股东API支持
 */
export interface ConstantIndividualShareholdersData {
	/** 持续持股个人股东明细 */
	constantIndividuals: ConstantIndividualShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

/**
 * 持续持股机构股东 API 响应数据接口
 * @interface ConstantInstitutionShareholdersData
 * @description 机构股东持续持股趋势API响应数据结构
 * <AUTHOR>
 * @created 2025-07-23 - 新增持续持股机构股东API支持
 */
export interface ConstantInstitutionShareholdersData {
	/** 持续持股机构股东明细 */
	constantInstitutions: ConstantInstitutionShareholder[];
	/** 符合条件的数据总条数 */
	total: number;
}

// 通用 API 响应接口已移至 base-shareholder-api.ts，使用 BaseApiResponse<T>

// 创建 API 客户端实例
const apiClientInstance = createShareholderApiClient();

/**
 * 股东变动分析 API 客户端
 * @description 提供股东变动分析相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-06-16 18:35:23
 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
 */
export const shareholderChangesApi = {
	/**
	 * 获取增持自然人股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 增持自然人股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getIncreaseIndividualShareholders("12345", 1, 5, "Desc", "increased_shares");
	 * console.log(data.data.increaseIndividuals);
	 * ```
	 */
	getIncreaseIndividualShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<IncreaseIndividualShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<IncreaseIndividualShareholdersData>(
			"increase-individual-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取增持自然人股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取增持机构股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 增持机构股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getIncreaseInstitutionShareholders("12345", 1, 5, "Desc", "increased_shares");
	 * console.log(data.data.increaseInstitutions);
	 * ```
	 */
	getIncreaseInstitutionShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<IncreaseInstitutionShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<IncreaseInstitutionShareholdersData>(
			"increase-institution-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取增持机构股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取减持自然人股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 减持自然人股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getDecreaseIndividualShareholders("12345", 1, 5, "Desc", "decreased_shares");
	 * console.log(data.data.decreaseIndividuals);
	 * ```
	 */
	getDecreaseIndividualShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<DecreaseIndividualShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<DecreaseIndividualShareholdersData>(
			"decrease-individual-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取减持自然人股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取减持机构股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 减持机构股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getDecreaseInstitutionShareholders("12345", 1, 5, "Desc", "decreased_shares");
	 * console.log(data.data.decreaseInstitutions);
	 * ```
	 */
	getDecreaseInstitutionShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<DecreaseInstitutionShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<DecreaseInstitutionShareholdersData>(
			"decrease-institution-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取减持机构股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取新进自然人股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 新进自然人股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-17 10:09:47 - 根据API文档拆分为独立方法，添加分页参数
	 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getNewIndividualShareholders("12345", 1, 5, "Desc", "number_of_shares");
	 * console.log(data.data.newIndividuals);
	 * ```
	 */
	getNewIndividualShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<NewIndividualShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<NewIndividualShareholdersData>(
			"new-individual-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取新进自然人股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取新进机构股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 新进机构股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-17 10:13:14 - 根据API文档添加独立方法
	 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getNewInstitutionShareholders("12345", 1, 5, "Desc", "number_of_shares");
	 * console.log(data.data.newInstitutions);
	 * ```
	 */
	getNewInstitutionShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<NewInstitutionShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<NewInstitutionShareholdersData>(
			"new-institution-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取新进机构股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取退出自然人股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 退出自然人股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-17 10:13:14 - 根据API文档拆分为独立方法，添加分页参数
	 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getExitIndividualShareholders("12345", 1, 5, "Desc", "prev_numberOfShares");
	 * console.log(data.data.exitIndividuals);
	 * ```
	 */
	getExitIndividualShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<ExitIndividualShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<ExitIndividualShareholdersData>(
			"exit-individual-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取退出自然人股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取退出机构股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 退出机构股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * @modified 2025-06-17 10:13:14 - 根据API文档拆分为独立方法，添加分页参数
	 * @modified 2025-06-18 09:40:56 - 重构：使用共用的基础 API 模块
	 * @modified 2025-06-18 18:01:01 - 添加排序参数支持，用于模块三API更新
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getExitInstitutionShareholders("12345", 1, 5, "Desc", "prev_numberOfShares");
	 * console.log(data.data.exitInstitutions);
	 * ```
	 */
	getExitInstitutionShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<ExitInstitutionShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<ExitInstitutionShareholdersData>(
			"exit-institution-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取退出机构股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取持续持股个人股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 持续持股个人股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * <AUTHOR>
	 * @created 2025-07-23 - 新增持续持股个人股东API支持
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getConstantIndividualShareholders("12345", 1, 5, "Desc", "current_shareholdingratio");
	 * console.log(data.data.constantIndividuals);
	 * ```
	 */
	getConstantIndividualShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<ConstantIndividualShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<ConstantIndividualShareholdersData>(
			"constant-individual-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取持续持股个人股东信息失败",
			order,
			orderBase,
		);
	},

	/**
	 * 获取持续持股机构股东信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @param page 页码，正整数，默认为1
	 * @param pageSize 每页条数，正整数，默认为5
	 * @param order 排序方向，可选参数，Desc或Asc
	 * @param orderBase 排序字段，可选参数，具体字段名参考响应参数
	 * @returns 持续持股机构股东数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 * <AUTHOR>
	 * @created 2025-07-23 - 新增持续持股机构股东API支持
	 *
	 * @example
	 * ```typescript
	 * const data = await shareholderChangesApi.getConstantInstitutionShareholders("12345", 1, 5, "Desc", "current_shareholdingratio");
	 * console.log(data.data.constantInstitutions);
	 * ```
	 */
	getConstantInstitutionShareholders: async (
		organizationId: string,
		page = 1,
		pageSize = 5,
		order?: "Desc" | "Asc",
		orderBase?: string,
	): Promise<BaseApiResponse<ConstantInstitutionShareholdersData>> => {
		return apiClientInstance.sendSortablePaginatedRequest<ConstantInstitutionShareholdersData>(
			"constant-institution-shareholders-trend",
			organizationId,
			page,
			pageSize,
			"获取持续持股机构股东信息失败",
			order,
			orderBase,
		);
	},
};
