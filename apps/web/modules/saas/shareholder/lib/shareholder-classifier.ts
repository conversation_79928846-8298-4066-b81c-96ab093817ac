/**
 * 股东类型匹配规则函数
 *
 * 功能：
 * 1. 为股东数据批量添加股东类型字段
 * 2. 管理股东分类规则的本地缓存
 * 3. 支持多种名册类型的字段兼容
 * 4. 基于优先级的规则匹配逻辑
 * 5. 基于matchField字段进行灵活匹配
 *
 * <AUTHOR>
 * @created 2025-06-25 13:38:45
 * @modified 2025-06-26 14:43:52 - 更新匹配逻辑支持matchField字段，使用分组后的规则数据结构
 * @description 在股东名册上传前为股东数据自动分类的核心函数
 */

import { encryptData, decryptData } from '@repo/utils/lib/crypto';
import {
  shareholderClassificationApi,
  type ShareholderClassificationRuleGroup
} from './classification-api';

/**
 * 本地存储键名配置
 */
const CACHE_KEYS = {
  RULES_DATA: 'shareholder_classification_rules_encrypted',    // 加密的规则数据
  UPDATE_TIME: 'shareholder_classification_update_time'        // 明文的更新时间
} as const;

/**
 * 股东名称和持有人类别字段映射
 * 根据不同名册类型映射对应的字段名
 */
const FIELD_MAPPING = {
  // 股东名称字段映射
  SHAREHOLDER_NAME: {
    '01': 'ZQZHMC',      // 深市01名册：证券账户名称
    '05': 'XYZHMC',      // 深市05名册：信用账户名称  
    't1': 'CYRMC',       // 沪市t1名册：持有人名称
    't2': 'CYRMC',       // 沪市t2名册：持有人名称
    't3': 'CYRMC'        // 沪市t3名册：持有人名称
  },
  // 持有人类别字段映射
  SHAREHOLDER_CATEGORY: {
    '01': 'CYRLBMS',     // 深市01名册：持有人类别代码
    '05': 'CYRLBMS',     // 深市05名册：持有人类别代码
    't1': 'CYRLB',       // 沪市t1名册：持有人类别
    't2': 'CYRLB',       // 沪市t2名册：持有人类别
    't3': 'CYRLB'        // 沪市t3名册：持有人类别
  }
} as const;

/**
 * 股东分类结果类型
 */
export interface ClassificationResult {
  shareholderType: string; // 匹配到的股东类型
  matchedRule?: {
    priority: number;
    type: string;
    rule: string;
  };
}

/**
 * 获取股东名称字段值
 *
 * @param shareholder 股东数据对象
 * @param registryType 名册类型
 * @returns 股东名称字符串
 */
function getShareholderName(shareholder: any, registryType: string): string {
  const fieldName = FIELD_MAPPING.SHAREHOLDER_NAME[registryType as keyof typeof FIELD_MAPPING.SHAREHOLDER_NAME];
  if (!fieldName) {
    // 删除调试日志 - 2025-06-25 17:53:16 hayden
    // console.warn(`[股东分类器调试] 未知的名册类型: ${registryType}`);
    return '';
  }

  const name = shareholder[fieldName];
  const result = typeof name === 'string' ? name.trim() : '';



  return result;
}

/**
 * 获取持有人类别字段值
 *
 * @param shareholder 股东数据对象
 * @param registryType 名册类型
 * @returns 持有人类别代码字符串
 */
function getShareholderCategory(shareholder: any, registryType: string): string {
  const fieldName = FIELD_MAPPING.SHAREHOLDER_CATEGORY[registryType as keyof typeof FIELD_MAPPING.SHAREHOLDER_CATEGORY];
  if (!fieldName) {
    // 删除调试日志 - 2025-06-25 17:53:16 hayden
    // console.warn('[股东分类器调试] 未知的名册类型:', registryType);
    return '';
  }

  const category = shareholder[fieldName];
  const result = typeof category === 'string' ? category.trim() : '';



  return result;
}

/**
 * 规则组匹配逻辑（基于API返回的matchField字段）
 *
 * @param shareholderName 股东名称
 * @param shareholderCategory 持有人类别代码
 * @param ruleGroup 分组后的规则数据
 * @returns 是否匹配成功
 * <AUTHOR>
 * @created 2025-06-26 14:43:52
 * @modified 2025-06-26 15:20:09 - 移除调试日志，保持代码简洁
 * @description 基于API返回的matchField字段进行匹配，避免硬编码逻辑
 */
function isRuleGroupMatch(
  shareholderName: string,
  shareholderCategory: string,
  ruleGroup: ShareholderClassificationRuleGroup
): boolean {
  const { matchField, rules, type } = ruleGroup;

  // 根据API返回的matchField确定匹配逻辑
  switch (matchField) {
    case "SHAREHOLDER_NAME": {
      // 股东名称匹配（支持完全匹配和包含匹配）
      return rules.some(rule => {
        if (rule === "*") {
          return true; // 兜底规则
        }

        // 优先尝试完全匹配
        const exactMatch = shareholderName === rule;
        if (exactMatch) {
          return true;
        }

        // 如果完全匹配失败，尝试包含匹配（适用于基金、证券等长名称）
        return shareholderName.includes(rule);
      });
    }

    case "SHAREHOLDER_CATEGORY": {
      // 持有人类别包含匹配（OR逻辑）
      return rules.some(rule => shareholderCategory.includes(rule));
    }

    case "SHAREHOLDER_NAME_INCLUDE": {
      // 股东名称包含匹配
      if (type === "保险") {
        // 保险类特殊处理：需要同时包含所有关键字（AND逻辑）
        const insuranceMatch = rules.every(rule => {
          const isMatch = shareholderName.includes(rule);
 
          return isMatch;
        });
        return insuranceMatch;
      }
      // 其他类型：包含任一关键字即可（OR逻辑）
      const includeMatch = rules.some(rule => {
        const isMatch = shareholderName.includes(rule);
 
        return isMatch;
      });
  
      return includeMatch;
    }

    case "DEFAULT": {
      // 兜底规则：通常是优先级最低的规则，匹配所有未匹配的股东
      // 检查是否有通配符规则
      const hasWildcard = rules.some(rule => rule === "*");
      return hasWildcard;
    }

    default:
      console.warn(`未知的匹配字段类型: ${matchField}`);
      return false;
  }
}

/**
 * 单个股东类型匹配
 *
 * @param shareholder 股东数据对象
 * @param ruleGroups 分组后的分类规则数组（已按优先级排序）
 * @param registryType 名册类型
 * @param debugIndex 调试索引（用于前20个股东的详细日志）
 * @returns 分类结果
 * <AUTHOR>
 * @modified 2025-06-25 16:08:56 - 添加优先级13（保险）的特殊处理逻辑，需要同时匹配"保险"和"公司"两个关键词
 * @modified 2025-06-25 19:03:48 - 移除调试日志，保持代码简洁
 * @modified 2025-06-26 14:43:52 - 更新为使用分组后的规则数据结构和基于matchField的匹配逻辑
 * @modified 2025-06-26 14:55:28 - 添加调试索引参数，支持详细匹配日志
 */
function classifySingleShareholder(
  shareholder: any,
  ruleGroups: ShareholderClassificationRuleGroup[],
  registryType: string,
): ClassificationResult {
  const shareholderName = getShareholderName(shareholder, registryType);
  const shareholderCategory = getShareholderCategory(shareholder, registryType);

  // 按优先级顺序遍历规则组
  for (const ruleGroup of ruleGroups.sort((a, b) => a.priority - b.priority)) {
    if (isRuleGroupMatch(shareholderName, shareholderCategory, ruleGroup)) {
      return {
        shareholderType: ruleGroup.type,
        matchedRule: {
          priority: ruleGroup.priority,
          type: ruleGroup.type,
          rule: ruleGroup.rules.join(', ')
        }
      };
    }
  }

  // 如果没有匹配到任何规则，返回默认类型
  return {
    shareholderType: '其他机构'
  };
}

/**
 * 获取或更新股东分类规则缓存
 *
 * @returns 解密后的分组规则数组（按优先级排序）
 * <AUTHOR>
 * @modified 2025-06-26 14:43:52 - 更新为使用分组后的规则数据结构
 */
async function getClassificationRules(): Promise<ShareholderClassificationRuleGroup[]> {
  try {
    // 检查是否需要更新缓存
    const needsUpdate = await checkRulesUpdateTime();

    if (needsUpdate) {
      // 获取最新规则数据
      const { rules, lastUpdatedAt } = await shareholderClassificationApi.fetchRules();

      // 加密规则数据并缓存
      const encryptedRules = encryptData(JSON.stringify(rules));
      localStorage.setItem(CACHE_KEYS.RULES_DATA, encryptedRules);
      localStorage.setItem(CACHE_KEYS.UPDATE_TIME, lastUpdatedAt);

      // 按优先级排序并返回
      return rules.sort((a, b) => a.priority - b.priority);
    }

    // 使用缓存的规则数据
    const encryptedRules = localStorage.getItem(CACHE_KEYS.RULES_DATA);
    if (!encryptedRules) {
      // 如果缓存不存在，强制从API获取
      const { rules, lastUpdatedAt } = await shareholderClassificationApi.fetchRules();

      // 缓存数据
      const encryptedRulesNew = encryptData(JSON.stringify(rules));
      localStorage.setItem(CACHE_KEYS.RULES_DATA, encryptedRulesNew);
      localStorage.setItem(CACHE_KEYS.UPDATE_TIME, lastUpdatedAt);

      return rules.sort((a, b) => a.priority - b.priority);
    }

    // 解密并解析规则数据
    const decryptedRules = decryptData(encryptedRules);
    const rules: ShareholderClassificationRuleGroup[] = JSON.parse(decryptedRules);

    // 按优先级排序并返回
    return rules.sort((a, b) => a.priority - b.priority);
  } catch (error) {
    console.error('获取股东分类规则失败:', error);

    // 如果获取规则失败，返回一个默认规则，确保功能不中断
    return [{
      priority: 20,
      type: '其他机构',
      matchField: 'SHAREHOLDER_NAME',
      rules: ['*']
    }];
  }
}

/**
 * 检查规则更新时间并决定是否需要更新缓存
 * 
 * @returns 是否需要更新缓存
 */
async function checkRulesUpdateTime(): Promise<boolean> {
  try {
    // 获取缓存的更新时间
    const cachedUpdateTime = localStorage.getItem(CACHE_KEYS.UPDATE_TIME);

    // 如果没有缓存，需要首次加载
    if (!cachedUpdateTime) {
      return true;
    }

    // 调用API检查最新更新时间
    const { lastUpdatedAt } = await shareholderClassificationApi.checkUpdateTime();

    // 比较时间是否一致
    return cachedUpdateTime !== lastUpdatedAt;
  } catch (error) {
    console.error('检查规则更新时间失败:', error);
    // 检查失败时，为了安全起见，重新获取规则
    return true;
  }
}

/**
 * 为股东数据批量添加股东类型
 *
 * @param shareholders 股东数据数组
 * @param registryType 名册类型（01/05/t1/t2/t3）
 * @returns 添加了shareholderType字段的股东数据数组
 * <AUTHOR>
 * @modified 2025-06-26 14:55:28 - 添加调试日志，打印前20个股东的匹配情况
 */
export async function classifyShareholdersBatch(
  shareholders: any[],
  registryType: string
): Promise<any[]> {
  try {
    // 获取分类规则（分组后的数据结构）
    const ruleGroups = await getClassificationRules();
    // 为每个股东进行分类
    const classifiedShareholders = shareholders.map((shareholder) => {

      const result = classifySingleShareholder(shareholder, ruleGroups, registryType);

      return {
        ...shareholder,
        shareholderType: result.shareholderType
      };
    });

    return classifiedShareholders;
  } catch (error) {
    console.error('股东类型批量匹配失败:', error);
    throw new Error(`股东类型批量匹配失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}
