/**
 * 独立股东详情分析 API 接口
 * @file single-shareholder-analysis-api.ts
 * @description 独立股东详情分析相关的 API 接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 20:29:20 - 重构使用base-shareholder-api的标准化API客户端
 */

import type { BaseApiResponse } from "@saas/shareholder/lib/base-shareholder-api";
import {
	validateOrganizationId,
	decryptApiResponseData,
	createApiResponse
} from "@saas/shareholder/lib/base-shareholder-api";
import { createEncryptedRequestAsync } from "@repo/utils";

/**
 * 股东趋势数据项接口
 * @interface ShareholderTrendItem
 * @description 单个股东趋势数据项的结构
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 - 更新字段类型以匹配新API返回的数据结构
 */
export interface ShareholderTrendItem {
	/** 登记日期 */
	registerDate: string;
	/** 持股数量（股） */
	numberOfShares: number;
	/** 持股比例（%） */
	shareholdingRatio: number;
}

/**
 * 独立股东详情数据接口
 * @interface SingleShareholderAnalysisData
 * @description 独立股东详情分析数据的结构，基于基金代码查股东名册API
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 - 重构为基金代码查股东名册API数据结构
 */
export interface SingleShareholderAnalysisData {
	/** 组织机构ID */
	organizationId: string;
	/** 记录唯一标识符 */
	id: string;
	/** 证件号码 */
	certificate_number: string;
	/** 一码通账户号码 */
	unified_account_number: string;
	/** 证券账户名称/股东名称 */
	account_name: string;
	/** 联系电话号码 */
	phone_number: string;
	/** 股东类型分类 */
	shareholder_type: string;
	/** 持有人类别代码 */
	shareholder_category: string;
	/** 通讯地址 */
	contact_address: string;
	/** 普通证券账户号码 */
	cash_account: string;
	/** 普通账户持股数量 */
	cash_account_shares: number;
	/** 信用证券账户号码 */
	margin_account: string;
	/** 信用账户持股数量 */
	margin_account_shares: number;
	/** 最新报告期日期 */
	latest_period_date: string;
	/** 当前持股数量 */
	current_shares: number;
	/** 当前持股比例 */
	current_ratio: number;
	/** 上期持股数量 */
	prev_shares: number;
	/** 上期持股比例 */
	prev_ratio: number;
	/** 上期报告期日期 */
	previous_period_date: string;
	/** 股东行为（增持/减持/不变/新增） */
	shareholder_behavior: string;
	/** 持股数量变化 */
	shares_change: number;
	/** 持股比例变化 */
	ratio_change: number;
	/** 持股排名 */
	holding_rank: number;
	/** 上一期排名 - 2025-08-07 hayden 新增排名变动分析字段 */
	previous_holding_rank: number;
	/** 排名变化 - 2025-08-07 hayden 新增排名变动分析字段 */
	rank_change: number;
	/** 排名描述 - 2025-08-07 hayden 新增排名变动分析字段 */
	rank_change_description: string;
	/** 所有期间持股记录 */
	all_periods_holdings: ShareholderTrendItem[];
	/** 匹配状态标识 */
	match_status: string;
	/** 公司冻结股总数 */
	company_frozen_shares: string;
	/** 公司限售股总数 */
	company_locked_shares: string;
	/** 公司冻结股比例 */
	company_frozen_ratio_percent: string;
	/** 公司限售股比例 */
	company_locked_ratio_percent: string;
	/** 最新持仓市值（元） */
	latest_market_value: number;
	/** 最新持仓收益（元） */
	latest_holding_profit: number;
	/** 预估持仓总成本（元） */
	estimated_total_cost: number;
	/** 最新股价 */
	latest_price: number;
	/** 平均股价 */
	average_stock_price: number;
	/** 计算时间戳 */
	calculation_time: string;
}

/**
 * 独立股东详情分析请求参数接口
 * @interface SingleShareholderAnalysisRequest
 * @description 独立股东详情分析请求参数的结构，支持基金代码和一码通查询
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 - 更新为支持基金代码和一码通查询参数
 */
export interface SingleShareholderAnalysisRequest {
	/** 组织机构ID，必填参数 */
	organizationId: string;
	/** 基金代码，与unified_account_number二选一 */
	fund_code?: string;
	/** 一码通账户号码，与fund_code二选一 */
	unified_account_number?: string;
}



/**
 * 独立股东详情分析 API 客户端
 * @description 提供独立股东详情分析相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-07-24 16:31:03
 * @modified 2025-08-06 20:29:20 - 重构使用标准化API客户端和自定义请求处理
 */
export const singleShareholderAnalysisApi = {
	/**
	 * 获取独立股东详情分析信息（基金代码查股东名册）
	 * @param organizationId 组织机构ID，必填参数
	 * @param fund_code 基金代码，与unified_account_number二选一
	 * @param unified_account_number 一码通账户号码，与fund_code二选一
	 * @returns 独立股东详情分析数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 *
	 * @example
	 * ```typescript
	 * // 基金代码查询
	 * const analysis = await singleShareholderAnalysisApi.getSingleShareholderAnalysis("Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36", "001384.OF");
	 * // 一码通查询
	 * const analysis = await singleShareholderAnalysisApi.getSingleShareholderAnalysis("Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36", undefined, "A123456789");
	 * console.log(analysis.data.account_name);
	 * ```
	 *
	 * <AUTHOR>
	 * @created 2025-07-24 16:31:03
	 * @modified 2025-08-06 20:29:20 - 重构使用标准化API客户端和自定义请求处理
	 */
	getSingleShareholderAnalysis: async (
		organizationId: string,
		fund_code?: string,
		unified_account_number?: string
	): Promise<BaseApiResponse<SingleShareholderAnalysisData>> => {
		try {
			// 使用标准化的参数验证
			validateOrganizationId(organizationId);

			// 验证查询参数
			if (!fund_code && !unified_account_number) {
				throw new Error(
					"INSUFFICIENT_QUERY_PARAMS: fund_code和unified_account_number至少需要提供一个参数",
				);
			}

			// 构建请求参数对象 - 修改记录 2025-08-11 - 使用具体类型替代any，提高类型安全性 - hayden
			const requestParams: {
				organizationId: string;
				fund_code?: string;
				unified_account_number?: string;
			} = {
				organizationId,
			};

			if (fund_code) {
				requestParams.fund_code = fund_code;
			}

			if (unified_account_number) {
				requestParams.unified_account_number = unified_account_number;
			}

			// 创建加密且带签名的请求参数
			const requestData =
				await createEncryptedRequestAsync(requestParams);

			// 发送请求到 n8n 代理接口
			const apiUrl = "/api/n8n_proxy/fundcode_sharehold";

			const response = await fetch(apiUrl, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify(requestData),
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(
					`HTTP ${response.status}: 请求失败 - ${errorText}`,
				);
			}

			const result = await response.json();


			// 检查响应状态 - 修复：支持多种成功状态码格式
			// 有些API返回code=0表示成功，有些返回code=200表示成功
			const isSuccess = result.code === 0 || result.code === 200 || result.success === true;

			if (!isSuccess) {

				// 处理业务错误
				// 检查字符串类型的错误码
				if (result.code === "MISSING_ORGANIZATIONID" || result.message?.includes("MISSING_ORGANIZATIONID")) {
					throw new Error("缺少必需参数：organizationId。请提供organizationId参数。");
				}
				if (result.code === "INSUFFICIENT_QUERY_PARAMS" || result.message?.includes("INSUFFICIENT_QUERY_PARAMS")) {
					throw new Error("fund_code和unified_account_number至少需要提供一个参数");
				}
				if (result.code === "ORGANIZATION_NOT_FOUND" || result.message?.includes("ORGANIZATION_NOT_FOUND")) {
					throw new Error("未找到对应的组织ID，请检查organizationcode是否正确");
				}

				// 检查特定的错误消息
				if (result.message === "没有找到该基金" || result.message?.includes("没有找到该基金")) {
					throw new Error("没有找到该基金，请检查基金代码是否正确");
				}

				// 检查数字类型的错误码
				if (result.code === 400) {
					throw new Error(`请求参数错误：${result.message || "参数验证失败"}`);
				}
				if (result.code === 404) {
					throw new Error(`未找到数据：${result.message || "请检查查询参数"}`);
				}
				if (result.code === 500) {
					throw new Error(`服务器内部错误：${result.message || "请稍后重试"}`);
				}

				throw new Error(result.message || `请求失败，错误码：${result.code}`);
			}

			// 使用标准化的解密函数处理响应数据
			const decryptedData = decryptApiResponseData<SingleShareholderAnalysisData[]>(result.data);

			// 取第一个匹配的股东数据
			const shareholderData =
				Array.isArray(decryptedData) && decryptedData.length > 0
					? decryptedData[0]
					: null;

			if (!shareholderData) {
				throw new Error("未找到匹配的股东数据");
			}
			// 获取时间戳，优先使用数据中的时间
			let timestamp: string;
			if (
				shareholderData.all_periods_holdings &&
				shareholderData.all_periods_holdings.length > 0
			) {
				timestamp =
					shareholderData.all_periods_holdings[0].registerDate ||
					new Date().toISOString();
			} else if (shareholderData.calculation_time) {
				timestamp = shareholderData.calculation_time;
			} else {
				timestamp = new Date().toISOString();
			}

			// 使用标准化的响应创建函数
			return createApiResponse(shareholderData, timestamp);
		} catch (error) {
			// 统一错误处理
			if (error instanceof Error) {
				throw error;
			}
			throw new Error(`获取独立股东详情分析失败: ${String(error)}`);
		}
	}
};
