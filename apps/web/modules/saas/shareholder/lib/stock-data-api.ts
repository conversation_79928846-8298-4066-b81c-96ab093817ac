/**
 * 股票数据 API 接口
 * @file stock-data-api.ts
 * @description 股票数据相关的 API 接口，使用 n8n 代理中间件和加解密功能
 * <AUTHOR>
 * @created 2025-07-21
 */

import { createShareholderApiClient, type BaseApiResponse } from "./base-shareholder-api";

/**
 * 股票数据项接口
 * @interface StockDataItem
 * @description 单个股票数据项的结构
 * <AUTHOR>
 * @created 2025-07-21
 * @modified 2025-07-22 - 添加 latest_price 字段支持持仓收益计算
 */
export interface StockDataItem {
	/** 交易天数 */
	trading_days_count: number;
	/** 开始日期，格式：YYYYMMDD */
	startdate: string;
	/** 结束日期，格式：YYYYMMDD */
	enddate: string;
	/** 平均股价 */
	average_stock_price: string;
	/** 开始日期股价 */
	start_date_stock_price: string;
	/** 结束日期股价 */
	end_date_stock_price: string;
	/** 最新股价 - 用于计算最新持仓市值 */
	latest_price: string;
}

/**
 * 股票数据 API 响应数据类型
 * @description 支持数组格式
 */
export type StockDataApiData = StockDataItem[];

// 创建 API 客户端实例
const apiClientInstance = createShareholderApiClient();

/**
 * 股票数据 API 客户端
 * @description 提供股票数据相关的 API 调用方法，使用 n8n 代理中间件
 * <AUTHOR>
 * @created 2025-07-21
 */
export const stockDataApi = {
	/**
	 * 获取股票数据信息
	 * @param organizationId 组织ID，必填参数，长度5-50个字符
	 * @returns 股票数据
	 * @throws {Error} 当请求失败或数据解析失败时抛出错误
	 *
	 * @example
	 * ```typescript
	 * const stockData = await stockDataApi.getStockData("12345");
	 * console.log(stockData.data[0].average_stock_price);
	 * ```
	 */
	getStockData: async (organizationId: string): Promise<BaseApiResponse<StockDataApiData>> => {
		// 修改记录 2025-08-11 - 删除调试日志，提高生产环境安全性 - hayden
		const response = await apiClientInstance.sendBaseRequest<StockDataApiData>(
			"Stock_price_interface",
			organizationId,
			"获取股票数据失败",
		);

		// 获取时间戳，优先使用数据中的时间
		let timestamp: string;
		if (Array.isArray(response.data) && response.data.length > 0) {
			// 使用第一条数据的结束日期作为时间戳
			const firstItem = response.data[0];
			if (firstItem.enddate) {
				// 将 YYYYMMDD 格式转换为 ISO 格式
				const year = firstItem.enddate.substring(0, 4);
				const month = firstItem.enddate.substring(4, 6);
				const day = firstItem.enddate.substring(6, 8);
				timestamp = `${year}-${month}-${day}T00:00:00.000Z`;
			} else {
				timestamp = response.timestamp;
			}
		} else {
			timestamp = response.timestamp;
		}

		// 返回带有正确时间戳的响应
		return {
			...response,
			timestamp,
		};
	}
};
