/**
 * 股东数据相关类型定义
 *
 * <AUTHOR>
 * @modified 2025-06-25 14:04:10 - 添加股东分类相关类型定义
 */

// 股东项类型
export interface ShareholderItem {
	id: string;
	shareholderId: string;
	unifiedAccountNumber: string;
	securitiesAccountName: string;
	shareholderCategory: string;
	numberOfShares: string;
	lockedUpShares: string;
	shareholdingRatio: string;
	frozenShares: string;
	contactAddress?: string;
	contactNumber?: string;
	zipCode?: string;
	cashAccount?: string;
	sharesInCashAccount?: string;
	marginAccount?: string;
	sharesInMarginAccount?: string;
	relatedPartyIndicator?: string;
	clientCategory?: string;
	remarks?: string;
	marginCollateralAccountNumber?: string;
	marginCollateralAccountName?: string;
	natureOfShares?: string;

	// 新增字段
	shareholderTags?: string[]; // 股东标签
	rankInPeriod?: number; // 当期排名
	shareholderType?: string; // 股东类型（新增：股东分类匹配结果）
	change?: {
		// 变动信息
		type:
			| "increase"
			| "decrease"
			| "new"
			| "exit"
			| "unchanged"
			| "modified";
		field?: string; // 变动的字段
		oldValue?: string; // 变动前的值
		newValue?: string; // 变动后的值
	};
}

// 股东列表响应类型
export interface ShareholdersResponse {
  shareholders: ShareholderItem[];
  pagination: PaginationInfo;
}

// 分页信息
export interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 期数日期项类型
export interface RegisterDateItem {
  registerDate: string;
  companyCode: string;
}

// 名册上传相关类型
export interface ShareholderRegistryUploadResult {
  id: string;
  fileName: string;
  recordCount: number;
  registerDate: string;
  uploadedAt: string;
  success?: boolean;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 名册上传响应扩展
export interface ShareholderRegistryUploadResponse {
  success: boolean;
  id?: string;
  fileName?: string;
  recordCount?: number;
  registerDate?: string;
  uploadedAt?: string;
  code?: number;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 名册列表项
export interface ShareholderRegistryItem {
  id: string;
  fileName: string;
  recordCount: number;
  registerDate: string;
  companyCode: string;
  companyName: string;
  uploadedAt: string;
  companyDetail?: {
    companyName: string;
    totalShares: string;
    totalShareholders: number;
    totalInstitutions: number;
    largeSharesCount: string;
    institutionShares: string;
    largeShareholdersCount: number;
    // 控股股东信息
    controllingShareholderInfo?: {
      securitiesAccountName: string;  // 证券账户名称
      shareholdingRatio: string;      // 持股比例
      numberOfShares: string;         // 持股数量
    };
    // 前十股东持股信息
    topTenShareholdersInfo?: {
      totalRatio: string;            // 前十大股东持股比例总和
      totalShares: string;           // 前十大股东持股数量总和
    };
  };
  userName?: string;
}

// 名册列表响应
export interface ShareholderRegistryListResponse {
  registries: ShareholderRegistryItem[];
  pagination: PaginationInfo;
}

// 排序方向类型
export type SortOrder = 'asc' | 'desc';

// 排序类型 - 修改于 2025-06-17 17:09:31.041，支持按排名、期数日期排序或具体期数日期排序
// 'rank': 按排名排序（净变化量）
// 'date': 按期数日期排序（最新期数日期）
// 具体日期格式(YYYY-MM-DD): 按指定期数的持股变化排序
export type SortType = 'rank' | 'date' | string;

// 股东查询参数
export interface ShareholderQueryParams {
  registerDate?: string;
  page?: number;
  limit?: number;
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: SortOrder;
}

/**
 * 股东持股变化分析相关类型
 * @created 2024-07-01 15:30:45.621
 */

// 股东持股变化项
export interface ShareholderChangeItem {
  rank: number;
  shareholderId: string;
  shareholderName: string;
  shareholderType: string;
  unifiedAccountNumber: string;
  netChange: number;
  startHolding: number;
  endHolding: number;
  periodChanges: PeriodChange[];
  
  // 为了兼容现有组件的字段
  id?: string;
  name?: string;
  shares?: string;
  idNumber?: string;
  stockCode?: string;
  changeAmount?: string;
  latestDate?: string;
  previousDate?: string;
  earliestDate?: string;
}

// 单期变动信息
export interface PeriodChange {
  date: string;
  change: number;
  holding: number;
}

// 股东持股变化分析结果
export interface ShareholderChangeAnalysisResult {
  analysisRange: {
    startDate: string;
    endDate: string;
    totalPeriods: number;
  };
  availableDates: string[];
  shareholderChanges: ShareholderChangeItem[];
  pagination: PaginationInfo;
}

// 股东持股变化查询参数 - 修改于 2025-06-17 17:09:31.041，更新sortType参数支持具体日期格式
export interface ShareholderChangeQueryParams {
  organizationId: string;
  startDate: string;
  endDate: string;
  shareholderType?: string;
  searchTerm?: string;
  sortType?: SortType; // 修改：排序类型，支持按排名、期数日期或具体期数日期(YYYY-MM-DD)排序
  sortOrder?: SortOrder;
  page?: number;
  limit?: number;
}

/**
 * 股东分类相关类型定义
 *
 * <AUTHOR>
 * @created 2025-06-25 14:04:10
 * @description 股东类型匹配规则和分类结果相关类型
 */

// 股东分类规则类型（原始单条规则）
export interface ShareholderClassificationRule {
  id: string;
  priority: number;        // 优先级 1-20
  type: string;           // 股东类型名称
  rule: string;           // 匹配规则
  updatedAt: string;      // 更新时间
}

// 分组后的股东分类规则类型（API返回的新格式）
export interface ShareholderClassificationRuleGroup {
  priority: number;        // 优先级 1-20
  type: string;           // 股东类型名称
  matchField: string;     // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  rules: string[];        // 该优先级和类型下的所有匹配规则
}

// 股东分类匹配结果类型
export interface ClassificationResult {
  shareholderType: string; // 匹配到的股东类型
  matchedRule?: {
    priority: number;
    type: string;
    rule: string;
  };
}

// 股东分类缓存数据类型（原始格式）
export interface ClassificationCache {
  rules: string;           // 加密的规则数据
  lastUpdatedAt: string;   // 最后更新时间（明文）
  cachedAt: number;        // 缓存时间戳
}

// 前端缓存数据结构（分组后的规则数据）
export interface ClassificationCacheData {
  rules: ShareholderClassificationRuleGroup[];  // 分组后的规则数据
  lastUpdatedAt: string;                        // 最后更新时间
  cachedAt: number;                            // 缓存时间戳
}

// 股东分类规则输入类型（用于批量上传）
export interface ShareholderClassificationRuleInput {
  priority: number;    // 优先级 1-20
  type: string;       // 股东类型
  rule: string;       // 匹配规则
}

// 独立股东详情分析相关类型定义
export interface SingleShareholderTrendItem {
	/** 登记日期 */
	registerDate: string;
	/** 持股数量（股） */
	numberOfShares: string;
	/** 持股比例（%） */
	shareholdingRatio: string;
	/** 总股本（股） */
	totalShares: string;
}

export interface SingleShareholderAnalysisData {
		/** 证券账户名称 */
		securitiesAccountName: string;
		/** 股东ID */
		shareholderId: string;
		/** 统一账户号码 */
		unifiedAccountNumber: string;
		/** 联系电话 */
		contactNumber: string;
		/** 股东类型 */
		shareholderType: string;
		/** 股东类别 */
		shareholderCategory: string;
		/** 联系地址 */
		contactAddress: string;
		/** 现金账户 */
		cashAccount: string;
		/** 现金账户持股数 */
		sharesInCashAccount: string;
		/** 信用账户 */
		marginAccount: string;
		/** 信用账户持股数 */
		sharesInMarginAccount: string;
		/** 锁定股份 */
		lockedUpShares: string;
		/** 冻结股份 */
		frozenShares: string;
		/** 总股本 */
		totalShares: string;
		/** 排名 */
		ranking: string;
		/** 当前持股数 */
		currentShares: string;
		/** 当前持股比例 */
		currentRatio: string;
		/** 持股变化 */
		sharesChange: string;
		/** 比例变化 */
		ratioChange: string;
		/** 上期排名 - 2025-08-07 hayden 新增排名变动分析字段 */
		prevRanking: string;
		/** 排名变化 - 2025-08-07 hayden 新增排名变动分析字段 */
		rankChange: string;
		/** 排名描述 - 2025-08-07 hayden 新增排名变动分析字段 */
		rankChangeDescription: string;
    /** 期数 */
    registerDate: string;
		/** 趋势数据 */
		trendData: SingleShareholderTrendItem[];
	}