/**
 * UI 工具函数
 * @file ui-utils.ts
 * @description 提供股东模块UI相关的工具函数，包括数字格式化、日期格式化等
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 */

/**
 * 数字格式化选项接口
 * @interface FormatNumberOptions
 */
export interface FormatNumberOptions {
	/** 小数位数，默认为0 */
	decimals?: number;
	/** 是否显示为百分比，默认为false */
	percentage?: boolean;
	/** 千分位分隔符，默认为',' */
	separator?: string;
	/** 小数点符号，默认为'.' */
	decimalPoint?: string;
}

/**
 * 格式化数字为千分位格式
 * @param value 数字值
 * @param options 格式化选项
 * @returns 格式化后的字符串
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * formatNumber(1234.56) // "1,234"
 * formatNumber(1234.56, { decimals: 2 }) // "1,234.56"
 * formatNumber(0.1234, { percentage: true, decimals: 2 }) // "12.34%"
 * ```
 */
export function formatNumber(
	value: string | number | undefined | null,
	options: FormatNumberOptions = {}
): string {
	try {
		// 处理undefined、null或空值
		if (value === undefined || value === null || value === '') {
			return '-';
		}

		const {
			decimals = 0,
			percentage = false,
			separator = ',',
			decimalPoint = '.'
		} = options;

		// 转换为数字
		const num = typeof value === 'string' ? Number.parseFloat(value) : value;

		if (Number.isNaN(num)) {
			return String(value);
		}

		// 如果是百分比，先乘以100
		const displayNum = percentage ? num * 100 : num;

		// 使用内置的本地化格式化
		const formatted = displayNum.toLocaleString('zh-CN', {
			minimumFractionDigits: decimals,
			maximumFractionDigits: decimals
		});

		// 如果是百分比，添加%符号
		return percentage ? `${formatted}%` : formatted;
	} catch (error) {
		console.error('格式化数字失败:', error);
		return String(value || '-');
	}
}

/**
 * 格式化百分比数字
 * @param value 数字值
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的百分比字符串
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * formatPercentage(0.1234) // "12.34%"
 * formatPercentage(0.1234, 4) // "12.3400%"
 * ```
 */
export function formatPercentage(
	value: string | number | undefined | null,
	decimals = 2
): string {
	return formatNumber(value, { percentage: true, decimals });
}

/**
 * 格式化日期字符串
 * @param dateString 日期字符串
 * @param format 格式化格式，默认为'YYYY-MM-DD'
 * @returns 格式化后的日期
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * formatDate('2024-06-19T10:30:00Z') // "2024-06-19"
 * formatDate('2024-06-19') // "2024-06-19"
 * ```
 */
export function formatDate(dateString: string, format = 'YYYY-MM-DD'): string {
	try {
		if (!dateString) {
			return "";
		}

		// 处理ISO日期格式
		if (dateString.includes('T')) {
			return dateString.split('T')[0];
		}

		// 处理其他日期格式
		const date = new Date(dateString);
		if (Number.isNaN(date.getTime())) {
			return dateString; // 如果无法解析，返回原字符串
		}

		// 简单的日期格式化
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');

		switch (format) {
			case 'YYYY-MM-DD':
				return `${year}-${month}-${day}`;
			case 'YYYY/MM/DD':
				return `${year}/${month}/${day}`;
			case 'MM/DD/YYYY':
				return `${month}/${day}/${year}`;
			default:
				return `${year}-${month}-${day}`;
		}
	} catch (error) {
		console.error('格式化日期失败:', error);
		return dateString;
	}
}

/**
 * 截断文本并添加省略号
 * @param text 要截断的文本
 * @param maxLength 最大长度
 * @param suffix 后缀，默认为'...'
 * @returns 截断后的文本
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * truncateText('这是一个很长的文本', 5) // "这是一个很..."
 * truncateText('短文本', 10) // "短文本"
 * ```
 */
export function truncateText(text: string, maxLength: number, suffix = '...'): string {
	if (!text || text.length <= maxLength) {
		return text;
	}
	return text.slice(0, maxLength) + suffix;
}

/**
 * 生成唯一ID
 * @param prefix 前缀，默认为空字符串
 * @returns 唯一ID字符串
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * generateId() // "1671456789123-abc123"
 * generateId('user') // "user-1671456789123-abc123"
 * ```
 */
export function generateId(prefix = ''): string {
	const timestamp = Date.now();
	const random = Math.random().toString(36).substring(2, 8);
	return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;
}

/**
 * 检查值是否为空
 * @param value 要检查的值
 * @returns 是否为空
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * isEmpty(null) // true
 * isEmpty('') // true
 * isEmpty('  ') // true
 * isEmpty('hello') // false
 * ```
 */
export function isEmpty(value: any): boolean {
	if (value === null || value === undefined) {
		return true;
	}
	if (typeof value === 'string') {
		return value.trim() === '';
	}
	if (Array.isArray(value)) {
		return value.length === 0;
	}
	if (typeof value === 'object') {
		return Object.keys(value).length === 0;
	}
	return false;
}

/**
 * 安全地获取对象属性值
 * @param obj 对象
 * @param path 属性路径，如'a.b.c'
 * @param defaultValue 默认值
 * @returns 属性值或默认值
 * <AUTHOR>
 * @created 2025-06-19 12:06:54
 * 
 * @example
 * ```typescript
 * const obj = { a: { b: { c: 'value' } } };
 * safeGet(obj, 'a.b.c') // 'value'
 * safeGet(obj, 'a.b.d', 'default') // 'default'
 * ```
 */
export function safeGet(obj: any, path: string, defaultValue: any = undefined): any {
	try {
		const keys = path.split('.');
		let result = obj;
		
		for (const key of keys) {
			if (result === null || result === undefined) {
				return defaultValue;
			}
			result = result[key];
		}
		
		return result !== undefined ? result : defaultValue;
	} catch (error) {
		return defaultValue;
	}
}
