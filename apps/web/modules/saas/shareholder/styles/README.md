# 股东名册模块样式文件组织

本目录包含股东名册模块的所有样式文件，采用模块化的组织方式以提升可维护性。

## 📁 目录结构

```
/styles/
├── components/          # 组件特定样式
│   ├── recharts-fixes.css    # 图表组件修复样式
│   └── index.ts              # 组件样式导入索引
├── themes/              # 主题相关样式（预留）
├── utilities/           # 工具类样式（预留）
├── index.ts             # 总样式导入索引
└── README.md            # 本文档
```

## 🎯 设计原则

### 1. 模块化组织
- 每个组件的样式文件独立管理
- 避免样式冲突和全局污染
- 便于定位和维护

### 2. 统一命名规范
- 文件名使用 `kebab-case` 格式
- 样式类名遵循 BEM 方法论
- CSS 变量优先于硬编码值

### 3. 文档完整性
- 每个样式文件包含功能说明
- 修改记录和作者信息
- 使用场景和注意事项

## 📋 使用指南

### 导入样式文件

```typescript
// 导入所有样式（推荐）
import '@saas/shareholder/styles';

// 导入特定组件样式
import '@saas/shareholder/styles/components/recharts-fixes.css';

// 导入组件样式包
import '@saas/shareholder/styles/components';
```

### 新增样式文件

1. **创建样式文件**
   ```bash
   # 在 components/ 目录下创建新的 CSS 文件
   touch styles/components/new-component.css
   ```

2. **添加样式内容**
   ```css
   /**
    * 新组件样式
    * @description 组件功能描述
    * <AUTHOR>
    * @date 创建日期
    */
   
   /* 样式规则 */
   .new-component {
     /* 使用 CSS 变量 */
     color: var(--foreground);
   }
   ```

3. **更新导入索引**
   ```typescript
   // 在 components/index.ts 中添加导入
   import './new-component.css';
   ```

### 最佳实践

✅ **推荐做法**
- 使用 CSS 变量而非硬编码颜色值
- 添加详细的文档注释
- 遵循现有的命名规范
- 测试样式在不同主题下的表现

❌ **避免做法**
- 使用全局样式选择器
- 硬编码颜色和尺寸值
- 缺少文档说明
- 影响其他组件的样式

## 🔧 维护指南

### 样式更新流程

1. **分析需求** - 确认样式修改的范围和影响
2. **修改样式** - 更新对应的 CSS 文件
3. **更新文档** - 修改注释和版本信息
4. **测试验证** - 确保样式在各种场景下正常工作
5. **代码审查** - 提交 PR 进行代码审查

### 性能考虑

- **CSS 文件大小** - 避免冗余样式规则
- **加载顺序** - 关键样式优先加载
- **缓存策略** - 利用浏览器缓存机制
- **压缩优化** - 生产环境自动压缩

## 📖 参考资源

- [CSS 变量文档](../../../ui/README.md)
- [主题系统说明](../../../../tooling/tailwind/README.md)
- [组件开发规范](../components/README.md)

## 🚀 未来规划

- [ ] 主题样式目录结构
- [ ] 工具类样式集合
- [ ] 样式自动化测试
- [ ] 性能监控和优化

---

如有问题或建议，请联系开发团队。
