/**
 * Recharts 焦点样式修复
 * @description 移除饼图点击后出现的黑色焦点描边，避免全局样式污染
 * <AUTHOR>
 * @date 2025-08-12
 */

/* 基础焦点样式重置 */
.recharts-wrapper:focus,
.recharts-surface:focus,
.recharts-sector:focus,
.recharts-pie-sector:focus {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

.recharts-layer:focus,
.recharts-layer:focus-visible,
.recharts-pie:focus,
.recharts-pie:focus-visible,
.recharts-pie path:focus,
.recharts-pie path:focus-visible,
.recharts-sector path:focus,
.recharts-sector path:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

/* 响应式容器焦点样式重置 */
.recharts-responsive-container,
.recharts-responsive-container:focus,
.recharts-responsive-container:focus-visible,
.recharts-responsive-container:focus-within,
.recharts-wrapper:focus-visible,
.recharts-wrapper:focus-within,
.recharts-surface:focus-visible,
.recharts-surface:focus-within,
svg.recharts-surface:focus,
svg.recharts-surface:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

.recharts-wrapper svg:focus,
.recharts-wrapper svg:focus-visible,
.recharts-wrapper svg g:focus,
.recharts-wrapper svg g:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}
