"use client";

import { useCookieConsent } from "@shared/hooks/cookie-consent";
import { Button } from "@ui/components/button";
import { CookieIcon } from "lucide-react";
import { useEffect, useState } from "react";

export function ConsentBanner() {
	const { userHasConsented, allowCookies, declineCookies } =
		useCookieConsent();
	const [mounted, setMounted] = useState(false);
	useEffect(() => {
		setMounted(true);
	}, []);

	if (!mounted) {
		return null;
	}

	if (userHasConsented) {
		return null;
	}

	/* 修改块开始: 暂时隐藏 Cookie 同意弹窗
	 * 修改范围: 通过添加额外条件控制弹窗显示
	 * 修改时间: 2025-05-20
	 * 恢复方法: 删除 SHOW_COOKIE_BANNER 变量和对应条件判断
	 */
	const SHOW_COOKIE_BANNER = false; // 设置为 true 时重新显示弹窗
	if (!SHOW_COOKIE_BANNER) {
		return null;
	}
	/* 修改块结束: 暂时隐藏 Cookie 同意弹窗 */

	return (
		<div className="fixed left-4 bottom-4 max-w-md z-50">
			<div className="flex gap-4 rounded-2xl border bg-card p-4 text-card-foreground shadow-xl">
				<CookieIcon className="block size-6 shrink-0 text-5xl text-primary/60 mt-1" />
				<div>
					<p className="text-sm leading-normal">
						本网站尚未使用 Cookie，请放心使用。
					</p>
					<div className="mt-4 flex gap-2">
						<Button
							variant="light"
							className="flex-1"
							onClick={() => declineCookies()}
						>
							拒绝
						</Button>
						<Button
							className="flex-1"
							onClick={() => allowCookies()}
						>
							允许
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
