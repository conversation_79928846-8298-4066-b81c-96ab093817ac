"use client";

import * as DialogPrimitive from "@radix-ui/react-dialog";
import * as React from "react";

import { cn } from "@ui/lib";
import { XIcon } from "lucide-react";
import { Skeleton } from "./skeleton";

/**
 * LargeDialog 组件 - 投资人详情容器弹窗组件，专门用于显示 API 返回的 HTML 内容
 * <AUTHOR>
 * @created 2025-07-03 16:08:10 - 创建大容器弹窗组件，支持全屏显示和响应式布局
 */

const LargeDialog = DialogPrimitive.Root;

const LargeDialogTrigger = DialogPrimitive.Trigger;

const LargeDialogPortal = ({ ...props }: DialogPrimitive.DialogPortalProps) => (
  <DialogPrimitive.Portal {...props} />
);
LargeDialogPortal.displayName = DialogPrimitive.Portal.displayName;

const LargeDialogOverlay = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-background/80 backdrop-blur-xs data-[state=closed]:animate-out data-[state=open]:animate-in",
      className,
    )}
    {...props}
  />
));
LargeDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const LargeDialogContent = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <LargeDialogPortal>
    <LargeDialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-[95vw] max-w-[1200px] h-[90vh] max-h-[900px] translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=closed]:animate-out data-[state=open]:animate-in sm:rounded-lg",
        className,
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-0 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground z-10 cursor-pointer">
        <XIcon className="size-6" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </LargeDialogPortal>
));
LargeDialogContent.displayName = DialogPrimitive.Content.displayName;

const LargeDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className,
    )}
    {...props}
  />
);
LargeDialogHeader.displayName = "LargeDialogHeader";

const LargeDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className,
    )}
    {...props}
  />
);
LargeDialogFooter.displayName = "LargeDialogFooter";

const LargeDialogTitle = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-2xl font-bold text-primary mb-4 leading-snug tracking-tight",
      className,
    )}
    {...props}
  />
));
LargeDialogTitle.displayName = DialogPrimitive.Title.displayName;

const LargeDialogDescription = React.forwardRef<
  React.ComponentRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-muted-foreground text-sm", className)}
    {...props}
  />
));
LargeDialogDescription.displayName = DialogPrimitive.Description.displayName;

/**
 * LargeDialogBody 组件 - 弹窗主体内容区域，支持滚动和响应式布局
 * <AUTHOR>
 * @created 2025-07-03 16:08:10 - 创建弹窗主体内容区域
 * @modified 2025-07-03 17:41:16 - 添加细蓝色自定义滚动条样式，替换原生滚动条
 */
const LargeDialogBody = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex-1 overflow-y-auto overflow-x-hidden pr-2 -mr-2",
      // 自定义滚动条样式类
      "large-dialog-scrollbar",
      className,
    )}
    {...props}
  >
    {children}
    <style jsx>{`
      /* 自定义细蓝色滚动条样式 */
      .large-dialog-scrollbar::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      .large-dialog-scrollbar::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 3px;
      }

      .large-dialog-scrollbar::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 3px;
        opacity: 0.7;
        transition: opacity 0.2s ease;
      }

      .large-dialog-scrollbar::-webkit-scrollbar-thumb:hover {
        opacity: 1;
        background: #9ca3af;
      }

      /* Firefox 滚动条样式 */
      .large-dialog-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #d1d5db transparent;
      }

      /* 暗色主题下的滚动条样式 */
      :global(.dark) .large-dialog-scrollbar::-webkit-scrollbar-thumb {
        background: #4b5563;
        opacity: 0.8;
      }

      :global(.dark) .large-dialog-scrollbar::-webkit-scrollbar-thumb:hover {
        opacity: 1;
        background: #6b7280;
      }

      :global(.dark) .large-dialog-scrollbar {
        scrollbar-color: #4b5563 transparent;
      }
    `}</style>
  </div>
));
LargeDialogBody.displayName = "LargeDialogBody";

/**
 * LargeDialogLoadingSkeleton 组件 - 加载状态骨架屏
 * <AUTHOR>
 * @created 2025-07-03 16:08:10 - 创建加载状态骨架屏组件
 */
const LargeDialogLoadingSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-8 w-3/4" />
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-5/6" />
    <Skeleton className="h-32 w-full" />
    <div className="grid grid-cols-2 gap-4">
      <Skeleton className="h-24 w-full" />
      <Skeleton className="h-24 w-full" />
    </div>
    <Skeleton className="h-4 w-2/3" />
    <Skeleton className="h-4 w-4/5" />
  </div>
);

/**
 * LargeDialogEmptyState 组件 - 空状态组件
 * <AUTHOR>
 * @created 2025-07-03 16:08:10 - 创建空状态组件
 */
const LargeDialogEmptyState = ({ 
  message = "暂无数据",
  className,
  ...props 
}: React.HTMLAttributes<HTMLDivElement> & { message?: string }) => (
  <div
    className={cn(
      "flex flex-col items-center justify-center py-12 text-center",
      className,
    )}
    {...props}
  >
    <div className="text-gray-400 text-lg mb-2">{message}</div>
    <div className="text-gray-500 text-sm">请稍后重试或联系管理员</div>
  </div>
);

/**
 * LargeDialogErrorState 组件 - 错误状态组件
 * <AUTHOR>
 * @created 2025-07-03 16:08:10 - 创建错误状态组件
 */
const LargeDialogErrorState = ({ 
  error,
  className,
  ...props 
}: React.HTMLAttributes<HTMLDivElement> & { error: string }) => (
  <div
    className={cn(
      "flex flex-col items-center justify-center py-12 text-center",
      className,
    )}
    {...props}
  >
    <div className="text-red-500 text-lg mb-2">加载失败</div>
    <div className="text-red-400 text-sm">{error}</div>
  </div>
);

export {
  LargeDialog,
  LargeDialogContent,
  LargeDialogDescription,
  LargeDialogFooter,
  LargeDialogHeader,
  LargeDialogTitle,
  LargeDialogTrigger,
  LargeDialogBody,
  LargeDialogLoadingSkeleton,
  LargeDialogEmptyState,
  LargeDialogErrorState,
};
