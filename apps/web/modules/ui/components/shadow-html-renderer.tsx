"use client";

import React, { useEffect, useRef } from "react";
import root from "react-shadow";
import { config } from "@repo/config";

/**
 * ShadowHtmlRenderer 组件属性接口
 * <AUTHOR>
 * @created 2025-07-03 15:01:58
 */
interface ShadowHtmlRendererProps {
  /** 要渲染的 HTML 字符串 */
  html: string;
  /** 额外的 CSS 类名（应用到宿主元素） */
  className?: string;
  /** 是否注入基础的 Tailwind 样式 */
  injectTailwindStyles?: boolean;
}


/**
 * 获取项目主题配置的 CSS 变量值
 * 使用 config.ui 配置，从父文档的计算样式中读取对应主题的变量值
 * <AUTHOR>
 * @created 2025-07-03 16:34:07
 * @returns 包含当前主题变量的对象
 */
function getProjectThemeVariables(): Record<string, string> {
  if (typeof document === 'undefined') {
    return {};
  }

  // 使用 config.ui 配置获取支持的主题
  const supportedThemes = config.ui.enabledThemes; // ["light", "dark"]
  const defaultTheme = config.ui.defaultTheme; // "light"

  // 检测当前主题
  const isDarkMode = document.documentElement.classList.contains('dark');
  const currentTheme = isDarkMode ? 'dark' : 'light';

  // 确保当前主题在支持的主题列表中，如果不支持则使用默认主题
  const activeTheme = supportedThemes.includes(currentTheme as any) ? currentTheme : defaultTheme;

  const rootStyles = getComputedStyle(document.documentElement);
  const variables = [
    '--border', '--input', '--ring', '--background', '--foreground',
    '--primary', '--primary-foreground', '--secondary', '--secondary-foreground',
    '--destructive', '--destructive-foreground', '--success', '--success-foreground',
    '--muted', '--muted-foreground', '--accent', '--accent-foreground',
    '--popover', '--popover-foreground', '--card', '--card-foreground',
    '--highlight', '--highlight-foreground', '--radius'
  ];

  const themeVars: Record<string, string> = {};
  for (const variable of variables) {
    const value = rootStyles.getPropertyValue(variable).trim();
    if (value) {
      themeVars[variable] = value;
    }
  }

  return themeVars;
}

/**
 * 生成 Tailwind 基础样式字符串，包含主题变量和基础样式
 * 结合 BASIC_TAILWIND_STYLES 的功能，支持暗色主题适配
 * <AUTHOR>
 * @created 2025-07-03 15:23:29
 * @modified 2025-07-03 16:34:07 - 重构为使用 config.ui 配置
 * @returns 包含主题变量和基础样式的 CSS 字符串
 */
function generateTailwindBaseStyles(): string {
  const themeVars = getProjectThemeVariables();

  // 生成 CSS 变量定义
  const cssVariables = Object.entries(themeVars)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n');

  // 获取支持的主题列表，用于生成对应的 CSS 规则
  const enabledThemes = config.ui.enabledThemes; // ["light", "dark"]

  return `
    /* CSS 变量定义 - 使用 config.ui 配置，与 Document.tsx 保持一致 */
    /* 支持的主题: ${enabledThemes.join(', ')} */
    /* 默认主题: ${config.ui.defaultTheme} */

    :host {
${cssVariables}
      /* 基础样式 */
      background-color: var(--background);
      color: var(--foreground);
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      line-height: 1.5;
    }

    /* 继承父文档的主题变量，支持主题切换 */
    :host(.dark) {
${cssVariables}
    }

    /* 根据主题模式动态应用样式 */
    .theme-dark {
${cssVariables}
      background-color: var(--background) !important;
      color: var(--foreground) !important;
    }

    /* Tailwind CSS 基础样式重置 */
    *, ::before, ::after {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
      border-color: var(--border);
    }

    /* 基础元素样式 */
    body {
      margin: 0;
      line-height: inherit;
      background-color: var(--background);
      color: var(--foreground);
    }

    /* 标题样式 - 支持暗色主题 */
    h1, h2, h3, h4, h5, h6 {
      color: var(--foreground);
      font-weight: 600;
    }
    h1 { font-size: 2.25rem; font-weight: 700; line-height: 2.5rem; margin-bottom: 1rem; margin-top: 1.5rem; }
    h1:first-child { margin-top: 0; }
    h2 { font-size: 1.875rem; font-weight: 600; line-height: 2.25rem; margin-bottom: 0.75rem; margin-top: 1.25rem; }
    h2:first-child { margin-top: 0; }
    h3 { font-size: 1.5rem; font-weight: 500; line-height: 2rem; margin-bottom: 0.5rem; margin-top: 1rem; }
    h3:first-child { margin-top: 0; }
    h4 { font-size: 1.25rem; font-weight: 500; line-height: 1.75rem; margin-bottom: 0.5rem; margin-top: 0.75rem; }
    h4:first-child { margin-top: 0; }
    h5 { font-size: 1.125rem; font-weight: 500; line-height: 1.75rem; margin-bottom: 0.5rem; margin-top: 0.75rem; }
    h5:first-child { margin-top: 0; }
    h6 { font-size: 1rem; font-weight: 500; line-height: 1.5rem; margin-bottom: 0.5rem; margin-top: 0.75rem; }
    h6:first-child { margin-top: 0; }

    /* 段落和文本 - 支持暗色主题 */
    p {
      margin-bottom: 0.75rem;
      line-height: 1.625;
      color: var(--foreground);
    }
    strong {
      font-weight: 700;
      color: var(--foreground);
    }
    em {
      font-style: italic;
      color: var(--foreground);
    }

    /* 列表 - 支持暗色主题 */
    ul, ol {
      margin-bottom: 0.75rem;
      padding-left: 1.5rem;
      color: var(--foreground);
    }
    ul { list-style-type: disc; }
    ol { list-style-type: decimal; }
    li {
      margin-bottom: 0.25rem;
      color: var(--foreground);
    }

    /* 链接 - 支持暗色主题 */
    a {
      color: var(--primary);
      text-decoration: underline;
    }
    a:hover {
      opacity: 0.8;
    }

    /* 引用 - 支持暗色主题 */
    blockquote {
      border-left: 4px solid var(--border);
      background-color: var(--muted);
      color: var(--muted-foreground);
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0.375rem;
    }

    /* 代码 - 支持暗色主题 */
    code {
      background-color: var(--muted);
      color: var(--muted-foreground);
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 0.875rem;
    }

    pre {
      background-color: var(--muted);
      color: var(--muted-foreground);
      padding: 1rem;
      border-radius: 0.375rem;
      overflow-x: auto;
      margin: 1rem 0;
    }

    pre code {
      background-color: transparent;
      padding: 0;
    }

    /* 图片 - 支持暗色主题 */
    img {
      max-width: 100%;
      border-radius: 0.375rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      margin: 1rem 0;
    }

    /* 分隔线 - 支持暗色主题 */
    hr {
      border: none;
      border-top: 1px solid var(--border);
      margin: 1.5rem 0;
    }

    /* 表格 - 支持暗色主题 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
      background-color: var(--card);
      color: var(--card-foreground);
      border-radius: 0.375rem;
      overflow: hidden;
    }

    th, td {
      padding: 0.75rem 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
      color: var(--card-foreground);
    }

    th {
      background-color: var(--muted);
      color: var(--muted-foreground);
      font-weight: 600;
      font-size: 0.875rem;
    }

    tr:last-child td {
      border-bottom: none;
    }

    /* Tailwind 主题工具类 - 让 Shadow DOM 内部可以像 Document.tsx 一样使用主题类 */
    .bg-background { background-color: var(--background) !important; }
    .bg-card { background-color: var(--card) !important; }
    .bg-primary { background-color: var(--primary) !important; }
    .bg-secondary { background-color: var(--secondary) !important; }
    .bg-muted { background-color: var(--muted) !important; }
    .bg-accent { background-color: var(--accent) !important; }
    .bg-destructive { background-color: var(--destructive) !important; }
    .bg-success { background-color: var(--success) !important; }

    .text-foreground { color: var(--foreground) !important; }
    .text-card-foreground { color: var(--card-foreground) !important; }
    .text-primary { color: var(--primary) !important; }
    .text-primary-foreground { color: var(--primary-foreground) !important; }
    .text-secondary-foreground { color: var(--secondary-foreground) !important; }
    .text-muted-foreground { color: var(--muted-foreground) !important; }
    .text-destructive { color: var(--destructive) !important; }
    .text-success { color: var(--success) !important; }

    .border-border { border-color: var(--border) !important; }

    /* 暗色主题强制样式 - 确保所有元素都正确显示 */
    .theme-dark * {
      color: var(--foreground) !important;
    }

    /* 强制覆盖所有可能的背景色 - 解决白色背景问题 */
    .theme-dark *:not(img):not(svg):not(canvas) {
      background-color: transparent !important;
      background: transparent !important;
    }

    /* 为容器元素提供正确的背景色 */
    .theme-dark div[style*="background"],
    .theme-dark div[style*="background-color"],
    .theme-dark section[style*="background"],
    .theme-dark section[style*="background-color"],
    .theme-dark article[style*="background"],
    .theme-dark article[style*="background-color"] {
      background-color: var(--card) !important;
      background: var(--card) !important;
    }

    /* 强制覆盖内联样式中的白色背景 */
    .theme-dark [style*="background-color: white"],
    .theme-dark [style*="background-color: #fff"],
    .theme-dark [style*="background-color: #ffffff"],
    .theme-dark [style*="background-color:white"],
    .theme-dark [style*="background-color:#fff"],
    .theme-dark [style*="background-color:#ffffff"],
    .theme-dark [style*="background: white"],
    .theme-dark [style*="background: #fff"],
    .theme-dark [style*="background: #ffffff"],
    .theme-dark [style*="background:white"],
    .theme-dark [style*="background:#fff"],
    .theme-dark [style*="background:#ffffff"] {
      background-color: var(--card) !important;
      background: var(--card) !important;
    }

    /* 强制覆盖浅色背景 */
    .theme-dark [style*="background-color: #f"],
    .theme-dark [style*="background-color:#f"],
    .theme-dark [style*="background: #f"],
    .theme-dark [style*="background:#f"] {
      background-color: var(--card) !important;
      background: var(--card) !important;
    }

    .theme-dark h1, .theme-dark h2, .theme-dark h3,
    .theme-dark h4, .theme-dark h5, .theme-dark h6 {
      color: var(--foreground) !important;
      background-color: transparent !important;
    }

    .theme-dark p, .theme-dark li, .theme-dark span, .theme-dark div {
      color: var(--foreground) !important;
    }

    /* 确保根容器有正确的背景 */
    .theme-dark {
      background-color: var(--background) !important;
      background: var(--background) !important;
    }

    .theme-dark table {
      background-color: var(--card) !important;
    }

    .theme-dark th {
      background-color: var(--muted) !important;
      color: var(--muted-foreground) !important;
    }

    .theme-dark td {
      color: var(--card-foreground) !important;
      background-color: transparent !important;
    }

    .theme-dark blockquote {
      background-color: var(--muted) !important;
      color: var(--muted-foreground) !important;
      border-left-color: var(--border) !important;
    }

    .theme-dark code {
      background-color: var(--muted) !important;
      color: var(--muted-foreground) !important;
    }

    .theme-dark pre {
      background-color: var(--muted) !important;
      color: var(--muted-foreground) !important;
    }

    /* 终极解决方案 - 使用 CSS 自定义属性覆盖 */
    .theme-dark {
      --white: var(--background);
      --gray-50: var(--muted);
      --gray-100: var(--muted);
      --gray-200: var(--border);
    }
  `;
}



/**
 * ShadowHtmlRenderer 组件
 * 使用 Shadow DOM 安全地渲染动态 HTML 内容，完全隔离样式冲突
 * 
 * 功能特性：
 * - 使用 Shadow DOM 完全隔离样式
 * - 注入基础 Tailwind 样式保持一致性
 * - 保持原始 HTML 布局不变
 * - 支持自定义样式注入
 * - 完全兼容 TypeScript
 * 
 * <AUTHOR>
 * @created 2025-07-03 15:01:58
 * @example
 * ```tsx
 * <ShadowHtmlRenderer 
 *   html="<div style='display: flex;'><div>左侧</div><div>右侧</div></div>" 
 *   className="w-full"
 *   injectTailwindStyles={true}
 * />
 * ```
 */
export function ShadowHtmlRenderer({
  html,
  className,
  injectTailwindStyles = true,
}: ShadowHtmlRendererProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  /**
   * 处理暗色主题下的内联样式
   * 移除或修改可能导致显示问题的内联样式
   * <AUTHOR>
   * @date 2025-07-03 17:30:59
   */
  const processInlineStyles = () => {
    if (!contentRef.current) {
      return;
    }

    // 检测当前是否为暗色主题
    const isDarkMode = document.documentElement.classList.contains('dark');
    if (!isDarkMode) {
      return;
    }

    // 获取所有带有 style 属性的元素
    const elementsWithStyle = contentRef.current.querySelectorAll('[style]');

    elementsWithStyle.forEach((element) => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      // 移除白色背景相关样式
      if (style.backgroundColor) {
        const bgColor = style.backgroundColor.toLowerCase();
        if (bgColor.includes('white') ||
            bgColor.includes('#fff') ||
            bgColor.includes('#ffffff') ||
            bgColor.includes('rgb(255, 255, 255)') ||
            bgColor.includes('rgba(255, 255, 255') ||
            bgColor.startsWith('#f')) {
          style.removeProperty('background-color');
        }
      }

      // 移除背景相关样式
      if (style.background) {
        const bg = style.background.toLowerCase();
        if (bg.includes('white') ||
            bg.includes('#fff') ||
            bg.includes('#ffffff') ||
            bg.includes('rgb(255, 255, 255)') ||
            bg.includes('rgba(255, 255, 255') ||
            bg.startsWith('#f')) {
          style.removeProperty('background');
        }
      }

      // 移除可能的浅色背景图片
      if (style.backgroundImage?.includes('linear-gradient')) {
        const bgImage = style.backgroundImage.toLowerCase();
        if (bgImage.includes('white') || bgImage.includes('#fff') || bgImage.includes('#f')) {
          style.removeProperty('background-image');
        }
      }
    });
  };

  // 监听主题变化和内容变化，处理内联样式
  useEffect(() => {
    processInlineStyles();

    // 监听主题变化
    const observer = new MutationObserver(() => {
      processInlineStyles();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, [html]);

  // 组合样式
  const combinedStyles = [
    injectTailwindStyles ? generateTailwindBaseStyles() : ""
  ].filter(Boolean).join("\n");

  return (
    <root.div className={className} mode="open">
      {combinedStyles && (
        <style>
          {combinedStyles}
        </style>
      )}
      <div
        ref={contentRef}
        // biome-ignore lint/security/noDangerouslySetInnerHtml: 这是一个安全的使用场景，因为：
        dangerouslySetInnerHTML={{ __html: html }}
      />
    </root.div>
  );
}

export default ShadowHtmlRenderer;
