"use client";

import { useTheme } from "next-themes";
import { Toaster as Sonner } from "sonner";

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
			<Sonner
				theme={theme as ToasterProps["theme"]}
				className="toaster group"
				closeButton={true} // 确保开启关闭按钮功能
				//visibleToasts={1} // 只显示一个单独的toast，新的会替换旧的
				toastOptions={{
					classNames: {
						toast: "group toast !rounded-lg !font-sans group-[.toaster]:bg-card group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-md",
						description: "group-[.toast]:text-muted-foreground",
						actionButton:
							"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
						cancelButton:
							"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
						success: "!text-success",
						error: "!text-destructive",
						// 无边框关闭按钮样式
						closeButton:
							"absolute top-2 right-2 p-1.5 rounded-full group-[.toast]:bg-transparent group-[.toast]:text-muted-foreground group-[.toast]:hover:bg-muted/30 group-[.toast]:hover:text-foreground group-[.toast]:focus:outline-none group-[.toast]:focus:ring-0 transition-all duration-200 ease-in-out",
					},
					duration: 5000,
				}}
				{...props}
			/>
		);
};

export { Toaster };