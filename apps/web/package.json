{"dependencies": {"@ant-design/icons": "^6.0.0", "@aws-sdk/client-s3": "3.437.0", "@fumadocs/content-collections": "^1.1.8", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.8", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@tanstack/react-query": "^5.66.9", "@tanstack/react-table": "^8.21.2", "@types/recharts": "^2.0.1", "ai": "^4.1.46", "antd": "^5.25.2", "better-auth": "1.1.21", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "1.6.2", "date-fns": "^4.1.0", "date-range-picker": "^0.3.5", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "fumadocs-core": "^15.0.13", "fumadocs-ui": "^15.0.13", "geist": "^1.3.1", "hono": "^4.7.2", "html-react-parser": "^5.2.5", "iconv-lite": "^0.6.3", "jotai": "2.12.1", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lucide-react": "^0.476.0", "mammoth": "^1.9.1", "next": "15.2.3", "next-intl": "3.26.5", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "nuqs": "^2.4.0", "oslo": "^1.2.1", "prettier": "3.4.2", "react": "19.0.0", "react-cropper": "^2.3.3", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "react-shadow": "^20.6.0", "recharts": "^2.15.3", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "ufo": "^1.5.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@content-collections/core": "^0.8.2", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@mdx-js/mdx": "^3.1.0", "@playwright/test": "^1.51.1", "@repo/auth": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@shikijs/rehype": "^3.2.1", "@tailwindcss/postcss": "^4.0.17", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "22.13.13", "@types/nprogress": "^0.2.3", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.21", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.5.3", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.11", "tailwindcss": "4.0.17"}, "name": "@repo/web", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "e2e": "pnpm exec playwright test --ui", "e2e:ci": "pnpm exec playwright install && pnpm exec playwright test", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.0.0"}