import { expect, test } from "@playwright/test";
import { testEmail, testPassword } from "./config";
// import path from "path";

test.describe("user settings functionality", () => {
  test("should navigate to settings and update user profile", async ({ page }) => {
    // 从登录页面开始
    await page.goto("/auth/login");
    
    // 登录
    await page.locator('input[autocomplete="email"]').fill(testEmail);
    await page.locator('input[autocomplete="current-password"]').fill(testPassword);
    await page.locator('button[type="submit"]').click();
    
    // 等待重定向到应用页面
    await page.waitForURL(/\/app(\/.*)?$/, { timeout: 10000 });
    
    // 处理可能出现的cookie同意弹窗
    try {
      const cookieButton = page.locator('button', { hasText: 'Allow' }).first();
      const isVisible = await cookieButton.isVisible({ timeout: 2000 });
      if (isVisible) {
        await cookieButton.click();
      }
    } catch (e) {
      console.log('Cookie banner not found or already closed');
    }
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 点击用户菜单
    try {
      await page.getByRole('button', { name: 'User menu' }).click({ timeout: 5000 });
    } catch (e) {
      console.log('Failed to click user menu by role, trying test ID');
      await page.getByTestId('user-menu-button').click({ force: true });
    }
    
    // 等待下拉菜单出现
    await page.waitForTimeout(1000);
    
    // 点击设置链接 - 根据UserMenu.tsx，设置链接文本是t("app.userMenu.accountSettings")
    try {
      // 尝试通过文本内容查找
      await page.getByRole('menuitem', { name: /settings|设置/i }).click({ timeout: 3000 });
    } catch (e) {
      console.log('Failed to click settings by text, trying direct navigation');
      // 如果点击失败，直接导航到设置页面
      await page.goto('/app/settings/general');
    }
    
    // 等待设置页面加载
    await page.waitForURL(/\/app\/settings\/general/, { timeout: 10000 });
    
    // 验证设置页面已加载
    expect(page.url()).toContain('/app/settings/general');
    
    // 1. 测试更改用户姓名
    // 获取当前姓名以便稍后验证
    const nameInput = page.locator('input[name="name"]').first();
    const currentName = await nameInput.inputValue();
    
    // 生成新的测试姓名 (添加时间戳以确保唯一性)
    const newName = `Test User ${Date.now()}`;
    
    // 清除当前姓名并输入新姓名
    await nameInput.clear();
    await nameInput.fill(newName);
    
    // 点击保存按钮 - 查找ChangeNameForm组件中的提交按钮
    await page.getByRole('button', { name: /save|保存/i }).first().click();
    
    // 等待成功提示出现
    await expect(page.locator('div[role="status"]')).toBeVisible({ timeout: 5000 });
    
    // 验证姓名已更新
    await expect(nameInput).toHaveValue(newName);
    
    // 2. 测试更改用户邮箱
    // 注意：这可能需要邮箱验证，所以我们只测试表单提交，不验证实际更改
    const emailInput = page.locator('input[name="email"]').first();
    const currentEmail = await emailInput.inputValue();
    
    // 生成新的测试邮箱 (添加时间戳以确保唯一性)
    const newEmail = `test${Date.now()}@example.com`;
    
    // 清除当前邮箱并输入新邮箱
    await emailInput.clear();
    await emailInput.fill(newEmail);
    
    // 点击保存按钮 - 查找ChangeEmailForm组件中的提交按钮
    await page.getByRole('button', { name: /save|保存/i }).nth(1).click();
    
    // 等待成功提示出现
    await expect(page.locator('div[role="status"]')).toBeVisible({ timeout: 5000 });
    
    // 3. 测试更改用户头像
    // 点击头像上传区域
    // await page.locator('button[aria-label="Change avatar"]').click();
    
    // // 等待文件选择对话框出现
    // const fileChooserPromise = page.waitForEvent('filechooser');
    // await page.getByText(/upload|上传/i).click();
    // const fileChooser = await fileChooserPromise;
    
    // 上传测试图片
    // 注意：需要准备一个测试图片文件
    // await fileChooser.setFiles(path.join(__dirname, 'test-avatar.png'));
    
    // 等待图片裁剪对话框出现
    // await expect(page.getByText(/crop|裁剪/i)).toBeVisible({ timeout: 5000 });
    
    // // 点击确认裁剪按钮
    // await page.getByRole('button', { name: /save|保存|confirm|确认/i }).click();
    
    // // 等待成功提示出现
    // await expect(page.locator('div[role="status"]')).toBeVisible({ timeout: 5000 });
    
    // 验证设置已保存
    // 刷新页面
    await page.reload();
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证姓名已更新
    await expect(page.locator('input[name="name"]').first()).toHaveValue(newName);
    
    // 恢复原始姓名 (可选，取决于测试需求)
    if (currentName) {
      await page.locator('input[name="name"]').first().clear();
      await page.locator('input[name="name"]').first().fill(currentName);
      await page.getByRole('button', { name: /save|保存/i }).first().click();
      await expect(page.locator('div[role="status"]')).toBeVisible({ timeout: 5000 });
    }
  });
});