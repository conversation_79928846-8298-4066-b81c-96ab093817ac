/**
 * E2E测试通用工具函数
 * <AUTHOR>
 * @created 2025-06-27 10:11:40
 * @description 提供登录、延时查找标签等通用功能，使用cookie注入方式实现快速登录
 */

import { type Page, expect } from '@playwright/test';

/**
 * 测试环境配置
 */
export const TEST_CONFIG = {
  baseUrl: 'http://***************:3000',
  loginUrl: 'http://***************:3000/auth/login?redirectTo=%2Fapp',
  // Cookie注入登录配置
  authCookies: [
    {
      name: 'consent',
      value: 'true',
      domain: '***************',
      path: '/',
    },
    {
      name: 'admin_session',
      value: 'Lt55oo6z9cHKp8rxLywBOwjtJzpUelmP.A%2B3xOB2q1BW8JKG57KNUv3aBX3nB%2FIjPaSs8AolyibQ%3D',
      domain: '***************',
      path: '/',
    },
    {
      name: 'better-auth.session_token',
      value: 'rGjnMhh0VNirsKks7YATN9yj5d3XOsbQ.BDXn7kU%2Bvwf8w1%2BTStV5Dgfe7OXAFYoSIbIHkoA257c%3D',
      domain: '***************',
      path: '/',
    },
  ],
  // 登录表单选择器（备用方案）
  loginSelectors: {
    emailInput: '#«R1ljrlpbl7»-form-item',
    passwordInput: '#«R25jrlpbl7»-form-item > input',
    loginButton: 'body > div.flex.min-h-screen.w-full.py-6 > div > div.container.flex.justify-center > main > div > form > button',
  },
} as const;

/**
 * 使用Cookie注入方式快速登录
 * @param page - Playwright页面对象
 * @param targetUrl - 登录后要跳转的目标URL，默认为应用首页
 * @returns Promise<void>
 */
export async function loginWithCookies(page: Page, targetUrl = '/app') {
  try {
    // 先访问基础URL以建立域名上下文
    await page.goto(TEST_CONFIG.baseUrl);

    // 注入认证Cookie
    await page.context().addCookies(TEST_CONFIG.authCookies);

    // 跳转到目标页面
    const fullTargetUrl = targetUrl.startsWith('http') ? targetUrl : `${TEST_CONFIG.baseUrl}${targetUrl}`;
    await page.goto(fullTargetUrl);

    // 验证登录成功（检查是否在应用页面而不是登录页面）
    await expect(page).not.toHaveURL(/.*\/auth\/login/);

    console.log(`✅ Cookie登录成功，已跳转到: ${fullTargetUrl}`);
  } catch (error) {
    console.error('❌ Cookie登录失败:', error);
    throw new Error(`Cookie登录失败: ${error}`);
  }
}

/**
 * 传统表单登录方式（备用方案）
 * @param page - Playwright页面对象
 * @param email - 邮箱地址
 * @param password - 密码
 * @returns Promise<void>
 */
export async function loginWithForm(
  page: Page,
  email = '<EMAIL>',
  password = 'password123'
) {
  try {
    await page.goto(TEST_CONFIG.loginUrl);

    // 填写登录表单
    await page.locator(TEST_CONFIG.loginSelectors.emailInput).fill(email);
    await page.locator(TEST_CONFIG.loginSelectors.passwordInput).fill(password);

    // 点击登录按钮
    await page.locator(TEST_CONFIG.loginSelectors.loginButton).click();

    // 等待重定向到应用页面
    await expect(page).toHaveURL(/\/app/);

    console.log(`✅ 表单登录成功，邮箱: ${email}`);
  } catch (error) {
    console.error('❌ 表单登录失败:', error);
    throw new Error(`表单登录失败: ${error}`);
  }
}

/**
 * 智能等待元素出现（支持多种选择器策略）
 * @param page - Playwright页面对象
 * @param selector - 元素选择器
 * @param options - 等待选项
 * @returns Promise<void>
 */
export async function waitForElement(
  page: Page,
  selector: string,
  options: {
    timeout?: number;
    state?: 'attached' | 'detached' | 'visible' | 'hidden';
  } = {}
): Promise<void> {
  const { timeout = 10000, state = 'visible' } = options;

  try {
    await page.locator(selector).waitFor({
      timeout,
      state
    });
    console.log(`✅ 元素已找到: ${selector}`);
  } catch (error) {
    console.error(`❌ 等待元素超时: ${selector}`, error);
    throw new Error(`等待元素超时: ${selector}`);
  }
}

/**
 * 智能等待文本内容出现
 * @param page - Playwright页面对象
 * @param text - 要等待的文本内容
 * @param options - 等待选项
 * @returns Promise<void>
 */
export async function waitForText(
  page: Page,
  text: string,
  options: {
    timeout?: number;
    exact?: boolean;
  } = {}
): Promise<void> {
  const { timeout = 10000, exact = false } = options;

  try {
    if (exact) {
      await page.getByText(text, { exact: true }).waitFor({ timeout });
    } else {
      await page.getByText(text).waitFor({ timeout });
    }
    console.log(`✅ 文本已找到: ${text}`);
  } catch (error) {
    console.error(`❌ 等待文本超时: ${text}`, error);
    throw new Error(`等待文本超时: ${text}`);
  }
}

/**
 * 等待API响应完成
 * @param page - Playwright页面对象
 * @param apiPath - API路径匹配模式
 * @param options - 等待选项
 * @returns Promise<Response>
 */
export async function waitForApiResponse(
  page: Page,
  apiPath: string,
  options: {
    timeout?: number;
    status?: number;
    method?: string;
  } = {}
) {
  const { timeout = 30000, status = 200, method } = options;

  try {
    const response = await page.waitForResponse(
      (response) => {
        const urlMatch = response.url().includes(apiPath);
        const statusMatch = response.status() === status;
        const methodMatch = method ? response.request().method() === method : true;
        return urlMatch && statusMatch && methodMatch;
      },
      { timeout }
    );

    console.log(`✅ API响应成功: ${apiPath} (${response.status()})`);
    return response;
  } catch (error) {
    console.error(`❌ 等待API响应超时: ${apiPath}`, error);
    throw new Error(`等待API响应超时: ${apiPath}`);
  }
}

/**
 * 延时函数
 * @param ms - 延时毫秒数
 * @returns Promise<void>
 */
export async function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 安全点击元素（等待元素可见后点击）
 * @param page - Playwright页面对象
 * @param selector - 元素选择器
 * @param options - 点击选项
 * @returns Promise<void>
 */
export async function safeClick(
  page: Page,
  selector: string,
  options: {
    timeout?: number;
    force?: boolean;
    trial?: boolean;
  } = {}
): Promise<void> {
  const { timeout = 10000, force = false, trial = false } = options;

  try {
    // 等待元素可见
    await page.locator(selector).waitFor({ state: 'visible', timeout });

    // 点击元素
    await page.locator(selector).click({ force, trial, timeout });

    console.log(`✅ 安全点击成功: ${selector}`);
  } catch (error) {
    console.error(`❌ 安全点击失败: ${selector}`, error);
    throw new Error(`安全点击失败: ${selector}`);
  }
}

/**
 * 安全填写输入框（等待元素可见后填写）
 * @param page - Playwright页面对象
 * @param selector - 输入框选择器
 * @param value - 要填写的值
 * @param options - 填写选项
 * @returns Promise<void>
 */
export async function safeFill(
  page: Page,
  selector: string,
  value: string,
  options: {
    timeout?: number;
    clear?: boolean;
  } = {}
): Promise<void> {
  const { timeout = 10000, clear = true } = options;

  try {
    // 等待元素可见
    await page.locator(selector).waitFor({ state: 'visible', timeout });

    // 清空并填写
    if (clear) {
      await page.locator(selector).clear();
    }
    await page.locator(selector).fill(value);

    console.log(`✅ 安全填写成功: ${selector} = ${value}`);
  } catch (error) {
    console.error(`❌ 安全填写失败: ${selector}`, error);
    throw new Error(`安全填写失败: ${selector}`);
  }
}

/**
 * 导航到指定页面并验证
 * @param page - Playwright页面对象
 * @param url - 目标URL
 * @param expectedText - 期望在页面中找到的文本（用于验证导航成功）
 * @returns Promise<void>
 */
export async function navigateAndVerify(
  page: Page,
  url: string,
  expectedText?: string
): Promise<void> {
  try {
    const fullUrl = url.startsWith('http') ? url : `${TEST_CONFIG.baseUrl}${url}`;

    // 导航到目标页面
    await page.goto(fullUrl);

    // 验证URL
    await expect(page).toHaveURL(fullUrl);

    // 如果提供了期望文本，则验证页面内容
    if (expectedText) {
      await waitForText(page, expectedText);
    }

    console.log(`✅ 导航成功: ${fullUrl}`);
  } catch (error) {
    console.error(`❌ 导航失败: ${url}`, error);
    throw new Error(`导航失败: ${url}`);
  }
}