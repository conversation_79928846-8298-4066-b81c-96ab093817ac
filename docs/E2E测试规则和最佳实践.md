# E2E测试规则和最佳实践

## 项目概述

本项目是基于Next.js App Router的SaaS应用，使用Playwright进行E2E测试。项目采用monorepo架构，主要包含营销页面和SaaS应用两个部分。

**技术栈**：
- **前端框架**：Next.js 15 (App Router)
- **UI组件库**：Shadcn UI + Radix UI + Ant Design
- **样式框架**：Tailwind CSS
- **测试框架**：Playwright
- **认证系统**：Better Auth
- **状态管理**：Jotai + React Query

## 项目结构分析

### 路由结构
```
apps/web/app/
├── (marketing)/[locale]/     # 营销页面（多语言）
│   ├── (home)/              # 首页
│   ├── blog/                # 博客
│   ├── docs/                # 文档
│   └── legal/               # 法律页面
├── (saas)/                  # SaaS应用
│   ├── auth/                # 认证页面
│   │   ├── login/           # 登录页面
│   │   ├── signup/          # 注册页面
│   │   ├── forgot-password/ # 忘记密码
│   │   └── reset-password/  # 重置密码
│   └── app/                 # 应用内页面
│       ├── (organizations)/[organizationSlug]/  # 组织相关页面
│       │   ├── market/      # 市值管理
│       │   ├── meeting/     # 会议管理
│       │   └── shareholder/ # 股东管理
│       ├── (account)/       # 账户设置
│       ├── choose-plan/     # 选择套餐
│       └── onboarding/      # 用户引导
└── api/                     # API路由
```

### 模块结构
```
apps/web/modules/
├── marketing/               # 营销模块
├── saas/                   # SaaS模块
│   ├── auth/               # 认证模块
│   ├── organizations/      # 组织管理
│   ├── shareholder/        # 股东管理
│   ├── meeting/            # 会议管理
│   ├── market/             # 市值管理
│   ├── settings/           # 设置模块
│   └── shared/             # 共享组件
├── shared/                 # 全局共享
└── ui/                     # UI组件库
```

## E2E测试规则

### 1. 文件组织规则

#### 1.1 测试文件结构
```
apps/web/tests/
├── auth/                   # 认证相关测试
│   ├── login.spec.ts
│   ├── signup.spec.ts
│   └── password-reset.spec.ts
├── shareholder/            # 股东管理测试
│   ├── registry-management.spec.ts
│   ├── data-analysis.spec.ts
│   └── holding-changes.spec.ts
├── market/                 # 市值管理测试
├── meeting/                # 会议管理测试
├── organizations/          # 组织管理测试
├── setup/                  # 测试设置
│   ├── auth.setup.ts       # 认证设置
│   └── data.setup.ts       # 数据设置
└── utils/                  # 测试工具
    ├── page-objects/       # 页面对象
    ├── fixtures/           # 测试数据
    └── helpers/            # 辅助函数
```

#### 1.2 命名规范
- **测试文件**：`{module-name}.spec.ts`
- **页面对象**：`{PageName}Page.ts`
- **设置文件**：`{setup-name}.setup.ts`
- **工具函数**：`{function-name}.helper.ts`

### 2. 元素定位规则

#### 2.1 选择器优先级
1. **data-testid** (最高优先级)
2. **role + name** (推荐)
3. **text content** (适用于唯一文本)
4. **CSS选择器** (最后选择)

#### 2.2 UI组件库选择器模式

**Shadcn UI + Radix UI组件**：
```typescript
// 按钮组件
page.getByRole('button', { name: '登录' })
page.locator('button:has-text("登录")')

// 表单组件
page.getByLabel('邮箱地址')
page.getByPlaceholder('请输入邮箱')

// 对话框组件
page.getByRole('dialog')
page.locator('[role="dialog"]')

// 下拉选择
page.getByRole('combobox')
page.getByRole('option', { name: '选项名称' })
```

**Ant Design组件**：
```typescript
// 表格组件
page.locator('.ant-table')
page.locator('.ant-table-row')

// 分页组件
page.locator('.ant-pagination')
page.locator('.ant-pagination-next')

// 日期选择器
page.locator('.ant-picker')
page.locator('.ant-picker-input')
```

#### 2.3 表单元素定位
```typescript
// 基于label关联
page.getByLabel('用户名')
page.getByLabel('密码')

// 基于placeholder
page.getByPlaceholder('请输入用户名')

// 基于表单字段ID（项目使用React Hook Form）
page.locator('#email-field')
page.locator('#password-field')
```

### 3. 认证和权限测试

#### 3.1 认证流程
```typescript
// 登录测试
test('用户登录流程', async ({ page }) => {
  await page.goto('/auth/login');
  await page.getByLabel('邮箱').fill('<EMAIL>');
  await page.getByLabel('密码').fill('password123');
  await page.getByRole('button', { name: '登录' }).click();
  
  // 验证重定向到应用首页
  await expect(page).toHaveURL('/app');
});
```

#### 3.2 权限保护测试
```typescript
// 未认证访问保护页面
test('未登录用户访问保护页面应重定向到登录', async ({ page }) => {
  await page.goto('/app/organization/test/shareholder');
  await expect(page).toHaveURL(/.*\/auth\/login/);
});
```

### 4. 路由和导航测试

#### 4.1 路由结构测试
```typescript
// 组织路由测试
test('组织路由导航', async ({ page }) => {
  await page.goto('/app/test-org/shareholder');
  
  // 验证面包屑导航
  await expect(page.locator('[data-testid="breadcrumb"]')).toContainText('股东管理');
  
  // 验证侧边栏菜单
  await expect(page.locator('[data-testid="sidebar-menu"]')).toBeVisible();
});
```

#### 4.2 多语言路由测试
```typescript
// 多语言支持测试
test('多语言路由切换', async ({ page }) => {
  await page.goto('/en');
  await expect(page.locator('html')).toHaveAttribute('lang', 'en');
  
  await page.goto('/zh');
  await expect(page.locator('html')).toHaveAttribute('lang', 'zh');
});
```

### 5. 数据交互测试

#### 5.1 API交互模式
```typescript
// 等待API响应
await page.waitForResponse(response => 
  response.url().includes('/api/shareholder-registry') && 
  response.status() === 200
);

// 模拟API响应
await page.route('/api/shareholder-registry/**', route => {
  route.fulfill({
    status: 200,
    contentType: 'application/json',
    body: JSON.stringify({ code: 200, data: mockData })
  });
});
```

#### 5.2 文件上传测试
```typescript
// 文件上传（股东名册导入）
test('股东名册文件上传', async ({ page }) => {
  await page.goto('/app/test-org/shareholder/registry');
  
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles('tests/fixtures/test-registry.xlsx');
  
  await page.getByRole('button', { name: '开始导入' }).click();
  
  // 等待上传完成
  await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible();
  await expect(page.locator('[data-testid="upload-success"]')).toBeVisible();
});
```

### 6. 响应式设计测试

#### 6.1 视口测试
```typescript
// 移动端测试
test('移动端响应式布局', async ({ page }) => {
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto('/app');
  
  // 验证移动端导航
  await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
  await expect(page.locator('[data-testid="desktop-menu"]')).toBeHidden();
});
```

### 7. 性能和可访问性测试

#### 7.1 页面加载性能
```typescript
// 页面加载时间测试
test('页面加载性能', async ({ page }) => {
  const startTime = Date.now();
  await page.goto('/app');
  await page.waitForLoadState('networkidle');
  const loadTime = Date.now() - startTime;
  
  expect(loadTime).toBeLessThan(3000); // 3秒内加载完成
});
```

#### 7.2 可访问性测试
```typescript
// 键盘导航测试
test('键盘导航可访问性', async ({ page }) => {
  await page.goto('/app');
  
  // Tab键导航
  await page.keyboard.press('Tab');
  await expect(page.locator(':focus')).toBeVisible();
  
  // Enter键激活
  await page.keyboard.press('Enter');
});
```

### 8. 错误处理测试

#### 8.1 网络错误处理
```typescript
// 网络错误模拟
test('网络错误处理', async ({ page }) => {
  await page.route('/api/**', route => route.abort());
  
  await page.goto('/app/shareholder');
  await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
  await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
});
```

### 9. 测试数据管理

#### 9.1 测试数据隔离
```typescript
// 使用独立的测试数据库
test.beforeEach(async ({ page }) => {
  // 清理测试数据
  await page.request.post('/api/test/cleanup');
  
  // 创建测试数据
  await page.request.post('/api/test/seed');
});
```

### 10. 并行测试和稳定性

#### 10.1 测试隔离
```typescript
// 使用不同的用户账号避免冲突
test.describe.configure({ mode: 'parallel' });

test('用户A的操作', async ({ page }) => {
  await loginAs(page, '<EMAIL>');
  // 测试逻辑
});

test('用户B的操作', async ({ page }) => {
  await loginAs(page, '<EMAIL>');
  // 测试逻辑
});
```

## 最佳实践总结

1. **优先使用语义化选择器**（role, label, text）
2. **避免依赖CSS类名**（除非是稳定的组件库类名）
3. **合理使用等待策略**（waitForSelector, waitForResponse）
4. **保持测试数据独立性**
5. **编写可维护的页面对象模式**
6. **关注测试的可读性和可维护性**
7. **定期更新测试用例以匹配功能变更**
