# Web应用测试规范

## 文档信息
- **版本**: v1.0
- **创建时间**: 2025年07月29日
- **作者**: hayden
- **适用范围**: 所有Web应用功能测试
- **工具要求**: Playwright、浏览器开发者工具

## 1. 测试准备阶段

### 1.1 环境检查
```bash
# 检查Playwright安装
npx playwright --version

# 如果未安装，执行安装
pnpm add -D @playwright/test -w
npx playwright install
```

### 1.2 测试信息记录
每次测试开始前，记录以下信息：
- **测试时间**: 使用 `get_current_time_time()` 获取准确时间
- **测试页面**: 完整URL地址
- **测试工具**: Playwright + 浏览器类型
- **测试人员**: 执行测试的人员姓名
- **测试范围**: 功能测试、性能测试、响应式测试、安全性检查

### 1.3 登录流程标准化
对于需要登录的应用，统一登录流程：

```javascript
// 1. 导航到登录页面
playwright_navigate_playwright(baseURL)

// 2. 点击登录按钮
playwright_click_playwright("text=登录")

// 3. 填写登录信息
playwright_fill_playwright("input[type='email']", "测试邮箱")
playwright_fill_playwright("input[type='password']", "测试密码")

// 4. 提交登录
playwright_click_playwright("button:has-text('登录')")

// 5. 验证登录成功
playwright_screenshot_playwright("login_success")
```

## 2. API监控规范

### 2.1 全面API监控设置
在进入目标页面前，必须设置完整的API监控：

```javascript
// 设置Performance API监控
const apiMonitorScript = `
// 获取所有网络请求的性能数据
const entries = performance.getEntriesByType('resource');
const apiRequests = entries.filter(entry => 
  entry.name.includes('/api/') && 
  entry.responseEnd > 0
).map(entry => ({
  url: entry.name,
  duration: Math.round(entry.responseEnd - entry.requestStart),
  startTime: Math.round(entry.startTime),
  responseStart: Math.round(entry.responseStart),
  responseEnd: Math.round(entry.responseEnd),
  transferSize: entry.transferSize || 0
}));
`;
```

### 2.2 API性能标准
| 性能等级 | 响应时间范围 | 评级 | 处理建议 |
|----------|--------------|------|----------|
| 🟢 优秀 | < 200ms | 优秀 | 保持现状 |
| 🟡 良好 | 200ms - 1000ms | 良好 | 可接受，监控趋势 |
| 🟠 一般 | 1000ms - 3000ms | 一般 | 需要优化 |
| 🔴 较差 | > 3000ms | 较差 | 立即优化 |

### 2.3 API监控数据记录格式
```markdown
#### X. API名称
- **请求URL**: `/api/endpoint`
- **请求方法**: GET/POST/PUT/DELETE
- **响应状态**: 200 OK / 错误码
- **响应时间**: XXXms
- **数据大小**: XXX bytes/KB
- **功能**: API功能描述
- **数据加密**: ✅/❌ 是否加密
```

## 3. 截图规范

### 3.1 截图命名规范
```
{测试类型}_{状态}_{功能描述}-{时间戳}.png

示例：
- login_success-2025-07-29T03-38-58-903Z.png
- api_error_timeout-2025-07-29T03-40-21-553Z.png
- responsive_mobile_layout-2025-07-29T03-42-15-123Z.png
```

### 3.2 必需截图类型
1. **成功状态截图**:
   - 登录成功
   - 页面加载成功
   - 功能操作成功
   - 响应式布局成功

2. **失败状态截图**:
   - 功能错误
   - API错误
   - 布局问题
   - 性能问题

3. **测试过程截图**:
   - 测试开始状态
   - 中间操作状态
   - 最终结果状态

### 3.3 截图技术要求
```javascript
// 全页面截图
playwright_screenshot_playwright({
  name: "screenshot_name",
  savePng: true,
  fullPage: true
})

// 特定元素截图
playwright_screenshot_playwright({
  name: "element_screenshot",
  selector: ".specific-element",
  savePng: true
})
```

## 4. 功能测试规范

### 4.1 基础功能测试清单
- [ ] 页面加载测试
- [ ] 导航功能测试
- [ ] 用户界面交互测试
- [ ] 数据展示测试
- [ ] 表单提交测试
- [ ] 文件上传/下载测试
- [ ] 搜索功能测试
- [ ] 分页功能测试

### 4.2 交互元素测试
```javascript
// 按钮点击测试
playwright_click_playwright("button[data-testid='submit-btn']")

// 输入框填写测试
playwright_fill_playwright("input[name='username']", "测试数据")

// 下拉选择测试
playwright_select_playwright("select[name='category']", "选项值")

// 文件上传测试
playwright_upload_file_playwright("input[type='file']", "/path/to/file")
```

### 4.3 数据验证测试
- 验证数据正确性
- 验证数据格式
- 验证数据完整性
- 验证数据实时性

## 5. 响应式测试规范

### 5.1 标准测试尺寸
```javascript
// 桌面端测试
playwright_navigate_playwright(url, {width: 1920, height: 1080})

// 平板端测试  
playwright_navigate_playwright(url, {width: 768, height: 1024})

// 移动端测试
playwright_navigate_playwright(url, {width: 375, height: 667})
```

### 5.2 响应式检查项目
- [ ] 布局适配性
- [ ] 导航菜单适配
- [ ] 图表/图片缩放
- [ ] 文字可读性
- [ ] 按钮可点击性
- [ ] 表格横向滚动

## 6. 性能测试规范

### 6.1 页面性能指标
```javascript
// 获取页面性能数据
const perfData = performance.getEntriesByType('navigation')[0];
const paintEntries = performance.getEntriesByType('paint');
const result = {
  loadTime: perfData.loadEventEnd - perfData.loadEventStart,
  domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
  firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || 0,
  firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
  totalLoadTime: perfData.loadEventEnd - perfData.fetchStart
};
```

### 6.2 性能评估标准
| 指标 | 优秀 | 良好 | 需改进 |
|------|------|------|--------|
| 首次内容绘制 | < 1.5s | 1.5s - 2.5s | > 2.5s |
| 最大内容绘制 | < 2.5s | 2.5s - 4.0s | > 4.0s |
| 首次输入延迟 | < 100ms | 100ms - 300ms | > 300ms |
| 累积布局偏移 | < 0.1 | 0.1 - 0.25 | > 0.25 |

## 7. 安全性测试规范

### 7.1 认证和授权测试
- [ ] 登录功能测试
- [ ] 权限控制测试
- [ ] 会话管理测试
- [ ] 登出功能测试

### 7.2 数据安全检查
- [ ] 敏感数据加密传输
- [ ] 用户信息脱敏显示
- [ ] API响应数据安全
- [ ] 本地存储安全

### 7.3 输入验证测试
```javascript
// XSS测试
playwright_fill_playwright("input[name='search']", "<script>alert('xss')</script>")

// SQL注入测试
playwright_fill_playwright("input[name='id']", "1' OR '1'='1")

// 特殊字符测试
playwright_fill_playwright("input[name='text']", "!@#$%^&*()_+{}[]")
```

## 8. 错误处理测试规范

### 8.1 控制台错误监控
```javascript
// 获取控制台错误
playwright_console_logs_playwright({
  type: "error",
  clear: false
})

// 获取控制台警告
playwright_console_logs_playwright({
  type: "warning",
  clear: false
})
```

### 8.2 边界条件测试
- [ ] 网络异常处理
- [ ] 数据为空处理
- [ ] 404页面处理
- [ ] 超时处理
- [ ] 权限不足处理

### 8.3 用户输入边界测试
- [ ] 空输入测试
- [ ] 超长输入测试
- [ ] 特殊字符输入测试
- [ ] 无效格式输入测试

## 9. 测试报告规范

### 9.1 报告文件命名
```
{功能模块}测试报告.md

示例：
- 股东名册分析页面测试报告.md
- 用户管理功能测试报告.md
- 数据导出功能测试报告.md
```

### 9.2 报告结构模板
```markdown
# {功能模块}测试报告

## 测试环境信息
- **测试时间**: {开始时间} - {结束时间}
- **测试页面**: {完整URL}
- **测试工具**: Playwright
- **作者**: {测试人员}
- **浏览器**: {浏览器类型}
- **测试范围**: {测试类型列表}

## 测试结果截图展示

### ✅ 成功测试截图
[成功测试的截图和说明]

### ❌ 失败测试截图
[失败测试的截图和说明]

## 功能测试结果
[详细的功能测试结果]

## API性能监控
[完整的API监控数据]

## 响应式布局测试
[不同屏幕尺寸的测试结果]

## 错误和安全性检查
[错误日志和安全性检查结果]

## 问题汇总
### 高优先级问题
### 中优先级问题
### 低优先级问题

## 改进建议
### 功能改进
### 性能优化
### 安全性改进

## 测试总结
### 整体评价
### 测试覆盖率
### 测试数据统计
### 建议优先级
```

### 9.3 问题严重程度分级
| 级别 | 描述 | 处理时间 | 示例 |
|------|------|----------|------|
| 🔴 高 | 影响核心功能，阻塞用户使用 | 立即修复 | 登录失败、数据丢失 |
| 🟡 中 | 影响用户体验，但不阻塞使用 | 1-3天修复 | 功能错误、性能问题 |
| 🟢 低 | 轻微影响，不影响主要功能 | 1-2周修复 | 样式问题、提示文案 |

## 10. 测试用例模板

### 10.1 功能测试用例模板
```markdown
## 测试用例: {功能名称}

### 基本信息
- **用例ID**: TC_{模块}_{编号}
- **测试类型**: 功能测试
- **优先级**: 高/中/低
- **前置条件**: {测试前需要满足的条件}

### 测试步骤
1. {步骤1描述}
   - 操作: {具体操作}
   - 预期结果: {预期的结果}

2. {步骤2描述}
   - 操作: {具体操作}
   - 预期结果: {预期的结果}

### 测试数据
- 输入数据: {测试使用的数据}
- 预期输出: {预期的输出结果}

### 验收标准
- [ ] {验收条件1}
- [ ] {验收条件2}
- [ ] {验收条件3}
```

### 10.2 API测试用例模板
```markdown
## API测试用例: {API名称}

### 基本信息
- **API端点**: {API URL}
- **请求方法**: GET/POST/PUT/DELETE
- **认证要求**: {是否需要认证}

### 测试场景
#### 正常场景
- **请求参数**: {正常参数}
- **预期响应**: {预期的响应}
- **响应时间**: {预期响应时间}

#### 异常场景
- **错误参数**: {错误参数}
- **预期错误**: {预期的错误响应}

### 性能要求
- **响应时间**: < {时间}ms
- **并发支持**: {并发数}
- **数据大小**: < {大小}KB
```
