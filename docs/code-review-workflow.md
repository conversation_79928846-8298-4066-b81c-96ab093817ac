# `@codereview.mdc` 标准化代码评审工作流

本文档详细说明了如何使用 `@codereview.mdc` 提示词，并定义了从 **发起评审** 到 **处理反馈并完成修复** 的完整闭环工作流程。

**核心原则：** 每次调用都必须清晰地提供 **【评审目标】** 和 **【评审范围】**。

---

### 场景一：评审拟合并分支的代码（准备合并时）

此场景用于功能开发完成，准备将特性分支合并回主干（如 `dev` 或 `main`）时。这是最重要、最正式的评审场景。

#### 第一步：发起评审

在目标分支（如 `dev`）上，通过试合并（`--no-commit`）来准备评审环境。

```sh
# 1. 确保目标分支是最新代码
git checkout dev
git pull origin dev

# 2. 试合并你的功能分支，--no-commit确保不产生合并提交，以便审查
git merge --no-commit --no-ff feature-shareholder-refactor
```

如果没出现任何冲突，看不到待合并的代码文件可以
- 执行 git reset --soft origin/dev
- 这会撤销所有提交但保留修改的文件在暂存区   

然后，在对话框中发起评审：

```
@codereview.mdc
- **评审目标**: 股东分析模块重构完成，准备合并到dev
- **评审范围**: 工作区更改: `git diff --cached`
```

#### 第二步：处理与跟进评审结果

收到评审报告（问题清单）后，严格分离 **“评审AI”** 和 **“修改AI”** 的职责，执行以下修复流程：

1.  **撤销试合并**：首先，放弃本次的试合并，保持目标分支（`dev`）的整洁。
    ```sh
    git merge --abort
    ```
2.  **返回开发分支**：切换回你自己的功能分支，准备进行代码修复。
    ```sh
    git checkout feature-shareholder-refactor
    ```
3.  **开启新对话，委托“修改AI”修复**：打开一个 **全新的对话**，将“评审AI”给出的“问题清单”部分粘贴进去，并委托“修改AI”进行处理。
    > 这是架构师的代码评审意见，请检查修复和回复，可以提出质疑和反驳解释。

4.  **监督修复并推送**：在“修改AI”完成代码修复后，仔细检查修改。确认无误后，将修复后的代码提交并推送到你的远程功能分支。
    ```sh
    git add .
    git commit -m "fix: address code review comments"
    git push
    ```
5.  **返回“评审AI”对话，再次发起评审**：回到最初发起评审的那个 **“评审AI”** 对话，执行第一步中的 `git merge --no-commit` 操作后，告诉它工程师已经修复完毕，并要求它重新评审。
    > 工程师已经完成修复，相关回复如下：[此处可以附上“修改AI”的回复或修复总结]。请再次发起代码评审。
    
    *   **特殊情况处理**：如果再次试合并后看不到代码差异，可使用 `git reset --soft origin/dev` 命令撤销合并提交但保留文件修改，然后再次要求评审。

---

### 场景二：评审待提交的代码（本地开发中）

当你在本地完成了部分修改，但还未提交（commit）时使用。

#### 第一步：发起评审

```
@codereview.mdc
- **评审目标**: 修复了AI摘要组件的样式问题
- **评审范围**: 工作区更改: `git diff`
```

#### 第二步：处理与跟进评审结果

1.  **开启新对话，委托“修改AI”修复**：打开一个 **新的对话**（“修改AI”），将“评审AI”给出的问题清单粘贴进去，并委托它处理。
    > 这是代码评审意见，请帮我修复这些问题。
2.  **返回原对话，再次发起评审**：“修改AI”修复完成后，你的代码仍在工作区。此时，回到发起评审的那个 **“评审AI”** 对话，并使用以下指令要求再次评审。
    > 工程师已经完成修复，请对工作区的代码再次发起评审。

---

### 场景三：评审两个分支的区别（常规对比）

用于了解任意两个分支之间的代码差异，主要用于分析和调研。

#### 第一步：发起评审

```
@codereview.mdc
- **评审目标**: 查看 `feature/new-ui` 分支相比 `feature/old-ui` 到底改了哪些UI组件
- **评审范围**: 分支对比 `git diff feature/old-ui..feature/new-ui`
```

#### 第二步：处理与跟进评审结果

此场景的评审结果通常用于技术决策。如果根据评审结果确认需要修改，请：
1.  切换到需要修改的分支（如 `git checkout feature/new-ui`）。
2.  严格参考 **场景二** 定义的、分离“评审AI”和“修改AI”的流程进行代码修复和再次评审。

---

### 场景四：评审分支上从某次提交到最新的全部代码

用于评审某个特性分支从一个已知的起点（如上一次评审过的commit）到当前最新状态的所有变更。

#### 第一步：发起评审

```
@codereview.mdc
- **评审目标**: 评审上次联调之后新增的所有后端接口改动
- **评审范围**: 提交范围 `git diff a1b2c3d..HEAD`
```

#### 第二步：处理与跟进评审结果

1.  **开启新对话，委托“修改AI”修复**：在当前分支，打开一个 **新的对话**（“修改AI”），将问题清单交给它修复。
2.  **提交修复**：“修改AI”完成修改后，将其作为一个新的 `fix` 提交。
    ```sh
    git commit -m "fix: address review comments for recent changes"
    ```
3.  **返回原对话，再次发起评审**：回到发起评审的那个 **“评审AI”** 对话，并使用以下指令要求再次评审。由于 `HEAD` 已经前移，评审范围会自动包含你的修复。
    > 工程师已经完成修复，请再次发起代码评审。

---

### 场景五：精确审查某一次或最近几次提交

用于对某一个或某几个特定的提交进行深入、精确的审查。

#### 第一步：发起评审

```
@codereview.mdc
- **评审目标**: 详细检查昨晚那个紧急安全修复的提交
- **评审范围**: 最近1次提交: `git diff HEAD~1`
```
#### 第二步：处理与跟进评审结果
对历史提交的修改通常不建议通过 `rebase` 修改历史，而是通过创建新提交来修复。

1.  **开启新对话，委托“修改AI”修复**：打开一个 **新的对话**（“修改AI”），根据评审报告，让它在当前分支的最新代码上进行修复。
2.  **创建新提交**：将“修改AI”的修复作为一个新的 `fix` 提交。
    ```sh
    git commit -m "fix: correct issue found in commit [被审查的commit_hash]"
    ```
3.  **返回原对话，评审修复提交**：回到发起评审的那个 **“评审AI”** 对话，要求它评审这个新的修复提交，以验证修复是否彻底。
    > 工程师已经完成修复，这是新的修复提交，请对这个提交 `git show [新commit_hash]` 进行评审。

---

### 场景六：评审手动指定的若干文件

当你只想评审几个特定的文件，不受任何分支或提交状态影响时使用。

#### 第一步：发起评审

```
@codereview.mdc
- **评审目标**: 优化 `AISummary` 和 `CompanyOverview` 两个组件的数据加载逻辑
- **评审范围**: 请评审以下文件:
  - `apps/web/modules/saas/shareholder/components/analyse/AISummary.tsx`
  - `apps/web/modules/saas/shareholder/components/analyse/CompanyOverview.tsx`
```

#### 第二步：处理与跟进评审结果

此场景的修复与跟进流程与 **场景二** 完全一致。你需要：
1.  **开启新对话** 委托“修改AI”修复指定文件。
2.  修复完成后，**返回原对话**，要求“评审AI”再次评审相同的文件列表。
    > 工程师已经完成修复，请再次对指定文件发起评审。
