# 基金代码查股东名册-计算股数 API 文档

## 概述

**API名称**: 基金代码查股东名册-计算股数
**API路径**: `/api/n8n_proxy/fundcode_sharehold`
**请求方法**: POST
**创建时间**: 2025-08-05 17:53:05
**作者**: hayden
**版本**: v1.1.0

## 功能描述

该API支持通过基金代码或一码通账号查询对应的股东名册信息，并计算相关股数数据。主要功能包括：

1. **基金代码查询模式**：根据基金代码查询基金基本信息，匹配基金名称与股东账户名称
2. **一码通账号查询模式**：直接通过一码通账号查询股东持仓信息
3. **混合查询模式**：同时支持基金代码和一码通账号的组合查询
4. 分析股东持股变动情况
5. 计算冻结股和限售股统计
6. 获取最新股价和持仓收益分析

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 请求体参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| organizationId | string | 是 | 组织机构ID，用于标识查询的组织 | "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36" |
| fund_code | string | 否 | 基金代码，支持同花顺格式。与unified_account_number二选一 | "001384.OF" |
| unified_account_number | string | 否 | 一码通账户号码，用于直接查询股东信息。与fund_code二选一 | "A123456789" |

### 请求示例

#### 基金代码查询模式
```json
{
  "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
  "fund_code": "001384.OF"
}
```

#### 一码通账号查询模式
```json
{
  "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
  "unified_account_number": "A123456789"
}
```

#### 混合查询模式
```json
{
  "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
  "fund_code": "001384.OF",
  "unified_account_number": "A123456789"
}
```

## 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
      "id": "12345",
      "certificate_number": "110101199001011234",
      "unified_account_number": "A123456789",
      "account_name": "招商银行股份有限公司－招商中证白酒指数分级证券投资基金",
      "phone_number": "***********",
      "shareholder_type": "基金",
      "shareholder_category": "A",
      "contact_address": "北京市朝阳区建国门外大街1号",
      "cash_account": "**********",
      "cash_account_shares": 1000000,
      "margin_account": "**********",
      "margin_account_shares": 0,
      "latest_period_date": "2025-06-30",
      "current_shares": 1000000,
      "current_ratio": 0.85,
      "prev_shares": 950000,
      "prev_ratio": 0.81,
      "previous_period_date": "2025-03-31",
      "shareholder_behavior": "增持",
      "shares_change": 50000,
      "ratio_change": 0.04,
      "holding_rank": 1,
      "all_periods_holdings": [
        {
          "registerDate": "2025-06-30",
          "numberOfShares": 1000000,
          "shareholdingRatio": 0.85
        },
        {
          "registerDate": "2025-03-31", 
          "numberOfShares": 950000,
          "shareholdingRatio": 0.81
        }
      ],
      "match_status": "EXACT_MATCH",
      "company_frozen_shares": "0.00",
      "company_locked_shares": "0.00",
      "company_frozen_ratio_percent": "0.0000",
      "company_locked_ratio_percent": "0.0000",
      "latest_market_value": ********.00,
      "latest_holding_profit": 2500000.00,
      "estimated_total_cost": ********.00,
      "latest_price": 15.00,
      "average_stock_price": 12.50,
      "calculation_time": "2025-08-05T17:53:05.110Z"
    }
  ]
}
```

### 错误响应

#### 参数验证失败 (HTTP 200)
```json
{
  "code": "MISSING_ORGANIZATIONID",
  "message": "缺少必需参数：organizationId。请提供organizationId参数。",
  "field": "organizationId",
  "value": null,
  "timestamp": "2025-08-05T17:53:05.110Z",
  "type": "VALIDATION_ERROR"
}
```

#### 基金不存在 (HTTP 200)
```json
{
  "code": 0,
  "message": "没有找到该基金"
}
```

#### 查询参数不足 (HTTP 200)
```json
{
  "code": "INSUFFICIENT_QUERY_PARAMS",
  "message": "fund_code和unified_account_number至少需要提供一个参数",
  "field": "query_params",
  "value": null,
  "timestamp": "2025-08-05T18:50:13.366Z",
  "type": "VALIDATION_ERROR"
}
```

#### 组织不存在 (HTTP 200)
```json
{
  "code": "ORGANIZATION_NOT_FOUND",
  "message": "未找到对应的组织ID，请检查organizationcode是否正确",
  "field": "organizationcode",
  "value": "invalid_org_id",
  "timestamp": "2025-08-05T17:53:05.110Z",
  "type": "VALIDATION_ERROR"
}
```

## 响应字段说明

### 基础信息字段
- `organizationId`: 组织机构ID
- `id`: 记录唯一标识符
- `certificate_number`: 证件号码
- `unified_account_number`: 一码通账户号码
- `account_name`: 证券账户名称/股东名称
- `phone_number`: 联系电话号码
- `shareholder_type`: 股东类型分类
- `shareholder_category`: 持有人类别代码
- `contact_address`: 通讯地址

### 账户持股信息字段
- `cash_account`: 普通证券账户号码
- `cash_account_shares`: 普通账户持股数量
- `margin_account`: 信用证券账户号码
- `margin_account_shares`: 信用账户持股数量

### 持股变动分析字段
- `latest_period_date`: 最新报告期日期
- `current_shares`: 当前持股数量
- `current_ratio`: 当前持股比例
- `prev_shares`: 上期持股数量
- `prev_ratio`: 上期持股比例
- `previous_period_date`: 上期报告期日期
- `shareholder_behavior`: 股东行为（增持/减持/不变/新增）
- `shares_change`: 持股数量变化
- `ratio_change`: 持股比例变化
- `holding_rank`: 持股排名
- `previous_holding_rank`: 上一期排名
- `rank_change`: 排名变化
- `rank_change_description`: 排名描述

### 历史持股记录字段
- `all_periods_holdings`: 所有期间持股记录数组
  - `registerDate`: 名册登记日期
  - `numberOfShares`: 持股数量
  - `shareholdingRatio`: 持股比例

### 匹配状态字段
- `match_status`: 匹配状态标识
  - `EXACT_MATCH`: 基金名称精确匹配（基金代码查询模式）
  - `UNIFIED_ACCOUNT_MATCH`: 一码通账号匹配（一码通查询模式）
  - `MIXED_QUERY`: 混合查询模式

### 公司整体统计字段
- `company_frozen_shares`: 公司冻结股总数
- `company_locked_shares`: 公司限售股总数
- `company_frozen_ratio_percent`: 公司冻结股比例
- `company_locked_ratio_percent`: 公司限售股比例

### 投资收益分析字段
- `latest_market_value`: 最新持仓市值（元）
- `latest_holding_profit`: 最新持仓收益（元）
- `estimated_total_cost`: 预估持仓总成本（元）
- `latest_price`: 最新股价
- `average_stock_price`: 平均股价
- `calculation_time`: 计算时间戳

## 业务逻辑说明

### 数据处理流程

1. **参数验证**: 验证organizationId必填，fund_code和unified_account_number至少提供一个
2. **查询模式判断**: 根据传入参数自动判断查询模式
   - 仅fund_code：基金代码查询模式
   - 仅unified_account_number：一码通账号查询模式
   - 两者都有：混合查询模式
3. **基金信息查询**: 根据fund_code查询同花顺基金管理托管表（基金代码模式）
4. **股东匹配**:
   - 基金代码模式：通过基金全称和托管人匹配股东账户名称
   - 一码通模式：直接通过一码通账号查询股东信息
   - 混合模式：合并两种查询结果并去重
5. **持股分析**: 计算最新期与上期的持股变化
6. **冻结股统计**: 统计公司整体冻结股和限售股情况
7. **股价获取**: 调用同花顺API获取最新股价和区间均价
8. **收益计算**: 计算持仓市值和投资收益
9. **数据整合**: 合并所有数据源的结果并按持股数量排序

### 股东行为判断逻辑

- **新增**: 上期持股数量为空
- **增持**: 当前持股数量 > 上期持股数量
- **减持**: 当前持股数量 < 上期持股数量  
- **不变**: 当前持股数量 = 上期持股数量

### 匹配规则

#### 基金代码查询模式匹配规则
- 股东账户名称格式：`托管人－基金全称`
- 通过`－`分隔符解析托管人和基金名称部分
- 基金名称部分需要与查询的基金全称完全匹配
- 托管人部分需要包含查询的托管人名称

#### 一码通账号查询模式匹配规则
- 直接通过一码通账号（unified_account_number）精确匹配
- 无需进行名称解析和匹配，查询效率更高
- 适用于已知具体股东一码通账号的场景

#### 混合查询模式匹配规则
- 同时执行基金代码和一码通账号两种查询
- 对查询结果进行去重处理（基于一码通账号和证件号码）
- 保留数据更完整的记录

## 注意事项

1. **查询参数**: fund_code和unified_account_number至少需要提供一个，建议根据业务场景选择合适的查询模式
2. **数据时效性**: 股价数据来源于同花顺API，具有一定延迟
3. **匹配精度**:
   - 基金名称匹配要求严格，需要完全匹配
   - 一码通账号查询为精确匹配，查询效率更高
4. **计算逻辑**: 投资收益基于区间均价估算，仅供参考
5. **权限控制**: 需要有效的organizationId才能查询数据
6. **数据完整性**: 部分字段可能为空，前端需要做好空值处理
7. **去重机制**: 混合查询模式会自动去重，避免重复数据

## 技术实现

- **数据库**: PostgreSQL (项目数据库 + 同花顺数据库)
- **外部API**: 同花顺量化API
- **工作流引擎**: n8n
- **响应格式**: JSON
- **错误处理**: 统一错误响应格式

## 更新记录

- **2025-08-05 17:53:05**: hayden - 初始版本创建，包含完整的API功能和文档
- **2025-08-05 18:50:13**: hayden - 新增unified_account_number字段支持，实现多查询模式（基金代码/一码通/混合查询），优化查询逻辑和文档说明
