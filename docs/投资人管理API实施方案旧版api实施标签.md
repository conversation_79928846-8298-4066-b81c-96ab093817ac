# 投资人管理API实施方案

**作者**: hayden
**创建时间**: 2025-07-07 11:40:43
**更新时间**: 2025-07-09 09:52:07
**版本**: v4.4

## 1. 项目结构分析

### 1.1 现有API架构模式

通过分析现有股东名册API代码，项目遵循以下架构模式：

```
packages/api/src/routes/
├── {module-name}/              # 模块目录
│   ├── router.ts              # 主路由文件，聚合所有子路由
│   ├── {feature}.ts           # 功能路由文件（如list.ts, create.ts）
│   ├── types.ts               # TypeScript类型定义
│   └── lib/                   # 工具库目录
│       └── validators.ts      # Zod验证器
```

### 1.2 中间件应用模式

所有API路由统一应用：
- `authMiddleware`: 用户身份验证
- `shareholderCryptoMiddleware()`: 请求/响应加解密
- 统一错误处理机制

### 1.3 数据库操作模式

- 使用 `@repo/database` 的 `db` 实例
- 支持事务操作和复杂查询
- Decimal类型需转换为字符串返回

### 1.4 数据库表结构概览

根据投资人管理模块数据库设计方案，核心表结构包括：

- **CompanyFilter表**: 组织级别的公司筛选配置（本司代码、对标公司代码）
- **InvestorTag表**: 通用标签系统，支持系统自动标签和用户标签
- **InvestorContact表**: 投资人联系人信息管理

**关键设计特点**：
- 标签系统支持动态扩展，无需硬编码枚举
- 级联删除机制确保数据一致性
- 系统自动标签与用户标签分离管理

## 2. 投资人管理模块实施方案

### 2.1 目录结构设计

```
packages/api/src/routes/investor-management/
├── router.ts                  # 主路由聚合器
├── types.ts                   # TypeScript类型定义
├── company-filter/            # 公司筛选配置功能
│   ├── upsert.ts             # 创建/更新公司筛选配置（统一接口）
│   ├── get.ts                # 获取公司筛选配置
├── tags/                      # 投资人标签功能（基于标签系统的收藏）
│   ├── create.ts             # 创建标签（收藏）
│   ├── delete.ts             # 删除用户收藏标签（取消收藏）
│   └── sync.ts               # 同步系统自动标签（系统标签与公司筛选配置联动）
├── contacts/                  # 投资人联系人功能
│   ├── create.ts             # 创建联系人
│   ├── update.ts             # 更新联系人
│   ├── delete.ts             # 删除联系人
│   └── list.ts               # 查询联系人列表
└── lib/                       # 工具库
    └── validators.ts          # Zod验证器
```

### 2.2 主路由实施（router.ts）

```typescript
/**
 * 投资人管理主路由
 * 聚合所有投资人管理相关的子路由
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @updated 2025-07-08 18:52:46 hayden 根据新的数据库设计方案更新路由结构，支持标签系统和公司筛选配置
 */
import { Hono } from "hono";
import { companyFilterUpsertRouter } from "./company-filter/upsert";
import { companyFilterGetRouter } from "./company-filter/get";
import { tagsCreateRouter } from "./tags/create";
import { tagsDeleteRouter } from "./tags/delete";
import { tagsListRouter } from "./tags/list";
import { tagsSyncRouter } from "./tags/sync";
import { contactsCreateRouter } from "./contacts/create";
import { contactsUpdateRouter } from "./contacts/update";
import { contactsDeleteRouter } from "./contacts/delete";
import { contactsListRouter } from "./contacts/list";

export const investorManagementRouter = new Hono()
  .basePath("/investor-management")
  .route("/company-filter", companyFilterUpsertRouter)
  .route("/company-filter", companyFilterGetRouter)
  .route("/tags", tagsCreateRouter)
  .route("/tags", tagsDeleteRouter)
  .route("/tags", tagsListRouter)
  .route("/tags", tagsSyncRouter)
  .route("/contacts", contactsCreateRouter)
  .route("/contacts", contactsUpdateRouter)
  .route("/contacts", contactsDeleteRouter)
  .route("/contacts", contactsListRouter);
```

### 2.3 验证器实施（lib/validators.ts）

```typescript
/**
 * 投资人管理模块验证器
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @updated 2025-07-08 19:57:55 hayden 修改公司筛选配置验证器，benchmarkCompanyCodes字段改为可选
 */
import { z } from "zod";

// 公司筛选配置相关验证器
export const CreateCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  companyCode: z.string().min(1, "本司公司代码不能为空"),
  benchmarkCompanyCodes: z.string().optional(),
});

export const GetCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人标签相关验证器（基于标签系统的收藏功能）
export const CreateInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().min(1, "投资人代码不能为空"),
  tagName: z.string().min(1, "标签名称不能为空"),
  tagCategory: z.enum(["system", "user"], {
  errorMap: () => ({ message: "标签分类必须是 system、user" })
}),
  tagMetadata: z.record(z.any()).optional(),
});

export const DeleteInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  id: z.string().min(1, "标签记录ID不能为空"),
});

export const ListInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().optional(),
  tagName: z.string().optional(),
  tagCategory: z.enum(["system", "user"]).optional(),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});

export const SyncInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人联系人相关验证器
export const CreateInvestorContactSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空"),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
});

export const UpdateInvestorContactSchema = z.object({
  contactId: z.string().min(1, "联系人ID不能为空"),
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空").optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
});

export const DeleteInvestorContactSchema = z.object({
  contactId: z.string().min(1, "联系人ID不能为空"),
  organizationId: z.string().min(1, "组织ID不能为空"),
});

export const ListInvestorContactsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().optional(),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});
```

## 3. 公司筛选配置功能实施

### 3.1 创建/更新公司筛选配置实施（company-filter/upsert.ts）

```typescript
/**
 * 创建/更新公司筛选配置路由（统一接口）
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @updated 2025-07-09 09:52:07 hayden 修改路由名称为upsert，更准确描述既创建又更新的功能
 * @description 组织级别的公司筛选配置管理，支持本司代码和对标公司代码的设置
 */
export const companyFilterUpsertRouter = new Hono().post(
  "/upsert",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateCompanyFilterSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, companyCode, benchmarkCompanyCodes } = validationResult.data;

      // 检查是否已存在配置
      const existingFilter = await db.companyFilter.findUnique({
        where: { organizationId }
      });

      let result;
      if (existingFilter) {
        // 更新现有配置
        result = await db.companyFilter.update({
          where: { organizationId },
          data: {
            companyCode,
            benchmarkCompanyCodes,
          }
        });
      } else {
        // 创建新配置
        result = await db.companyFilter.create({
          data: {
            organizationId,
            companyCode,
            benchmarkCompanyCodes,
          }
        });
      }

      c.set("response", {
        code: 200,
        message: existingFilter ? "公司筛选配置更新成功" : "公司筛选配置创建成功",
        data: {
          id: result.id
        }
      });
      return;

    } catch (error) {
      // 统一错误处理...
    }
  }
);
```

### 3.2 获取公司筛选配置及关联标签实施（company-filter/get.ts）

```typescript
/**
 * 获取公司筛选配置及关联标签路由
 *
 * <AUTHOR>
 * @date 2025-07-08 19:09:10
 * @updated 2025-07-08 19:09:10 hayden 根据数据库设计方案，公司筛选和标签表已关联，需要一起查询
 * @description 获取组织的公司筛选配置，同时返回关联的投资人标签数据
 */
export const companyFilterGetRouter = new Hono().post(
  "/get",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");

      // 参数验证
      const validationResult = GetCompanyFilterSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId } = validationResult.data;

      // 查询配置及关联的标签
      const filter = await db.companyFilter.findUnique({
        where: { organizationId },
        include: {
          investorTags: {
            orderBy: { modifiedAt: "desc" },
            include: {
              organization: { select: { name: true } }
            }
          }
        }
      });

      if (!filter) {
        c.set("response", {
          code: 404,
          message: "公司筛选配置不存在",
          data: null
        });
        return;
      }

      c.set("response", {
        code: 200,
        message: "查询成功",
        data: {
          companyFilter: {
            id: filter.id,
            organizationId: filter.organizationId,
            companyCode: filter.companyCode,
            benchmarkCompanyCodes: filter.benchmarkCompanyCodes,
            modifiedAt: filter.modifiedAt.toISOString(),
          },
          investorTags: filter.investorTags.map(tag => ({
            id: tag.id,
            investorCode: tag.investorCode,
            tagName: tag.tagName,
            tagCategory: tag.tagCategory,
            tagMetadata: tag.tagMetadata,
            modifiedAt: tag.modifiedAt.toISOString(),
            organizationName: tag.organization.name
          }))
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

## 4. 投资人标签功能实施（基于标签系统的收藏）

### 4.1 创建标签实施（tags/create.ts）

```typescript
/**
 * 创建投资人标签路由（收藏功能基于标签系统实现）
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @description 基于标签系统实现的收藏功能，支持用户自定义标签和系统自动标签
 */
export const tagsCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateInvestorTagSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, investorCode, tagName, tagCategory, tagMetadata } = validationResult.data;

      // 检查是否已存在相同标签
      const existingTag = await db.investorTag.findFirst({
        where: {
          organizationId,
          investorCode,
          tagName
        }
      });

      if (existingTag) {
        // 如果已存在，返回已标记信息
        c.set("response", {
          code: 200,
          message: "该投资人已有此标签",
          data: {
            id: existingTag.id
          }
        });
        return;
      } else {
        // 创建新的标签记录
        const newTag = await db.investorTag.create({
          data: {
            organizationId,
            investorCode,
            tagName,
            tagCategory,
            tagMetadata: tagMetadata || {},
            // 用户标签不关联公司筛选配置，系统标签需要关联
            companyFilterId: tagCategory === "system" ?
              (await db.companyFilter.findUnique({ where: { organizationId } }))?.id :
              null,
          }
        });

        c.set("response", {
          code: 200,
          message: "标签创建成功",
          data: {
            id: newTag.id
          }
        });
        return;
      }

    } catch (error) {
      // 统一错误处理...
    }
  }
);
```

### 4.2 删除用户收藏标签实施（tags/delete.ts）

```typescript
/**
 * 删除投资人标签路由（仅限用户收藏标签）
 *
 * <AUTHOR>
 * @date 2025-07-08 19:09:10
 * @updated 2025-07-09 09:52:07 hayden 明确删除功能仅适用于用户收藏标签，系统标签通过公司筛选配置联动管理
 * @description 删除用户收藏标签（取消收藏功能），系统标签与公司筛选配置挂钩，公司更新时系统标签自动删除
 */
export const tagsDeleteRouter = new Hono().post(
  "/delete",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = DeleteInvestorTagSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, id } = validationResult.data;

      // 查找标签记录
      const existingTag = await db.investorTag.findFirst({
        where: {
          id,
          organizationId,
        }
      });

      if (!existingTag) {
        c.set("response", {
          code: 404,
          message: "标签记录不存在",
          data: null
        });
        return;
      }

      // 仅支持删除用户收藏标签（取消收藏），系统标签通过公司筛选配置联动管理
      if (existingTag.tagCategory !== "user") {
        c.set("response", {
          code: 403,
          message: "只能删除用户收藏标签，系统标签通过公司筛选配置自动管理",
          data: null
        });
        return;
      }

      // 删除标签记录
      await db.investorTag.delete({
        where: { id }
      });

      c.set("response", {
        code: 200,
        message: "取消收藏成功",
        data: {
          id: existingTag.id
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

### 4.4 同步系统自动标签实施（tags/sync.ts）

```typescript
/**
 * 同步系统自动标签路由
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @updated 2025-07-09 09:52:07 hayden 明确系统标签与公司筛选配置的联动关系
 * @description 调用同花顺API同步投资人持仓数据，自动生成系统标签。系统标签与公司筛选配置挂钩，公司代码更新时自动删除旧标签
 */
export const tagsSyncRouter = new Hono().post(
  "/sync",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = SyncInvestorTagsSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId } = validationResult.data;

      // 获取公司筛选配置
      const companyFilter = await db.companyFilter.findUnique({
        where: { organizationId }
      });

      if (!companyFilter) {
        throw new HTTPException(404, { message: "请先配置公司筛选条件" });
      }

      // 删除现有的系统自动标签（公司筛选配置更新时自动清理）
      await db.investorTag.deleteMany({
        where: {
          organizationId,
          tagCategory: "system",
          companyFilterId: companyFilter.id
        }
      });

      // 调用同花顺API获取持仓数据（这里需要实际的API调用逻辑）
      // const holdingData = await callThirdPartyAPI(companyFilter);

      // 模拟数据处理逻辑
      const mockHoldingData = [
        { investorCode: "001001", holdingType: "持仓本司" },
        { investorCode: "001002", holdingType: "持仓对标" },
        { investorCode: "001003", holdingType: "持仓其他公司" },
      ];

      // 批量创建系统自动标签
      const newTags = await db.investorTag.createMany({
        data: mockHoldingData.map(item => ({
          organizationId,
          companyFilterId: companyFilter.id,
          investorCode: item.investorCode,
          tagName: item.holdingType,
          tagCategory: "system" as const,
          tagMetadata: {
            autoGenerated: true,
            syncTime: new Date().toISOString()
          }
        }))
      });

      c.set("response", {
        code: 200,
        message: "系统标签同步成功",
        data: {
          syncedCount: newTags.count,
          companyCode: companyFilter.companyCode,
          benchmarkCompanyCodes: companyFilter.benchmarkCompanyCodes,
          syncTime: new Date().toISOString()
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

## 5. 投资人联系人功能实施

### 5.1 创建联系人实施（contacts/create.ts）

```typescript
/**
 * 创建投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-07 16:09:38
 */
export const contactsCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, name, phoneNumber, email, address, remarks } = validationResult.data;

      // 创建联系人记录
      const newContact = await db.investorContact.create({
        data: {
          organizationId,
          name,
          phoneNumber: phoneNumber || "",
          email: email || "",
          address: address || "",
          remarks: remarks || "",
          createdBy: user.id,
        }
      });

      c.set("response", {
        code: 200,
        message: "联系人创建成功",
        data: {
          contactId: newContact.contactId
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

### 5.2 更新联系人实施（contacts/update.ts）

```typescript
/**
 * 更新投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-07 16:09:38
 */
export const contactsUpdateRouter = new Hono().post(
  "/update",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = UpdateInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { contactId, organizationId, name, phoneNumber, email, address, remarks, updatedBy } = validationResult.data;

      // 验证联系人是否存在且属于指定组织
      const existingContact = await db.investorContact.findFirst({
        where: { contactId, organizationId }
      });

      if (!existingContact) {
        throw new HTTPException(404, { message: "联系人不存在" });
      }

      // 更新联系人信息
      const updatedContact = await db.investorContact.update({
        where: { contactId },
        data: {
          ...(name !== undefined ? { name } : {}),
          ...(phoneNumber !== undefined ? { phoneNumber } : {}),
          ...(email !== undefined ? { email } : {}),
          ...(address !== undefined ? { address } : {}),
          ...(remarks !== undefined ? { remarks } : {}),
          updatedBy: updatedBy || user.id,
        }
      });

      c.set("response", {
        code: 200,
        message: "联系人更新成功",
        data: {
          contactId: updatedContact.contactId
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

### 5.3 删除联系人实施（contacts/delete.ts）

```typescript
/**
 * 删除投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-07 19:13:44
 */
export const contactsDeleteRouter = new Hono().post(
  "/delete",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = DeleteInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { contactId, organizationId } = validationResult.data;

      // 查找联系人记录
      const existingContact = await db.investorContact.findFirst({
        where: {
          contactId,
          organizationId,
        }
      });

      if (!existingContact) {
        c.set("response", {
          code: 404,
          message: "联系人不存在或无权限访问",
          data: null
        });
        return;
      }

      // 删除联系人记录
      await db.investorContact.delete({
        where: { contactId }
      });

      c.set("response", {
        code: 200,
        message: "联系人删除成功",
        data: {
          contactId: existingContact.contactId
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

### 5.4 查询联系人列表实施（contacts/list.ts）

```typescript
/**
 * 查询投资人联系人列表路由
 *
 * <AUTHOR>
 * @date 2025-07-07 16:09:38
 */
export const contactsListRouter = new Hono().post(
  "/list",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");

      // 参数验证
      const validationResult = ListInvestorContactsSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, name, page, limit } = validationResult.data;

      // 构建查询条件
      const where = {
        organizationId,
        ...(name ? { name: { contains: name } } : {}),
      };

      // 查询数据
      const [total, contacts] = await Promise.all([
        db.investorContact.count({ where }),
        db.investorContact.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        })
      ]);

      c.set("response", {
        code: 200,
        message: "查询成功",
        data: {
          contacts: contacts.map(contact => ({
            contactId: contact.contactId,
            name: contact.name,
            phoneNumber: contact.phoneNumber,
            email: contact.email,
            address: contact.address,
            remarks: contact.remarks,
            createdAt: contact.createdAt.toISOString(),
            updatedAt: contact.updatedAt ? contact.updatedAt.toISOString() : contact.createdAt.toISOString(),
            createdBy: contact.createdBy,
            updatedBy: contact.updatedBy || null,
          })),
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
      return;

    } catch (error) {
      // 错误处理...
    }
  }
);
```

## 6. 统一错误处理机制

### 6.1 错误处理模式

```typescript
// 统一错误处理逻辑
catch (error) {
  if (error instanceof HTTPException) {
    c.set("response", {
      code: error.status,
      message: error.message,
      data: null
    });
    return;
  }
  
  console.error("投资人管理API错误:", error);
  c.set("response", {
    code: 500,
    message: "服务器内部错误",
    data: null
  });
  return;
}
```

## 7. 集成到主应用

### 7.1 在app.ts中注册路由

```typescript
// packages/api/src/app.ts
import { investorManagementRouter } from "./routes/investor-management/router";

const appRouter = app
  // ... 其他路由
  .route("/", investorManagementRouter); // 添加投资人管理路由
```

## 8. 类型定义实施（types.ts）

### 8.1 请求类型定义

```typescript
/**
 * 投资人管理模块类型定义
 *
 * <AUTHOR>
 * @date 2025-07-08 18:52:46
 * @updated 2025-07-08 18:52:46 hayden 根据新的数据库设计方案更新类型定义，支持标签系统和公司筛选配置
 */
import type { z } from "zod";
import {
  CreateCompanyFilterSchema,
  GetCompanyFilterSchema,
  CreateInvestorTagSchema,
  DeleteInvestorTagSchema,
  ListInvestorTagsSchema,
  SyncInvestorTagsSchema,
  CreateInvestorContactSchema,
  UpdateInvestorContactSchema,
  DeleteInvestorContactSchema,
  ListInvestorContactsSchema,
} from "./lib/validators";

// 公司筛选配置请求类型
export type CreateCompanyFilterRequest = z.infer<typeof CreateCompanyFilterSchema>;
export type GetCompanyFilterRequest = z.infer<typeof GetCompanyFilterSchema>;

// 投资人标签请求类型
export type CreateInvestorTagRequest = z.infer<typeof CreateInvestorTagSchema>;
export type DeleteInvestorTagRequest = z.infer<typeof DeleteInvestorTagSchema>;
export type ListInvestorTagsRequest = z.infer<typeof ListInvestorTagsSchema>;
export type SyncInvestorTagsRequest = z.infer<typeof SyncInvestorTagsSchema>;

// 投资人联系人请求类型
export type CreateInvestorContactRequest = z.infer<typeof CreateInvestorContactSchema>;
export type UpdateInvestorContactRequest = z.infer<typeof UpdateInvestorContactSchema>;
export type DeleteInvestorContactRequest = z.infer<typeof DeleteInvestorContactSchema>;
export type ListInvestorContactsRequest = z.infer<typeof ListInvestorContactsSchema>;
```

### 8.2 响应类型定义

```typescript
// 公司筛选配置响应类型
export interface CreateCompanyFilterResponse {
  id: string;
}

export interface GetCompanyFilterResponse {
  companyFilter: {
    id: string;
    organizationId: string;
    companyCode: string;
    benchmarkCompanyCodes: string;
    modifiedAt: string;
  };
  investorTags: InvestorTagItem[];
}

// 投资人标签响应类型
export interface CreateInvestorTagResponse {
  id: string;
}

export interface DeleteInvestorTagResponse {
  id: string;
}

export interface InvestorTagItem {
  id: string;
  investorCode: string;
  tagName: string;
  tagCategory: string;
  tagMetadata: Record<string, any>;
  modifiedAt: string;
  organizationName: string;
  companyFilter?: {
    companyCode: string;
    benchmarkCompanyCodes: string;
  } | null;
}

export interface ListInvestorTagsResponse {
  companyFilter: {
    id: string;
    companyCode: string;
    benchmarkCompanyCodes: string;
    modifiedAt: string;
  };
  tags: InvestorTagItem[];
  pagination: Pagination;
}

export interface SyncInvestorTagsResponse {
  syncedCount: number;
  companyCode: string;
  benchmarkCompanyCodes: string;
  syncTime: string;
}

// 投资人联系人响应类型
export interface CreateInvestorContactResponse {
  contactId: string;
}

export interface UpdateInvestorContactResponse {
  contactId: string;
}

export interface DeleteInvestorContactResponse {
  contactId: string;
}

export interface InvestorContactItem {
  contactId: string;
  name: string;
  phoneNumber: string;
  email: string;
  address: string;
  remarks: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string | null;
}

export interface ListInvestorContactsResponse {
  contacts: InvestorContactItem[];
  pagination: Pagination;
}

// 通用分页类型
export interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```


## 11. 实施步骤详细说明

### 11.1 第一阶段：基础架构搭建

1. **创建目录结构**
   ```bash
   mkdir -p packages/api/src/routes/investor-management/{company-filter,tags,contacts,lib}
   ```

2. **创建基础文件**
   - `router.ts`: 主路由聚合器
   - `types.ts`: TypeScript类型定义
   - `lib/validators.ts`: Zod验证器

### 11.2 第二阶段：公司筛选配置功能实施

1. **实施创建/更新公司筛选配置接口** (`company-filter/upsert.ts`)
   - 参数验证
   - 存在性检查
   - 数据库操作（统一创建/更新逻辑）
   - 响应构建

2. **实施获取公司筛选配置接口** (`company-filter/get.ts`)
   - 参数验证
   - 查询操作
   - 错误处理

### 11.3 第三阶段：投资人标签功能实施（基于标签系统的收藏）

1. **实施创建标签接口** (`tags/create.ts`)
   - 参数验证
   - 重复检查
   - 标签分类处理
   - 数据库操作

2. **实施删除用户收藏标签接口** (`tags/delete.ts`)
   - 仅支持删除用户收藏标签（取消收藏）
   - 系统标签通过公司筛选配置联动管理
   - 记录删除操作
   - 错误处理

3. **实施查询标签接口** (`tags/list.ts`)
   - 分页查询
   - 多条件筛选
   - 性能优化

4. **实施同步系统标签接口** (`tags/sync.ts`)
   - 调用第三方API
   - 公司筛选配置更新时自动删除旧系统标签
   - 批量创建新系统标签
   - 系统标签与公司筛选配置联动管理

### 11.4 第四阶段：投资人联系人功能实施

1. **实施创建联系人接口** (`contacts/create.ts`)
   - 备注字段限制300字符验证
2. **实施更新联系人接口** (`contacts/update.ts`)
   - 添加updatedBy字段追踪
   - 备注字段限制300字符验证
3. **实施删除联系人接口** (`contacts/delete.ts`)
   - 权限验证
   - 记录删除操作
4. **实施查询联系人接口** (`contacts/list.ts`)
   - updatedBy字段在未更新时为null

### 11.5 第五阶段：集成和测试

1. **集成到主应用**
   - 在 `app.ts` 中注册路由
   - 验证路由正确性

2. **使用api post接口工具进行功能测试**
   - 单元测试
   - 集成测试
   - 错误场景测试
   - 级联删除测试


