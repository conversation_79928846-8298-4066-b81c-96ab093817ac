# 投资人管理API文档

**作者**: hayden
**创建时间**: 2025-07-09 19:02:52
**更新时间**: 2025-07-09 19:06:43
**版本**: v1.1

## 1. 概述

投资人管理模块提供了完整的投资人信息管理功能，包括公司筛选配置、投资人标签管理（基于标签系统的收藏功能）和投资人联系人管理。所有API接口都采用POST方法，支持请求/响应加解密，需要用户身份验证。

### 1.1 基础信息

- **基础路径**: `/api/investor-management`
- **认证方式**: Bearer Token（通过authMiddleware验证）
- **加密方式**: 请求和响应数据通过shareholderCryptoMiddleware进行加解密
- **数据格式**: JSON

### 1.2 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

## 2. 公司筛选配置API

### 2.1 创建/更新公司筛选配置

**接口路径**: `POST /api/investor-management/company-filter/upsert`

**功能描述**: 创建或更新组织的公司筛选配置，包括本司公司代码和对标公司代码。如果配置已存在则更新，不存在则创建。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| organizationId | string | 是 | 组织ID | 不能为空 |
| companyCode | string | 是 | 本司公司代码 | 不能为空 |
| benchmarkCompanyCodes | string | 否 | 对标公司代码 | 多个代码用逗号分隔，可选 |

#### 请求示例

```json
{
  "organizationId": "org_123456",
  "companyCode": "000001",
  "benchmarkCompanyCodes": "000002,000003,000004"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "公司筛选配置保存成功",
  "data": {
    "id": "filter_789012"
  }
}
```

## 3. 投资人标签管理API

### 3.1 创建投资人标签（收藏功能）

**接口路径**: `POST /api/investor-management/tags/create`

**功能描述**: 基于标签系统实现的收藏功能，支持用户自定义标签和系统自动标签。如果标签已存在，返回已标记信息。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| organizationId | string | 是 | 组织ID | 不能为空 |
| investorCode | string | 是 | 投资人代码（基金代码） | 不能为空 |
| tagName | string | 是 | 标签名称 | 不能为空 |
| tagCategory | string | 是 | 标签分类 | 枚举值：system（系统自动生成）、user（用户操作生成） |
| tagMetadata | object | 否 | 标签元数据 | JSON对象，存储标签的额外配置信息 |

#### 请求示例

```json
{
  "organizationId": "org_123456",
  "investorCode": "159001",
  "tagName": "收藏",
  "tagCategory": "user",
  "tagMetadata": {
    "color": "#ff6b6b",
    "icon": "star"
  }
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "标签创建成功",
  "data": {
    "id": "tag_345678"
  }
}
```

### 3.2 删除投资人标签（取消收藏）

**接口路径**: `POST /api/investor-management/tags/delete`

**功能描述**: 删除用户收藏标签（取消收藏功能）。仅支持删除用户收藏标签，系统标签与公司筛选配置挂钩，会自动管理。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| organizationId | string | 是 | 组织ID | 不能为空 |
| id | string | 是 | 标签记录ID | 不能为空 |

#### 请求示例

```json
{
  "organizationId": "org_123456",
  "id": "tag_345678"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "取消收藏成功",
  "data": {
    "id": "tag_345678"
  }
}
```

## 4. 投资人联系人管理API

### 4.1 创建投资人联系人

**接口路径**: `POST /api/investor-management/contacts/create`

**功能描述**: 创建投资人联系人信息，支持姓名、电话、邮箱、地址和备注信息。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| organizationId | string | 是 | 组织ID | 不能为空 |
| name | string | 是 | 联系人姓名 | 不能为空 |
| phoneNumber | string | 否 | 电话号码 | 可选 |
| email | string | 否 | 邮箱地址 | 邮箱格式验证，可选 |
| address | string | 否 | 联系地址 | 可选 |
| remarks | string | 否 | 备注信息 | 最大300字符，可选 |

#### 请求示例

```json
{
  "organizationId": "org_123456",
  "name": "张三",
  "phoneNumber": "13800138000",
  "email": "<EMAIL>",
  "address": "北京市朝阳区xxx街道xxx号",
  "remarks": "重要联系人"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "联系人创建成功",
  "data": {
    "contactId": "contact_901234"
  }
}
```

### 4.2 更新投资人联系人

**接口路径**: `POST /api/investor-management/contacts/update`

**功能描述**: 更新投资人联系人信息，支持部分字段更新，自动记录更新人和更新时间。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| contactId | string | 是 | 联系人ID | 不能为空 |
| organizationId | string | 是 | 组织ID | 不能为空 |
| name | string | 否 | 联系人姓名 | 不能为空（如果提供） |
| phoneNumber | string | 否 | 电话号码 | 可选 |
| email | string | 否 | 邮箱地址 | 邮箱格式验证，可选 |
| address | string | 否 | 联系地址 | 可选 |
| remarks | string | 否 | 备注信息 | 最大300字符，可选 |

#### 请求示例

```json
{
  "contactId": "contact_901234",
  "organizationId": "org_123456",
  "name": "张三",
  "phoneNumber": "13900139000",
  "email": "<EMAIL>"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "联系人更新成功",
  "data": {
    "contactId": "contact_901234"
  }
}
```

### 4.3 删除投资人联系人

**接口路径**: `POST /api/investor-management/contacts/delete`

**功能描述**: 删除投资人联系人信息，需要验证权限和存在性。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| contactId | string | 是 | 联系人ID | 不能为空 |
| organizationId | string | 是 | 组织ID | 不能为空 |

#### 请求示例

```json
{
  "contactId": "contact_901234",
  "organizationId": "org_123456"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "联系人删除成功",
  "data": {
    "contactId": "contact_901234"
  }
}
```

### 4.4 查询投资人联系人列表

**接口路径**: `POST /api/investor-management/contacts/list`

**功能描述**: 查询投资人联系人列表，支持分页、姓名/手机号码/邮箱搜索和排序功能。

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| organizationId | string | 是 | 组织ID | 不能为空 |
| name | string | 否 | 联系人姓名（模糊搜索） | 可选 |
| phoneNumber | string | 否 | 手机号码（模糊搜索） | 可选 |
| email | string | 否 | 邮箱地址（模糊搜索） | 可选 |
| page | number | 否 | 页码 | 正整数，默认值：1 |
| limit | number | 否 | 每页数量 | 正整数，最大100，默认值：10 |

#### 请求示例

```json
{
  "organizationId": "org_123456",
  "name": "张",
  "phoneNumber": "138",
  "email": "example.com",
  "page": 1,
  "limit": 10
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "contacts": [
      {
        "contactId": "contact_901234",
        "organizationId": "org_123456",
        "name": "张三",
        "phoneNumber": "13900139000",
        "email": "<EMAIL>",
        "address": "北京市朝阳区xxx街道xxx号",
        "remarks": "重要联系人",
        "createdAt": "2025-07-09T11:00:00.000Z",
        "updatedAt": "2025-07-09T11:30:00.000Z",
        "createdBy": "user_123",
        "updatedBy": "user_123"
      }
    ]
  }
}
```

## 5. 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数无效 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 检查认证token是否有效 |
| 403 | 权限不足 | 检查用户权限，系统标签不能手动删除 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 6. 注意事项

1. **认证要求**: 所有接口都需要通过authMiddleware进行用户身份验证
2. **加密传输**: 请求和响应数据通过shareholderCryptoMiddleware进行加解密
3. **标签系统**: 系统标签（tagCategory="system"）与公司筛选配置关联，会自动管理；用户标签（tagCategory="user"）支持手动创建和删除
4. **数据验证**: 所有输入参数都会通过Zod验证器进行严格验证
5. **组织隔离**: 所有数据都按organizationId进行隔离，确保多租户数据安全

## 7. 数据库模型关系

- **CompanyFilter**: 公司筛选配置表，与Organization一对一关系
- **InvestorTag**: 投资人标签表，与Organization多对一关系，可选关联CompanyFilter
- **InvestorContact**: 投资人联系人表，与Organization多对一关系

---

**文档维护**: 本文档基于实际API代码生成，如有API变更请及时更新文档。
