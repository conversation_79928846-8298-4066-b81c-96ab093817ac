# 投资人管理API设计文档

**作者**: hayden
**创建时间**: 2025-07-08 19:28:19
**更新时间**: 2025-07-08 19:28:19
**版本**: v2.0

## 1. 概述

基于投资人管理模块数据库设计方案，本文档详细描述投资人标签管理（基于标签系统的收藏功能）、公司筛选配置管理和投资人联系人管理的API接口设计。API遵循项目现有的架构模式，包括认证中间件、加解密中间件和统一错误处理机制。

## 2. API架构设计

### 2.1 路由结构

```
/api/investor-management/
├── company-filter/     # 公司筛选配置相关接口
│   ├── create         # 创建/更新公司筛选配置
│   ├── get            # 获取公司筛选配置及关联标签
│   └── update         # 更新公司筛选配置
├── tags/              # 投资人标签相关接口（基于标签系统的收藏功能）
│   ├── create         # 创建标签（收藏）
│   ├── delete         # 删除标签（取消收藏）
│   ├── list           # 查询标签列表
│   └── sync           # 同步系统自动标签
└── contacts/          # 投资人联系人相关接口
    ├── create         # 创建联系人
    ├── update         # 更新联系人
    ├── delete         # 删除联系人
    └── list           # 查询联系人列表
```

### 2.2 中间件应用

所有API接口统一应用以下中间件：

- `authMiddleware`: 用户身份验证
- `shareholderCryptoMiddleware`: 请求/响应加解密
- 统一错误处理机制
## 3. 公司筛选配置API设计

### 3.1 创建/更新公司筛选配置

**接口路径**: `POST /api/investor-management/company-filter/create`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| companyCode | string | 是 | 本司公司代码 |
| benchmarkCompanyCodes | string | 是 | 对标公司代码（多个代码用逗号分隔） |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef",
  "companyCode": "000001",
  "benchmarkCompanyCodes": "000002,000003,000004"
}
```

**响应数据**:

```typescript
interface CreateCompanyFilterResponse {
  id: string;                      // 配置记录ID
  organizationId: string;          // 组织ID
  companyCode: string;             // 本司公司代码
  benchmarkCompanyCodes: string;   // 对标公司代码
  modifiedAt: string;              // 修改时间
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "公司筛选配置创建成功",
  "data": {
    "id": "clx9876543210fedcba",
    "organizationId": "clx1234567890abcdef",
    "companyCode": "000001",
    "benchmarkCompanyCodes": "000002,000003,000004",
    "modifiedAt": "2025-07-08T19:28:19.839Z"
  }
}
```

**更新响应示例**:

```json
{
  "code": 200,
  "message": "公司筛选配置更新成功",
  "data": {
    "id": "clx9876543210fedcba",
    "organizationId": "clx1234567890abcdef",
    "companyCode": "000001",
    "benchmarkCompanyCodes": "000002,000003,000005",
    "modifiedAt": "2025-07-08T19:28:19.839Z"
  }
}
```
### 3.2 获取公司筛选配置及关联标签

**接口路径**: `POST /api/investor-management/company-filter/get`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef"
}
```

**响应数据**:

```typescript
interface GetCompanyFilterResponse {
  companyFilter: {
    id: string;                      // 配置记录ID
    organizationId: string;          // 组织ID
    companyCode: string;             // 本司公司代码
    benchmarkCompanyCodes: string;   // 对标公司代码
    modifiedAt: string;              // 修改时间
  };
  investorTags: InvestorTagItem[];   // 关联的投资人标签
}

interface InvestorTagItem {
  id: string;                        // 标签记录ID
  investorCode: string;              // 投资人代码
  tagName: string;                   // 标签名称
  tagCategory: string;               // 标签分类（system/user/custom）
  tagMetadata: Record<string, any>;  // 标签元数据
  modifiedAt: string;                // 修改时间
  organizationName: string;          // 组织名称
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "companyFilter": {
      "id": "clx9876543210fedcba",
      "organizationId": "clx1234567890abcdef",
      "companyCode": "000001",
      "benchmarkCompanyCodes": "000002,000003,000004",
      "modifiedAt": "2025-07-08T19:28:19.839Z"
    },
    "investorTags": [
      {
        "id": "clx1111222233334444",
        "investorCode": "INV_000001",
        "tagName": "持仓本司",
        "tagCategory": "system",
        "tagMetadata": {
          "autoGenerated": true,
          "syncTime": "2025-07-08T19:28:19.839Z"
        },
        "modifiedAt": "2025-07-08T19:28:19.839Z",
        "organizationName": "测试组织"
      }
    ]
  }
}
```

**错误响应示例**:

```json
{
  "code": 404,
  "message": "公司筛选配置不存在",
  "data": null
}
```
## 4. 投资人标签API设计（基于标签系统的收藏功能）

### 4.1 创建投资人标签（收藏）

**接口路径**: `POST /api/investor-management/tags/create`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| investorCode | string | 是 | 投资人代码 |
| tagName | string | 是 | 标签名称 |
| tagCategory | string | 是 | 标签分类（system/user/custom） |
| tagMetadata | object | 否 | 标签元数据 |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef",
  "investorCode": "INV_000001",
  "tagName": "重点关注",
  "tagCategory": "user",
  "tagMetadata": {
    "priority": "high",
    "notes": "重要客户"
  }
}
```

**响应数据**:

```typescript
interface CreateInvestorTagResponse {
  id: string;                // 标签记录ID
  investorCode: string;      // 投资人代码
  tagName: string;           // 标签名称
  tagCategory: string;       // 标签分类
  modifiedAt: string;        // 修改时间
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "标签创建成功",
  "data": {
    "id": "clx9876543210fedcba",
    "investorCode": "INV_000001",
    "tagName": "重点关注",
    "tagCategory": "user",
    "modifiedAt": "2025-07-08T19:28:19.839Z"
  }
}
```

**已存在响应示例**:

```json
{
  "code": 200,
  "message": "该投资人已有此标签",
  "data": {
    "id": "clx9876543210fedcba",
    "investorCode": "INV_000001",
    "tagName": "重点关注",
    "tagCategory": "user",
    "modifiedAt": "2025-07-08T19:28:19.839Z"
  }
}
```

### 4.2 删除投资人标签（取消收藏）

**接口路径**: `POST /api/investor-management/tags/delete`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| id | string | 是 | 标签记录ID |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef",
  "id": "clx9876543210fedcba"
}
```

**响应数据**:

```typescript
interface DeleteInvestorTagResponse {
  investorCode: string;      // 投资人代码
  tagName: string;           // 标签名称
  tagCategory: string;       // 标签分类
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "标签删除成功",
  "data": {
    "investorCode": "INV_000001",
    "tagName": "持仓本司",
    "tagCategory": "system"
  }
}
```

**用户收藏标签不可删除响应示例**:

```json
{
  "code": 403,
  "message": "用户收藏标签不允许删除",
  "data": {
    "investorCode": "INV_000001",
    "tagName": "重点关注",
    "tagCategory": "user"
  }
}
```

**错误响应示例**:

```json
{
  "code": 404,
  "message": "标签记录不存在",
  "data": null
}
```


### 4.3 同步系统自动标签

**接口路径**: `POST /api/investor-management/tags/sync`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef"
}
```

**响应数据**:

```typescript
interface SyncInvestorTagsResponse {
  syncedCount: number;               // 同步的标签数量
  companyCode: string;               // 本司公司代码
  benchmarkCompanyCodes: string;     // 对标公司代码
  syncTime: string;                  // 同步时间
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "系统标签同步成功",
  "data": {
    "syncedCount": 150,
    "companyCode": "000001",
    "benchmarkCompanyCodes": "000002,000003,000004",
    "syncTime": "2025-07-08T19:28:19.839Z"
  }
}
```

**错误响应示例**:

```json
{
  "code": 404,
  "message": "请先配置公司筛选条件",
  "data": null
}
```

## 5. 投资人联系人API设计

### 5.1 创建投资人的联系人

**接口路径**: `POST /api/investor-management/contacts/create`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| name | string | 是 | 联系人姓名 |
| phoneNumber | string | 否 | 电话号码 |
| email | string | 否 | 邮箱地址 |
| address | string | 否 | 联系地址 |
| remarks | string | 否 | 备注信息（最大300字符） |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef",
  "name": "张经理",
  "phoneNumber": "13800138000",
  "email": "<EMAIL>",
  "address": "北京市朝阳区金融街88号",
  "remarks": "投资人客户经理，负责机构业务"
}
```

**响应数据**:

```typescript
interface CreateInvestorContactResponse {
  contactId: string;         // 联系人ID
  name: string;              // 联系人姓名
  createdAt: string;         // 创建时间
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "联系人创建成功",
  "data": {
    "contactId": "clx5555666677778888",
    "name": "张经理",
    "createdAt": "2025-07-07T16:28:12.789Z"
  }
}
```

**错误响应示例**:

```json
{
  "code": 400,
  "message": "联系人姓名不能为空",
  "data": null
}
```
### 5.2 更新投资人的联系人

**接口路径**: `POST /api/investor-management/contacts/update`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contactId | string | 是 | 联系人ID |
| organizationId | string | 是 | 组织ID |
| name | string | 否 | 联系人姓名 |
| phoneNumber | string | 否 | 电话号码 |
| email | string | 否 | 邮箱地址 |
| address | string | 否 | 联系地址 |
| remarks | string | 否 | 备注信息（最大300字符） |

**请求示例**:

```json
{
  "contactId": "clx5555666677778888",
  "organizationId": "clx1234567890abcdef",
  "phoneNumber": "13900139000",
  "email": "<EMAIL>",
  "remarks": "投资人高级客户经理，负责机构业务和产品咨询"
}
```

**响应数据**:

```typescript
interface UpdateInvestorContactResponse {
  contactId: string;         // 联系人ID
  name: string;              // 联系人姓名
  updatedAt: string;         // 更新时间
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "联系人更新成功",
  "data": {
    "contactId": "clx5555666677778888",
    "name": "张经理",
    "updatedAt": "2025-07-07T16:28:12.456Z"
  }
}
```

**错误响应示例**:

```json
{
  "code": 404,
  "message": "联系人不存在或无权限访问",
  "data": null
}
```
### 5.3 删除投资人的联系人

**接口路径**: `POST /api/investor-management/contacts/delete`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contactId | string | 是 | 联系人ID |
| organizationId | string | 是 | 组织ID |

**请求示例**:

```json
{
  "contactId": "clx5555666677778888",
  "organizationId": "clx1234567890abcdef"
}
```

**响应数据**:

```typescript
interface DeleteInvestorContactResponse {
  contactId: string;         // 联系人ID
  name: string;              // 联系人姓名
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "联系人删除成功",
  "data": {
    "contactId": "clx5555666677778888",
    "name": "张经理"
  }
}
```

**错误响应示例**:

```json
{
  "code": 404,
  "message": "联系人不存在或无权限访问",
  "data": null
}
```

### 5.4 查询投资人的联系人列表

**接口路径**: `POST /api/investor-management/contacts/list`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| name | string | 否 | 联系人姓名（模糊查询） |
| page | number | 否 | 页码（默认1） |
| limit | number | 否 | 每页条数（默认10） |

**请求示例**:

```json
{
  "organizationId": "clx1234567890abcdef",
  "name": "张",
  "page": 1,
  "limit": 10
}
```

**响应数据**:

```typescript
interface ListInvestorContactsResponse {
  contacts: InvestorContactItem[];
  pagination: Pagination;
}

interface InvestorContactItem {
  contactId: string;         // 联系人ID
  name: string;              // 联系人姓名
  phoneNumber: string;       // 电话号码
  email: string;             // 邮箱地址
  address: string;           // 联系地址
  remarks: string;           // 备注信息
  createdAt: string;         // 创建时间
  updatedAt: string;         // 更新时间
  createdBy: string;         // 创建人
  updatedBy: string | null;  // 更新人（如果没有更新过则为null）
}
```

**成功响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "contacts": [
      {
        "contactId": "clx5555666677778888",
        "name": "张经理",
        "phoneNumber": "13900139000",
        "email": "<EMAIL>",
        "address": "北京市朝阳区金融街88号",
        "remarks": "投资人高级客户经理，负责机构业务和产品咨询",
        "createdAt": "2025-07-07T16:28:12.789Z",
        "updatedAt": "2025-07-07T16:28:12.456Z",
        "createdBy": "clx_user_123456",
        "updatedBy": "clx_user_123456"
      },
      {
        "contactId": "clx9999000011112222",
        "name": "张总监",
        "phoneNumber": "13700137000",
        "email": "<EMAIL>",
        "address": "上海市浦东新区陆家嘴环路1000号",
        "remarks": "投资机构投资总监",
        "createdAt": "2025-07-06T14:20:10.123Z",
        "updatedAt": null,
        "createdBy": "clx_user_789012",
        "updatedBy": null
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "limit": 10,
      "totalPages": 2
    }
  }
}
```
## 6. 通用类型定义

### 6.1 分页信息

```typescript
interface Pagination {
  total: number;             // 总记录数
  page: number;              // 当前页码
  limit: number;             // 每页条数
  totalPages: number;        // 总页数
}
```

### 6.2 统一响应格式

```typescript
interface ApiResponse<T = any> {
  code: number;              // 响应状态码
  message: string;           // 响应消息
  data: T;                   // 响应数据
}
```

## 7. 错误处理

### 7.1 错误码定义

- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误

### 7.2 错误响应格式

```typescript
interface ErrorResponse {
  code: number;              // 错误状态码
  message: string;           // 错误消息
  data: null;                // 错误时数据为null
}
```

## 8. 重要说明

### 8.1 标签系统特性

- **系统自动标签**: 由数据系统根据第三方API自动生成（如"持仓本司"、"持仓对标"、"持仓其他公司"）
- **用户收藏标签**: 用户手动创建的收藏标签，不允许删除
- **自定义标签**: 用户可创建和删除的自定义标签
- **标签关联**: 系统标签与公司筛选配置关联，用户标签独立存在

### 8.2 业务规则

- `remarks`字段限制最大300字符
- `updatedBy`字段在联系人未被更新过时为`null`
- 删除投资人标签时使用标签记录的`id`而非`investorCode`
- 用户收藏标签（tagCategory为"user"）不允许删除
- 同步系统标签前需要先配置公司筛选条件
- 公司筛选配置支持创建和更新操作，同一组织只能有一个配置

### 8.3 数据一致性

- 修改公司筛选配置时，会级联删除相关的系统自动标签
- 标签同步操作会先删除现有系统标签，再创建新的系统标签
- 所有时间字段统一使用ISO 8601格式

---

**文档更新记录**:
- 2025-07-08 19:28:19 hayden 根据投资人管理API实施方案更新，将收藏功能改为基于标签系统设计，增加公司筛选配置功能
