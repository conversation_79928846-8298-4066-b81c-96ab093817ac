# 投资人管理模块数据库设计方案

## 1. 项目背景

基于当前股东名册管理系统的成功实施，现需要扩展系统功能，增加投资人管理模块。该模块主要包含两个核心功能：

1. 组织下的投资人收藏管理
2. 组织下的投资人联系人管理

## 2. 现有数据库表结构分析

### 2.1 核心表结构概览

当前系统已建立完善的用户-组织-权限体系：

- **User表**: 用户基础信息，包含认证、权限等字段
- **Organization表**: 组织信息，支持多租户架构
- **Member表**: 用户与组织的关联关系，包含角色权限
- **Session表**: 会话管理，支持组织切换

### 2.2 数据关联模式分析

现有表遵循以下设计模式：

- 所有业务数据都通过 `organizationId` 与组织关联
- 使用级联删除 (`onDelete: Cascade`) 确保数据一致性
- 采用 `@default(cuid())` 生成唯一主键
- 建立合理的索引优化查询性能
- 使用 `@@unique` 约束防止重复数据

## 3. 投资人管理模块设计方案

### 3.1 公司筛选表 (CompanyFilter)

#### 3.1.1 表结构设计
```prisma
/**
 * 公司筛选表 - 存储组织级别的本司和对标公司代码配置
 * <AUTHOR>
 * @created 2025-07-08 14:22:25
 * @updated 2025-07-08 18:36:13 hayden 将组织公司代码配置表改为公司筛选表，并添加与InvestorTag表的关联
 * @description 组织级别的公司代码配置，用于标签系统的基础数据，与投资人标签表建立关联关系
 */
model CompanyFilter {
  id                    String       @id @default(cuid()) // 主键，自动生成
  organizationId        String       @unique // 关联的组织ID（一个组织只能有一套配置）
  organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyCode           String       // 本司公司代码
  benchmarkCompanyCodes String       // 对标公司代码（多个用逗号分隔）
  modifiedAt            DateTime     @default(now()) @updatedAt // 变更时间（创建和更新都使用此字段）

  // 关联关系：与投资人标签表建立关联，当公司筛选配置删除时，相关标签也会被级联删除
  investorTags          InvestorTag[] // 关联的投资人标签

  @@map("company_filter") // 映射到数据库表"company_filter"
}
```

### 3.2 投资人标签表 (InvestorTag)

#### 3.2.1 表结构设计
```prisma
/**
 * 投资人标签表 - 通用标签系统
 * <AUTHOR>
 * @created 2025-07-08 14:22:25
 * @updated 2025-07-08 17:04:48 hayden 改造标签类型设计，避免硬编码枚举
 * @updated 2025-07-08 18:36:13 hayden 添加与公司筛选表的关联关系，实现级联删除
 * @description 支持系统自动标签（持仓类型）和用户操作标签（收藏）的统一管理，与公司筛选表建立关联
 */
model InvestorTag {
  id               String        @id @default(cuid()) // 主键，自动生成
  organizationId   String        // 关联的组织ID
  organization     Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyFilterId  String?       // 关联的公司筛选配置ID（可选，仅系统自动生成的持仓标签需要）
  companyFilter    CompanyFilter? @relation(fields: [companyFilterId], references: [id], onDelete: Cascade) // 关联公司筛选配置，级联删除
  investorCode     String        // 投资人代码（基金代码）
  tagName          String        // 标签名称（如：持有本司, 收藏等，支持动态）
  tagCategory      String        // 标签分类（system: 系统自动生成, user: 用户操作生成, custom: 自定义标签）
  tagMetadata      Json?         // 标签元数据（存储标签的额外配置信息，如颜色、图标、排序等）
  modifiedAt       DateTime      @default(now()) @updatedAt // 变更时间（创建和更新都使用此字段）

  @@index([investorCode]) // 基金代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([companyFilterId]) // 公司筛选配置ID索引
  @@index([tagName]) // 标签名称索引
  @@index([tagCategory]) // 标签分类索引
  @@index([modifiedAt]) // 变更时间索引
  @@unique([organizationId, investorCode, tagName]) // 唯一约束：防重复标签
  @@map("investor_tag") // 映射到数据库表"investor_tag"
}
```

#### 3.2.2 设计说明

**完全动态标签系统解决方案**：
1. **tagName字段**：标签名称，既是标识符又是显示名称，完全动态，无需预定义
2. **tagCategory字段**：标签分类（system/user/custom），便于管理和权限控制
3. **tagMetadata字段**：JSON格式存储标签的元数据（颜色、图标、排序、描述等）
4. **modifiedAt字段**：统一的时间字段，记录创建和更新时间
5. **companyFilterId字段**：关联公司筛选配置，实现级联删除机制

**级联删除机制说明**：
- 系统自动生成的持仓标签（tagCategory = 'system'）关联 `companyFilterId`
- 用户操作的收藏标签（tagCategory = 'user'）不关联 `companyFilterId`（设为 null）
- 当公司筛选配置被更新时（本司代码或对标公司代码变更），只有关联的持仓标签会被级联删除，收藏标签得以保留

#### 3.2.3 业务流程说明

1. **公司筛选配置流程**：
   用户输入本司代码和对标公司代码 → 点击编辑按钮（可编辑状态）→ 点击刷新按钮 → 保存到 `CompanyFilter` 表

2. **系统自动标签生成流程**：
   保存公司筛选配置 → 调用同花顺API → 获取基金持仓数据 → 动态查询可用标签类型 → 自动生成持仓标签 → 保存到 `InvestorTag` 表（关联 `companyFilterId`）


3. **用户收藏流程**：
   用户点击基金卡片收藏按钮 → 动态查询收藏标签类型 → 创建收藏标签 → 保存到 `InvestorTag` 表（不关联 `companyFilterId`）


4. **公司筛选配置修改流程**：
   用户修改公司代码 → 通过级联删除自动删除旧的持仓标签（关联 `companyFilterId` 的标签） → 保留用户标签（`companyFilterId` 为 null 的标签） → 重新生成新的持仓标签

5. **动态标签类型扩展流程**：
   需要新增标签类型 → 通过API创建新标签类型记录 → 系统自动识别并使用新标签类型 → 无需修改任何代码

### 3.3 投资人联系人表 (InvestorContact)

#### 3.3.1 表结构设计
```prisma
/**
 * 投资人联系人表 - 存储投资人联系人信息
 * <AUTHOR>
 * @created 2025-07-08 14:22:25
 * @description 组织级别的投资人联系人管理，支持联系人信息的增删改查
 */
model InvestorContact {
  contactId      String       @id @default(cuid()) // 联系人ID，自动生成
  organizationId String       // 关联的组织ID
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  name           String       // 联系人姓名（必填）
  phoneNumber    String?      // 电话号码
  email          String?      // 邮箱地址
  address        String?      // 联系地址
  remarks        String?      // 备注信息
  createdAt      DateTime     @default(now()) // 创建时间
  updatedAt      DateTime     @updatedAt // 更新时间
  createdBy      String?      // 创建人用户ID
  updatedBy      String?      // 更新人用户ID

  @@index([organizationId]) // 组织ID索引
  @@index([name]) // 姓名索引
  @@index([phoneNumber]) // 电话号码索引
  @@index([email]) // 邮箱索引
  @@index([createdAt]) // 创建时间索引
  @@map("investor_contact") // 映射到数据库表"investor_contact"
}
```

## 4. Organization表关联更新

需要在 `Organization` 模型中添加新的关联关系：

```prisma
model Organization {
  // ... 现有字段 ...

  // 新增关联关系
  investorTags                  InvestorTag[]                   // 关联的投资人标签
  investorContacts              InvestorContact[]               // 关联的投资人联系人
  companyFilters                CompanyFilter[]                 // 关联公司筛选表
  // ... 其他现有关联 ...
}
```

## 5. 数据库迁移策略

1. 修改 `schema.prisma` 文件，添加新模型定义
2. 应用迁移到数据库：`pnpm --filter database push`
3. 重新生成Prisma客户端：`pnpm --filter database generate`

---

## 4.1 标签表流程图
```mermaid
flowchart TD
    %% 样式定义
    classDef userAction fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef api fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef decision fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef cascade fill:#ffebee,stroke:#c62828,stroke-width:3px

    %% 1. 公司筛选配置流程
    A[用户输入本司代码和对标公司代码]:::userAction
    B[点击编辑按钮<br/>进入可编辑状态]:::userAction
    C[编辑完成后会变更保存按钮，点击保存按钮]:::userAction
    D[保存到CompanyFilter表<br/>更新modifiedAt字段]:::database

    %% 2. 系统自动标签生成流程
    E[调用同花顺API]:::api
    F[获取基金持仓数据]:::systemAction
    G[分析持仓关系<br/>本司/对标/其他公司]:::systemAction
    H[生成持仓标签<br/>tagCategory='system'<br/>companyFilterId=关联ID]:::systemAction
    I[保存到InvestorTag表<br/>建立关联关系]:::database

    %% 3. 用户收藏流程
    J[用户点击基金卡片收藏按钮]:::userAction
    K[创建收藏标签<br/>tagCategory='user'<br/>companyFilterId=null]:::systemAction
    L[保存到InvestorTag表<br/>不关联公司筛选配置]:::database

    %% 4. 公司筛选配置修改流程
    M[用户修改公司代码]:::userAction
    N{检查是否有关联的<br/>持仓标签?}:::decision
    O[级联删除旧的持仓标签<br/>WHERE companyFilterId IS NOT NULL<br/>AND tagCategory='system']:::cascade
    P[保留用户收藏标签<br/>WHERE companyFilterId IS NULL<br/>AND tagCategory='user']:::systemAction
    Q[更新CompanyFilter表<br/>modifiedAt字段自动更新]:::database
    R[重新生成新的持仓标签<br/>基于新的公司代码配置]:::systemAction

    %% 主流程连接
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I

    %% 收藏流程独立分支
    J --> K
    K --> L

    %% 修改流程
    M --> N
    N -->|存在关联标签| O
    N -->|无关联标签| Q
    O --> P
    P --> Q
    Q --> R
    R --> E

    %% 数据表关系说明
    subgraph "数据表关系与级联删除机制"
        direction TB
        CF[CompanyFilter 公司筛选表<br/>id, organizationId, companyCode<br/>benchmarkCompanyCodes, modifiedAt]:::database
        IT[InvestorTag 投资人标签表<br/>id, organizationId, companyFilterId<br/>investorCode, tagName, tagCategory<br/>tagMetadata, modifiedAt]:::database

        CF -.->|级联删除<br/>onDelete: Cascade| IT

        subgraph "标签类型说明"
            ST1[系统持仓标签<br/>companyFilterId ≠ null<br/>tagCategory = 'system']:::cascade
            ST2[用户收藏标签<br/>companyFilterId = null<br/>tagCategory = 'user']:::userAction
        end
    end

    %% 流程说明图例
    subgraph "图例说明"
        direction LR
        S1["🔵 用户操作"]:::userAction
        S2["🟣 系统处理"]:::systemAction
        S3["🟢 数据库操作"]:::database
        S4["🟠 外部API调用"]:::api
        S5["🔴 条件判断"]:::decision
        S6["🔴 级联删除"]:::cascade
    end

    %% 关键字段更新说明
    subgraph "关键字段更新机制"
        direction TB
        U1[modifiedAt字段<br/>自动更新时间戳<br/>@updatedAt装饰器]:::database
        U2[companyFilterId字段<br/>建立表间关联<br/>实现级联删除]:::database
        U3[tagCategory字段<br/>区分标签类型<br/>控制删除策略]:::database
    end
```
