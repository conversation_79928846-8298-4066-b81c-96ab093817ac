# 投资人联系人API字段扩展实施方案

## 文档信息
- **作者**: hayden
- **创建时间**: 2025-07-28 15:41:48
- **更新时间**: 2025-07-31 10:56:09
- **版本**: v2.2

## 概述

基于投资人联系人表字段扩展方案，本文档详细规划了投资人联系人API的修改实施方案，包括验证器更新、CRUD接口调整和业务逻辑优化，以支持基金代码和一码通ID的新增字段功能。特别新增了股东名册点击创建联系人的自动化业务流程。

## 业务需求回顾

### 核心功能需求
1. **投资人分类管理**: 区分基金投资者和个人投资者
2. **身份转换支持**: 个人投资者可转为基金投资者
3. **数据关联**: 与股东名册数据进行有效关联
4. **查询优化**: 支持按投资者类型、标识符查询
5. **股东名册集成**: 支持从股东名册直接创建联系人，自动识别基金类型

### 重要说明：投资者类型识别方案
**当前阶段（临时方案）**：
- 通过手动上传字段（fundCode/unifiedAccountId）来区分投资者类型
- 基于字段是否为空来判断是基金投资者还是个人投资者
- 这是过渡期的解决方案，数据准确性依赖于手动输入

**股东名册集成方案（新增）**：
- 支持从股东名册点击创建联系人，携带一码通账号
- 根据一码通账号查询Shareholder表获取securitiesAccountName（股东名称）
- 使用股东名称查询FundManagementCustody表匹配基金全称
- 自动识别基金类型并填充fundCode字段
- 优先级规则：当查询到多个基金代码时，优先选择带A或a的代码
- 托管人匹配：支持模糊匹配，因为数据库中只有简称（如"中国银行"），而股东名称中是全称（如"中国银行股份有限公司"）

**查询联系人方案（更新）**：
- 主要查询方式：通过基金代码（fundCode）或一码通账号（unifiedAccountId）查询联系人
- 这两个标识是查询联系人的核心标识符，替代了原有的姓名模糊查询方式
- 支持投资者类型筛选作为辅助查询功能

**未来完善方案**：
- 投资人数据源开发完成后，将通过查询投资人数据库来自动识别投资者类型
- 系统将根据投资人代码/姓名自动判断该联系人是个人还是基金
- 届时将减少对手动字段的依赖，提高数据准确性和一致性
- 当前的字段设计将保留，作为数据源查询的补充和备份

### 新增字段
- `fundCode`: 基金代码（String?）
- `unifiedAccountId`: 一码通ID（String?）

### 股东名册业务逻辑
**股东名称示例分析**：
- 示例：`中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金`
- 托管人：`中国银行股份有限公司`（在FundManagementCustody.custodian字段中为简称"中国银行"）
- 基金全称：`易方达医疗保健行业混合型证券投资基金`

**基金代码优先级规则**：
```
数据示例：
019020.OF	易方达医疗保健行业混合C	易方达医疗保健行业混合型证券投资基金	中国银行	易方达基金管理有限公司
110023.OF	易方达医疗保健行业混合A	易方达医疗保健行业混合型证券投资基金	中国银行	易方达基金管理有限公司

优先级：110023.OF（带A）> 019020.OF（带C）
```

## 实施计划

### 阶段一：验证器更新（当前实施）
**预计时间**: 1.5小时
**文件**: `packages/api/src/routes/investor-management/lib/validators.ts`
**说明**: 实现基于字段的临时投资者类型识别，支持股东名册创建联系人的验证逻辑

### 阶段二：基金查询工具函数（新增）
**预计时间**: 2小时
**文件**: `packages/api/src/routes/investor-management/lib/fund-utils.ts`（新建）
**说明**: 实现股东名称到基金代码的查询逻辑，包含优先级规则

### 阶段三：CRUD接口调整（当前实施）
**预计时间**: 4小时
**文件**:
- `packages/api/src/routes/investor-management/contacts/create.ts`
- `packages/api/src/routes/investor-management/contacts/update.ts`
- `packages/api/src/routes/investor-management/contacts/list.ts`
**说明**: 支持新字段的CRUD操作、股东名册创建联系人逻辑和基础类型筛选

### 阶段四：测试验证（当前实施）
**预计时间**: 3小时
**内容**: API接口测试、业务逻辑验证、股东名册集成测试
**说明**: 验证股东名册创建联系人的完整流程

### 阶段五：投资人数据源集成（未来完善）
**预计时间**: 待定（依赖投资人数据源开发进度）
**内容**:
- 集成投资人数据源查询接口
- 实现自动投资者类型识别
- 优化数据准确性和一致性
- 保留现有字段作为备份方案

## 详细实施方案

### 1. 验证器更新方案

#### 1.1 新增字段验证规则

```typescript
/**
 * 验证投资者标识字段的业务逻辑
 * @param fundCode 基金代码
 * @param unifiedAccountId 一码通ID
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-07-28 15:41:48
 * @updated 2025-07-28 16:14:14 hayden 修改验证逻辑，允许同时存在两个字段以支持身份转换追踪
 * @updated 2025-07-28 16:41:53 hayden 移除具体格式验证，等待最终格式确认
 * @description 支持个人转基金的场景，允许同时保留一码通ID和基金代码
 */
const validateInvestorIdentifier = (fundCode?: string, unifiedAccountId?: string): boolean => {
  // 至少有一个标识字段
  if (!fundCode && !unifiedAccountId) {
    return false;
  }

  // 允许同时提供两个标识字段（支持个人转基金的场景）
  return true;
};

// 注意：基金代码和一码通ID的具体格式验证将在业务规则确认后添加
// 当前仅进行基础的非空验证，避免过早限制格式
```

#### 1.2 更新现有验证器

**CreateInvestorContactSchema 更新**:
```typescript
export const CreateInvestorContactSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空"),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(),
  unifiedAccountId: z.string().min(1, "一码通ID不能为空").optional(),
}).refine((data) => validateInvestorIdentifier(data.fundCode, data.unifiedAccountId), {
  message: "基金代码和一码通ID必须至少提供其中一个"
});
```

**UpdateInvestorContactSchema 更新**:
```typescript
export const UpdateInvestorContactSchema = z.object({
  contactId: z.string().min(1, "联系人ID不能为空"),
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空").optional(),
  phoneNumber: z.string().optional(),
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(),
  unifiedAccountId: z.string().min(1, "一码通ID不能为空").optional(),
}).refine((data) => {
  // 更新时允许不提供标识字段（保持原有值）
  if (data.fundCode !== undefined || data.unifiedAccountId !== undefined) {
    return validateInvestorIdentifier(data.fundCode, data.unifiedAccountId);
  }
  return true;
}, {
  message: "基金代码和一码通ID必须至少提供其中一个"
});
```

**ListInvestorContactsSchema 更新**:
```typescript
export const ListInvestorContactsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  name: z.string().optional(),
  phoneNumber: z.string().optional(),
  email: z.string().optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(), // 新增：按基金代码查询
  unifiedAccountId: z.string().min(1, "一码通账号不能为空").optional(), // 新增：按一码通账号查询
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
}).refine((data) => {
  // 必须提供基金代码或一码通账号其中一个
  return !!(data.fundCode || data.unifiedAccountId);
}, {
  message: "必须提供基金代码或一码通账号其中一个查询条件"
});
```

### 2. CRUD接口调整方案

#### 2.1 创建接口调整 (create.ts)

**主要修改点**:
1. 添加新字段的数据处理
2. 实现股东名册创建联系人的自动化逻辑
3. 更新数据库创建逻辑
4. 优化错误处理

```typescript
// 解构新增字段
const {
  organizationId, name, phoneNumber, email, address, remarks,
  fundCode, unifiedAccountId // 新增字段
} = validationResult.data;

// 股东名册创建联系人的自动化处理
let finalFundCode = fundCode;

// 判断是否需要进行股东名册查询：
// 1. 提供了一码通ID但没有提供基金代码
// 2. 或者明确要求进行股东名册查询
if (unifiedAccountId && !fundCode) {
  try {
    // 1. 根据一码通账号查询股东名称
    const shareholder = await db.shareholder.findFirst({
      where: {
        unifiedAccountNumber: unifiedAccountId,
        organizationId
      },
      select: { securitiesAccountName: true }
    });

    if (shareholder?.securitiesAccountName) {
      // 2. 根据股东名称查询基金代码
      const queriedFundCode = await queryFundCodeByShareholderName(
        shareholder.securitiesAccountName
      );

      if (queriedFundCode) {
        finalFundCode = queriedFundCode;
      }
    }
  } catch (error) {
    // 记录错误但不阻止创建流程
    console.warn('股东名册基金查询失败:', error);
  }
}

// 创建联系人记录
const newContact = await db.investorContact.create({
  data: {
    organizationId,
    name,
    phoneNumber: phoneNumber || null,
    email: email || null,
    address: address || null,
    remarks: remarks || null,
    fundCode: finalFundCode || null, // 可能是自动查询的基金代码
    unifiedAccountId: unifiedAccountId || null,
    createdBy: user.id,
  }
});
```

#### 2.2 更新接口调整 (update.ts)

**主要修改点**:
1. 添加新字段的更新逻辑
2. 实现身份转换功能
3. 增强数据验证

```typescript
// 解构新增字段
const { 
  contactId, organizationId, name, phoneNumber, email, address, remarks,
  fundCode, unifiedAccountId // 新增字段
} = validationResult.data;

// 更新联系人信息
const updatedContact = await db.investorContact.update({
  where: { contactId },
  data: {
    ...(name !== undefined ? { name } : {}),
    ...(phoneNumber !== undefined ? { phoneNumber } : {}),
    ...(email !== undefined ? { email } : {}),
    ...(address !== undefined ? { address } : {}),
    ...(remarks !== undefined ? { remarks } : {}),
    ...(fundCode !== undefined ? { fundCode } : {}), // 新增字段更新
    ...(unifiedAccountId !== undefined ? { unifiedAccountId } : {}), // 新增字段更新
    updatedBy: user.id,
  }
});
```

#### 2.3 查询接口调整 (list.ts)

**主要修改点**:
1. 要求必须提供基金代码或一码通账号作为主要查询条件
2. 实现基于标识符的精确查询（替代原有的模糊查询）
3. 保留姓名、电话、邮箱作为可选的辅助查询条件
4. 优化返回数据结构，包含投资者类型和转换状态信息

```typescript
// 解构新增查询参数
const {
  organizationId, name, phoneNumber, email,
  fundCode, unifiedAccountId, // 主要查询参数：必须提供其中一个
  page, limit
} = validationResult.data;

// 构建查询条件（基于标识符的精确查询）
const where = {
  organizationId,
  // 基金代码查询（精确匹配）
  ...(fundCode ? { fundCode: fundCode } : {}),
  // 一码通账号查询（精确匹配）
  ...(unifiedAccountId ? { unifiedAccountId: unifiedAccountId } : {}),
  // 可选的辅助查询条件
  ...(name ? { name: { contains: name } } : {}),
  ...(phoneNumber ? { phoneNumber: { contains: phoneNumber } } : {}),
  ...(email ? { email: { contains: email } } : {}),
};

// 优化返回数据结构
contacts: contacts.map(contact => ({
  contactId: contact.contactId,
  name: contact.name,
  phoneNumber: contact.phoneNumber,
  email: contact.email,
  address: contact.address,
  remarks: contact.remarks,
  fundCode: contact.fundCode, // 新增返回字段
  unifiedAccountId: contact.unifiedAccountId, // 新增返回字段
  isConverted: !!(contact.fundCode && contact.unifiedAccountId), // 新增：是否为转换投资者
  createdAt: contact.createdAt.toISOString(),
  updatedAt: contact.updatedAt ? contact.updatedAt.toISOString() : contact.createdAt.toISOString(),
  createdBy: contact.createdBy,
  updatedBy: contact.updatedBy || null,
}))
```

### 3. 新增工具函数

#### 3.1 基金查询工具函数（新增 - 股东名册集成）

```typescript
/**
 * 根据股东名称查询基金代码
 * @param shareholderName 股东名称（证券账户名称）
 * @returns 基金代码或null
 * <AUTHOR>
 * @created 2025-07-31 10:49:26
 * @updated 2025-07-31 10:49:26 hayden 修改查询逻辑，支持托管人模糊匹配和基金全称精确匹配
 * @description 根据股东名称查询FundManagementCustody表，匹配基金全称并验证托管人，返回基金代码
 * @example
 * 输入: "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"
 * 输出: "110023.OF" (优先选择带A的基金代码)
 */
export const queryFundCodeByShareholderName = async (
  shareholderName: string
): Promise<string | null> => {
  try {
    // 解析股东名称，提取托管人和基金全称
    // 格式：托管人－基金全称
    const parts = shareholderName.split('－');
    if (parts.length < 2) {
      return null; // 不是基金格式的股东名称
    }

    const custodianFullName = parts[0].trim(); // 托管人全称，如"中国银行股份有限公司"
    const fundFullName = parts[1].trim(); // 基金全称，如"易方达医疗保健行业混合型证券投资基金"

    // 查询基金管理和托管信息表
    // 1. 基金全称精确匹配
    // 2. 托管人模糊匹配（因为数据库中只有简称，如"中国银行"）
    const fundRecords = await db.fundManagementCustody.findMany({
      where: {
        fundFullName: {
          equals: fundFullName
        },
        custodian: {
          // 托管人模糊匹配：数据库中的简称应该包含在股东名称的托管人部分中
          // 例如：股东名称中的"中国银行股份有限公司"应该包含数据库中的"中国银行"
          not: null
        }
      },
      select: {
        fundCode: true,
        fundName: true,
        custodian: true
      }
    });

    if (fundRecords.length === 0) {
      return null;
    }

    // 进一步验证托管人匹配
    const matchedFunds = fundRecords.filter(fund => {
      if (!fund.custodian) return false;
      // 检查股东名称中的托管人部分是否包含数据库中的托管人简称
      return custodianFullName.includes(fund.custodian);
    });

    if (matchedFunds.length === 0) {
      return null;
    }

    // 优先级规则：带A或a的基金代码优先
    const priorityFund = matchedFunds.find(fund =>
      fund.fundName.includes('A') || fund.fundName.includes('a')
    );

    return priorityFund ? priorityFund.fundCode : matchedFunds[0].fundCode;
  } catch (error) {
    console.error('查询基金代码失败:', error);
    return null;
  }
};

/**
 * 验证一码通账号是否存在于股东表中
 * @param unifiedAccountId 一码通账号
 * @param organizationId 组织ID
 * @returns 股东信息或null
 * <AUTHOR>
 * @created 2025-07-30 19:03:34
 * @description 验证一码通账号的有效性，返回对应的股东信息
 */
export const validateUnifiedAccount = async (
  unifiedAccountId: string,
  organizationId: string
): Promise<{ securitiesAccountName: string } | null> => {
  try {
    const shareholder = await db.shareholder.findFirst({
      where: {
        unifiedAccountNumber: unifiedAccountId,
        organizationId
      },
      select: {
        securitiesAccountName: true
      }
    });

    return shareholder;
  } catch (error) {
    console.error('验证一码通账号失败:', error);
    return null;
  }
};
```

#### 3.2 身份转换函数（当前实施）

```typescript
/**
 * 个人投资者转为基金投资者
 * @param contactId 联系人ID
 * @param fundCode 基金代码
 * @param userId 操作用户ID
 * @returns 更新结果
 * <AUTHOR>
 * @created 2025-07-28 15:41:48
 * @updated 2025-07-28 16:14:14 hayden 修改转换逻辑，保留一码通ID以追踪转换历史
 * @description 个人转基金时保留原一码通ID，同时添加基金代码，便于追踪投资者身份变化历史
 */
export const convertPersonalToFund = async (
  contactId: string,
  fundCode: string,
  userId: string
) => {
  return await db.investorContact.update({
    where: { contactId },
    data: {
      fundCode: fundCode, // 添加基金代码
      // unifiedAccountId 保持不变，保留原一码通ID以追踪转换历史
      updatedBy: userId,
    }
  });
};
```

#### 3.2 查询工具函数（当前实施 - 更新方案）

```typescript
/**
 * 者标识查询联系人（主要查询方式）按投资
 * @param organizationId 组织ID
 * @param fundCode 基金代码（可选）
 * @param unifiedAccountId 一码通账号（可选）
 * @returns 联系人列表
 * <AUTHOR>
 * @created 2025-07-31 10:56:09
 * @description 通过基金代码或一码通账号查询联系人，这是查询联系人的主要方式，必须提供其中一个标识
 */
export const getContactsByIdentifier = async (
  organizationId: string,
  fundCode?: string,
  unifiedAccountId?: string
) => {
  // 验证至少提供一个查询标识
  if (!fundCode && !unifiedAccountId) {
    throw new Error('必须提供基金代码或一码通账号其中一个查询条件');
  }

  const where = {
    organizationId,
    // 基金代码查询（精确匹配）
    ...(fundCode ? { fundCode: fundCode } : {}),
    // 一码通账号查询（精确匹配）
    ...(unifiedAccountId ? { unifiedAccountId: unifiedAccountId } : {}),
  };

  return await db.investorContact.findMany({
    where,
    orderBy: { createdAt: "desc" }
  });
};
```

## 实施检查清单

### 验证器更新
- [ ] 添加基金代码格式验证正则
- [ ] 添加一码通ID格式验证正则
- [ ] 更新 CreateInvestorContactSchema（支持新字段）
- [ ] 更新 UpdateInvestorContactSchema
- [ ] 更新 ListInvestorContactsSchema
- [ ] 添加业务逻辑验证函数

### 基金查询工具函数（新增）
- [ ] 创建 fund-utils.ts 文件
- [ ] 实现 queryFundCodeByShareholderName 函数
- [ ] 实现 validateUnifiedAccount 函数
- [ ] 添加基金代码优先级逻辑（带A/a优先）
- [ ] 添加错误处理和日志记录

### 创建接口 (create.ts)
- [ ] 更新字段解构（支持新字段）
- [ ] 实现股东名册创建联系人逻辑（基于参数判断）
- [ ] 集成基金查询工具函数
- [ ] 修改数据库创建逻辑
- [ ] 测试股东名册创建功能（仅一码通ID场景）
- [ ] 测试普通创建功能
- [ ] 验证业务逻辑约束

### 更新接口 (update.ts)
- [ ] 更新字段解构
- [ ] 修改数据库更新逻辑
- [ ] 实现身份转换功能
- [ ] 测试部分字段更新

### 查询接口 (list.ts)
- [ ] 添加新查询参数
- [ ] 实现投资者类型筛选
- [ ] 更新返回数据结构
- [ ] 测试各种查询组合

### 工具函数
- [ ] 实现身份转换函数
- [ ] 实现类型查询函数
- [ ] 添加相关测试用例
