# 投资人联系人API接口文档

## 文档信息
- **作者**: hayden
- **创建时间**: 2025-07-31 14:11:41
- **版本**: v1.0

## 概述

投资人联系人管理API提供了完整的CRUD操作，支持基金投资者和个人投资者的分类管理，具备股东名册集成和投资者身份转换功能。

## 加解密说明

所有API接口均使用股东加密中间件（shareholderCryptoMiddleware）进行数据加解密处理，确保数据传输安全。请求数据在中间件中自动解密，响应数据在中间件中自动加密。

## 1. 创建投资人联系人

### 接口信息
- **URL**: `/api/investor-management/contacts/create`
- **方法**: POST
- **描述**: 创建投资人联系人信息，支持股东名册自动创建联系人功能

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| name | string | 是 | 联系人姓名 |
| phoneNumber | string | 否 | 电话号码 |
| email | string | 否 | 邮箱地址（需符合邮箱格式） |
| address | string | 否 | 联系地址 |
| remarks | string | 否 | 备注信息（最大300字符） |
| fundCode | string | 否 | 基金代码，用于标识机构投资者 |
| unifiedAccountId | string | 否 | 一码通账户ID，用于标识个人投资者 |

**验证规则**: fundCode 和 unifiedAccountId 必须至少提供其中一个

### 请求示例

```json
{
  "organizationId": "org_123456",
  "name": "张三",
  "phoneNumber": "***********",
  "email": "<EMAIL>",
  "address": "北京市朝阳区",
  "remarks": "重要客户",
  "fundCode": "110023.OF",
  "unifiedAccountId": "A123456789"
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | number | 响应状态码 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.contactId | string | 创建的联系人ID |

### 响应示例

**成功响应**:
```json
{
  "code": 200,
  "message": "联系人创建成功",
  "data": {
    "contactId": "contact_789012"
  }
}
```

**失败响应**:
```json
{
  "code": 400,
  "message": "基金代码和一码通ID必须至少提供其中一个",
  "data": null
}
```

## 2. 更新投资人联系人

### 接口信息
- **URL**: `/api/investor-management/contacts/update`
- **方法**: POST
- **描述**: 更新投资人联系人信息，支持部分字段更新和身份转换

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contactId | string | 是 | 联系人ID |
| organizationId | string | 是 | 组织ID |
| name | string | 否 | 联系人姓名 |
| phoneNumber | string | 否 | 电话号码 |
| email | string | 否 | 邮箱地址 |
| address | string | 否 | 联系地址 |
| remarks | string | 否 | 备注信息（最大300字符） |
| fundCode | string | 否 | 基金代码 |
| unifiedAccountId | string | 否 | 一码通账户ID |

**验证规则**: 如果提供 fundCode 或 unifiedAccountId，则必须至少提供其中一个

### 请求示例

```json
{
  "contactId": "contact_789012",
  "organizationId": "org_123456",
  "name": "张三（更新）",
  "phoneNumber": "***********",
  "fundCode": "110023.OF"
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | number | 响应状态码 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.contactId | string | 更新的联系人ID |

### 响应示例

**成功响应**:
```json
{
  "code": 200,
  "message": "联系人更新成功",
  "data": {
    "contactId": "contact_789012"
  }
}
```

**失败响应**:
```json
{
  "code": 404,
  "message": "联系人不存在",
  "data": null
}
```

## 3. 查询投资人联系人列表

### 接口信息
- **URL**: `/api/investor-management/contacts/list`
- **方法**: POST
- **描述**: 查询投资人联系人列表，支持基金代码和一码通ID精确查询

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| fundCode | string | 否 | 基金代码（精确匹配） |
| unifiedAccountId | string | 否 | 一码通账号（精确匹配） |
| name | string | 否 | 联系人姓名（模糊匹配） |
| phoneNumber | string | 否 | 电话号码（模糊匹配） |
| email | string | 否 | 邮箱地址（模糊匹配） |
| page | number | 否 | 页码（默认1） |
| limit | number | 否 | 每页数量（默认10，最大100） |

**验证规则**: fundCode 和 unifiedAccountId 必须至少提供其中一个

### 请求示例

```json
{
  "organizationId": "org_123456",
  "fundCode": "110023.OF",
  "page": 1,
  "limit": 10
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | number | 响应状态码 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.contacts | array | 联系人列表 |
| data.contacts[].contactId | string | 联系人ID |
| data.contacts[].name | string | 联系人姓名 |
| data.contacts[].phoneNumber | string | 电话号码 |
| data.contacts[].email | string | 邮箱地址 |
| data.contacts[].address | string | 联系地址 |
| data.contacts[].remarks | string | 备注信息 |
| data.contacts[].fundCode | string | 基金代码 |
| data.contacts[].unifiedAccountId | string | 一码通账户ID |
| data.contacts[].isConverted | boolean | 是否为转换投资者 |
| data.contacts[].createdAt | string | 创建时间 |
| data.contacts[].updatedAt | string | 更新时间 |
| data.contacts[].createdBy | string | 创建人ID |
| data.contacts[].updatedBy | string | 更新人ID |
| data.pagination | object | 分页信息 |
| data.pagination.total | number | 总记录数 |
| data.pagination.page | number | 当前页码 |
| data.pagination.limit | number | 每页数量 |
| data.pagination.totalPages | number | 总页数 |

### 响应示例

**成功响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "contacts": [
      {
        "contactId": "contact_789012",
        "name": "张三",
        "phoneNumber": "***********",
        "email": "<EMAIL>",
        "address": "北京市朝阳区",
        "remarks": "重要客户",
        "fundCode": "110023.OF",
        "unifiedAccountId": "A123456789",
        "isConverted": true,
        "createdAt": "2025-07-31T14:11:41.950Z",
        "updatedAt": "2025-07-31T14:11:41.950Z",
        "createdBy": "user_123",
        "updatedBy": null
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "totalPages": 1
    }
  }
}
```

## 4. 删除投资人联系人

### 接口信息
- **URL**: `/api/investor-management/contacts/delete`
- **方法**: POST
- **描述**: 删除投资人联系人信息

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contactId | string | 是 | 联系人ID |
| organizationId | string | 是 | 组织ID |

### 请求示例

```json
{
  "contactId": "contact_789012",
  "organizationId": "org_123456"
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | number | 响应状态码 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.contactId | string | 删除的联系人ID |

### 响应示例

**成功响应**:
```json
{
  "code": 200,
  "message": "联系人删除成功",
  "data": {
    "contactId": "contact_789012"
  }
}
```

**失败响应**:
```json
{
  "code": 404,
  "message": "联系人不存在或无权限访问",
  "data": null
}
```

## 业务逻辑说明

### 投资者类型识别
- **基金投资者**: fundCode 不为空
- **个人投资者**: unifiedAccountId 不为空且 fundCode 为空
- **转换投资者**: 同时拥有 fundCode 和 unifiedAccountId

### 股东名册集成功能
当创建联系人时，如果只提供 unifiedAccountId 而未提供 fundCode，系统将自动：
1. 根据一码通账号查询股东表获取股东名称
2. 解析股东名称提取托管人和基金全称
3. 查询基金管理托管表匹配基金信息
4. 应用优先级规则选择基金代码（带A/a优先）
5. 自动填充 fundCode 字段

### 错误码说明
- **200**: 操作成功
- **400**: 请求参数无效
- **404**: 资源不存在
- **500**: 服务器内部错误
