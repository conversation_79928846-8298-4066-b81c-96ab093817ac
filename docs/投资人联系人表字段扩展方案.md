# 投资人联系人表字段扩展方案

## 文档信息
- **作者**: hayden
- **创建时间**: 2025-07-28 15:19:05
- **更新时间**: 2025-07-28 15:19:05
- **版本**: v1.0

## 概述

根据投资人管理模块的业务需求，需要对 `InvestorContact` 模型进行字段扩展，新增基金代码和股东名册一码通ID字段，以支持投资人信息的完整管理和股东分析功能。

## 业务背景

### 需求场景
1. **投资人弹窗展示**: 展示投资人信息（基金），需要基金代码进行数据关联
2. **股东分析功能**: 根据股东名称查询数据库获取基金代码，需要一码通ID进行个人股东识别
3. **联系人创建逻辑**:
   - 有基金代码：使用基金代码创建联系人
   - 无基金代码：判定为个人股东，使用一码通ID创建联系人

### 数据流程
```
股东名册数据 → 基金名称匹配 → 获取基金代码 → 联系人创建
            ↓
         一码通ID → 个人股东识别 → 联系人创建
```

## 数据库设计方案

### 新增字段规格

#### 1. 基金代码字段 (fundCode)
- **字段名**: `fundCode`
- **数据类型**: `String?` (可选)
- **描述**: 基金代码，用于标识机构投资者
- **业务规则**:
  - 当联系人为基金投资者时必填
  - 当联系人为个人投资者时为空
  - 格式遵循标准基金代码规范

#### 2. 一码通ID字段 (unifiedAccountId)
- **字段名**: `unifiedAccountId`
- **数据类型**: `String?` (可选)
- **描述**: 股东名册中的一码通账户ID，用于标识个人投资者
- **业务规则**:
  - 当联系人为个人投资者时必填
  - 当联系人为基金投资者时为空
  - 用于与股东名册数据进行关联匹配

### 修改后的模型结构

```prisma
/**
 * <AUTHOR>
 * @created 2025-07-09 17:06:43
 * @updated 2025-07-28 15:19:05 hayden 新增基金代码和一码通ID字段支持投资人分类管理
 * @description 组织级别的投资人联系人管理，支持联系人信息的增删改查，区分基金投资者和个人投资者
 */
model InvestorContact {
  contactId         String       @id @default(cuid()) // 联系人ID，自动生成
  organizationId    String       // 关联的组织ID
  organization      Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  name              String       // 联系人姓名（必填）
  phoneNumber       String?      // 电话号码
  email             String?      // 邮箱地址
  address           String?      // 联系地址
  remarks           String?      // 备注信息
  fundCode          String?      // 基金代码，用于标识机构投资者（新增）
  unifiedAccountId  String?      // 一码通账户ID，用于标识个人投资者（新增）
  createdAt         DateTime     @default(now()) // 创建时间
  updatedAt         DateTime     @updatedAt // 更新时间
  createdBy         String?      // 创建人用户ID
  updatedBy         String?      // 更新人用户ID

  @@index([organizationId]) // 组织ID索引
  @@index([name]) // 姓名索引
  @@index([phoneNumber]) // 电话号码索引
  @@index([email]) // 邮箱索引
  @@index([fundCode]) // 基金代码索引（新增）
  @@index([unifiedAccountId]) // 一码通ID索引（新增）
  @@index([createdAt]) // 创建时间索引
  @@map("investor_contact") // 映射到数据库表"investor_contact"
}
```
### 3. 迁移步骤

**生产环境必要流程**：
1. **数据库备份**：迁移前必须完整备份生产数据库
2. **变更审查**：至少一名团队成员审查迁移内容
3. **回滚计划**：准备详细的回滚方案
4. **维护窗口**：在预设的维护时间执行迁移
5. **迁移验证**：迁移后验证数据库结构和应用功能

### 2. Schema文件修改

#### 2.1 修改 schema.prisma 文件
在 `packages/database/prisma/schema.prisma` 中的 `InvestorContact` 模型添加新字段

## 实施步骤

### 1. 数据库迁移准备

根据项目数据库操作规范，不同环境使用不同的操作命令：

#### 1.1 开发环境操作
```powershell
# 开发环境：直接推送修改，适用于快速迭代
pnpm --filter database push

# 推送后更新类型定义
pnpm --filter database generate
```

#### 1.2 测试环境操作
```powershell
# 测试环境：创建并应用迁移文件，记录变更历史
pnpm --filter database migrate
```

**迁移名称建议**: `add_fund_code_unified_account_investor_contact`

#### 1.3 生产环境操作
```powershell
# 生产环境：使用已在测试环境验证过的迁移文件
pnpm --filter database migrate
```

