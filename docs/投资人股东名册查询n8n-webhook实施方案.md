# 投资人股东名册查询 n8n Webhook 实施方案

## 文档信息
- **作者**: hayden
- **创建时间**: 2025-07-28 18:21:03
- **更新时间**: 2025-07-28 18:21:03
- **版本**: v1.0

## 概述

基于投资人详情页面的业务需求，本文档详细规划了使用 n8n webhook 实现投资人模糊查询股东名册功能的实施方案。该方案利用 n8n 的可视化流程优势，实现高性能的数据库直连查询，为投资人详情页面提供股东信息关联展示。

**当前实施状态说明**：
- **数据源现状**：目前基金数据源尚未建立，但查询方法已完整保留
- **查询逻辑**：根据基金名称查询股东名册，匹配成功后可同时获得基金数据和股东名册数据
- **索引优化**：股东名册相关索引已在数据库schema中完整配置，支持高性能查询

## 业务流程图

```mermaid
graph TD
    A[前端发起查询请求] --> B[n8n Webhook 接收请求]
    B --> C{验证请求参数}
    C -->|参数无效| D[返回错误响应]
    C -->|参数有效| E[解析投资人名称]
    
    E --> F[连接数据库]
    F --> G[执行模糊查询 SQL]
    G --> H[获取股东名册数据]
    
    H --> I{是否有匹配结果}
    I -->|无匹配| J[返回空结果]
    I -->|有匹配| K[计算匹配度分数]
    
    K --> L[按匹配度排序]
    L --> M[构造响应数据]
    M --> N[返回查询结果]
    
    Q[日志记录] -.-> B
    R[性能监控] -.-> G
```

## n8n Workflow 架构图

```mermaid
graph LR
    A[Webhook Trigger] --> B[参数验证节点]
    B --> C[数据库连接节点]
    C --> D[SQL查询节点]
    
    D --> E[数据处理节点]
    E --> F[匹配度计算节点]
    F --> G[响应构造节点]
    G --> H[Webhook Response]
    
    K[错误处理节点] -.-> B
    K -.-> C
    K -.-> D
    K -.-> E
    
    L[日志记录节点] -.-> A
    L -.-> J
```

## 核心节点实现

### 1. Webhook Trigger 节点

**配置**:
- **Webhook URL**: `/webhook/shareholder-search`
- **HTTP Method**: POST
- **认证方式**: 使用项目加密中间件

**请求格式**:
```json
{
  "organizationId": "org-123",
  "investorName": "张三基金"
}
```

### 2. 参数验证节点 (Code Node)

```javascript
/**
 * 验证请求参数
 * <AUTHOR>
 * @created 2025-07-29 10:24:01
 * @description 验证前端传入的查询参数，确保数据完整性和安全性
 */
const { organizationId, investorName } = $json;

// 参数验证
if (!organizationId || !investorName) {
  return [{
    json: {
      success: false,
      error: "组织ID和投资人名称不能为空",
      code: "INVALID_PARAMS",
      timestamp: new Date().toISOString()
    }
  }];
}

// 记录查询开始时间
const startTime = Date.now();

return [{
  json: {
    organizationId,
    investorName: investorName.trim(),
    startTime
  }
}];
```

### 3. SQL查询节点 (Postgres Node)

**查询语句**:
```sql
-- 投资人股东名册精准查询
-- <AUTHOR>
-- @created 2025-07-29 10:24:01
-- @description 根据投资人名称精准匹配股东名册，用于基金名称与股东信息的关联查询
SELECT
  s.shareholder_id,
  s.securities_account_name,
  s.unified_account_number,
  s.shareholder_category,
  s.number_of_shares,
  s.shareholding_ratio,
  s.contact_address,
  s.contact_number,
  s.register_date,
  sr.company_code,
  ci.company_name,
  sr.register_date as registry_date
FROM shareholder s
JOIN shareholder_registry sr ON s.registry_id = sr.id
LEFT JOIN company_info ci ON sr.id = ci.registry_id
WHERE s.organization_id = $1
  AND s.securities_account_name = $2
ORDER BY
  s.number_of_shares DESC,
  s.register_date DESC;
```

**参数绑定**:
- `$1`: organizationId
- `$2`: investorName

### 4. 匹配度计算节点 (Code Node)

```javascript
/**
 * 计算匹配度分数
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 * @description 基于编辑距离和关键词匹配算法计算投资人名称与股东名称的匹配度
 */

// 编辑距离算法
function levenshteinDistance(str1, str2) {
  const matrix = [];
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  return matrix[str2.length][str1.length];
}

// 匹配度计算函数
function calculateMatchScore(investorName, shareholderName) {
  // 完全匹配
  if (investorName === shareholderName) {
    return 100;
  }
  
  // 包含匹配
  if (shareholderName.includes(investorName) || investorName.includes(shareholderName)) {
    return 85;
  }
  
  // 编辑距离匹配
  const editDistance = levenshteinDistance(investorName, shareholderName);
  const maxLength = Math.max(investorName.length, shareholderName.length);
  const similarity = (maxLength - editDistance) / maxLength;
  
  // 关键词匹配加分
  const keywordBonus = shareholderName.includes('基金') && investorName.includes('基金') ? 0.1 : 0;
  
  return Math.round((similarity + keywordBonus) * 100);
}

// 处理查询结果
const investorName = $('参数验证节点').item(0).json.investorName;
const results = $json.map(item => ({
  shareholderId: item.shareholder_id,
  shareholderName: item.securities_account_name,
  unifiedAccountNumber: item.unified_account_number,
  shareholderCategory: item.shareholder_category,
  numberOfShares: item.number_of_shares,
  shareholdingRatio: item.shareholding_ratio,
  contactAddress: item.contact_address,
  contactNumber: item.contact_number,
  registerDate: item.register_date,
  companyCode: item.company_code,
  companyName: item.company_name,
  registryDate: item.registry_date,
  matchScore: calculateMatchScore(investorName, item.securities_account_name)
}));

// 过滤低匹配度结果（匹配度低于60分的过滤掉）
const filteredResults = results.filter(item => item.matchScore >= 60);

return [{ json: filteredResults }];
```

### 5. 响应构造节点 (Code Node)

```javascript
/**
 * 构造最终响应数据
 * <AUTHOR>
 * @created 2025-07-29 10:24:01
 * @description 构造符合前端要求的响应格式，包含查询统计信息
 */
const validationData = $('参数验证节点').item(0).json;
const { startTime } = validationData;
const results = $json;

// 计算查询耗时
const duration = Date.now() - startTime;

// 构造响应数据
const response = {
  success: true,
  data: {
    matches: results,
    statistics: {
      totalMatches: results.length,
      highMatchCount: results.filter(r => r.matchScore >= 90).length,
      mediumMatchCount: results.filter(r => r.matchScore >= 70 && r.matchScore < 90).length,
      lowMatchCount: results.filter(r => r.matchScore >= 60 && r.matchScore < 70).length
    },
    queryInfo: {
      duration,
      timestamp: new Date().toISOString()
    }
  },
  message: results.length > 0
    ? `查询完成，找到 ${results.length} 条匹配记录`
    : '未找到匹配的股东记录'
};

return [{ json: response }];
```

## 错误处理和监控

### 6. 错误处理节点 (Code Node)

```javascript
/**
 * 统一错误处理
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 * @description 统一处理各个节点可能出现的错误，返回标准化错误响应
 */
const error = $json.error || {};
const errorCode = error.code || 'UNKNOWN_ERROR';
const errorMessage = error.message || '查询失败，请稍后重试';

// 错误类型映射
const errorMessages = {
  'INVALID_PARAMS': '请求参数无效',
  'DATABASE_ERROR': '数据库查询异常',
  'TIMEOUT_ERROR': '查询超时',
  'PERMISSION_DENIED': '权限不足',
  'UNKNOWN_ERROR': '未知错误'
};

const errorResponse = {
  success: false,
  error: {
    code: errorCode,
    message: errorMessages[errorCode] || errorMessage,
    timestamp: new Date().toISOString(),
    details: error.details || null
  }
};

// 记录错误日志
console.error('Shareholder search error:', {
  errorCode,
  errorMessage,
  originalError: error,
  timestamp: new Date().toISOString()
});

return [{ json: errorResponse }];
```

### 7. 日志记录节点 (Code Node)

```javascript
/**
 * 查询日志记录
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 * @description 记录查询请求和结果的详细日志，用于监控和分析
 */
const validationData = $('参数验证节点').item(0).json;
const responseData = $('响应构造节点').item(0).json;

const logData = {
  type: 'shareholder_search',
  organizationId: validationData.organizationId,
  investorName: validationData.investorName,
  resultCount: responseData.data?.matches?.length || 0,
  duration: responseData.data?.queryInfo?.duration || 0,
  timestamp: new Date().toISOString(),
  success: responseData.success
};

// 记录到日志系统
console.log('Shareholder search completed:', logData);

// 性能告警：查询时间超过5秒
if (logData.duration > 5000) {
  console.warn('Slow shareholder search detected:', logData);
}

return [{ json: logData }];
```

## 性能优化策略

### 1. 数据库索引优化

**当前索引状态**：股东名册相关索引已在 `packages/database/prisma/schema.prisma` 中完整配置，包括：

```typescript
/**
 * 股东信息表索引配置
 * <AUTHOR>
 * @created 2025-07-29 10:13:22
 * @description 已在Prisma Schema中配置的索引，支持高性能查询
 */
@@index([registryId]) // 名册ID索引
@@index([organizationId]) // 组织ID索引
@@index([unifiedAccountNumber]) // 一码通账户索引
@@index([securitiesAccountName]) // 证券账户名称索引 - 支持基金名称模糊查询
@@index([registerDate]) // 名册日期索引
@@index([shareholderCategory]) // 持有人类别索引
@@index([shareholderType]) // 股东类型索引
@@index([shareholderId, registerDate]) // 组合索引：优化查询相同证件号码在不同名册期的记录
```

### 2. 查询优化策略

**基于现有数据库索引的优化策略**：

- **索引利用**: 充分利用已配置的 `securitiesAccountName` 索引进行基金名称模糊查询
- **组合查询**: 结合 `organizationId` 和 `registerDate` 索引优化多条件查询
- **精准查询**: 基于基金名称的精准匹配，避免大量无关结果
- **查询超时**: 设置合理的查询超时时间

**数据源集成说明**：
- **当前状态**: 基金数据源尚未建立，但查询框架已完整
- **未来扩展**: 基金数据源建立后，可通过匹配算法关联基金信息和股东名册
- **数据关联**: 匹配成功后可同时返回基金详情和对应的股东持仓信息

## 前端调用方式

### 1. API代理调用

```typescript
/**
 * 调用 n8n webhook 查询股东信息
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 * @description 通过API代理调用n8n webhook，支持加密传输
 */
interface ShareholderSearchParams {
  organizationId: string;
  investorName: string;
}

interface ShareholderSearchResponse {
  success: boolean;
  data?: {
    matches: ShareholderMatch[];
    statistics: {
      totalMatches: number;
      highMatchCount: number;
      mediumMatchCount: number;
      lowMatchCount: number;
    };
    queryInfo: {
      duration: number;
      timestamp: string;
    };
  };
  error?: {
    code: string;
    message: string;
    timestamp: string;
  };
  message?: string;
}

async function searchShareholdersByInvestor(
  params: ShareholderSearchParams
): Promise<ShareholderSearchResponse> {
  try {
    const response = await fetch('/api/n8n_proxy/shareholder_search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Shareholder search error:', error);
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: '网络请求失败',
        timestamp: new Date().toISOString()
      }
    };
  }
}
```

### 2. React Hook 封装

```typescript
/**
 * 股东查询 React Hook
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 * @description 封装股东查询逻辑，提供加载状态和错误处理
 */
import { useState, useCallback } from 'react';

interface UseShareholderSearchResult {
  data: ShareholderSearchResponse | null;
  loading: boolean;
  error: string | null;
  search: (params: ShareholderSearchParams) => Promise<void>;
  reset: () => void;
}

export function useShareholderSearch(): UseShareholderSearchResult {
  const [data, setData] = useState<ShareholderSearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (params: ShareholderSearchParams) => {
    setLoading(true);
    setError(null);

    try {
      const result = await searchShareholdersByInvestor(params);
      setData(result);

      if (!result.success) {
        setError(result.error?.message || '查询失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return { data, loading, error, search, reset };
}
```

## 部署配置

### 1. n8n Workflow 配置

```json
{
  "name": "投资人股东名册查询",
  "active": true,
  "settings": {
    "executionTimeout": 30,
    "saveManualExecutions": true,
    "callerPolicy": "workflowsFromSameOwner"
  },
  "tags": ["投资人管理", "股东查询", "数据查询"]
}
```

### 2. 环境变量配置

```bash
# n8n 配置
N8N_WEBHOOK_URL=https://your-domain.com/webhook/shareholder-search
N8N_WEBHOOK_TIMEOUT=30000

# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=supstar_intranet
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# 性能配置
SHAREHOLDER_SEARCH_MAX_RESULTS=50
SHAREHOLDER_SEARCH_TIMEOUT=30000
```

### 3. 监控告警配置

```javascript
/**
 * 监控告警配置
 * <AUTHOR>
 * @created 2025-07-28 18:21:03
 */
const MONITORING_CONFIG = {
  // 性能指标阈值
  PERFORMANCE_THRESHOLDS: {
    QUERY_TIMEOUT: 5000,        // 查询超时阈值（毫秒）
    SLOW_QUERY: 2000,           // 慢查询阈值（毫秒）
    ERROR_RATE: 0.05,           // 错误率阈值（5%）
  },

  // 告警配置
  ALERTS: {
    ENABLE_PERFORMANCE_ALERTS: true,
    ENABLE_ERROR_ALERTS: true,
    WEBHOOK_URL: 'https://your-alert-webhook.com',
  }
};
```

## 实施检查清单

### n8n Workflow 创建
- [ ] 创建新的 n8n workflow
- [ ] 配置 Webhook Trigger 节点
- [ ] 实现参数验证节点
- [ ] 配置数据库连接节点
- [ ] 实现 SQL 查询节点
- [ ] 实现匹配度计算节点

- [ ] 实现响应构造节点
- [ ] 配置错误处理节点
- [ ] 配置日志记录节点

### 性能优化
- [x] 数据库索引已配置（Prisma Schema中已完成）
- [ ] 配置查询超时控制
- [ ] 实现性能监控
- [ ] 优化 SQL 查询语句
- [ ] 可选：添加GIN索引提升查询性能（如需支持模糊查询）

### 前端集成
- [ ] 创建 API 代理接口
- [ ] 实现前端调用函数
- [ ] 封装 React Hook
- [ ] 添加错误处理和加载状态

### 测试验证
- [ ] 单元测试（匹配算法）
- [ ] 集成测试（完整流程）
- [ ] 性能测试（大数据量）
- [ ] 安全测试（权限控制）

### 部署上线
- [ ] 配置生产环境变量
- [ ] 部署 n8n workflow
- [ ] 配置监控告警
- [ ] 验证生产环境功能

## 总结

本实施方案利用 n8n webhook 的优势，实现了高性能的投资人股东名册查询功能。主要特点包括：

1. **可视化流程**: 利用 n8n 的可视化优势，便于维护和调试
2. **高性能查询**: 直连数据库，减少 API 层开销，充分利用已配置的数据库索引
3. **智能匹配**: 基于编辑距离的模糊匹配算法，支持基金名称与股东名册的精确关联
4. **数据库优化**: 索引已在Prisma Schema中完整配置，支持高性能查询
5. **完善监控**: 日志记录和性能监控
6. **易于扩展**: 模块化设计，便于后期功能扩展
7. **数据源预留**: 为未来基金数据源集成预留完整的查询框架

**当前实施状态**：
- ✅ 数据库索引已完整配置
- ✅ 查询算法和匹配逻辑已设计完成
- 🔄 基金数据源待建立，但查询方法已保留
- 🔄 匹配成功后可同时获得基金数据和股东名册数据

该方案为投资人详情页面提供了可靠、高效的股东信息查询服务，并为未来的基金数据集成做好了充分准备。
