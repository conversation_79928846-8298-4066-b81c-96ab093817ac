# 搜索框推荐语 API 文档

**基础URL:** `http://192.168.138.244:6789/webhook`

**文档更新时间:** 2025-08-04 14:32:54

**文档作者:** hayden

---

## 接口概述

### 搜索框推荐语接口

**接口描述:** 基于用户输入的推荐语，通过AI智能判断是否与基金相关，如果相关则返回优化后的推荐语和对应的基金代码列表

**请求信息:**
- **URL:** `/recommend`
- **HTTP方法:** `POST`
- **使用n8n内网代理中间件:** 是
- **需要加密传输:** 是

---

## 加密参数说明

### 加密方式
本接口使用n8n内网代理中间件，遵循以下加密规范：

1. **加密算法:** AES-CBC
2. **签名验证:** HMAC-SHA256
3. **传输格式:** JSON

### 加密请求格式
```json
{
  "encryptedData": "加密后的请求数据",
  "signature": "HMAC-SHA256签名",
  "timestamp": "请求时间戳"
}
```

### 解密响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "解密后的响应数据"
}
```

---

## 请求参数

### 原始请求参数（加密前）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ana | string | 是 | 用户输入的推荐语，最少1个字符，不能为空 |

### 请求示例（加密前）
```json
{
  "ana": "如何选择一只好的基金？"
}
```

### 请求示例（加密后）
```json
{
  "encryptedData": "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt",
  "signature": "a1b2c3d4e5f6789012345678901234567890abcd",
  "timestamp": "2025-08-04T14:32:54.097Z"
}
```

---

## 响应参数

### 成功响应（基金相关）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| fundCodes | array | 基金代码列表 |
| reportDate | string | 报告日期，格式：YYYYMMDD |
| totalCount | number | 基金代码总数量 |
| timestamp | string | 响应时间戳（ISO 日期时间字符串） |

#### fundCodes 数组说明
- 数组元素类型：string
- 格式：基金代码.OF（如：000220.OF）
- 说明：符合推荐语的基金代码列表

### 成功响应示例（基金相关）
```json
{
  "fundCodes": [
    "000220.OF",
    "000242.OF",
    "000339.OF",
    "000418.OF",
    "000586.OF",
    "000634.OF",
    "000739.OF",
    "001106.OF"
  ],
  "reportDate": "20250630",
  "totalCount": 117,
  "timestamp": "2025-08-04T06:03:04.970Z"
}
```

### 错误响应（非基金相关）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| err_finance | string | 错误提示信息 |

### 错误响应示例（非基金相关）
```json
{
  "err_finance": "您输入的内容与基金无关，请提供基金、ETF、REITs等基金相关问题。"
}
```

---

## 错误码

| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 200 | MISSING_ANA | 缺少必需参数：ana |
| 200 | INVALID_ANA | ana参数验证失败，期望类型：string |
| 200 | NO_INPUT_DATA | 未接收到任何输入数据 |
| 200 | INVALID_INPUT_FORMAT | 输入数据格式无效，期望JSON对象 |
| 200 | UNEXPECTED_ERROR | 系统发生未预期的错误 |
| 400 | DECRYPTION_FAILED | 数据解密失败 |
| 401 | AUTHENTICATION_FAILED | 认证失败 |
| 504 | REQUEST_TIMEOUT | 请求超时 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

### 验证错误响应示例
```json
{
  "validationResult": "error",
  "error": {
    "code": "MISSING_ANA",
    "message": "缺少必需参数：ana。请提供ana参数。",
    "field": "ana",
    "value": null,
    "timestamp": "2025-08-04T14:32:54.097Z",
    "type": "VALIDATION_ERROR"
  }
}
```

---

## 前端调用示例

### 使用加密工具调用
```typescript
import { createEncryptedRequest } from "@repo/utils/lib/crypto";

/**
 * 调用搜索框推荐语API
 * <AUTHOR>
 * @created 2025-08-04T14:32:54.097Z
 * @param ana 用户输入的推荐语
 * @returns 基金代码列表或错误信息
 */
async function getRecommendations(ana: string) {
  try {
    const encryptedRequest = createEncryptedRequest({ ana });
    
    const response = await fetch('/api/n8n_proxy/recommend', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(encryptedRequest)
    });
    
    const result = await response.json();
    
    if (result.code !== 200) {
      throw new Error(result.message);
    }
    
    return result.data;
  } catch (error) {
    console.error('推荐语API调用失败:', error);
    throw error;
  }
}

// 使用示例
const recommendations = await getRecommendations("如何选择一只好的基金？");
```

---

## 业务逻辑说明

### AI判断逻辑
1. **基金相关判定:** AI会判断输入内容是否与基金投资相关
2. **支持的基金类型:** 公募基金、私募基金、ETF、REITs、各类基金产品等
3. **优化处理:** 对基金相关的推荐语进行精炼优化（≤20字）
4. **拒绝处理:** 非基金相关内容会被拒绝并返回提示信息

### 数据处理流程
1. 参数验证 → 2. AI智能判断 → 3. 基金数据查询 → 4. 结果格式化

### 报告日期说明
- 默认使用 `20250630` 作为报告日期
- 实际业务中会根据数据可用性动态调整

---

**最后更新:** 2025-08-04 14:32:54
