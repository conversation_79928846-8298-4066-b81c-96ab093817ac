# 环境变量配置文档

> **作者**: hayden  
> **创建时间**: 2025-06-30 14:53:28  
> **文档版本**: 1.0.0  
> **项目**: supastarter  

## 概述

本文档详细描述了 `.env.local.example` 文件中所有环境变量的配置说明，包括用途、格式要求和配置示例。

## 数据库配置 (Database)

### DATABASE_URL
- **用途**: 主数据库连接字符串
- **格式**: `postgresql://username:password@host:port/database`
- **必需**: 是
- **示例**: `postgresql://user:pass@localhost:5432/mydb`

### DIRECT_URL
- **用途**: Supabase 直连数据库 URL（使用 Supabase 时需要）
- **格式**: PostgreSQL 连接字符串
- **必需**: 使用 Supabase 时必需
- **示例**: `postgresql://postgres:<EMAIL>:5432/postgres`

## 站点配置 (Site Configuration)

### NEXT_PUBLIC_SITE_URL
- **用途**: 网站的公开访问 URL，用于生成绝对链接
- **格式**: 完整的 URL 地址
- **必需**: 是
- **示例**: `https://yourdomain.com` 或 `http://localhost:3000`

## 身份验证配置 (Authentication)

### BETTER_AUTH_SECRET
- **用途**: Better Auth 库的加密密钥
- **格式**: 随机字符串（建议至少 32 字符）
- **必需**: 是
- **安全性**: 高度机密，生产环境必须使用强随机字符串

### GitHub OAuth
- **GITHUB_CLIENT_ID**: GitHub 应用的客户端 ID
- **GITHUB_CLIENT_SECRET**: GitHub 应用的客户端密钥
- **获取方式**: 在 GitHub Developer Settings 中创建 OAuth App

### Google OAuth
- **GOOGLE_CLIENT_ID**: Google 应用的客户端 ID
- **GOOGLE_CLIENT_SECRET**: Google 应用的客户端密钥
- **获取方式**: 在 Google Cloud Console 中创建 OAuth 2.0 凭据

## 邮件服务配置 (Mail Services)

### Nodemailer (SMTP)
```env
MAIL_HOST="smtp.example.com"
MAIL_PORT="465"
MAIL_USER="<EMAIL>"
MAIL_PASS="your-password"
```

### 第三方邮件服务
- **PLUNK_API_KEY**: Plunk 邮件服务 API 密钥
- **RESEND_API_KEY**: Resend 邮件服务 API 密钥
- **POSTMARK_SERVER_TOKEN**: Postmark 服务器令牌
- **MAILGUN_API_KEY**: Mailgun API 密钥
- **MAILGUN_DOMAIN**: Mailgun 域名

## 支付服务配置 (Payment Services)

### Lemonsqueezy
- **LEMONSQUEEZY_API_KEY**: API 密钥
- **LEMONSQUEEZY_WEBHOOK_SECRET**: Webhook 验证密钥
- **LEMONSQUEEZY_STORE_ID**: 商店 ID

### Stripe
- **STRIPE_SECRET_KEY**: Stripe 密钥（以 sk_ 开头）
- **STRIPE_WEBHOOK_SECRET**: Webhook 端点密钥

### Chargebee
- **CHARGEBEE_SITE**: Chargebee 站点名称
- **CHARGEBEE_API_KEY**: Chargebee API 密钥

### Creem
- **CREEM_API_KEY**: Creem API 密钥
- **CREEM_WEBHOOK_SECRET**: Webhook 验证密钥

### Polar
- **POLAR_ACCESS_TOKEN**: Polar 访问令牌
- **POLAR_WEBHOOK_SECRET**: Webhook 验证密钥

## 产品价格配置 (Product Pricing)

### 价格 ID 配置
- **NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY**: 专业版月付价格 ID
- **NEXT_PUBLIC_PRICE_ID_PRO_YEARLY**: 专业版年付价格 ID
- **NEXT_PUBLIC_PRICE_ID_LIFETIME**: 终身版价格 ID

> **注意**: 这些 ID 需要在对应的支付服务提供商后台获取

## 分析服务配置 (Analytics)

### 支持的分析服务
- **NEXT_PUBLIC_PIRSCH_CODE**: Pirsch 分析代码
- **NEXT_PUBLIC_PLAUSIBLE_URL**: Plausible 分析 URL
- **NEXT_PUBLIC_MIXPANEL_TOKEN**: Mixpanel 项目令牌
- **NEXT_PUBLIC_GOOGLE_ANALYTICS_ID**: Google Analytics 跟踪 ID

## 存储服务配置 (Storage)

### S3 兼容存储
- **S3_ACCESS_KEY_ID**: 访问密钥 ID
- **S3_SECRET_ACCESS_KEY**: 访问密钥
- **S3_ENDPOINT**: 存储服务端点 URL
- **S3_REGION**: 存储区域
- **NEXT_PUBLIC_AVATARS_BUCKET_NAME**: 头像存储桶名称（默认: avatars）

## AI 服务配置 (AI Services)

### OpenAI
- **OPENAI_API_KEY**: OpenAI API 密钥
- **格式**: 以 sk- 开头的字符串
- **用途**: 调用 OpenAI GPT 等 AI 服务

## 腾讯会议配置 (Tencent Meeting)

### 基础配置
- **TENCENT_APP_ID**: 腾讯会议应用 ID
- **TENCENT_SDK_ID**: SDK ID
- **TENCENT_SECRET_ID**: 密钥 ID
- **TENCENT_SECRET_KEY**: 密钥
- **TENCENT_OPERATOR_ID**: 管理员账号 userid

> **获取方式**: 在腾讯会议后台"企业管理" → "企业自建应用"中创建应用获取

## 股东名册系统配置 (Shareholder Registry)

### API 加密配置
- **NEXT_PUBLIC_SHAREHOLDER_API_KEY**: 公开 API 密钥
- **NEXT_PUBLIC_SHAREHOLDER_API_SECRET**: 服务端加密密钥
- **NEXT_PUBLIC_SHAREHOLDER_API_IV**: 初始化向量

### 批量处理配置
- **SHAREHOLDER_BATCH_SIZE**: 后端批量创建批次大小（默认: 200）
- **NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE**: 前端分批上传批次大小（默认: 200）
- **SHAREHOLDER_CONCURRENCY_LIMIT**: 并发限制（默认: 20）
- **SHAREHOLDER_TRANSACTION_TIMEOUT**: 事务超时时间，毫秒（默认: 30000）

## n8n 代理服务配置 (n8n Proxy Service)

### 服务端点配置
- **N8N_BASE_URL**: 主要 n8n 服务器 URL
- **N8N_BASE_URL1**: 可选的多版本 n8n 服务器配置

**示例配置**:
```env
N8N_BASE_URL="http://192.168.138.244:6789/webhook/v1"
N8N_BASE_URL1="http://192.168.138.244:6789/webhook-test/v1"
```

## 配置最佳实践

### 安全性建议
1. **生产环境**: 所有密钥和敏感信息必须使用强随机字符串
2. **版本控制**: 永远不要将 `.env.local` 文件提交到版本控制系统
3. **权限管理**: 限制对环境变量文件的访问权限

### 开发环境配置
1. 复制 `.env.local.example` 为 `.env.local`
2. 根据需要的功能填写对应的环境变量
3. 不需要的服务可以留空或注释掉

### 部署注意事项
1. 确保所有必需的环境变量都已正确配置
2. 验证数据库连接和第三方服务的可用性
3. 定期轮换敏感密钥和令牌

---

**文档维护**: 当添加新的环境变量时，请及时更新此文档并记录变更历史。
