# 投资人管理页面测试用例

## 基本信息
- **测试页面名称**: 投资人管理页面
- **测试页面URL**: http://192.168.138.123:3000/app/hayden/market
- **测试负责人**: hayden
- **测试日期**: 2025-07-29
- **测试环境**: 开发环境

## 前置条件
- [x] 用户已登录系统 (<EMAIL>)
- [x] 用户具有投资人管理页面访问权限
- [x] 网络连接正常
- [x] 浏览器环境：Chrome/Firefox/Safari
- [ ] 其他特殊前置条件：需要绑定公司代码

## 1. 页面基础功能测试

### 1.1 页面加载测试
- [ ] **页面正常加载**: 投资人管理页面能够正常打开，无白屏或错误页面
- [ ] **页面标题正确**: 浏览器标题栏显示"投资人 - 星链资本"
- [ ] **页面布局完整**: 搜索区域、筛选区域、投资人卡片列表正常显示
- [ ] **加载时间合理**: 页面首屏加载时间 < 3秒
- [ ] **数据完整性**: 投资人数据完整显示，包括基金信息、持仓状态等

### 1.2 导航功能测试
- [ ] **主导航菜单**: 顶部导航"投资人"、"股东名册"、"路演会议"可正常点击
- [ ] **面包屑导航**: 显示当前位置路径
- [ ] **标签页切换**: 无标签页切换功能
- [ ] **返回功能**: 浏览器前进后退按钮正常工作
- [ ] **页面刷新**: 刷新页面后筛选条件和数据状态保持

### 1.3 用户界面测试
- [ ] **用户信息显示**: 右上角用户头像、姓名正确显示
- [ ] **用户菜单功能**: 用户下拉菜单正常展开和收起
- [ ] **登出功能**: 登出按钮正常工作
- [ ] **权限控制**: 用户只能看到自己组织的投资人数据

## 2. 交互元素功能测试

### 2.1 按钮功能测试
- [ ] **公司代码设置按钮**: 点击后弹出公司代码设置弹窗
- [ ] **收藏按钮**: 投资人卡片上的收藏/取消收藏功能
- [ ] **筛选按钮**: "全部"、"收藏"、"未收藏"筛选按钮切换
- [ ] **刷新按钮**: 数据刷新功能正常
- [ ] **按钮状态**: 按钮hover、active、disabled状态正确
- [ ] **按钮反馈**: 点击后有适当的视觉反馈

### 2.2 表单功能测试
- [ ] **公司代码输入**: 本司代码输入框验证（6位数字+.+大写字母）
- [ ] **对标代码输入**: 对标公司代码输入框验证（逗号分隔的多个代码）
- [ ] **代码格式验证**: 输入错误格式时显示错误提示
- [ ] **保存功能**: 公司代码保存后触发自动标签更新
- [ ] **表单重置**: 取消按钮恢复原始数据

### 2.3 数据展示测试
- [ ] **投资人卡片**: 投资人信息卡片正确显示基金名称、代码、资产规模
- [ ] **持仓标签**: 显示"持有本司"、"持有对标"、"持有其他公司"标签
- [ ] **收藏状态**: 收藏的投资人显示星标图标
- [ ] **数据分组**: 按4层分组显示（本司+对标、仅本司、仅对标、其他）
- [ ] **滚动加载**: 页面滚动时自动加载更多投资人数据
- [ ] **数据排序**: 同组内按资产规模排序

## 3. API接口测试

### 3.1 核心业务API
- [ ] **组织信息API**: `/api/auth/organization/get-full-organization` - 获取组织信息 - <500ms
- [ ] **公司代码查询API**: `/api/n8n_proxy/fund_inquiry_test` - 获取基金代码列表 - <2s
- [ ] **投资人详情API**: `/api/n8n_proxy/fund_manager_inquiry_test` - 获取投资人详细信息 - <1s
- [ ] **标签管理API**: `/api/investor/tags` - 投资人标签管理 - <500ms
- [ ] **收藏管理API**: `/api/investor/favorites` - 收藏功能 - <500ms

### 3.2 API性能要求
- [ ] **响应时间**: 核心API响应时间 < 2秒
- [ ] **数据完整性**: API返回数据结构正确，包含必要字段
- [ ] **错误处理**: API异常时显示友好错误提示
- [ ] **并发处理**: 滚动加载时多个API并发请求正常

### 3.3 认证和权限API
- [ ] **用户认证**: 登录状态验证API正常
- [ ] **组织权限**: 只能访问自己组织的投资人数据
- [ ] **会话管理**: 会话超时处理正确

## 4. 响应式布局测试

### 4.1 不同屏幕尺寸测试
- [ ] **桌面端** (1920x1080): 投资人卡片3-4列布局
- [ ] **笔记本** (1366x768): 投资人卡片2-3列布局
- [ ] **平板端** (768x1024): 投资人卡片2列布局
- [ ] **手机端** (375x667): 投资人卡片1列布局
- [ ] **超宽屏** (2560x1440): 投资人卡片4-5列布局

### 4.2 布局适配测试
- [ ] **导航菜单**: 移动端导航菜单收起显示
- [ ] **搜索区域**: 搜索框在小屏幕下自适应宽度
- [ ] **筛选按钮**: 筛选按钮在移动端正常显示
- [ ] **投资人卡片**: 卡片内容在不同尺寸下完整显示

## 5. 性能测试

### 5.1 页面加载性能
- [ ] **首屏加载时间** < 3秒
- [ ] **完整页面加载时间** < 5秒
- [ ] **投资人数据加载** < 2秒
- [ ] **滚动加载响应** < 1秒

### 5.2 交互性能
- [ ] **收藏按钮响应** < 200ms
- [ ] **筛选切换响应** < 300ms
- [ ] **滚动加载流畅**: 无明显卡顿
- [ ] **页面切换流畅**: 导航切换无延迟

## 6. 错误处理和边界测试

### 6.1 网络异常测试
- [ ] **网络断开**: 显示网络错误提示
- [ ] **API超时**: 显示加载超时提示
- [ ] **服务器错误**: 显示服务器异常提示
- [ ] **数据加载失败**: 显示数据加载失败提示

### 6.2 数据边界测试
- [ ] **无投资人数据**: 显示空状态页面
- [ ] **大量投资人数据**: 滚动加载性能正常
- [ ] **公司代码格式错误**: 显示格式错误提示
- [ ] **无匹配投资人**: 显示无匹配结果提示

## 7. 安全性测试

### 7.1 数据安全
- [ ] **投资人数据**: 敏感投资人信息适当保护
- [ ] **API数据传输**: 重要数据传输加密
- [ ] **输入验证**: 公司代码输入XSS过滤
- [ ] **数据权限**: 只能访问有权限的投资人数据

### 7.2 权限安全
- [ ] **页面权限**: 未登录用户无法访问
- [ ] **功能权限**: 收藏、设置功能需要权限
- [ ] **数据隔离**: 不同组织数据完全隔离

## 8. 兼容性测试

### 8.1 浏览器兼容性
- [ ] **Chrome**: 最新版本正常工作
- [ ] **Firefox**: 最新版本正常工作
- [ ] **Safari**: 最新版本正常工作
- [ ] **Edge**: 最新版本正常工作

### 8.2 设备兼容性
- [ ] **Windows**: Windows系统正常工作
- [ ] **macOS**: macOS系统正常工作
- [ ] **iOS**: iOS设备正常工作
- [ ] **Android**: Android设备正常工作

## 9. 用户体验测试

### 9.1 易用性测试
- [ ] **操作流程**: 投资人搜索和筛选流程直观
- [ ] **提示信息**: 公司代码设置有清晰说明
- [ ] **错误提示**: 错误信息友好易懂
- [ ] **加载状态**: 数据加载时有loading指示

### 9.2 可访问性测试
- [ ] **键盘导航**: 支持Tab键导航
- [ ] **屏幕阅读器**: 投资人卡片有适当标签
- [ ] **颜色对比度**: 文字和背景对比度合适
- [ ] **字体大小**: 投资人信息字体大小适中

## 10. 特殊功能测试

### 10.1 投资人管理特有功能
- [ ] **自动标签功能**: 设置公司代码后自动生成持仓标签
- [ ] **4层分组显示**: 按持仓情况分组显示投资人
- [ ] **滚动懒加载**: 滚动到底部自动加载更多数据
- [ ] **收藏同步**: 收藏状态实时同步到服务器

### 10.2 集成功能测试
- [ ] **n8n数据集成**: 投资人数据来源于n8n工作流
- [ ] **实时数据更新**: 投资人数据定期更新
- [ ] **标签系统集成**: 与标签管理系统集成

## 测试结果记录

### 通过的测试项
- [待测试后填写]

### 失败的测试项
- [待测试后填写]

### 需要优化的项目
- [待测试后填写]

## 测试总结
- **总测试项**: [待统计]
- **通过项**: [待统计] ([百分比])
- **失败项**: [待统计] ([百分比])
- **整体评价**: [待评价]
- **主要问题**: [待总结]
- **改进建议**: [待提出]
