# 股东名册上传流程图

## 文档信息

- **创建时间**: 2025-06-19 19:40:52
- **作者**: hayden
- **版本**: 1.0.0
- **描述**: 股东名册上传处理的完整流程图，包含01/05名册和t1/t2/t3名册的处理逻辑

## 概述

本文档详细描述了股东名册上传系统的完整流程，包括文件解析、数据预处理、验证和上传等各个环节。系统支持两大类名册：
- **深市名册**: 01类型和05类型（DBF格式）
- **沪市名册**: t1、t2、t3类型（Excel格式）

## 主要流程图

### 1. 总体上传流程

```mermaid
flowchart TD
    A[用户选择文件] --> B{文件格式检查}
    B -->|DBF文件| C[DBF解析流程]
    B -->|Excel文件| D[Excel解析流程]
    B -->|ZIP文件| E[ZIP解压流程]
    B -->|不支持格式| F[显示错误信息]
    
    C --> G[文件名解析]
    D --> H[文件名解析]
    E --> I{解压后文件类型}
    I -->|DBF| C
    I -->|Excel| D
    I -->|无效文件| F
    
    G --> J{名册类型判断}
    H --> K{名册类型判断}
    
    J -->|01类型| L[01名册解析]
    J -->|05类型| M[05名册解析]
    J -->|无法识别| N[尝试01解析]
    
    K -->|t1类型| O[t1名册解析]
    K -->|t2类型| P[t2名册解析]
    K -->|t3类型| Q[t3名册解析]
    K -->|无法识别| R[返回错误]
    
    L --> S[数据验证]
    M --> S
    N --> T{01解析成功?}
    T -->|是| S
    T -->|否| U[尝试05解析]
    U --> V{05解析成功?}
    V -->|是| S
    V -->|否| F
    
    O --> W[t1数据预处理]
    P --> X[t2数据预处理]
    Q --> Y[t3数据预处理]
    
    W --> Z[数据合并去重]
    X --> Z
    Y --> Z
    
    S --> AA[字段验证]
    Z --> AA
    
    AA --> BB{验证通过?}
    BB -->|是| CC[准备上传数据]
    BB -->|否| F
    
    CC --> DD[调用后端API]
    DD --> EE[数据库事务处理]
    EE --> FF[上传完成]
```

### 2. DBF文件解析流程（01/05名册）

```mermaid
flowchart TD
    A[DBF文件输入] --> B[文件名解析]
    B --> C{提取公司代码和日期}
    C -->|成功| D[读取DBF文件头]
    C -->|失败| E[使用默认值]
    
    D --> F[解析字段定义]
    E --> F
    F --> G[解析记录数据]
    G --> H[编码转换GBK->UTF8]
    H --> I{名册类型检测}
    
    I -->|01类型| J[验证01必填字段]
    I -->|05类型| K[验证05必填字段]
    I -->|自动检测| L[尝试01字段验证]
    
    J --> M{字段验证通过?}
    K --> M
    L --> N{01验证通过?}
    N -->|是| M
    N -->|否| O[尝试05字段验证]
    O --> M
    
    M -->|是| P[提取公司信息]
    M -->|否| Q[返回验证错误]
    
    P --> R[数据格式化]
    R --> S[返回解析结果]
```

### 3. Excel文件解析流程（t1/t2/t3名册）

```mermaid
flowchart TD
    A[Excel文件输入] --> B[文件名解析]
    B --> C{提取公司代码和日期}
    C -->|成功| D[读取Excel工作表]
    C -->|失败| E[使用默认值]
    
    D --> F{名册类型检测}
    E --> F
    
    F -->|t1类型| G[t1解析器]
    F -->|t2类型| H[t2解析器]
    F -->|t3类型| I[t3解析器]
    F -->|无法识别| J[返回类型错误]
    
    G --> K[查找SJLX=801记录]
    H --> L[查找SJLX=802记录]
    I --> M[查找SJLX=803记录]
    
    K --> N[提取股东数据]
    L --> N
    M --> N
    
    N --> O[提取公司基本信息]
    O --> P[数据预处理]
    P --> Q{预处理类型}
    
    Q -->|t1| R[t1记录合并]
    Q -->|t2| S[t2记录合并]
    Q -->|t3| T[t3记录合并]
    
    R --> U[按持股数量排序]
    S --> U
    T --> U
    
    U --> V[限制记录数量]
    V --> W[返回解析结果]
```

### 4. 数据预处理流程

```mermaid
flowchart TD
    A[解析后的原始数据] --> B{名册类型}
    
    B -->|t1/t2/t3| C[记录合并预处理]
    B -->|01/05| D[直接进入验证]
    
    C --> E[生成组合键]
    E --> F[ZJHM + YMTZHHM]
    F --> G{存在重复记录?}
    
    G -->|是| H[合并重复记录]
    G -->|否| I[保留原记录]
    
    H --> J[叠加持股数量]
    J --> K[保留其他字段]
    K --> I
    
    I --> L[按持股数量排序]
    L --> M[限制记录数量]
    M --> N[更新记录统计]
    N --> D
    
    D --> O[字段映射验证]
    O --> P[必填字段检查]
    P --> Q[数据类型验证]
    Q --> R[返回验证结果]
```

### 5. 后端上传处理流程

```mermaid
flowchart TD
    A[前端上传请求] --> B{名册类型路由}
    
    B -->|01类型| C[upload01Handler]
    B -->|05类型| D[upload05Handler]
    B -->|t1类型| E[uploadT1Handler]
    B -->|t2类型| F[uploadT2Handler]
    B -->|t3类型| G[uploadT3Handler]
    
    C --> H[字段验证]
    D --> H
    E --> H
    F --> H
    G --> H
    
    H --> I{验证通过?}
    I -->|否| J[返回验证错误]
    I -->|是| K[开始数据库事务]
    
    K --> L[验证组织和名册]
    L --> M[处理公司绑定]
    M --> N[创建/更新名册记录]
    N --> O[批量处理股东数据]
    O --> P[更新记录统计]
    P --> Q[提交事务]
    Q --> R[返回成功结果]
    
    subgraph "事务超时配置"
        S[01/t1/t2/t3: 固定超时]
        T[05: 动态超时计算]
    end
```

## 关键配置说明

### 文件支持格式
- **DBF文件**: 深市01/05名册
- **Excel文件**: 沪市t1/t2/t3名册（.xls/.xlsx）
- **ZIP文件**: 包含上述格式的压缩文件

### 数据量限制
- 默认最大记录数: 200条
- 可通过环境变量 `NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE` 配置
- 按持股数量降序排序后取前N条

### 字段映射规则
- **01名册**: YMTH、ZQZHMC等字段
- **05名册**: 类似01但字段略有差异
- **t1名册**: ZJHM、YMTZHHM、CYSL等字段
- **t2名册**: 包含沪市特有字段
- **t3名册**: ZCYSL、PTZQZHCYSL、XYCYSL等字段

## 错误处理机制

1. **文件格式错误**: 不支持的文件类型
2. **解析错误**: 文件损坏或格式不正确
3. **字段验证错误**: 缺少必填字段或字段类型不匹配
4. **数据验证错误**: 公司代码不匹配、日期格式错误等
5. **数据库错误**: 事务超时或数据库操作失败

## 性能优化

1. **批量处理**: 使用数据库事务批量插入数据
2. **内存管理**: 大文件分块处理，避免内存溢出
3. **超时控制**: 根据数据量动态调整事务超时时间
4. **预处理优化**: 在前端完成数据合并和排序，减少后端处理时间

---

**注意**: 本流程图基于当前系统实现，如有更新请及时同步文档内容。
