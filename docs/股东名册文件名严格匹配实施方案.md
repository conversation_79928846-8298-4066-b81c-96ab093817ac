# 股东名册文件名严格匹配实施方案

**时间：** 2025年06月23日 15:50:20
**作者：** hayden
**版本：** v1.1

## 1. 项目背景

根据PRD文档要求，需要对股东名册上传功能进行严格的文件名匹配验证，确保只有符合规范的文件名才能进行解析上传。

### 1.1 当前支持的文件类型
- **DBF文件：** c01、c05 格式（如：DQMC01_300723_20230430.DBF、DQMC05_300723_20240330.DBF）
- **Excel文件：** t1、t2、t3 格式（如：t16053380320240625all.625.xls、t26053380320241231t200.c31.xls、t36053380320241231t200.c31.xls）
- **ZIP压缩文件：** 包含上述DBF或Excel文件的压缩包（如：DQMC05_300723_20240430.zip、t36053380320241231t200.c31.zip）

### 1.2 问题分析
当前系统的文件名检测逻辑过于宽松，使用 `includes()` 方法进行模糊匹配，可能导致不符合规范的文件名也能通过验证。

### 1.3 导入流程说明
系统采用两阶段导入流程：
1. **文件选择阶段：** 用户选择文件后，系统进行基础验证（文件类型、大小等）
2. **导入执行阶段：** 用户点击"开始导入"按钮后，系统进行严格的文件名验证和内容解析，并提供详细的反馈信息

**重要：** 文件名严格匹配验证和详细错误反馈只在用户点击"开始导入"按钮时触发，确保用户获得完整的操作反馈。

## 2. 文件结构分析

### 2.1 项目目录结构

```
d:\supstar/
├── apps/web/modules/saas/shareholder/
│   ├── components/
│   │   ├── registry-import/
│   │   │   └── ShareholderRegistryImport.tsx          # 股东名册上传组件
│   │   └── dialogs/
│   │       └── ShareholderRegistryImportErrorDialog.tsx # 错误提示对话框
│   └── lib/
│       ├── config.ts                                  # 配置文件（需新增文件名规则）
│       ├── file-parser.ts                            # 统一文件解析入口
│       ├── dbf-parser.ts                             # DBF解析器入口
│       ├── registry-merger.ts                        # t名册预处理器
│       ├── zip-parser.ts                             # ZIP文件解析器（需新增）
│       ├── dbf-parser/
│       │   ├── common.ts                             # DBF通用工具（需修改检测逻辑）
│       │   ├── type-01.ts                            # c01名册解析器
│       │   └── type-05.ts                            # c05名册解析器
│       └── excel-parser/
│           ├── index.ts                              # Excel解析器入口
│           ├── common.ts                             # Excel通用工具（需修改检测逻辑）
│           ├── type-t1.ts                            # t1名册解析器
│           ├── type-t2.ts                            # t2名册解析器
│           └── type-t3.ts                            # t3名册解析器
└── packages/api/src/routes/shareholder-registry/
    ├── upload.ts                                     # 后端上传接口
    └── lib/
        └── utils.ts                                  # 后端工具函数（文件名检测）
```

### 2.2 核心文件及其职责

| 文件路径 | 完整路径 | 主要职责 | 修改优先级 |
|---------|---------|---------|-----------|
| `config.ts` | `apps/web/modules/saas/shareholder/lib/config.ts` | 配置文件名规则和正则表达式 | **高** |
| `dbf-parser/common.ts` | `apps/web/modules/saas/shareholder/lib/dbf-parser/common.ts` | DBF文件名检测逻辑 | **高** |
| `excel-parser/common.ts` | `apps/web/modules/saas/shareholder/lib/excel-parser/common.ts` | Excel文件名检测逻辑 | **高** |
| `zip-parser.ts` | `apps/web/modules/saas/shareholder/lib/zip-parser.ts` | ZIP文件解析和文件名检测逻辑 | **高** |
| `file-parser.ts` | `apps/web/modules/saas/shareholder/lib/file-parser.ts` | 统一文件解析入口 | **中** |
| `ShareholderRegistryImport.tsx` | `apps/web/modules/saas/shareholder/components/registry-import/ShareholderRegistryImport.tsx` | 前端文件上传组件 | **中** |
| `ShareholderRegistryImportErrorDialog.tsx` | `apps/web/modules/saas/shareholder/components/dialogs/ShareholderRegistryImportErrorDialog.tsx` | 错误提示组件 | **低** |
| `utils.ts` | `packages/api/src/routes/shareholder-registry/lib/utils.ts` | 后端文件名检测逻辑 | **中** |

### 2.3 各文件详细功能说明

#### 2.3.1 前端组件文件

**ShareholderRegistryImport.tsx**
- **功能：** 股东名册文件上传的主要UI组件
- **当前逻辑：** 使用 `filterFiles()` 进行基础文件类型和大小验证
- **需要修改：** 在 `validateAndImportNext()` 中添加严格文件名验证（点击"开始导入"时触发）
- **关键函数：** `handleFileSelect()`, `preprocessFiles()`, `validateAndImportNext()`
- **导入流程：** 文件选择 → 基础验证 → 点击"开始导入" → 严格验证 → 解析反馈

**ShareholderRegistryImportErrorDialog.tsx**
- **功能：** 显示文件上传错误信息的对话框组件
- **当前逻辑：** 显示通用错误信息和操作按钮
- **需要修改：** 优化文件名格式错误的提示内容
- **关键属性：** `message`, `fileName`, `onOpenHelpDialog`

#### 2.3.2 解析器核心文件

**config.ts**
- **功能：** 定义解析器的配置常量和枚举
- **当前内容：** `MAX_RECORDS_COUNT`, `RegistryType`, `FileFormat`, `ParsePhase`
- **需要新增：** 文件名正则表达式规则、严格验证开关
- **影响范围：** 所有解析器文件都会引用此配置

**file-parser.ts**
- **功能：** 统一的文件解析入口，根据文件类型分发到不同解析器
- **当前逻辑：** `parseShareholderRegistryFile()` 根据文件扩展名选择解析器
- **需要修改：** 在解析前添加文件名格式验证，支持ZIP文件解析
- **关键函数：** `parseShareholderRegistryFile()`, `parseFileName()`, `parseZipFile()`

**zip-parser.ts（新增）**
- **功能：** ZIP压缩文件解析器，解压后验证内部文件名格式
- **主要逻辑：** 解压ZIP文件 → 验证内部文件名 → 调用相应解析器
- **支持格式：** DQMC05_300723_20240430.zip、t36053380320241231t200.c31.zip
- **关键函数：** `parseZipFile()`, `extractAndValidateZipContents()`, `validateZipFileName()`

#### 2.3.3 DBF解析器文件

**dbf-parser.ts**
- **功能：** DBF文件解析的入口文件
- **当前逻辑：** 调用 `detectRegistryType()` 判断01或05类型
- **依赖关系：** 依赖 `dbf-parser/common.ts` 的检测函数
- **关键函数：** `parseDBFFile()`, `parseFileName()`

**dbf-parser/common.ts**
- **功能：** DBF解析器的通用工具函数
- **当前逻辑：** 使用 `includes()` 方法进行文件名模糊匹配
- **需要修改：** `detectRegistryType()` 函数改为严格正则匹配
- **关键函数：** `detectRegistryType()`, `isSupportedFileType()`, `isProcessableFileType()`

**dbf-parser/type-01.ts & type-05.ts**
- **功能：** 分别处理c01和c05类型的DBF文件解析
- **当前逻辑：** 包含各自的 `parseFileName01()` 和 `parseFileName05()` 函数
- **需要修改：** 文件名解析函数需要使用严格匹配
- **关键函数：** `parseDBFFile01()`, `parseFileName01()`, `parseDBFFile05()`, `parseFileName05()`

#### 2.3.4 Excel解析器文件

**excel-parser/index.ts**
- **功能：** Excel解析器的入口文件
- **当前逻辑：** 导出各种解析函数和工具函数
- **依赖关系：** 依赖 `common.ts` 的检测函数和各类型解析器
- **关键导出：** `isExcelFile`, `isTSeriesRegistry`, `parseExcelFile`

**excel-parser/common.ts**
- **功能：** Excel解析器的通用工具函数
- **当前逻辑：** `detectExcelRegistryType()` 使用 `startsWith()` 和 `includes()` 混合检测
- **需要修改：** 移除备选的 `includes()` 检测，改为严格正则匹配
- **关键函数：** `detectExcelRegistryType()`, `isTSeriesRegistry()`, `isExcelFile()`

**excel-parser/type-t1.ts, type-t2.ts, type-t3.ts**
- **功能：** 分别处理t1、t2、t3类型的Excel文件解析
- **当前逻辑：** 包含各自的解析逻辑和文件名验证
- **需要修改：** 文件名验证部分需要使用严格匹配
- **关键函数：** `parseExcelFileT1()`, `parseExcelFileT2()`, `parseExcelFileT3()`

#### 2.3.5 后端相关文件

**packages/api/src/routes/shareholder-registry/upload.ts**
- **功能：** 处理股东名册文件上传的API接口
- **当前逻辑：** 使用双重验证机制检测名册类型
- **依赖关系：** 依赖 `lib/utils.ts` 的检测函数
- **关键函数：** 上传处理逻辑、名册类型验证

**packages/api/src/routes/shareholder-registry/lib/utils.ts**
- **功能：** 后端的工具函数，包含文件名检测逻辑
- **当前逻辑：** `detectRegistryTypeByFileName()` 使用 `startsWith()` 检测
- **需要修改：** 与前端保持一致的严格文件名验证
- **关键函数：** `detectRegistryTypeByFileName()`, `detectRegistryTypeByFields()`

### 2.4 当前检测逻辑问题

#### DBF文件检测（`dbf-parser/common.ts`）
```typescript
// 当前逻辑 - 过于宽松
export function detectRegistryType(fileName: string): '01' | '05' | 'unknown' {
  const lowerFileName = fileName.toLowerCase();
  if (lowerFileName.includes('01') || lowerFileName.includes('dqmc01')) {
    return '01';
  }
  if (lowerFileName.includes('05') || lowerFileName.includes('dqmc05')) {
    return '05';
  }
  return 'unknown';
}
```

#### Excel文件检测（`excel-parser/common.ts`）
```typescript
// 当前逻辑 - 部分严格但仍有改进空间
export function detectExcelRegistryType(fileName: string): RegistryType {
  const lowercaseName = fileName.toLowerCase();
  if (lowercaseName.startsWith('t3')) return RegistryType.TYPE_T3;
  if (lowercaseName.startsWith('t2')) return RegistryType.TYPE_T2;
  if (lowercaseName.startsWith('t1')) return RegistryType.TYPE_T1;
  // 备选方案仍使用includes()
}
```

### 2.5 文件依赖关系图

```
前端文件上传流程：
ShareholderRegistryImport.tsx
    ↓ (调用)
file-parser.ts → parseShareholderRegistryFile()
    ↓ (根据文件类型分发)
    ├── DBF文件 → dbf-parser.ts → parseDBFFile()
    │       ↓ (依赖)
    │   dbf-parser/common.ts → detectRegistryType()
    │       ↓ (调用具体解析器)
    │   ├── dbf-parser/type-01.ts → parseDBFFile01()
    │   └── dbf-parser/type-05.ts → parseDBFFile05()
    │
    └── Excel文件 → excel-parser/index.ts → parseExcelFile()
            ↓ (依赖)
        excel-parser/common.ts → detectExcelRegistryType()
            ↓ (调用具体解析器)
        ├── excel-parser/type-t1.ts → parseExcelFileT1()
        ├── excel-parser/type-t2.ts → parseExcelFileT2()
        └── excel-parser/type-t3.ts → parseExcelFileT3()

配置文件依赖：
config.ts (被所有解析器文件引用)
    ↓ (提供配置)
    ├── RegistryType 枚举
    ├── FileFormat 枚举
    ├── ParsePhase 枚举
    └── MAX_RECORDS_COUNT 常量
    └── (新增) FILE_NAME_PATTERNS 正则规则
    └── (新增) STRICT_FILENAME_VALIDATION 开关

后端验证流程：
packages/api/src/routes/shareholder-registry/upload.ts
    ↓ (调用)
packages/api/src/routes/shareholder-registry/lib/utils.ts
    ↓ (使用)
detectRegistryTypeByFileName() 函数
```

### 2.6 数据流分析

#### 2.6.1 文件名检测数据流
```
用户上传文件
    ↓
前端组件获取文件名
    ↓
调用文件名检测函数
    ↓ (当前逻辑)
使用 includes() 或 startsWith() 模糊匹配
    ↓ (新逻辑)
使用正则表达式严格匹配
    ↓
返回检测结果 (成功/失败 + 名册类型)
    ↓
根据结果决定是否继续解析
```

#### 2.6.2 错误处理数据流
```
文件名验证失败
    ↓
生成详细错误信息
    ↓
传递给错误对话框组件
    ↓
显示用户友好的错误提示
    ↓
提供帮助文档链接
```

## 3. 实施方案

### 3.1 第一阶段：配置文件增强（config.ts）

**目标：** 在配置文件中定义严格的文件名匹配规则

**文件路径：** `apps/web/modules/saas/shareholder/lib/config.ts`

**实施内容：**
1. 添加文件名正则表达式配置
2. 定义支持的文件名格式枚举
3. 添加文件名验证配置项
4. 添加错误信息模板

**新增配置项：**
```typescript
/**
 * 文件名匹配规则
 * 定义各类型股东名册文件的严格文件名格式
 *
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */
export const FILE_NAME_PATTERNS = {
  // DBF文件名规则 - 格式：DQMC[01|05]_XXXXXX_YYYYMMDD.DBF
  DBF_C01: /^DQMC01_\d{6}_\d{8}\.DBF$/i,
  DBF_C05: /^DQMC05_\d{6}_\d{8}\.DBF$/i,

  // Excel文件名规则
  // t1格式：t1XXXXXXXX[YY]YYYYMMDDall.XXX.xls
  EXCEL_T1: /^t1\d{8}\d{2}\d{8}all\.\d{3}\.xls$/i,
  // t2格式：t2XXXXXXXX[YY]YYYYMMDDtXXX.cXX.xls
  EXCEL_T2: /^t2\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.xls$/i,
  // t3格式：t3XXXXXXXX[YY]YYYYMMDDtXXX.cXX.xls
  EXCEL_T3: /^t3\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.xls$/i,

  // ZIP文件名规则 - 压缩包文件名应与内部文件名一致（去掉扩展名）
  // 例如：DQMC05_300723_20240430.zip 内部应包含 DQMC05_300723_20240430.DBF
  // 例如：t36053380320241231t200.c31.zip 内部应包含 t36053380320241231t200.c31.xls
  ZIP_DBF_C01: /^DQMC01_\d{6}_\d{8}\.zip$/i,
  ZIP_DBF_C05: /^DQMC05_\d{6}_\d{8}\.zip$/i,
  ZIP_EXCEL_T1: /^t1\d{8}\d{2}\d{8}all\.\d{3}\.zip$/i,
  ZIP_EXCEL_T2: /^t2\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.zip$/i,
  ZIP_EXCEL_T3: /^t3\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.zip$/i,
};

/**
 * 支持的文件名示例
 * 用于错误提示中显示正确的文件名格式
 *
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */
export const FILE_NAME_EXAMPLES = {
  DBF_C01: 'DQMC01_300723_20230430.DBF',
  DBF_C05: 'DQMC05_300723_20240330.DBF',
  EXCEL_T1: 't16053380320240625all.625.xls',
  EXCEL_T2: 't26053380320241231t200.c31.xls',
  EXCEL_T3: 't36053380320241231t200.c31.xls',
  ZIP_DBF_C01: 'DQMC01_300723_20230430.zip',
  ZIP_DBF_C05: 'DQMC05_300723_20240430.zip',
  ZIP_EXCEL_T1: 't16053380320240625all.625.zip',
  ZIP_EXCEL_T2: 't26053380320241231t200.c31.zip',
  ZIP_EXCEL_T3: 't36053380320241231t200.c31.zip',
};

/**
 * 文件名验证错误信息模板
 *
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */
export const FILENAME_ERROR_MESSAGES = {
  INVALID_DBF_FORMAT: '文件名格式不正确，DBF文件名应为：DQMC[01|05]_公司代码_日期.DBF',
  INVALID_EXCEL_FORMAT: '文件名格式不正确，Excel文件名应为：t[1|2|3]开头的规范格式',
  INVALID_ZIP_FORMAT: '文件名格式不正确，ZIP文件名应与内部文件名一致（仅扩展名不同）',
  ZIP_CONTENT_MISMATCH: 'ZIP文件内容与文件名不匹配，内部文件名应与ZIP文件名对应',
  UNSUPPORTED_TYPE: '不支持的文件类型，请上传c01、c05、t1、t2、t3格式的文件或对应的ZIP压缩包',
};
```

### 3.2 第二阶段：ZIP文件解析器新增

**目标：** 新增 `zip-parser.ts` 支持ZIP压缩文件解析

**文件路径：** `apps/web/modules/saas/shareholder/lib/zip-parser.ts`

**实施内容：**
1. 新增ZIP文件解析器
2. 实现ZIP文件解压和内容验证
3. 支持ZIP文件名严格匹配
4. 验证ZIP内部文件与文件名的一致性

**具体实现内容：**

**1. 新增 `zip-parser.ts` 文件**
```typescript
/**
 * ZIP文件解析器
 * 支持解压ZIP文件并验证内部文件名格式
 *
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */

import JSZip from 'jszip';
import { FILE_NAME_PATTERNS, FILENAME_ERROR_MESSAGES } from './config';
import { parseDBFFile } from './dbf-parser';
import { parseExcelFile } from './excel-parser';

/**
 * 验证ZIP文件名格式
 *
 * @param fileName ZIP文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */
export function validateZipFileName(fileName: string): {
  isValid: boolean;
  expectedInternalFileName?: string;
  registryType?: string;
  error?: string;
} {
  // 检查各种ZIP文件名格式
  if (FILE_NAME_PATTERNS.ZIP_DBF_C01.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.DBF'),
      registryType: 'c01'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_DBF_C05.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.DBF'),
      registryType: 'c05'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T1.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't1'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T2.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't2'
    };
  }

  if (FILE_NAME_PATTERNS.ZIP_EXCEL_T3.test(fileName)) {
    return {
      isValid: true,
      expectedInternalFileName: fileName.replace(/\.zip$/i, '.xls'),
      registryType: 't3'
    };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.INVALID_ZIP_FORMAT
  };
}

/**
 * 解析ZIP文件
 *
 * @param file ZIP文件
 * @returns 解析结果
 * <AUTHOR>
 * @created 2025-06-23 15:50:20
 */
export async function parseZipFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 验证ZIP文件名格式
    const zipValidation = validateZipFileName(file.name);
    if (!zipValidation.isValid) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILENAME_ERROR",
          message: zipValidation.error || "ZIP文件名格式不正确"
        }
      };
    }

    // 解压ZIP文件
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(file);

    // 查找内部文件
    const files = Object.keys(zipContent.files);
    if (files.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "ZIP文件为空"
        }
      };
    }

    // 查找期望的内部文件
    const expectedFileName = zipValidation.expectedInternalFileName!;
    const internalFile = files.find(name =>
      name.toLowerCase() === expectedFileName.toLowerCase()
    );

    if (!internalFile) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: FILENAME_ERROR_MESSAGES.ZIP_CONTENT_MISMATCH,
          suggestion: `期望的内部文件名：${expectedFileName}`
        }
      };
    }

    // 提取内部文件
    const internalFileContent = await zipContent.files[internalFile].async('blob');
    const internalFileObj = new File([internalFileContent], expectedFileName);

    // 根据文件类型调用相应的解析器
    if (expectedFileName.toLowerCase().endsWith('.dbf')) {
      return await parseDBFFile(internalFileObj);
    } else if (expectedFileName.toLowerCase().endsWith('.xls')) {
      return await parseExcelFile(internalFileObj);
    }

    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: "不支持的内部文件格式"
      }
    };

  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "ZIP文件解析失败"
      }
    };
  }
}
```

### 3.3 第三阶段：DBF文件名检测增强

**目标：** 修改 `dbf-parser/common.ts` 中的检测逻辑

**文件路径：** `apps/web/modules/saas/shareholder/lib/dbf-parser/common.ts`

**实施内容：**
1. 替换 `detectRegistryType` 函数实现
2. 添加严格的正则表达式匹配
3. 保持向后兼容性（可配置）
4. 新增专门的DBF文件名验证函数

**具体修改内容：**

**1. 导入新的配置项**
```typescript
import {
  FILE_NAME_PATTERNS,
  STRICT_FILENAME_VALIDATION,
  FILENAME_ERROR_MESSAGES,
  FILE_NAME_EXAMPLES
} from '../config';
```

**2. 修改 `detectRegistryType()` 函数**
```typescript
/**
 * 判断DBF文件是01名册还是05名册
 * 根据文件名严格匹配判断名册类型
 *
 * @param fileName 文件名
 * @returns 名册类型 '01' | '05' | 'unknown'
 * <AUTHOR>
 * @update 2025-06-23 15:25:37 - 添加严格文件名匹配逻辑
 */
export function detectRegistryType(fileName: string): '01' | '05' | 'unknown' {
  if (STRICT_FILENAME_VALIDATION) {
    // 严格模式：使用正则表达式精确匹配
    if (FILE_NAME_PATTERNS.DBF_C01.test(fileName)) {
      return '01';
    }
    if (FILE_NAME_PATTERNS.DBF_C05.test(fileName)) {
      return '05';
    }
    return 'unknown';
  } else {
    // 兼容模式：保留原有的宽松匹配逻辑
    const lowerFileName = fileName.toLowerCase();
    if (lowerFileName.includes('01') || lowerFileName.includes('dqmc01')) {
      return '01';
    }
    if (lowerFileName.includes('05') || lowerFileName.includes('dqmc05')) {
      return '05';
    }
    return 'unknown';
  }
}
```

**3. 新增 `validateDBFFileName()` 函数**
```typescript
/**
 * 验证DBF文件名格式
 *
 * @param fileName 文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 15:25:37
 */
export function validateDBFFileName(fileName: string): {
  isValid: boolean;
  registryType?: '01' | '05';
  error?: string;
  suggestion?: string;
} {
  if (FILE_NAME_PATTERNS.DBF_C01.test(fileName)) {
    return { isValid: true, registryType: '01' };
  }

  if (FILE_NAME_PATTERNS.DBF_C05.test(fileName)) {
    return { isValid: true, registryType: '05' };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.INVALID_DBF_FORMAT,
    suggestion: `正确格式示例：${FILE_NAME_EXAMPLES.DBF_C01} 或 ${FILE_NAME_EXAMPLES.DBF_C05}`
  };
}
```

**4. 修改 `isSupportedFileType()` 函数**
```typescript
/**
 * 检查文件类型是否为支持的格式
 * 支持的格式包括: .dbf, .zip, .xls, .xlsx
 * 在严格模式下还会验证文件名格式
 *
 * @param fileName 文件名
 * @returns 是否为支持的文件格式
 * <AUTHOR>
 * @update 2025-06-23 15:25:37 - 添加严格文件名格式验证
 */
export function isSupportedFileType(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  const isBasicSupported = (
    lowercaseName.endsWith(".dbf") ||
    lowercaseName.endsWith(".zip") ||
    lowercaseName.endsWith(".xls") ||
    lowercaseName.endsWith(".xlsx")
  );

  if (!isBasicSupported) {
    return false;
  }

  // 在严格模式下，DBF文件还需要验证文件名格式
  if (STRICT_FILENAME_VALIDATION && lowercaseName.endsWith(".dbf")) {
    const validation = validateDBFFileName(fileName);
    return validation.isValid;
  }

  return true;
}
```

### 3.4 第四阶段：Excel文件名检测增强

**目标：** 修改 `excel-parser/common.ts` 中的检测逻辑

**文件路径：** `apps/web/modules/saas/shareholder/lib/excel-parser/common.ts`

**实施内容：**
1. 增强 `detectExcelRegistryType` 函数
2. 移除备选的 `includes()` 检测方案
3. 添加严格的正则表达式匹配
4. 新增专门的Excel文件名验证函数

**具体修改内容：**

**1. 导入新的配置项**
```typescript
import {
  FILE_NAME_PATTERNS,
  STRICT_FILENAME_VALIDATION,
  FILENAME_ERROR_MESSAGES,
  FILE_NAME_EXAMPLES,
  RegistryType
} from '../config';
```

**2. 修改 `detectExcelRegistryType()` 函数**
```typescript
/**
 * 检测名册类型
 * 根据文件名严格匹配判断是t1、t2还是t3名册
 *
 * @param fileName 文件名
 * @returns 名册类型
 * <AUTHOR>
 * @update 2025-06-23 15:25:37 - 添加严格文件名匹配逻辑，移除备选检测方案
 */
export function detectExcelRegistryType(fileName: string): RegistryType {
  if (STRICT_FILENAME_VALIDATION) {
    // 严格模式：使用正则表达式精确匹配
    if (FILE_NAME_PATTERNS.EXCEL_T3.test(fileName)) {
      return RegistryType.TYPE_T3;
    }
    if (FILE_NAME_PATTERNS.EXCEL_T2.test(fileName)) {
      return RegistryType.TYPE_T2;
    }
    if (FILE_NAME_PATTERNS.EXCEL_T1.test(fileName)) {
      return RegistryType.TYPE_T1;
    }
    return RegistryType.UNKNOWN;
  } else {
    // 兼容模式：保留原有的检测逻辑
    const lowercaseName = fileName.toLowerCase();

    // 更精确的检测方式：检查文件名是否以t1/t2/t3开头
    if (lowercaseName.startsWith('t3')) {
      return RegistryType.TYPE_T3;
    }
    if (lowercaseName.startsWith('t2')) {
      return RegistryType.TYPE_T2;
    }
    if (lowercaseName.startsWith('t1')) {
      return RegistryType.TYPE_T1;
    }

    // 备选方案：使用includes方法
    if (/\bt3\b/.test(lowercaseName) || lowercaseName.includes('t3')) {
      return RegistryType.TYPE_T3;
    }
    if (/\bt2\b/.test(lowercaseName) || lowercaseName.includes('t2')) {
      return RegistryType.TYPE_T2;
    }
    if (/\bt1\b/.test(lowercaseName) || lowercaseName.includes('t1')) {
      return RegistryType.TYPE_T1;
    }

    return RegistryType.UNKNOWN;
  }
}
```

**3. 新增 `validateExcelFileName()` 函数**
```typescript
/**
 * 验证Excel文件名格式
 *
 * @param fileName 文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 15:25:37
 */
export function validateExcelFileName(fileName: string): {
  isValid: boolean;
  registryType?: RegistryType;
  error?: string;
  suggestion?: string;
} {
  if (FILE_NAME_PATTERNS.EXCEL_T1.test(fileName)) {
    return { isValid: true, registryType: RegistryType.TYPE_T1 };
  }

  if (FILE_NAME_PATTERNS.EXCEL_T2.test(fileName)) {
    return { isValid: true, registryType: RegistryType.TYPE_T2 };
  }

  if (FILE_NAME_PATTERNS.EXCEL_T3.test(fileName)) {
    return { isValid: true, registryType: RegistryType.TYPE_T3 };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.INVALID_EXCEL_FORMAT,
    suggestion: `正确格式示例：${FILE_NAME_EXAMPLES.EXCEL_T1}、${FILE_NAME_EXAMPLES.EXCEL_T2}、${FILE_NAME_EXAMPLES.EXCEL_T3}`
  };
}
```

**4. 修改 `isTSeriesRegistry()` 函数**
```typescript
/**
 * 检查文件是否为t1/t2/t3名册
 * 在严格模式下使用正则表达式验证
 *
 * @param fileName 文件名
 * @returns 是否为t1/t2/t3名册
 * <AUTHOR>
 * @update 2025-06-23 15:25:37 - 添加严格文件名格式验证
 */
export function isTSeriesRegistry(fileName: string): boolean {
  if (STRICT_FILENAME_VALIDATION) {
    // 严格模式：使用正则表达式验证
    return FILE_NAME_PATTERNS.EXCEL_T1.test(fileName) ||
           FILE_NAME_PATTERNS.EXCEL_T2.test(fileName) ||
           FILE_NAME_PATTERNS.EXCEL_T3.test(fileName);
  } else {
    // 兼容模式：保留原有的宽松检测
    const lowercaseName = fileName.toLowerCase();
    return lowercaseName.includes('t1') ||
           lowercaseName.includes('t2') ||
           lowercaseName.includes('t3');
  }
}
```

### 3.5 第五阶段：统一文件解析入口修改

**目标：** 修改 `file-parser.ts` 统一文件解析逻辑

**文件路径：** `apps/web/modules/saas/shareholder/lib/file-parser.ts`

**实施内容：**
1. 在解析前进行严格文件名验证
2. 提供详细的错误信息
3. 统一错误处理流程
4. 新增统一的文件名验证入口

**具体修改内容：**

**1. 导入新的验证函数**
```typescript
import { validateDBFFileName } from './dbf-parser/common';
import { validateExcelFileName } from './excel-parser/common';
import { validateZipFileName, parseZipFile } from './zip-parser';
import { STRICT_FILENAME_VALIDATION, FILENAME_ERROR_MESSAGES } from './config';
```

**2. 新增 `validateFileName()` 统一验证函数**
```typescript
/**
 * 统一文件名验证入口
 * 根据文件类型调用相应的验证函数
 *
 * @param file 要验证的文件
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 15:25:37
 */
export function validateFileName(file: File): {
  isValid: boolean;
  registryType?: string;
  error?: string;
  suggestion?: string;
} {
  const fileName = file.name;

  if (!STRICT_FILENAME_VALIDATION) {
    return { isValid: true };
  }

  // 根据文件扩展名选择验证方式
  if (isDbfFile(fileName)) {
    return validateDBFFileName(fileName);
  }

  if (ExcelParser.isExcelFile(fileName)) {
    const result = validateExcelFileName(fileName);
    return {
      isValid: result.isValid,
      registryType: result.registryType,
      error: result.error,
      suggestion: result.suggestion
    };
  }

  if (isZipFile(fileName)) {
    const result = validateZipFileName(fileName);
    return {
      isValid: result.isValid,
      registryType: result.registryType,
      error: result.error,
      suggestion: result.expectedInternalFileName ? `期望的内部文件：${result.expectedInternalFileName}` : undefined
    };
  }

  return {
    isValid: false,
    error: FILENAME_ERROR_MESSAGES.UNSUPPORTED_TYPE,
    suggestion: '请上传c01、c05、t1、t2、t3格式的文件或对应的ZIP压缩包'
  };
}
```

**3. 修改 `parseShareholderRegistryFile()` 函数**
```typescript
/**
 * 解析股东名册文件
 * 根据文件类型选择适当的解析方法
 *
 * @param file 要解析的文件
 * @returns 解析结果
 * <AUTHOR>
 * @update 2025-06-23 15:25:37 - 添加文件名预验证逻辑
 */
export async function parseShareholderRegistryFile(file: File): Promise<ShareholderRegistryParseResult> {
  try {
    // 第一步：进行文件名格式验证
    const nameValidation = validateFileName(file);
    if (!nameValidation.isValid) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILENAME_ERROR",
          message: nameValidation.error || "文件名格式不正确",
          suggestion: nameValidation.suggestion
        },
      };
    }

    // 第二步：根据文件类型选择解析方法
    if (isDbfFile(file.name)) {
      return await parseDBFFile(file);
    }

    if (ExcelParser.isExcelFile(file.name)) {
      // 检测Excel文件的名册类型
      const registryType = detectExcelRegistryType(file.name);

      // 对于t1名册，使用特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T1) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT1Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }

      // 对于t2名册，添加特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T2) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT2Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }

      // 对于t3名册，添加特定的解析和预处理流程
      if (registryType === RegistryType.TYPE_T3) {
        const result = await ExcelParser.parseExcelFile(file);

        // 如果解析成功且有记录，进行预处理（合并重复记录、排序）
        if (result.success && result.records && result.records.length > 0) {
          // 预处理：合并重复记录
          const mergedRecords = preprocessT3Records(result.records);

          // 更新结果中的记录
          result.records = mergedRecords;
          result.recordCount = mergedRecords.length;
        }

        return result;
      }

      // 其他类型的Excel文件使用通用解析方法
      return await ExcelParser.parseExcelFile(file);
    }

    if (isZipFile(file.name)) {
      return await parseZipFile(file);
    }

    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: "不支持的文件格式，请上传DBF、XLS、XLSX或ZIP文件",
      },
    };
  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "文件解析失败",
      },
    };
  }
}
```

### 3.6 第六阶段：前端组件优化

**目标：** 优化前端用户体验和错误提示

**实施内容：**
1. 在点击"开始导入"时进行严格文件名验证
2. 提供更友好的错误提示，包括ZIP文件相关错误
3. 添加文件名格式说明，包括ZIP文件格式

**修改组件：**
- `ShareholderRegistryImport.tsx` - 在 `validateAndImportNext()` 中添加严格文件名验证
- `ShareholderRegistryImportErrorDialog.tsx` - 优化错误提示内容，支持ZIP文件错误信息

**关键修改点：**
1. **导入时机：** 严格验证只在用户点击"开始导入"按钮时执行
2. **ZIP支持：** 添加ZIP文件格式的错误提示和帮助信息
3. **用户引导：** 提供ZIP文件名与内部文件名对应关系的说明

## 4. 技术实现细节

### 4.1 正则表达式设计

#### DBF文件名规则
```typescript
// DQMC01_300723_20230430.DBF
const DBF_C01_PATTERN = /^DQMC01_\d{6}_\d{8}\.DBF$/i;

// DQMC05_300723_20240330.DBF  
const DBF_C05_PATTERN = /^DQMC05_\d{6}_\d{8}\.DBF$/i;
```

#### Excel文件名规则
```typescript
// t16053380320240625all.625.xls
const EXCEL_T1_PATTERN = /^t1\d{8}\d{2}\d{8}all\.\d{3}\.xls$/i;

// t26053380320241231t200.c31.xls
const EXCEL_T2_PATTERN = /^t2\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.xls$/i;

// t36053380320241231t200.c31.xls
const EXCEL_T3_PATTERN = /^t3\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.xls$/i;
```

#### ZIP文件名规则
```typescript
// DQMC05_300723_20240430.zip (内部包含 DQMC05_300723_20240430.DBF)
const ZIP_DBF_C01_PATTERN = /^DQMC01_\d{6}_\d{8}\.zip$/i;
const ZIP_DBF_C05_PATTERN = /^DQMC05_\d{6}_\d{8}\.zip$/i;

// t36053380320241231t200.c31.zip (内部包含 t36053380320241231t200.c31.xls)
const ZIP_EXCEL_T1_PATTERN = /^t1\d{8}\d{2}\d{8}all\.\d{3}\.zip$/i;
const ZIP_EXCEL_T2_PATTERN = /^t2\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.zip$/i;
const ZIP_EXCEL_T3_PATTERN = /^t3\d{8}\d{2}\d{8}t\d{3}\.c\d{2}\.zip$/i;
```

### 4.2 验证函数设计

```typescript
/**
 * 严格验证文件名格式
 * @param fileName 文件名
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-06-23 15:19:17
 */
export function validateStrictFileName(fileName: string): {
  isValid: boolean;
  registryType?: RegistryType;
  error?: string;
}
```
