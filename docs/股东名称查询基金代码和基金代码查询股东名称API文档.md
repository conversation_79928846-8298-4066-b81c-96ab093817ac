# 股东名称查询基金代码和基金代码查询股东名称 API 文档

## 概述

本API提供双向查询功能：
1. 根据基金代码查询对应的股东名称信息
2. 根据股东名称查询对应的基金代码信息

**API端点**: `/api/n8n_proxy/fund_institution`  
**请求方法**: `POST`  
**内容类型**: `application/json`

---

## 请求参数

### 通用参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| `organizationId` | string | 是 | 组织ID，用于数据隔离 | `"Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36"` |

### 查询类型1：基金代码查询股东名称

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| `fund_code` | string | 是 | 基金代码，格式为6位数字.后缀 | `"001384.OF"` |

### 查询类型2：股东名称查询基金代码

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| `shareholder_name` | string | 是 | 股东名称（证券账户名称） | `"中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"` |

---

## 请求示例

### 示例1：根据基金代码查询股东名称

```json
{
  "fund_code": "001384.OF",
  "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36"
}
```

### 示例2：根据股东名称查询基金代码

```json
{
  "shareholder_name": "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金",
  "organizationId": "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36"
}
```

---

## 响应格式

### 成功响应（基金代码查询股东名称）

**HTTP状态码**: `200`

```json
[
	{
		"id": "cmde0cfb6000chvdgb6zy2j1i",
		"shareholderId": "易方证基(2020)2460",
		"unifiedAccountNumber": "************",
		"securitiesAccountName": "中国工商银行股份有限公司－易方达医药生物股票型证券投资基金",
		"numberOfShares": "5416708.00",
		"shareholdingRatio": "1.19",
		"registerDate": "2024-03-29T00:00:00.000Z",
		"contactNumber": "***********",
		"contactAddress": "",
		"custodian_part": "中国工商银行股份有限公司",
		"fund_name_part": "易方达医药生物股票型证券投资基金",
		"match_status": "EXACT_MATCH"
	}
]

或者

[
	{
		"fund_code": "010387.OF",
		"fund_name": "易方达医药生物股票A",
		"fund_full_name": "易方达医药生物股票型证券投资基金",
		"query_timestamp": "2025-08-05T04:00:42.884Z"
	}
]
```

### 成功响应（股东名称查询基金代码）

**HTTP状态码**: `200`

```json
{
  "fund_code": "110023.OF",
  "fund_name": "易方达医疗保健行业混合A",
  "fund_full_name": "易方达医疗保健行业混合型证券投资基金",
  "query_timestamp": "2025-08-05T11:57:11.744Z"
}
```

### 错误响应

#### 参数验证失败

**HTTP状态码**: `200`

```json
{
	"code": "MISSING_ORGANIZATIONID",
	"message": "缺少必需参数：organizationId。请提供organizationId参数。",
	"field": "organizationId",
	"value": "",
	"timestamp": "2025-08-05T03:59:21.379Z",
	"type": "VALIDATION_ERROR"
}
```

#### 基金不存在

**HTTP状态码**: `200`

```json
{
  "code": 0,
  "message": "没有找到该基金"
}
```

#### 股东名称未找到

**HTTP状态码**: `200`

```json
{
  "code": 0,
  "message": "没有找到该股东对应的基金"
}
```

---

## 业务逻辑说明

### 查询优先级

1. **基金代码查询**：当请求中包含 `fund_code` 参数时，优先执行基金代码查询逻辑
2. **股东名称查询**：当请求中只包含 `shareholder_name` 参数时，执行股东名称查询逻辑

### 数据源

- **基金基础信息**：来源于 `ths_fund_management_custody` 表（同花顺数据库）
- **股东名册信息**：来源于项目数据库的股东名册表

### 特殊处理逻辑

1. **基金筛选**：当股东名称查询返回多个基金时，优先返回以"A"结尾的基金
2. **名称解析**：股东名称按照"托管人－基金全称"格式进行解析
3. **模糊匹配**：支持基金名称的模糊匹配查询

---

## 注意事项

1. **数据时效性**：基金数据会定期更新，`table_updated_at` 字段显示最后更新时间
2. **查询限制**：每次查询限制返回1条记录
3. **组织隔离**：所有查询都基于 `organizationId` 进行数据隔离
4. **响应时间**：查询响应时间包含 `query_timestamp` 字段，便于追踪

---

## 技术实现

**作者**: hayden  
**创建时间**: 2025-08-05 11:57:11  
**更新时间**: 2025-08-05 11:57:11  
**版本**: 1.0.0

本API基于n8n工作流实现，支持加密传输和统一错误处理。
