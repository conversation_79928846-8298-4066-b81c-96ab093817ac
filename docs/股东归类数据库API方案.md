# 股东归类数据库和API方案文档

## 版本信息
| 版本 | 日期       | 作者   | 修订内容描述                                                                 |
|------|------------|--------|------------------------------------------------------------------------------|
| V.8  | 2025-06-26 | hayden | 规则查询接口分组返回时保留matchField字段，确保前端知道匹配方式               |
| V.7  | 2025-06-26 | hayden | 优化规则查询接口，按priority和type分组返回，减少前端缓存数据量               |
| V.6  | 2025-06-26 | hayden | 新增matchField字段存储匹配字段类型，支持SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE三种匹配方式 |
| V.5  | 2025-06-25 | hayden | 新增更新时间检查接口，所有接口改为POST加密，明确API文件目录结构             |

## 1. 概述

### 1.1 背景
为实现股东的智能分类，需要建立一套基于优先级规则的股东归类系统。该系统通过"股东分类规则表"驱动，在股东名册上传时自动执行分类逻辑。

### 1.2 核心目标
1. 建立简化的股东分类规则存储表
2. 提供JSON格式规则批量上传API接口
3. 提供更新时间检查接口，支持前端缓存机制
4. 通过updatedAt字段查询最新规则

### 1.3 技术架构
- 数据库：基于现有Prisma Schema扩展
- API：基于Hono框架，遵循项目现有API规范，使用POST方式加密
- 缓存：前端缓存规则数据，通过更新时间检查接口判断是否需要重新拉取
- 查询：基于updatedAt字段获取最新规则

## 2. 数据库设计

### 2.1 股东分类规则表 (ShareholderClassificationRule)

| 字段名 | 类型 | 描述 | 说明 |
|--------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| priority | Int | 优先级 | 1-20，1为最高优先级 |
| type | String | 股东类型 | 如"知名牛散"、"境内个人"等 |
| rule | String | 具体匹配规则 | 如"葛卫东"、"境内自然人（03）"等 |
| matchField | String | 匹配字段类型 | SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE |
| updatedAt | DateTime | 更新时间 | @updatedAt |

### 2.2 匹配字段类型说明

| matchField值 | 描述 | 匹配逻辑 | 示例 |
|-------------|------|----------|------|
| SHAREHOLDER_NAME | 股东名称完全匹配 | 股东名称字段 === rule值 | rule: "证券股份有限公司" |
| SHAREHOLDER_CATEGORY | 持有人类别匹配 | 持有人类别字段包含rule值 | rule: "国有法人(01)" 或 "2100" |
| SHAREHOLDER_NAME_INCLUDE | 股东名称包含匹配 | 股东名称字段同时包含rule值 | rule: "保险" 或 "公司" |

### 2.3 Prisma Schema 扩展

```prisma
// 股东分类规则表（包含匹配字段类型）
model ShareholderClassificationRule {
  id         String   @id @default(cuid())
  priority   Int      // 优先级 1-20
  type       String   // 股东类型名称
  rule       String   // 具体匹配规则
  matchField String   // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  updatedAt  DateTime @updatedAt

  @@index([priority]) // 优先级索引
  @@index([type]) // 类型索引
  @@index([matchField]) // 匹配字段类型索引
  @@index([updatedAt]) // 更新时间索引，用于查询最新规则
  @@map("shareholder_classification_rule")
}
```

## 3. API接口设计

### 3.1 API文件目录结构

所有股东归类相关的API接口文件应放置在以下目录：
```
packages/api/src/routes/shareholder-registry/
├── classification-rules.ts          # 股东归类规则相关接口
├── classification-update-time.ts    # 更新时间检查接口
└── router.ts                        # 路由配置文件（需要添加新路由）
```

### 3.2 规则批量上传接口

**接口路径：** `POST /api/shareholder-registry/rules/batch-upload`

**功能描述：** 接收JSON格式的股东分类规则数组，批量插入到数据库中（需要加密验证）

**请求参数：**
```typescript
interface BatchUploadRequest {
  rules: ShareholderClassificationRuleInput[]
}

interface ShareholderClassificationRuleInput {
  priority: number    // 优先级 1-20
  type: string       // 股东类型
  rule: string       // 匹配规则
  matchField: string // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
}
```

**字段校验规则：**
```typescript
// 在 packages/api/src/routes/shareholder-registry/lib/validators.ts 中添加
export const ShareholderClassificationRuleSchema = z.object({
	priority: z.number().int().min(1),
	type: z.string().min(1),
	rule: z.string().min(1)
});

export const BatchUploadRulesSchema = z.object({
	rules: z.array(ShareholderClassificationRuleSchema).min(1)
});

```

**错误响应示例：**
```json
{
  "code": 400,
  "message": "请求参数验证失败",
  "data": {
    "errors": [
      {
        "field": "rules[0].priority",
        "message": "优先级最小值为1"
      },
      {
        "field": "rules[1].type",
        "message": "股东类型不能为空"
      },
      {
        "field": "rules",
        "message": "优先级不能重复"
      }
    ]
  }
}
```

**请求示例：**
```json
{
  "rules": [
    {
      "priority": 1,
      "type": "知名牛散",
      "rule": "葛卫东",
      "matchField": "SHAREHOLDER_NAME"
    },
    {
      "priority": 13,
      "type": "保险",
      "rule": "保险",
      "matchField": "SHAREHOLDER_NAME_INCLUDE"
    },
    {
      "priority": 13,
      "type": "保险",
      "rule": "公司",
      "matchField": "SHAREHOLDER_NAME_INCLUDE"
    },
    {
      "priority": 16,
      "type": "国有机构",
      "rule": "国有法人(01)",
      "matchField": "SHAREHOLDER_CATEGORY"
    },
    {
      "priority": 16,
      "type": "国有机构",
      "rule": "2100",
      "matchField": "SHAREHOLDER_CATEGORY"
    }
  ]
}
```

**响应格式：**
```typescript
interface BatchUploadResponse {
  code: number
  message: string
  data: {
    totalInserted: number
    insertedIds: string[]
    updatedAt: string // ISO格式时间戳
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "规则批量上传成功",
  "data": {
    "totalInserted": 3,
    "insertedIds": ["cm123abc", "cm456def", "cm789ghi"],
    "updatedAt": "2025-06-25T09:51:57.673Z"
  }
}
```

### 3.3 更新时间检查接口

**接口路径：** `POST /api/shareholder-registry/rules/check-update-time`

**功能描述：** 获取股东分类规则的最新更新时间，用于前端缓存判断（需要加密验证）

**请求参数：**
```typescript
interface CheckUpdateTimeRequest {
  // 空对象，不需要参数
}
```

**请求示例：**
```json
{}
```

**响应格式：**
```typescript
interface CheckUpdateTimeResponse {
  code: number
  message: string
  data: {
    lastUpdatedAt: string // 最新的updatedAt时间
    totalRules: number    // 规则总数
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取更新时间成功",
  "data": {
    "lastUpdatedAt": "2025-06-25T09:51:57.673Z",
    "totalRules": 20
  }
}
```

### 3.4 规则查询接口

**接口路径：** `POST /api/shareholder-classification/rules/list`

**功能描述：** 查询所有股东分类规则，按优先级排序（需要加密验证）

**请求参数：**
```typescript
interface RulesListRequest {
  // 空对象，查询所有规则
}
```

**请求示例：**
```json
{}
```

**响应格式：**
```typescript
interface RulesQueryResponse {
  code: number
  message: string
  data: {
    rules: ShareholderClassificationRuleGroup[]
    total: number
    lastUpdatedAt: string // 最新的updatedAt时间
  }
}

// 分组后的规则结构，保留matchField确保前端知道匹配方式
interface ShareholderClassificationRuleGroup {
  priority: number    // 优先级
  type: string       // 股东类型
  matchField: string // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  rules: string[]    // 该优先级和类型下的所有匹配规则
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "rules": [
      {
        "priority": 1,
        "type": "知名牛散",
        "matchField": "SHAREHOLDER_NAME",
        "rules": ["葛卫东"]
      },
      {
        "priority": 13,
        "type": "保险",
        "matchField": "SHAREHOLDER_NAME_INCLUDE",
        "rules": ["保险", "公司"]
      },
      {
        "priority": 14,
        "type": "证券",
        "matchField": "SHAREHOLDER_NAME",
        "rules": ["证券股份有限公司", "金融股份有限公司"]
      },
      {
        "priority": 15,
        "type": "年金",
        "matchField": "SHAREHOLDER_NAME",
        "rules": ["年金"]
      },
      {
        "priority": 16,
        "type": "国有机构",
        "matchField": "SHAREHOLDER_CATEGORY",
        "rules": ["国有法人(01)", "2100"]
      },
      {
        "priority": 17,
        "type": "境内机构",
        "matchField": "SHAREHOLDER_CATEGORY",
        "rules": ["境内一般法人(02)", "2000", "2002", "2001"]
      }
    ],
    "total": 6,
    "lastUpdatedAt": "2025-06-25T09:51:57.673Z"
  }
}
```

### 3.5 规则查询接口分组逻辑说明

**分组处理逻辑：**
1. **数据库查询**：查询所有规则记录，按 `priority`、`type` 和 `matchField` 分组
2. **分组聚合**：将相同 `priority`、`type` 和 `matchField` 的记录合并，`rule` 字段组成数组
3. **matchField保留**：保留 `matchField` 字段返回给前端，确保前端知道匹配方式
4. **前端匹配**：前端根据 `matchField` 确定使用股东名称、持有人类别还是包含匹配

**后端分组实现示例：**
```typescript
// 伪代码：后端分组逻辑
const rawRules = await prisma.shareholderClassificationRule.findMany({
  orderBy: [{ priority: 'asc' }, { type: 'asc' }]
});

// 按 priority + type + matchField 分组
const groupedRules = rawRules.reduce((acc, rule) => {
  const key = `${rule.priority}-${rule.type}-${rule.matchField}`;
  if (!acc[key]) {
    acc[key] = {
      priority: rule.priority,
      type: rule.type,
      matchField: rule.matchField,
      rules: []
    };
  }
  acc[key].rules.push(rule.rule);
  return acc;
}, {});

const result = Object.values(groupedRules);
```

**优势：**
- 减少前端缓存数据量（去除id、updatedAt等字段）
- 保留关键匹配信息（priority、type、matchField、rules）
- 分组后数据结构更紧凑，便于缓存和使用
- 前端明确知道每个规则组的匹配方式

## 4. 前端缓存机制

### 4.1 缓存流程
1. **首次加载**：调用`/rules/list`接口获取分组后的规则数据，同时缓存`lastUpdatedAt`时间
2. **后续检查**：每次上传股东名册前，调用`/check-update-time`接口
3. **缓存判断**：比较返回的`lastUpdatedAt`与缓存时间
4. **更新策略**：如果时间不一致，重新调用`/rules/list`接口更新缓存

### 4.2 缓存优势
- 减少不必要的数据传输（分组后数据量更小）
- 提高股东名册处理速度
- 降低服务器负载
- 简化前端数据结构，便于存储和使用
- 前端明确知道每个规则组的匹配方式

### 4.3 前端缓存数据结构
```typescript
// 前端缓存的数据结构
interface CachedClassificationData {
  rules: ShareholderClassificationRuleGroup[];  // 分组后的规则数据
  lastUpdatedAt: string;                        // 最后更新时间
  cachedAt: number;                            // 缓存时间戳
}

// 前端使用示例
const cachedData: CachedClassificationData = {
  rules: [
    { priority: 1, type: "知名牛散", matchField: "SHAREHOLDER_NAME", rules: ["葛卫东"] },
    { priority: 13, type: "保险", matchField: "SHAREHOLDER_NAME_INCLUDE", rules: ["保险", "公司"] },
    { priority: 16, type: "国有机构", matchField: "SHAREHOLDER_CATEGORY", rules: ["国有法人(01)", "2100"] }
  ],
  lastUpdatedAt: "2025-06-26T14:04:31.464Z",
  cachedAt: Date.now()
};
```

## 5. 实现说明

### 5.1 数据库操作
- 批量上传时，先清空现有规则，再插入新规则
- 使用Prisma的`deleteMany()`和`createMany()`操作
- 所有操作在事务中执行，确保数据一致性

### 5.2 索引优化
- `updatedAt`字段建立索引，用于快速查询最新规则
- `priority`字段建立索引，用于排序查询
- `type`字段建立索引，用于按类型筛选
- `priority + type`组合索引，用于分组查询优化

### 5.3 API实现要点
- 使用Hono框架的现有模式
- 所有接口使用POST方式，支持加密验证
- 遵循项目的错误处理和响应格式规范
- 添加适当的参数验证和错误处理

### 5.4 API文件实现位置
- **classification-rules.ts**：实现批量上传和规则查询接口（查询接口需要实现分组逻辑）
- **classification-update-time.ts**：实现更新时间检查接口
- **router.ts**：需要添加新的路由配置，引入上述两个文件的路由
- **lib/validators.ts**：需要添加`ShareholderClassificationRuleSchema`和`BatchUploadRulesSchema`验证模式

### 6. 数据库配置
1. 修改schema.prisma文件，添加新模型定义
2. 应用迁移到数据库：pnpm --filter database push
3. 重新生成Prisma客户端：pnpm --filter database generate

---

**文档作者：** hayden
**最后更新：** 2025-06-26 14:04:31
**文档版本：** V.8