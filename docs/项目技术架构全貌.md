# 项目技术架构全貌

## 文档信息

**创建时间**: 2025年07月07日 12:05:48  
**作者**: hayden  
**版本**: v1.0  
**描述**: 项目整体技术架构说明，包含前后端结构、数据流程和股东名册模块的完整实现架构

## 1. 项目整体架构概览

本项目采用现代化的全栈架构，基于 Monorepo 管理模式，使用 TypeScript 作为主要开发语言。

### 1.1 技术栈核心组件

- **前端框架**: Next.js 14 (App Router)
- **后端框架**: Hono.js + Node.js
- **数据库**: PostgreSQL + Prisma ORM
- **认证系统**: Better Auth
- **UI组件库**: Radix UI + Tailwind CSS
- **状态管理**: Jotai
- **包管理**: pnpm (Monorepo)
- **类型系统**: TypeScript + Zod

### 1.2 项目目录结构

```
supstar_intranet/
├── apps/                          # 应用层
│   └── web/                       # Next.js 前端应用
│       ├── app/                   # App Router 路由
│       ├── modules/               # 功能模块
│       ├── middleware.ts          # Next.js 中间件
│       └── public/                # 静态资源
├── packages/                      # 共享包
│   ├── api/                       # API 服务层
│   │   └── src/
│   │       ├── routes/            # API 路由
│   │       ├── middleware/        # API 中间件
│   │       └── lib/               # 工具库
│   ├── database/                  # 数据库层
│   │   ├── prisma/                # Prisma 配置
│   │   └── src/                   # 数据库工具
│   ├── config/                    # 配置管理
│   ├── payments/                  # 支付模块
│   └── ui/                        # UI 组件库
└── docs/                          # 项目文档
```

## 2. 前端架构设计

### 2.1 Next.js 应用结构

```
apps/web/
├── app/                           # App Router 主目录
│   ├── (marketing)/               # 营销页面分组
│   │   └── [locale]/              # 国际化路由
│   ├── (saas)/                    # SaaS 应用分组
│   │   ├── auth/                  # 认证页面
│   │   └── app/                   # 应用内页面
│   ├── api/                       # API 路由代理
│   ├── layout.tsx                 # 根布局
│   └── middleware.ts              # 路由中间件
├── modules/                       # 功能模块
│   ├── analytics/                 # 分析模块
│   ├── i18n/                      # 国际化
│   ├── marketing/                 # 营销模块
│   ├── saas/                      # SaaS 功能
│   ├── shared/                    # 共享组件
│   └── ui/                        # UI 组件
└── public/                        # 静态资源
```

### 2.2 中间件处理流程

Next.js 中间件 (`apps/web/middleware.ts`) 负责以下功能：

1. **路由保护**: 验证用户认证状态
2. **组织管理**: 处理多组织架构的路由逻辑
3. **国际化**: 处理多语言路由
4. **订阅验证**: 检查用户订阅状态
5. **重定向逻辑**: 根据用户状态进行页面跳转

## 3. 后端架构设计

### 3.1 API 服务架构

```
packages/api/src/
├── app.ts                         # 主应用入口
├── routes/                        # API 路由模块
│   ├── auth/                      # 认证路由
│   ├── organizations/             # 组织管理
│   ├── payments/                  # 支付处理
│   ├── shareholder-registry/      # 股东名册 (核心模块)
│   ├── meeting/                   # 会议管理
│   ├── n8n_proxy/                 # n8n 代理
│   └── time/                      # 时间同步
├── middleware/                    # 中间件
│   ├── auth.ts                    # 认证中间件
│   ├── cors.ts                    # 跨域处理
│   ├── logger.ts                  # 日志记录
│   └── shareholder-crypto.ts      # 股东数据加密
└── lib/                           # 工具库
    ├── crypto-helper.ts           # 加密工具
    └── utils.ts                   # 通用工具
```

### 3.2 API 路由注册流程

```typescript
// packages/api/src/app.ts
const appRouter = app
  .route("/", authRouter)              // 认证路由 - 最高优先级
  .route("/", healthRouter)            // 健康检查
  .route("/", timeRouter)              // 时间同步
  .route("/", webhooksRouter)          // Webhook
  .route("/", aiRouter)                // AI 服务
  .route("/", uploadsRouter)           // 文件上传
  .route("/", paymentsRouter)          // 支付处理
  .route("/", contactRouter)           // 联系管理
  .route("/", newsletterRouter)        // 新闻订阅
  .route("/", organizationsRouter)     // 组织管理
  .route("/", adminRouter)             // 管理员功能
  .route("/", shareholderRegistryRouter) // 股东名册 (核心)
  .route("/", meetingApiRouter)        // 会议API
  .route("/", n8nProxyRouter);         // n8n代理
```

## 4. 数据库架构

### 4.1 核心数据模型

```
数据库模型关系:
├── User (用户)
├── Organization (组织)
├── Session (会话)
├── Account (账户)
├── Member (成员)
├── ShareholderRegistry (股东名册) ⭐
├── CompanyInfo (公司信息) ⭐
├── Shareholder (股东信息) ⭐
├── Purchase (购买记录)
└── AiChat (AI聊天)
```

### 4.2 股东名册数据模型

股东名册模块包含三个核心表：

1. **ShareholderRegistry**: 名册基本信息
2. **CompanyInfo**: 公司详细信息  
3. **Shareholder**: 股东详细信息

## 5. 股东名册模块架构 (核心功能)

### 5.1 模块文件结构

```
packages/api/src/routes/shareholder-registry/
├── router.ts                      # 主路由聚合器
├── types.ts                       # TypeScript 类型定义
├── upload.ts                      # 名册上传处理
├── list.ts                        # 名册列表查询
├── register-dates.ts              # 期数日期查询
├── shareholders.ts                # 股东信息查询
├── delete.ts                      # 名册删除操作
├── shareholding-changes.ts        # 持股变化分析
├── shareholder-types.ts           # 股东类型查询
├── classification-rules.ts        # 分类规则管理
├── classification-update-time.ts  # 分类更新时间
├── handlers/                      # 业务处理器
│   ├── upload-01.ts               # 深市01名册处理
│   ├── upload-05.ts               # 深市05名册处理
│   ├── upload-t1.ts               # 沪市t1名册处理
│   ├── upload-t2.ts               # 沪市t2名册处理
│   ├── upload-t3.ts               # 沪市t3名册处理
│   ├── merge-01-05.ts             # 深市名册合并
│   └── merge-t1-t2-t3.ts          # 沪市名册合并
└── lib/                           # 工具库
    ├── config.ts                  # 配置管理
    ├── field-mapping.ts           # 字段映射
    ├── registry-handlers.ts       # 核心处理逻辑
    ├── utils.ts                   # 工具函数
    └── validators.ts              # 数据验证
```

### 5.2 API 接口设计

股东名册模块提供以下核心接口：

1. **POST /api/shareholder-registry/upload** - 上传股东名册
2. **POST /api/shareholder-registry/list** - 查询名册列表
3. **POST /api/shareholder-registry/register-dates** - 获取期数日期
4. **POST /api/shareholder-registry/shareholders** - 查询股东信息
5. **POST /api/shareholder-registry/delete** - 删除名册
6. **POST /api/shareholder-registry/shareholding-changes** - 持股变化分析
7. **POST /api/shareholder-registry/shareholder-types** - 股东类型查询
8. **POST /api/shareholder-registry/classification-rules** - 分类规则管理

### 5.3 数据安全机制

股东名册模块实现了完整的数据加密保护：

1. **请求加密**: 使用 AES-256-GCM 加密请求数据
2. **响应加密**: 自动加密返回的敏感数据
3. **签名验证**: HMAC-SHA256 防篡改验证
4. **时间戳验证**: 防重放攻击机制

## 6. 开发流程和规范

### 6.1 代码规范

- **TypeScript**: 严格类型检查
- **Biome**: 代码格式化和检查
- **JSDoc**: 完整的函数注释
- **Zod**: 运行时类型验证

### 6.2 API 开发规范

1. 所有 API 使用 POST 请求（便于加密）
2. 统一的错误处理和响应格式
3. 完整的参数验证和类型安全
4. 详细的 JSDoc 注释和变更记录

### 6.3 安全规范

1. 认证中间件保护所有敏感接口
2. 敏感数据自动加解密处理
3. 组织级别的数据隔离
4. 完整的审计日志记录

## 7. 部署和运维

### 7.1 环境配置

- **开发环境**: Windows PowerShell + pnpm
- **数据库**: PostgreSQL
- **缓存**: Redis (可选)
- **文件存储**: 本地存储 + 云存储

### 7.2 监控和日志

- API 请求日志记录
- 错误监控和告警
- 性能指标收集
- 数据库查询优化

## 8. 数据流程详解

### 8.1 用户认证流程

```
用户访问 → Next.js中间件 → 认证检查 → 会话验证 → 组织权限 → 页面渲染
```

1. **路由拦截**: middleware.ts 拦截所有请求
2. **认证验证**: 检查用户登录状态和会话有效性
3. **组织管理**: 验证用户组织权限和活跃组织
4. **订阅检查**: 验证用户订阅状态和功能权限
5. **页面重定向**: 根据用户状态进行相应页面跳转

### 8.2 API请求流程

```
前端请求 → 数据加密 → API路由 → 中间件链 → 业务处理 → 数据库操作 → 响应加密 → 前端接收
```

1. **请求加密**: 前端使用AES-256-GCM加密敏感数据
2. **路由分发**: Hono.js根据路径分发到对应处理器
3. **中间件处理**: 认证→解密→验证→权限检查
4. **业务逻辑**: 执行具体的业务处理逻辑
5. **数据持久化**: 通过Prisma ORM操作PostgreSQL数据库
6. **响应处理**: 加密响应数据并返回给前端

### 8.3 股东名册处理流程

```
Excel上传 → 文件解析 → 类型识别 → 字段映射 → 数据验证 → 信息提取 → 数据库保存 → 响应返回
```

1. **文件上传**: 前端上传Excel文件到后端
2. **类型识别**: 根据文件名和内容识别名册类型（01/05/t1/t2/t3）
3. **数据解析**: 使用对应的处理器解析Excel数据
4. **字段映射**: 将Excel列映射到标准数据字段
5. **数据验证**: 验证数据格式和业务规则
6. **信息提取**: 提取公司信息和股东信息
7. **事务保存**: 使用数据库事务确保数据一致性

## 9. 关键技术特性

### 9.1 安全机制

- **端到端加密**: AES-256-GCM + HMAC-SHA256
- **防重放攻击**: 时间戳验证机制
- **权限控制**: 基于组织的数据隔离
- **会话管理**: Better Auth提供的安全会话

### 9.2 性能优化

- **数据库优化**: Prisma查询优化和索引设计
- **缓存策略**: 合理的数据缓存和查询缓存
- **批量处理**: 大数据量的批量操作优化
- **异步处理**: 非阻塞的异步数据处理