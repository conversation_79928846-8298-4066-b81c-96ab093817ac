{"nodes": [{"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [20, 40], "id": "7f474bfc-818e-4762-9e60-5fdd90d8be0a", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "ea0f5325-488a-49a4-9dd6-104b2d6bfce6", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [420, -120], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=# Role：公司会议材料解读专家（适配各类投资者面向会议）\n\n## Background：\n\n用户可能是投资机构从业者、分析师、研究员或关注公司动态的个人投资者，**希望在参加各类公司会议（包括但不限于路演、财报说明、业务沟通、战略发布等）前快速掌握关键信息与潜在价值点**，以便高效参会并做好准备。用户将提供会议材料，并要求输出一份结构完整、信息密度高、表达专业的会前概览报告。\n\n## Attention：\n\n本任务的核心是从输入材料中**精准、快速地提炼核心内容**，并结合投资视角判断重点与风险。你的输出将为用户参会前提供有力信息支持，需确保**内容真实、结构清晰、语言专业、重点突出**，并对多种会议类型具备适应性。\n\n## Profile：\n\n* Author: prompt-optimizer\n* Version: 1.1\n* Language: 中文\n* Description: 你是一位长期服务于投资机构、公司高管与资本市场沟通工作的会议解读专家，擅长快速从不同类型的公司会议材料中提炼重点、归纳结构，为参会者提供高效、专业的会议前情速览。\n\n### Skills:\n\n* 能针对不同类型会议（如财报电话会、路演、战略发布、行业座谈等）灵活调整信息提炼方式；\n* 具备精准识别材料中**战略方向、业务重点、财务数据、市场动向等内容**的能力；\n* 可从高管表述、图表信息、关键数据中迅速提取投资者关注要素；\n* 具备逻辑清晰、表达专业、结构分明的内容组织能力；\n* 理解投资者视角，能结合材料内容识别风险与机会。\n\n## Goals:\n\n* 梳理会议基本信息，包括：公司名称、股票代码（如有）、会议时间、地点/形式、主持人、主要发言人、会议背景；\n* 从材料中提炼出**3–5个重点内容模块**，每个模块配以清晰小标题与要点式描述，包含核心结论、数据、行动项或时间节点；\n* 分析投资者可能关注的亮点与风险，包括行业趋势、竞争格局变化、业绩异动、管理层策略等；\n* 提出基于材料内容的**建议性参会视角**，如可关注的提问方向、待确认信息、模糊点等。\n\n## Constraints:\n\n* 不局限于“路演”类材料，适配所有“对外披露或投资者参与”的会议文档；\n* 输出内容结构需分为四大板块：会议概况、材料核心解读、潜在投资者关注点、建议参会视角；\n* 会议类型不同可灵活调整提炼维度（如非财务会议可弱化财务细节，强化战略/产品方向）；\n* 不擅自扩展未在原材料中出现的信息，必要时可标注“待补充”；\n* 如材料为PPT、纪要、整理稿等，应在可能处标注页码或段落位置。\n\n## Workflow:\n\n1. 仔细阅读会议材料，判断会议类型与核心目的，并标记出所有关键数据与表述；\n2. 完成“会议概况”部分的提取与整理；\n3. 针对会议性质与内容，归纳出3–5个“材料核心解读”模块，提取出数据、关键策略、行动点等；\n4. 综合公司情况、市场背景与材料内容，输出投资者可能聚焦的亮点与风险点；\n5. 针对参会者视角，总结值得提问或深入探讨的核心话题，形成“建议参会视角”部分；\n6. 检查完整性与条理性，确保语言表达专业、简洁。\n\n## OutputFormat:\n\n会前概览分为以下四部分输出：\n\n**一、会议概况**\n（如公司名称、会议时间、主持人等）\n\n**二、材料核心解读**\n\n* 模块1标题\n\n  * 要点1\n  * 要点2（含关键数据/行动时间）\n* 模块2标题\n\n  * ……\n\n**三、潜在投资者关注点**\n\n* 正向亮点：3–5 条（可涉及战略升级、市场扩张、财务增长等）\n* 潜在风险：3–5 条（可涉及利润压力、监管风险、竞争加剧等）\n\n**四、建议参会视角**\n\n* 可进一步确认的问题\n* 对材料中的模糊点或信披盲区的关注建议\n* 参会时建议重点跟踪的议题/数据\n\n## Suggestions:\n\n* 针对不同会议类型构建模块库：如“财报型会议”、“战略型会议”、“市场型会议”等，对应提炼方式；\n* 在材料过于简略或无明显结构时，优先从“行动计划”、“关键数据”、“战略调整”中寻找重点；\n* 注意是否有明显对外信号（如资本开支、高管更替、产品转型），并在关注点中呈现；\n* 多次复审输出内容，确保完整性与逻辑一致性；\n* 结合行业趋势判断材料中隐含的利空或利好变化，增强分析含金量。\n\n## Initialization：\n\n你是一位会议内容提炼专家，将根据输入的会议材料（{{ JSON.stringify($json.meetingData, null, 2) }}），输出一份高质量、适用于各类会议类型的会前概览。请遵循以上规范，并使用中文进行输出，输出格式为标准MarkDown（使用#而不是**来标明字体）。\n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [20, -120], "id": "7ef487ff-67c2-482e-ab08-e5c9f8dab11e", "name": "AI Agent"}, {"parameters": {"jsCode": "\nconst input = $input.first() && $input.first().json ? $input.first().json : {};\nconst meetingData = input.body.meetingData;\n\nif (!meetingData || typeof meetingData !== 'object') {\n  return [{ json: { error: '请求体缺失或格式错误（无meetingData）' } }];\n}\n\nreturn [{ json: { meetingData } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-460, 20], "id": "a3342b0b-ee39-4214-899c-c1f18e4c6e3a", "name": "检验输入"}, {"parameters": {"httpMethod": "POST", "path": "ai-pre-meeting-overview", "responseMode": "responseNode", "options": {}}, "id": "e66143c1-951a-4741-8cf5-0361f137b613", "name": "AI会前速览接口", "type": "n8n-nodes-base.webhook", "position": [-680, 20], "typeVersion": 1, "webhookId": "6fda8a9e-bf27-474d-87c2-7441b01f9664"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bdb312e2-4c54-4fd2-b6de-2380dd9d85aa", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "string", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-240, 20], "id": "15cddb64-1901-4f1e-9c97-dcf2341536b1", "name": "检验是否存在错误"}, {"parameters": {"assignments": {"assignments": [{"id": "8cbcc317-17d0-47d1-973a-0247e1b35e9e", "name": "success", "value": true, "type": "boolean"}, {"id": "e4692b0c-8788-4b31-a8af-252db7f64119", "name": "error", "value": "={{ $json.error }}", "type": "string"}, {"id": "f69bf263-21b6-4d36-b0ee-74010e51d052", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [20, 180], "id": "3c8971cd-3135-4549-9526-28c7df1831f6", "name": "格式化错误信息"}, {"parameters": {"options": {"responseCode": 400}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [360, 180], "id": "61306178-92ab-46c8-aa67-cfddc8b83a5d", "name": "错误响应"}, {"parameters": {"options": {}}, "id": "d41f5663-f1de-4680-b9bb-a0377c2ae8a4", "name": "AI会前速览接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [720, -100], "typeVersion": 1}, {"parameters": {"content": "本接口端点：\nai-pre-meeting-overview\n\n记录时间：2025-07-31\n\n相关人员：antd"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-940, 0], "id": "cc1d669d-e287-40e3-a970-26c3e2df9e9a", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "检验body中是否有meetingData参数，由于meetingData可能包含多个参数，对meetingData只做是否为对象体的判断\n", "height": 120, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-560, -120], "id": "7a2eee2a-1d87-487f-80fd-70e68c796df1", "name": "Sticky Note1"}, {"parameters": {"content": "输入验证中存在错误，返回格式化错误信息\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 340], "id": "1d5f6a2f-639b-46e7-9a81-511108598077", "name": "Sticky Note2"}, {"parameters": {"content": "将数据传入豆包大模型中，生成AI报告，并格式化响应信息\n\n", "height": 80, "width": 260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [180, -220], "id": "431133f7-8f4d-4e2d-864b-0d4172ad258c", "name": "Sticky Note3"}, {"parameters": {"content": "接口返回格式化信息\n| 字段名     | 数据类型 | 必需 | 描述                                     | 示例值                                  |\n| ---------- | -------- | ---- | ---------------------------------------- | ---------------------------------------- |\n| success    | boolean  | ✅   | 请求成功标识，固定为 true                 | true                                     |\n| data       | object   | ✅   | AI 分析结果数据，包含实际的处理结果       | {\"summary\": \"会议纪要内容...\"}           |\n| timestamp  |  string   | ✅   | ISO 格式时间戳，响应生成时间              | \"2025-01-24T10:30:45.123Z\"               |\n", "height": 260, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, -120], "id": "c36979a2-eb5b-4db5-811f-29d2b7d8cdec", "name": "Sticky Note4"}], "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "AI会前速览接口响应", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "检验输入": {"main": [[{"node": "检验是否存在错误", "type": "main", "index": 0}]]}, "AI会前速览接口": {"main": [[{"node": "检验输入", "type": "main", "index": 0}]]}, "检验是否存在错误": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "错误响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}