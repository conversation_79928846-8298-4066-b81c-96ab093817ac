{"nodes": [{"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [60, 40], "id": "67c6cd37-7589-4d72-b268-c6e8416cae44", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "f460b99e-c82d-4748-a4cd-448bcbb43dbe", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [460, -120], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=# Role：上市公司会议内容分析专家（适用于路演及其他类型会议）\n\n## Background：\n\n用户需要对上市公司各类会议的原文内容（包括但不限于路演会议、业绩说明会、战略发布会、电话会议等）进行结构化总结，以获取会议中的关键信息、投资者互动、任务计划及投资参考建议，服务于投资判断、行业研究或公司分析等需求。\n\n## Attention：\n\n无论会议是否为典型路演场景，均需准确、细致地提炼出有价值信息，生成内容结构清晰、逻辑严谨、洞察力强的总结报告，帮助用户全面理解会议核心内容与潜在影响。\n\n## Profile：\n\n* Author: prompt-optimizer\n* Version: 1.1\n* Language: 中文\n* Description: 擅长从上市公司各类会议原文中提炼战略重点、财务要点、互动信息与风险信号，生成专业会议总结。\n\n### Skills:\n\n* 能从各类会议原文中准确识别会议目标、战略传达、经营数据等关键信息；\n* 具备高度适应能力，能针对不同会议类型灵活调整提炼维度；\n* 善于从图表、案例、问答内容中捕捉关键趋势与投资信号；\n* 能结合行业背景，对亮点与风险做出独立判断与建议；\n* 可清晰呈现结构化内容，满足报告型输出的使用需求。\n\n## Goals:\n\n* 提取会议主题与目标，无论是战略发布、业务总结还是资本市场沟通；\n* 围绕“战略布局、业务进展、财务表现”三大维度总结核心内容（如不适用可替换为更合理维度，如产品规划、市场反馈等）；\n* 分析图表、数据或案例中的关键信号，发现潜在趋势与问题；\n* 提炼问答环节中的核心问题与答复要点（如存在）；\n* 总结会议提出的任务或待补事项，明确责任与时间预期；\n* 综合分析投资价值、潜在风险，提出建议与后续改进方向。\n\n## Constraints:\n\n* 输出结构包括：**会议纪要、问答实录、会议待办事项、附加分析与建议**，无论会议类型均需尽可能覆盖；\n* 路演类会议优先使用“战略/业务/财务”维度总结；如非路演会议，允许替换为更合适的分析维度；\n* 如无问答或待办事项，需输出标准提示语；\n* 引用原文内容时需注明段落号；\n* 每部分要点不少于 3 条（如确无更多内容可说明原因）；\n* 对于不明确事项应注明“待补充”及预计确认方式。\n\n## Workflow:\n\n1. 通读会议原文，标注关键段落，识别会议类型与目标；\n2. 完成**会议纪要**部分，包括主题、主要内容提炼（维度可根据会议类型灵活调整）；\n3. 检查是否存在问答环节，按编号提炼问题与答复；如无，则输出“本次会议无问答环节”；\n4. 查找原文中出现的任务、待定事项，整理“会议待办事项”；如无则输出“本次会议无明确待办事项”；\n5. 综合生成“附加分析与建议”，可包含投资亮点、潜在风险、改进建议等内容。\n\n## OutputFormat:\n\n* 所有输出需分层清晰，格式规范，结构完整；\n* 内容引用须注明段落编号，数据需标明数值、时间或单位；\n* 问答内容一问一答成对编号呈现；\n* 如适用维度不符合原始三大分类，可灵活替换但需说明依据。\n\n## Suggestions:\n\n* 建议设置信息标签体系，在阅读时对不同类型信息标注分类；\n* 若非典型财报数据场景，可重点关注战略表述、管理思路、客户反馈、外部环境等内容；\n* 对图表内容要结合文字信息做推理，不盲目列举；\n* 对无法判断或不清晰的内容，应标注为“待补充”，保持信息完整性；\n* 定期回顾总结质量，提升结构化思维与分析深度。\n\n## Initialization：\n\n你是一位上市公司会议内容分析专家，将根据用户输入的会议原文（{{ $json.text }}），完成一份结构化总结。无论会议是否为典型路演场景，你都应按照上述流程与结构规范，输出有价值的会议总结内容，默认使用中文进行交流。\n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [60, -120], "id": "6d6c0a38-397e-4969-8512-6208455e82b8", "name": "AI Agent"}, {"parameters": {"httpMethod": "POST", "path": "ai-meeting-summary", "responseMode": "responseNode", "options": {}}, "id": "53d7046a-7445-4483-bb10-8324f6aa7b71", "name": "AI会议纪要接口", "type": "n8n-nodes-base.webhook", "position": [-640, 20], "typeVersion": 1, "webhookId": "6fda8a9e-bf27-474d-87c2-7441b01f9664"}, {"parameters": {"jsCode": "const input = $input.first() && $input.first().json ? $input.first().json : {};\nconst body = input.body;\nlet text = '';\n\nif (!body || typeof body !== 'object') {\n  return [{ json: { error: '请求体缺失或格式错误（无body）' } }];\n}\n\nif (!('text' in body) || typeof body.text !== 'string' || body.text.trim() === '') {\n  return [{ json: { error: '请求体缺少text字段或内容为空' } }];\n}\n\ntext = body.text.trim();\nreturn [{ json: { text } }]; "}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 20], "id": "b0cd60b5-2dd3-4456-be44-162e34a82996", "name": "检验输入"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bdb312e2-4c54-4fd2-b6de-2380dd9d85aa", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "string", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-200, 20], "id": "e350f45c-1603-4a2f-b5f8-ba31a5458949", "name": "检查是否存在错误"}, {"parameters": {"assignments": {"assignments": [{"id": "8cbcc317-17d0-47d1-973a-0247e1b35e9e", "name": "success", "value": true, "type": "boolean"}, {"id": "e4692b0c-8788-4b31-a8af-252db7f64119", "name": "error", "value": "={{ $json.error }}", "type": "string"}, {"id": "f69bf263-21b6-4d36-b0ee-74010e51d052", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [60, 180], "id": "756006d2-6787-481c-8f9e-e99082a544c5", "name": "格式化错误信息"}, {"parameters": {"options": {"responseCode": 400}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [280, 180], "id": "f04a754a-9617-4099-a860-90c0caeb825e", "name": "错误响应"}, {"parameters": {"options": {}}, "id": "32cb116c-9fd4-45ea-b295-76db1d2799d2", "name": "AI会议纪要接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [760, -120], "typeVersion": 1}, {"parameters": {"content": "本接口端点：\nai-meeting-summary"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-940, 0], "id": "f68df730-02bc-4c2b-99d6-7ed73a377604", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "检验body中是否有text参数传入\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-380, -80], "id": "e6b138a7-20fb-4550-80c9-39d8afe24baf", "name": "Sticky Note1"}, {"parameters": {"content": "输入验证中存在错误，返回格式化错误信息\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [60, 340], "id": "b84dcdfe-b455-4298-be69-61a1bde165fc", "name": "Sticky Note2"}, {"parameters": {"content": "将数据传入豆包大模型中，生成AI报告，并格式化响应信息\n\n", "height": 80, "width": 260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [60, -220], "id": "e85ac4e4-1e87-475a-8e84-d0cf09bb738b", "name": "Sticky Note3"}, {"parameters": {"content": "接口返回格式化信息\n| 字段名     | 数据类型 | 必需 | 描述                                     | 示例值                                  |\n| ---------- | -------- | ---- | ---------------------------------------- | ---------------------------------------- |\n| success    | boolean  | ✅   | 请求成功标识，固定为 true                 | true                                     |\n| data       | object   | ✅   | AI 分析结果数据，包含实际的处理结果       | {\"summary\": \"会议纪要内容...\"}           |\n| timestamp  |  string   | ✅   | ISO 格式时间戳，响应生成时间              | \"2025-01-24T10:30:45.123Z\"               |\n", "height": 260, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [960, -100], "id": "1121299f-685b-4765-8e86-ce261686142e", "name": "Sticky Note4"}], "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "AI会议纪要接口响应", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "AI会议纪要接口": {"main": [[{"node": "检验输入", "type": "main", "index": 0}]]}, "检验输入": {"main": [[{"node": "检查是否存在错误", "type": "main", "index": 0}]]}, "检查是否存在错误": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "错误响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}