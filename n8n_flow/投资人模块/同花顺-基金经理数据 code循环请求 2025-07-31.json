{"nodes": [{"parameters": {"options": {"responseCode": 200}}, "id": "5681870c-c857-43b5-b059-75296828d126", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "position": [960, -340], "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, -340], "id": "896a5978-cb19-496a-870d-c89d7ab4a59a", "name": "获取（短期）access token"}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"refresh_token\": \"eyJzaWduX3RpbWUiOiIyMDI1LTA3LTEwIDEwOjQ2OjAxIn0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6529EA27B119F50E6966447B09E7214EF92C1DFD560ABA582055425A090DB61B\"\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, -340], "id": "87e11020-a6c7-4de2-8230-bb736557b2b1", "name": "固定同花顺refresh"}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.data }}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, -340], "id": "9abc0282-e5e0-4a37-bf27-b398e0fb1334", "name": "缓存acc token"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：验证investors数据", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-720, 0], "id": "e752a062-5dc9-43d9-944a-c86146235e9e", "name": "Sticky Note1"}, {"parameters": {"content": "提取设计api所需要的参数：investors数组", "height": 260, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1160, -20], "id": "518f8b63-4242-4f33-8c74-c38a26e0a4aa", "name": "<PERSON><PERSON>"}, {"parameters": {"httpMethod": "POST", "path": "fund_manager", "responseMode": "responseNode", "options": {}}, "id": "8aec1d40-4d08-4c93-9994-056a873d94d4", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-1400, 40], "typeVersion": 2, "webhookId": "0dd71422-0c45-4bff-890b-0cc1c95ab3c4"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "61c80289-57a7-45a9-981f-d95d24e5ae0f", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [140, 460], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "7842d9c8-d849-4d83-9f1f-77da032ffe8e", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-200, 80], "typeVersion": 2}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-11T10:27:58.826Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n * - 2025-07-11 修复数组类型转换问题，添加智能类型转换器\n * - 2025-07-11 10:35:27 hayden 增强数组处理逻辑，修复字符串格式化问题\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 数据类型转换处理器\n  const typeConverters = {\n    array: (val) => {\n      if (Array.isArray(val)) {\n        // 处理数组中的每个元素，清理可能的字符串格式问题\n        return val.map(item => {\n          if (typeof item === 'string') {\n            // 清理字符串中的引号和方括号\n            const cleaned = item.replace(/^\\[\"|\"\\]$|^\"|\"$|^\\[|\\]$/g, '').trim();\n            return cleaned;\n          }\n          return item;\n        });\n      }\n      \n      if (typeof val === 'string') {\n        try {\n          // 尝试解析JSON字符串\n          const parsed = JSON.parse(val);\n          if (Array.isArray(parsed)) {\n            // 递归处理解析后的数组\n            return typeConverters.array(parsed);\n          }\n          // 如果不是数组，尝试按逗号分割\n          return val.split(',').map(item => item.trim()).filter(item => item !== '');\n        } catch {\n          // JSON解析失败，尝试按逗号分割\n          return val.split(',').map(item => item.trim()).filter(item => item !== '');\n        }\n      }\n      return Array.isArray(val) ? val : [val]; // 非数组值转为单元素数组\n    },\n    \n    number: (val) => {\n      if (typeof val === 'number') {\n        return val;\n      }\n      if (typeof val === 'string') {\n        const num = Number(val);\n        return isNaN(num) ? val : num;\n      }\n      return val;\n    },\n    \n    boolean: (val) => {\n      if (typeof val === 'boolean') {\n        return val;\n      }\n      if (typeof val === 'string') {\n        return val.toLowerCase() === 'true' || val === '1';\n      }\n      if (typeof val === 'number') {\n        return val === 1;\n      }\n      return val;\n    },\n    \n    string: (val) => {\n      if (typeof val === 'string') {\n        return val;\n      }\n      return String(val);\n    },\n    \n    object: (val) => {\n      if (typeof val === 'object' && val !== null && !Array.isArray(val)) {\n        return val;\n      }\n      if (typeof val === 'string') {\n        try {\n          const parsed = JSON.parse(val);\n          if (typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)) {\n            return parsed;\n          }\n        } catch {\n          // JSON解析失败，返回原值\n        }\n      }\n      return val;\n    }\n  };\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') {\n        return false;\n      }\n      if (options.minLength && val.length < options.minLength) {\n        return false;\n      }\n      if (options.maxLength && val.length > options.maxLength) {\n        return false;\n      }\n      if (options.pattern && !options.pattern.test(val)) {\n        return false;\n      }\n      if (options.notEmpty && val.trim() === '') {\n        return false;\n      }\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) {\n        return false;\n      }\n      if (options.min !== undefined && num < options.min) {\n        return false;\n      }\n      if (options.max !== undefined && num > options.max) {\n        return false;\n      }\n      if (options.integer && !Number.isInteger(num)) {\n        return false;\n      }\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) {\n        return false;\n      }\n      if (options.minLength && val.length < options.minLength) {\n        return false;\n      }\n      if (options.maxLength && val.length > options.maxLength) {\n        return false;\n      }\n      if (options.notEmpty && val.length === 0) {\n        return false;\n      }\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) {\n        return false;\n      }\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'investors',\n      type: 'array',\n      required: true,\n      options: { \n        notEmpty: true, \n        minLength: 1,\n        itemType: 'string',\n        itemOptions: {\n          pattern: /^\\d{6}\\.[A-Za-z]+$/  // 验证公司代码格式：6位数字+点+字母\n        }\n      },\n      defaultValue: []\n    },\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 🔥 关键修复：先进行类型转换，再进行验证 🔥\n    const converter = typeConverters[type];\n    if (converter) {\n      value = converter(value);\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}，实际值：${JSON.stringify(value)}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}"}, "id": "32ba4eae-28fd-4e9c-92bf-0a16e5f40c76", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-600, 80], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "3e95dc5e-e172-4e12-912e-d25797b69220", "name": "investors", "value": "={{ $json.query?.investors || $json.body?.investors || $json.investors || '' }}", "type": "array"}]}, "options": {}}, "id": "41bf1200-e470-48f4-a0ef-fa2053746afd", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-1080, 60], "typeVersion": 3.4, "notesInFlow": false}, {"parameters": {"jsCode": "/**\n * 循环网络请求获取基金经理资料\n * <AUTHOR>\n * @created 2025-07-15 09:52:20\n * @modified 2025-01-27 14:30:00 - 修改为使用this.helpers.httpRequest方法\n * @description 使用code节点循环请求基金经理数据，替代原有的splitInBatches+httpRequest组合\n */\n\n// 获取输入的基金代码数组\nconst investors = $input.all();\nconst accessToken = $('缓存acc token').item.json.access_token;\nconst results = [];\n\n// 循环处理每个基金代码\nfor (const item of investors) {\n  const investorCode = item.json.investors;\n  \n  try {\n    // 构建请求选项\n    const benchmarkOptions = {\n      url: 'https://quantapi.51ifind.com/api/v1/data_pool',\n      method: 'POST',\n      headers: {\n        'access_token': accessToken,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        \"reportname\": \"p00407\",\n        \"functionpara\": {\n          \"jjlb\": investorCode\n        },\n        \"outputpara\": \"jydm,jydm_mc,p00407_f002,p00407_f009,p00407_f011,p00407_f013,p00407_f015,p00407_f018,p00407_f022,p00407_f023,p00407_f024,p00407_f019\"\n      }),\n      json: true\n    };\n    \n    // 发送HTTP请求\n    const response = await this.helpers.httpRequest(benchmarkOptions);\n    \n    // 将响应数据添加到结果数组，并标记对应的基金代码\n    results.push({\n      investorCode: investorCode,\n      responseData: response,\n      requestTime: new Date().toISOString()\n    });\n    \n  } catch (error) {\n    // 错误处理：记录失败的请求\n    results.push({\n      investorCode: investorCode,\n      error: error.message || 'Request failed',\n      requestTime: new Date().toISOString()\n    });\n  }\n}\n\n// 返回所有结果\nreturn results.map(result => ({\n  json: result\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, -340], "id": "f2112861-22e6-4a3c-9b45-a80cdcadab09", "name": "循环网路请求1", "alwaysOutputData": false, "retryOnFail": false}, {"parameters": {"jsCode": "// 获取输入字段中的字符串并处理为数组\nconst investors = $('检查验证结果').first().json.investors\n\n\n// 输出为数组形式，每个元素是一个代码\nreturn investors.map(code => ({\n  json: { investors: code }\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, -340], "id": "a697e698-4595-465c-9468-751fe560c2f3", "name": "基金代码整理"}, {"parameters": {"jsCode": "/**\n * 重新组织基金数据结构，将零散的数组键值对转换为标准对象数组\n * <AUTHOR>\n * @created 2025-06-24 18:12:45\n * @modified 2025-01-27 14:35:00 - 修改数据结构处理逻辑，适配responseData格式\n * @modified 2025-01-27 14:40:00 - 移除sourceInvestorCode和requestTime字段，严格按照API文档规范\n */\nfunction processItems() {\n  const results = [];\n  \n  // 遍历所有输入项\n  for (const item of $input.all()) {\n    // 获取responseData中的tables数据\n    const responseData = item.json.responseData || {};\n    const tables = responseData.tables || [];\n    \n    // 检查是否有错误\n    if (responseData.errorcode !== 0) {\n      console.warn(`基金代码 ${item.json.investorCode} 请求失败: ${responseData.errmsg}`);\n      continue;\n    }\n    \n    if (tables.length > 0 && tables[0].table) {\n      const tableData = tables[0].table;\n      const itemCount = tableData.jydm ? tableData.jydm.length : 0;\n      \n      // 遍历每个索引位置，创建对应的对象\n      for (let i = 0; i < itemCount; i++) {\n        const fundObject = {};\n        \n        // 遍历表格中的所有字段，提取对应索引的值\n        Object.keys(tableData).forEach((key) => {\n          if (Array.isArray(tableData[key]) && tableData[key][i] !== undefined) {\n            // 将字段名映射为更易读的名称\n            let fieldName = key;\n            \n            // 字段名映射表\n            const fieldMappings = {\n              'jydm': 'fundCode',\n              'jydm_mc': 'fundName',\n              'p00407_f002': 'nav',\n              'p00407_f009': 'totalAssets',\n              'p00407_f011': 'manager',\n              'p00407_f013': 'company',\n              'p00407_f015': 'establishDate',\n              'p00407_f018': 'benchmark',\n              'p00407_f019': 'fundType',\n              'p00407_f022': 'benchmarkCodes',\n              'p00407_f023': 'benchmarkNames',\n              'p00407_f024': 'benchmarkTypes'\n            };\n            \n            // 使用映射名称或原始名称\n            fieldName = fieldMappings[key] || key;\n            \n            // 设置字段值\n            fundObject[fieldName] = tableData[key][i];\n          }\n        });\n        \n        // 添加额外的处理字段（仅包含API文档中定义的字段）\n        fundObject.id = `fund-${fundObject.fundCode || i}`;\n        fundObject.processedAt = new Date().toISOString();\n        \n        // 将处理后的对象添加到结果数组\n        results.push(fundObject);\n      }\n    } else if (item.json.error) {\n      // 处理请求错误的情况\n      console.warn(`基金代码 ${item.json.investorCode} 请求错误: ${item.json.error}`);\n    }\n  }\n  \n  // 创建新的输出项\n  const outputItem = {\n    json: {\n      funds: results,\n      totalCount: results.length,\n      processedAt: new Date().toISOString()\n    }\n  };\n  \n  return [outputItem];\n}\n\n// 执行处理并返回结果\nreturn processItems();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, -340], "id": "04cb9223-d975-480d-9cc1-00eb2696c6f9", "name": "整理基金经理数据"}, {"parameters": {"content": "路由地址：/fund_manager\n\n记录时间：2025-07-31\n\n相关人：hayden jason\n\n相关文档：https://starlinkcap.feishu.cn/wiki/BhiEwGtgzi5mxFkAAJ4cA6g6nQf", "height": 220, "width": 340}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1840, -40], "id": "ac37ae82-1dcf-4f41-8b9e-5393bd769b46", "name": "Sticky Note2"}, {"parameters": {"content": "返回示例：\n\n[\n    {\n        \"funds\": [\n            {\n                \"fundCode\": \"510300.SH\",\n                \"fundName\": \"沪深300ETF\",\n                \"nav\": \"4.2041\",\n                \"totalAssets\": \"3747.04\",\n                \"manager\": \"柳军\",\n                \"company\": \"华泰柏瑞基金管理有限公司\",\n                \"establishDate\": \"2012/05/04\",\n                \"benchmark\": \"沪深300指数\",\n                \"benchmarkCodes\": \"\",\n                \"benchmarkNames\": \"沪深300指数\",\n                \"benchmarkTypes\": \"\",\n                \"fundType\": \"被动指数型股票基金\",\n                \"id\": \"fund-510300.SH\",\n                \"processedAt\": \"2025-07-30T07:39:01.957Z\"\n            },\n            {\n                \"fundCode\": \"512200.SH\",\n                \"fundName\": \"房地产ETF\",\n                \"nav\": \"1.4676\",\n                \"totalAssets\": \"62.32\",\n                \"manager\": \"罗文杰\",\n                \"company\": \"南方基金管理股份有限公司\",\n                \"establishDate\": \"2017/08/25\",\n                \"benchmark\": \"中证全指房地产指数\",\n                \"benchmarkCodes\": \"\",\n                \"benchmarkNames\": \"中证全指房地产指数\",\n                \"benchmarkTypes\": \"\",\n                \"fundType\": \"被动指数型股票基金\",\n                \"id\": \"fund-512200.SH\",\n                \"processedAt\": \"2025-07-30T07:39:01.957Z\"\n            }\n        ],\n        \"totalCount\": 2,\n        \"processedAt\": \"2025-07-30T07:39:01.957Z\"\n    }\n]", "height": 840, "width": 420}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1220, -580], "id": "3e8eda2c-e45e-40cf-bb34-5f6b63e237df", "name": "Sticky Note3"}], "connections": {"获取（短期）access token": {"main": [[{"node": "缓存acc token", "type": "main", "index": 0}]]}, "固定同花顺refresh": {"main": [[{"node": "获取（短期）access token", "type": "main", "index": 0}]]}, "缓存acc token": {"main": [[{"node": "基金代码整理", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "固定同花顺refresh", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "循环网路请求1": {"main": [[{"node": "整理基金经理数据", "type": "main", "index": 0}]]}, "基金代码整理": {"main": [[{"node": "循环网路请求1", "type": "main", "index": 0}]]}, "整理基金经理数据": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}