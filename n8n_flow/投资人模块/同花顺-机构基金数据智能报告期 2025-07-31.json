{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "aefbb3bf-bcba-4b74-90ba-2c2fdc5fad66", "name": "organizationId", "value": "={{ $json.query?.organizationId || $json.body?.organizationId || $json.organizationId || '' }}", "type": "string"}]}, "options": {}}, "id": "05b29962-9205-484d-992f-a70e8106e5c9", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-2920, 200], "typeVersion": 3.4}, {"parameters": {"jsCode": "/**\n * 智能日期计算器 - 向后兼容的季度报告期日期生成\n * <AUTHOR>\n * @date 2025-07-14T17:03:59.319Z\n * 功能说明：\n * - 根据当前日期智能计算应该使用的季度报告期\n * - 生成向后兼容的日期序列，用于API重试机制\n * - 当前季度 → 上一季度 → 上上季度 → 上上上季度\n * 修改记录：\n * - 2025-07-14: 初始创建，实现智能日期判断和向后兼容\n */\n\ntry {\n  const currentDate = new Date();\n  const currentYear = currentDate.getFullYear();\n  const currentMonth = currentDate.getMonth() + 1; // getMonth() 返回 0-11\n  const currentDay = currentDate.getDate();\n  \n  // 定义季度报告期映射\n  const quarterEndDates = {\n    1: '0331', // Q1: 3月31日\n    2: '0630', // Q2: 6月30日  \n    3: '0930', // Q3: 9月30日\n    4: '1231'  // Q4: 12月31日\n  };\n  \n  // 计算当前应该属于哪个季度\n  let currentQuarter;\n  if (currentMonth <= 3) {\n    currentQuarter = 1;\n  } else if (currentMonth <= 6) {\n    currentQuarter = 2;\n  } else if (currentMonth <= 9) {\n    currentQuarter = 3;\n  } else {\n    currentQuarter = 4;\n  }\n  \n  // 生成向后兼容的日期序列（最多4个季度）\n  const dateSequence = [];\n  let year = currentYear;\n  let quarter = currentQuarter;\n  \n  // 特殊逻辑：如果当前是季度初期（比如7月14日），可能上一季度的数据更可靠\n  // 这里可以根据实际业务需求调整\n  const isEarlyInQuarter = (\n    (currentQuarter === 1 && currentMonth === 1) ||\n    (currentQuarter === 2 && currentMonth === 4) ||\n    (currentQuarter === 3 && currentMonth === 7 && currentDay <= 31) ||\n    (currentQuarter === 4 && currentMonth === 10)\n  );\n  \n  // 如果是季度初期，优先使用上一季度的数据\n  if (isEarlyInQuarter) {\n    quarter = quarter - 1;\n    if (quarter === 0) {\n      quarter = 4;\n      year = year - 1;\n    }\n  }\n  \n  // 生成4个季度的日期序列\n  for (let i = 0; i < 4; i++) {\n    const dateString = `${year}${quarterEndDates[quarter]}`;\n    dateSequence.push({\n      date: dateString,\n      year: year,\n      quarter: quarter,\n      description: `${year}年Q${quarter}`,\n      priority: i + 1\n    });\n    \n    // 计算下一个（实际是上一个）季度\n    quarter = quarter - 1;\n    if (quarter === 0) {\n      quarter = 4;\n      year = year - 1;\n    }\n  }\n  \n  // 构建响应数据\n  const result = {\n    currentDate: currentDate.toISOString(),\n    currentDateString: `${currentYear}${String(currentMonth).padStart(2, '0')}${String(currentDay).padStart(2, '0')}`,\n    recommendedDate: dateSequence[0].date,\n    dateSequence: dateSequence,\n    calculationInfo: {\n      currentYear: currentYear,\n      currentMonth: currentMonth,\n      currentDay: currentDay,\n      currentQuarter: currentQuarter,\n      isEarlyInQuarter: isEarlyInQuarter,\n      adjustedQuarter: dateSequence[0].quarter,\n      adjustedYear: dateSequence[0].year\n    },\n    message: `智能日期计算完成，推荐使用${dateSequence[0].description}数据（${dateSequence[0].date}）`\n  };\n  \n  return result;\n  \n} catch (error) {\n  // 异常处理 - 返回默认的日期序列\n  const currentYear = new Date().getFullYear();\n  const fallbackSequence = [\n    { date: `${currentYear}0630`, year: currentYear, quarter: 2, description: `${currentYear}年Q2`, priority: 1 },\n    { date: `${currentYear}0331`, year: currentYear, quarter: 1, description: `${currentYear}年Q1`, priority: 2 },\n    { date: `${currentYear-1}1231`, year: currentYear-1, quarter: 4, description: `${currentYear-1}年Q4`, priority: 3 },\n    { date: `${currentYear-1}0930`, year: currentYear-1, quarter: 3, description: `${currentYear-1}年Q3`, priority: 4 }\n  ];\n  \n  return {\n    currentDate: new Date().toISOString(),\n    recommendedDate: fallbackSequence[0].date,\n    dateSequence: fallbackSequence,\n    error: error.message,\n    message: `日期计算异常，使用默认日期序列：${fallbackSequence[0].description}（${fallbackSequence[0].date}）`\n  };\n}"}, "id": "9bfbe4e2-8c67-4aba-ba50-2924a29cd23c", "name": "智能日期计算", "type": "n8n-nodes-base.code", "position": [-720, 0], "typeVersion": 2}, {"parameters": {"httpMethod": "POST", "path": "fund_inquiry", "responseMode": "responseNode", "options": {}}, "id": "9a4cbcf7-4fa9-4c23-b217-8ff02200a264", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-3140, 200], "typeVersion": 2, "webhookId": "e96be27a-08d7-427f-88b4-6ddc8000abf0"}, {"parameters": {"assignments": {"assignments": [{"id": "success-timestamp", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}, {"id": "26beaaf0-687c-4384-83cc-6da613190d9e", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}]}, "options": {}}, "id": "8b5d56ca-b833-4973-9086-8f10f2409d49", "name": "成功数据格式化", "type": "n8n-nodes-base.set", "position": [-2260, 100], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 查询组织的公司筛选配置\n-- <AUTHOR>\n-- @date {{ new Date().toISOString() }}\n-- 功能：根据organizationId查询CompanyFilter表获取companyFilterId\n-- 修改记录：添加查询状态返回\n\nWITH query_result AS (\n    SELECT \n        id as \"companyFilterId\",\n        \"organizationId\",\n        \"companyCode\",\n        \"benchmarkCompanyCodes\",\n        \"modifiedAt\",\n        'success' as \"status\"\n    FROM company_filter \n    WHERE \"organizationId\" = '{{ $('成功数据格式化').item.json.organizationId }}'\n    LIMIT 1\n)\nSELECT * FROM query_result\nUNION ALL\nSELECT \n    NULL as \"companyFilterId\",\n    '{{ $('成功数据格式化').item.json.organizationId }}' as \"organizationId\",\n    NULL as \"companyCode\",\n    NULL as \"benchmarkCompanyCodes\",\n    NULL as \"modifiedAt\",\n    'not_found' as \"status\"\nWHERE NOT EXISTS (SELECT 1 FROM query_result);", "options": {}}, "id": "74169c6d-4b39-4004-b51c-1222d1487a4b", "name": "查询公司筛选配置", "type": "n8n-nodes-base.postgres", "position": [-2040, 100], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.data }}", "options": {}}, "id": "e7305854-89a2-4b36-b0ad-f37cbbf15519", "name": "缓存acc token", "type": "n8n-nodes-base.set", "position": [-940, 0], "typeVersion": 3.4}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"refresh_token\": \"eyJzaWduX3RpbWUiOiIyMDI1LTA3LTEwIDEwOjQ2OjAxIn0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6529EA27B119F50E6966447B09E7214EF92C1DFD560ABA582055425A090DB61B\"\n}\n", "options": {}}, "id": "019b774f-2273-45f7-9e21-16bb03deeb6d", "name": "固定同花顺refresh", "type": "n8n-nodes-base.set", "position": [-1380, 0], "typeVersion": 3.4}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "id": "ccf94b58-01ba-4655-a119-c3e0296e1b7a", "name": "获取（短期）access token", "type": "n8n-nodes-base.httpRequest", "position": [-1160, 0], "typeVersion": 4.2}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "ee8ac808-b011-4067-929b-27c8938980ae", "name": "检查已存在的投资人标签", "type": "n8n-nodes-base.postgres", "position": [-60, -100], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "/**\n * 智能机构持股查询 - Code节点网络请求实现\n * <AUTHOR>\n * @date 2025-01-15T08:30:00.000Z\n * @description 使用Code节点进行HTTP请求，自动尝试多个日期获取机构持股数据\n * @modified hayden 2025-01-15T08:30:00.000Z 优化日期策略：本司优先当前季度，对标支持向后兼容\n * @modified hayden 2025-07-10T11:15:05.351Z 优化请求字段：只请求必要的p00155_f004和p00155_f006字段\n * @modified hayden 2025-07-10T11:30:25.123Z 修复reportDate：使用dateInfo中的日期信息构建标准格式的报告期\n * @modified hayden 2025-07-16T14:30:51.581Z 优化reportDate显示：每个投资人显示实际找到数据的日期作为reportDate\n * @modified hayden 2025-07-16T14:45:32.891Z 修复reportDate日期问题：确保使用真实查询到数据的日期\n * 功能说明：\n * - 调用同花顺API获取机构持股数据\n * - 本司数据：优先使用当前季度，找到即停止\n * - 对标数据：支持向后兼容，当前季度找不到则尝试更早日期\n * - 支持查询本公司和对标公司的机构持股数据\n * - 为投资人添加标签：持有本司和持有对标\n * - 只返回investorTags和dateInfo数据\n */\n\ntry {\n  // 获取输入数据\n  const items = $input.all();\n  \n  // 获取前置节点的数据\n  const accessToken = $('缓存acc token').item.json.access_token;\n  const companyCode = $('查询公司筛选配置').item.json.companyCode;\n  const benchmarkCompanyCodes = $('查询公司筛选配置').item.json.benchmarkCompanyCodes || '';\n  const dateSequence = $('智能日期计算').item.json.dateSequence || [];\n  const organizationId = $('成功数据格式化').item.json.organizationId || '';\n\n  // 验证必要参数\n  if (!accessToken) {\n    throw new Error('缺少access_token，请检查token获取流程');\n  }\n  \n  if (!companyCode) {\n    throw new Error('缺少companyCode，请检查公司筛选配置');\n  }\n  \n  if (!dateSequence || dateSequence.length === 0) {\n    throw new Error('缺少dateSequence，请检查日期计算结果');\n  }\n  \n  /**\n   * 将日期格式化为标准报告期格式 YYYY-MM-DD\n   * @param {string} dateStr - 格式为YYYYMMDD的日期字符串\n   * @return {string} 格式为YYYY-MM-DD的日期字符串\n   */\n  function formatReportDate(dateStr) {\n    if (!dateStr || dateStr.length !== 8) {\n      return '';\n    }\n    return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`;\n  }\n  \n  // 记录所有尝试过的日期\n  const attemptedDates = [];\n  let finalResponse = null;\n  let successDate = null;\n  let successDateInfo = null;\n  let lastAttemptedDateInfo = null;\n  \n  /**\n   * 本司数据查询 - 优先使用当前季度\n   * @description 按日期顺序尝试，找到有效数据即停止\n   */\n  for (const dateInfo of dateSequence) {\n    const currentDate = dateInfo.date;\n    attemptedDates.push(currentDate);\n    lastAttemptedDateInfo = dateInfo;\n    \n    // 配置HTTP请求选项 - 本公司\n    const options = {\n      url: 'https://quantapi.51ifind.com/api/v1/data_pool',\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'access_token': accessToken\n      },\n      body: {\n        reportname: 'p00155',\n        functionpara: {\n          thscode: companyCode,\n          date: currentDate\n        },\n        outputpara: 'p00155_f004,p00155_f006'\n      },\n      json: true,\n      timeout: 30000,\n      skipSslCertificateValidation: false\n    };\n    \n    // 执行HTTP请求 - 本公司\n    const response = await this.helpers.httpRequest(options);\n    \n    // 检查是否有有效的基金数据\n    const hasValidData = response.errorcode !== -4001 && \n                        response.tables && \n                        response.tables.length > 0 && \n                        response.tables[0].table && \n                        response.tables[0].table.p00155_f004 && \n                        response.tables[0].table.p00155_f004.length > 0 &&\n                        response.tables[0].table.p00155_f006 &&\n                        response.tables[0].table.p00155_f006.some(type => type === \"基金\");\n\n    if (hasValidData) {\n      // 找到有数据的日期，保存结果并跳出循环\n      finalResponse = response;\n      successDate = currentDate;\n      successDateInfo = dateInfo;\n      break;\n    }\n\n    // 如果是最后一个日期且仍然没有数据，保存最后一次响应\n    if (dateInfo === dateSequence[dateSequence.length - 1]) {\n      finalResponse = response;\n    }\n  }\n  \n  // 如果所有日期都没有数据，使用最后一次响应\n  if (!finalResponse) {\n    throw new Error('所有日期都没有返回有效数据');\n  }\n  \n  // 创建投资人标签数据\n  const investorTags = [];\n  const currentTimestamp = new Date().toISOString();\n  \n  // 处理本公司数据\n  if (successDate && finalResponse.tables && finalResponse.tables.length > 0 && finalResponse.tables[0].table) {\n    const tableData = finalResponse.tables[0].table;\n    const recordCount = tableData.p00155_f004.length;\n    // 格式化报告期日期 - 使用实际找到数据的日期\n    const reportDate = formatReportDate(successDate);\n    \n    console.log(`本司(${companyCode})数据使用日期: ${successDate}, 格式化后: ${reportDate}`);\n\n    // 转换表格数据为对象数组，只处理机构类型为\"基金\"的记录\n    for (let i = 0; i < recordCount; i++) {\n      if (tableData.p00155_f006[i] === \"基金\") {\n        investorTags.push({\n          investorCode: tableData.p00155_f004[i],\n          fundInfo: tableData.p00155_f004[i],\n          tagName: `持有本司(${companyCode})`,\n          tagCategory: \"system\",\n          modifiedAt: currentTimestamp,\n          reportDate: reportDate // 使用本司实际找到数据的日期\n        });\n      }\n    }\n  }\n  \n  /**\n   * 对标公司数据查询 - 支持向后兼容\n   * @description 每个对标公司独立尝试所有日期，直到找到有效数据\n   */\n  if (benchmarkCompanyCodes) {\n    // 解析对标公司代码\n    const benchmarkCodes = typeof benchmarkCompanyCodes === 'string' \n      ? benchmarkCompanyCodes.split(',').map(code => code.trim()).filter(code => code)\n      : Array.isArray(benchmarkCompanyCodes) ? benchmarkCompanyCodes : [];\n    \n    // 如果有有效的对标公司代码\n    if (benchmarkCodes.length > 0) {\n      // 对每个对标公司代码进行查询\n      for (const benchmarkCode of benchmarkCodes) {\n        if (!benchmarkCode) {\n          continue;\n        }\n        \n        let benchmarkSuccessDate = null;\n        let benchmarkResponse = null;\n        let benchmarkDateInfo = null;\n        \n        // 为对标公司尝试所有日期，支持向后兼容\n        for (const dateInfo of dateSequence) {\n          const currentDate = dateInfo.date;\n          \n          // 配置HTTP请求选项 - 对标公司\n          const benchmarkOptions = {\n            url: 'https://quantapi.51ifind.com/api/v1/data_pool',\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'access_token': accessToken\n            },\n            body: {\n              reportname: 'p00155',\n              functionpara: {\n                thscode: benchmarkCode,\n                date: currentDate\n              },\n              outputpara: 'p00155_f004,p00155_f006'\n            },\n            json: true,\n            timeout: 30000,\n            skipSslCertificateValidation: false\n          };\n          \n          try {\n            // 执行HTTP请求 - 对标公司\n            const response = await this.helpers.httpRequest(benchmarkOptions);\n            \n            // 检查是否有有效的基金数据\n            const hasValidBenchmarkData = response.errorcode !== -4001 && \n                                        response.tables && \n                                        response.tables.length > 0 && \n                                        response.tables[0].table && \n                                        response.tables[0].table.p00155_f004 && \n                                        response.tables[0].table.p00155_f004.length > 0 &&\n                                        response.tables[0].table.p00155_f006 &&\n                                        response.tables[0].table.p00155_f006.some(type => type === \"基金\");\n            \n            if (hasValidBenchmarkData) {\n              // 找到有数据的日期，保存结果并跳出循环\n              benchmarkResponse = response;\n              benchmarkSuccessDate = currentDate;\n              benchmarkDateInfo = dateInfo;\n              break;\n            } \n            \n            // 如果是最后一个日期且仍然没有数据，保存最后一次响应\n            if (dateInfo === dateSequence[dateSequence.length - 1]) {\n              benchmarkResponse = response;\n            }\n          } catch (benchmarkError) {\n            console.error(`查询对标公司 ${benchmarkCode} 日期 ${currentDate} 数据失败: ${benchmarkError.message}`);\n            // 继续尝试下一个日期\n          }\n        }\n        \n        // 处理对标公司数据（如果找到了有效数据）\n        if (benchmarkSuccessDate && benchmarkResponse && benchmarkResponse.tables &&\n            benchmarkResponse.tables.length > 0 && benchmarkResponse.tables[0].table) {\n\n          const tableData = benchmarkResponse.tables[0].table;\n          const recordCount = tableData.p00155_f004.length;\n          // 格式化报告期日期 - 使用该对标公司实际找到数据的日期\n          const reportDate = formatReportDate(benchmarkSuccessDate);\n          \n          console.log(`对标公司(${benchmarkCode})数据使用日期: ${benchmarkSuccessDate}, 格式化后: ${reportDate}`);\n\n          // 转换表格数据为对象数组，只处理机构类型为\"基金\"的记录\n          for (let i = 0; i < recordCount; i++) {\n            if (tableData.p00155_f006[i] === \"基金\") {\n              investorTags.push({\n                investorCode: tableData.p00155_f004[i],\n                fundInfo: tableData.p00155_f004[i],\n                tagName: `持有对标(${benchmarkCode})`,\n                tagCategory: \"system\",\n                modifiedAt: currentTimestamp,\n                reportDate: reportDate // 使用该对标公司实际找到数据的日期\n              });\n            }\n          }\n        }\n      }\n    }\n  } \n  \n  // 准备返回结果对象\n  const result = {\n    organizationId: organizationId,\n    dateInfo: successDateInfo || lastAttemptedDateInfo,\n    investorTags: investorTags,\n    attemptedDates: attemptedDates,\n    hasValidData: !!successDate\n  };\n  \n  return [{\n    json: result\n  }];\n  \n} catch (error) {\n  // 详细的错误处理\n  console.error(`智能机构持股查询失败: ${error.message}`);\n  \n  // 构建简化的错误响应\n  const errorResponse = {\n    error: true,\n    errorMessage: error.message,\n    organizationId: $('成功数据格式化').item?.json?.organizationId || '',\n    dateInfo: null,\n    investorTags: []\n  };\n  \n  return [{\n    json: errorResponse\n  }];\n}\n"}, "id": "ad1d301d-a1f7-437f-a0e2-f6abc0c5e62e", "name": "智能机构持股查询（网络请求）", "type": "n8n-nodes-base.code", "position": [-500, 0], "typeVersion": 2}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "d74c67a9-1d85-436c-a713-4464e045d7b0", "name": "查询所有标签", "type": "n8n-nodes-base.postgres", "position": [-3140, 560], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.pattern && !options.pattern.test(val)) return false;\n      if (options.notEmpty && val.trim() === '') return false;\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) return false;\n      if (options.min !== undefined && num < options.min) return false;\n      if (options.max !== undefined && num > options.max) return false;\n      if (options.integer && !Number.isInteger(num)) return false;\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'organizationId',\n      type: 'string',\n      required: true,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: \"\"\n    }\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value);\n    } else if (type === 'boolean') {\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1;\n      }\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}\n"}, "id": "e6a707e4-b888-498d-b866-5f511313abee", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-2700, 200], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "9814f244-b18f-413f-8b60-87931ab82dda", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-2480, 200], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "8ea28efc-ee74-4029-b845-c7a62f8577a0", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-2260, 300], "typeVersion": 1.1}, {"parameters": {"jsCode": "/**\n * 检查公司筛选配置查询结果\n * <AUTHOR>\n * @date {{ new Date().toISOString() }}\n * @description 检查是否成功获取到公司筛选配置，如果没有则返回错误信息\n */\n\nconst result = $input.item.json;\n\n// 检查是否有查询结果或status为not_found\nif (!result || \n    Object.keys(result).length === 0 || \n    result.status === 'not_found') {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'COMPANY_FILTER_NOT_FOUND',\n      message: '未找到组织的公司筛选配置，请先配置公司代码',\n      field: 'organizationId',\n      value: $('成功数据格式化').item.json.organizationId,\n      timestamp: new Date().toISOString(),\n      type: 'DATA_ERROR'\n    }\n  };\n}\n\n// 如果有查询结果，则返回原始数据并添加验证成功标记\nreturn {\n  ...result,\n  validationResult: 'success'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1820, 100], "id": "1645f293-5324-49e9-b9bb-e02c10b33217", "name": "检查筛选配置结果"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b832b402-9f27-4203-8f99-a511120985e1", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "contains"}}, {"id": "c9f72fd3-c137-48e8-8378-e58024055520", "leftValue": "", "rightValue": "", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1600, 100], "id": "7e30758c-506c-4943-a3f9-fb392f2229d8", "name": "If1"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 400}}, "id": "23e093f8-4691-4681-ae11-d652915c0434", "name": "配置错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1380, 200], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"message\": \"没有查询投资人数据\",\n  \"dateInfo\":\"{{ $json.dateInfo.date }}\"\n} ", "options": {"responseCode": 200}}, "id": "ac313968-ae28-4de1-b4d4-6dcdce0e0347", "name": "没有数据响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-60, 100], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ff1e175-1b12-4c69-839a-b34ba861fee6", "leftValue": "={{ $json.investorTags }}", "rightValue": 0, "operator": {"type": "array", "operation": "lengthGt", "rightType": "number"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-280, 0], "id": "72b2bacd-c436-400d-b6e7-a59b0d038560", "name": "判断查询的投资人数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ebf45ca2-97b9-44ca-acb2-88135882f6d8", "leftValue": "={{ $json.needDatabaseUpdate }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [380, -100], "id": "55082145-498f-43f0-b89f-31676ac5eda6", "name": "If"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').first().json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "8618ce85-03c6-416f-9bfa-3aac235aaef3", "name": "创建后检查存在的标签1", "type": "n8n-nodes-base.postgres", "position": [1040, 100], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "3c8bb600-9ee0-444b-8869-51ec765b0b14", "name": "成功响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [1260, 0], "typeVersion": 1.1}, {"parameters": {"jsCode": "/**\n * 准备投资人标签SQL插入语句\n * <AUTHOR>\n * @date 2023-11-28T15:12:34.567Z\n * @description 从智能机构持股查询结果中提取投资人标签数据，并准备SQL插入语句\n * 功能说明：\n * - 获取上游节点中的投资人标签数据\n * - 检查标签数据有效性\n * - 准备批量插入SQL语句\n * - 添加时间戳和组织ID\n * 修改记录：\n * - 2023-11-28: 初始创建，实现SQL准备逻辑\n * - 2023-11-28: 修复SQL字段错误，将createdAt改为modifiedAt\n * - 2023-11-28: 添加companyFilterId字段到SQL语句中\n * - 2023-11-28: 在tagMetadata中添加reportDate字段数据\n * - 2025-07-21: 去重逻辑改为 organizationId + investorCode + companyFilterId + tagName\n */\n\ntry {\n  // 获取上游节点的投资人标签数据\n  const investorTags = $('智能机构持股查询（网络请求）').first().json.investorTags || [];\n\n  // 获取组织ID和公司筛选配置ID\n  const organizationId = $('成功数据格式化').item.json.organizationId || '';\n  const companyFilterId = $('查询公司筛选配置').first().json.companyFilterId || null;\n\n  // 检查数据有效性\n  if (!Array.isArray(investorTags) || investorTags.length === 0) {\n    return {\n      success: false,\n      needDatabaseUpdate: false,\n      message: '没有找到有效的投资人标签数据',\n      sql: '',\n      newTagsCount: 0,\n      newTags: [],\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 获取已存在的标签数据用于去重\n  const existingTagsItems = $('检查已存在的投资人标签').all() || [];\n  const existingTags = existingTagsItems.map(item => item.json);\n  const existingTagMap = new Map();\n\n  // 调试信息 - 检查数据来源\n  console.log('organizationId:', organizationId);\n  console.log('companyFilterId:', companyFilterId);\n  console.log('existingTags数量:', existingTags.length);\n  console.log('investorTags数量:', investorTags.length);\n\n  // 构建已存在标签的映射 - 使用与数据库冲突检查相同的key\n  existingTags.forEach(tag => {\n    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;\n    existingTagMap.set(key, true);\n    console.log('已存在标签key:', key);\n  });\n\n  // 过滤出需要新增的标签\n  const newTags = investorTags.filter(tag => {\n    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;\n    const exists = existingTagMap.has(key);\n    console.log('检查标签key:', key, '是否存在:', exists);\n    return !exists;\n  });\n\n  if (newTags.length === 0) {\n    return {\n      success: false,\n      needDatabaseUpdate: false,\n      message: '没有找到新的投资人标签数据',\n      sql: '',\n      newTagsCount: 0,\n      newTags: [],\n      existingTagsCount: investorTags.length,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 准备SQL插入语句\n  const timestamp = new Date().toISOString();\n  const valueStrings = newTags.map(tag => {\n    // 使用UUID生成ID\n    const id = 'cuid_' + Math.random().toString(36).substring(2, 15);\n\n    // 创建包含reportDate的tagMetadata JSON对象\n    const tagMetadata = {\n      reportDate: tag.reportDate || null\n    };\n\n    // 将tagMetadata转换为JSON字符串并转义单引号\n    const tagMetadataJson = JSON.stringify(tagMetadata).replace(/'/g, \"''\");\n\n    // 处理companyFilterId可能为null的情况\n    const filterIdValue = companyFilterId ? `'${companyFilterId}'` : 'NULL';\n\n    return `('${id}', '${tag.investorCode}', '${tag.tagName}', '${tag.tagCategory || 'system'}', '${organizationId}', ${filterIdValue}, '${tagMetadataJson}', '${timestamp}')`;\n  });\n\n  // 修正SQL语句，使用正确的字段名，并添加companyFilterId和tagMetadata\n  const sql = `INSERT INTO investor_tag (\"id\", \"investorCode\", \"tagName\", \"tagCategory\", \"organizationId\", \"companyFilterId\", \"tagMetadata\", \"modifiedAt\") VALUES ${valueStrings.join(', ')} ON CONFLICT (\"organizationId\", \"investorCode\", \"tagName\") DO UPDATE SET \"modifiedAt\" = EXCLUDED.\"modifiedAt\", \"tagMetadata\" = EXCLUDED.\"tagMetadata\";`;\n\n  return {\n    success: true,\n    needDatabaseUpdate: true,\n    message: `发现${newTags.length}条新的投资人标签，需要执行数据库更新`,\n    sql: sql,\n    newTagsCount: newTags.length,\n    newTags: newTags.map(tag => ({\n      investorCode: tag.investorCode,\n      tagName: tag.tagName,\n      tagCategory: tag.tagCategory || 'system',\n      reportDate: tag.reportDate\n    })),\n    totalTagsFromAPI: investorTags.length,\n    existingTagsCount: investorTags.length - newTags.length,\n    companyFilterId: companyFilterId,\n    timestamp: timestamp\n  };\n} catch (error) {\n  return {\n    success: false,\n    needDatabaseUpdate: false,\n    error: {\n      code: 'SQL_PREPARATION_ERROR',\n      message: '准备SQL语句时发生错误',\n      details: error.message || '未知错误',\n      timestamp: new Date().toISOString()\n    }\n  };\n}\n"}, "id": "9f990cc0-dc39-46af-8293-529b55b76ff0", "name": "准备sql1", "type": "n8n-nodes-base.code", "position": [160, -100], "typeVersion": 2}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{ $json.sql }}", "options": {}}, "id": "c1ddb165-36f8-4f3a-b581-8da93f253030", "name": "创建标签1", "type": "n8n-nodes-base.postgres", "position": [600, -200], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ff1e175-1b12-4c69-839a-b34ba861fee6", "leftValue": "={{ $json.success }}", "rightValue": 0, "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [820, -200], "id": "99e6ad45-2795-4d3a-aaf4-a79234b1b4e0", "name": "判断查询的投资人数据3"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"message\": \"创建系统标签出错\",\n} ", "options": {"responseCode": 200}}, "id": "e4e4abd9-a50c-48d5-b03e-ab064cc85f22", "name": "没有数据响应2", "type": "n8n-nodes-base.respondToWebhook", "position": [1040, -300], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "598222ef-a85e-4002-9872-1a697ae0c2f6", "name": "创建后检查存在的标签2", "type": "n8n-nodes-base.postgres", "position": [1040, -100], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "相关人员：hayden jason\n\napi地址：/fund_inquiry\n\n记录时间 ：2025-07-31 \n\n相关文档：https://starlinkcap.feishu.cn/wiki/BhiEwGtgzi5mxFkAAJ4cA6g6nQf", "height": 220, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3480, 160], "id": "a3b707f1-88e0-4371-ad07-32874c337c62", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "获取组织id", "height": 80, "width": 160}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2960, 60], "id": "bf987906-6e4a-4e6e-a134-c9d1ef046a69", "name": "Sticky Note2"}, {"parameters": {"content": "查询组织下公司代码", "height": 80, "width": 160}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2060, -40], "id": "dca93bad-6342-4cc4-9ee4-a2d3bb2895bf", "name": "Sticky Note3"}, {"parameters": {"content": "目前没有机构数据\n\n固定了两组数据：300723、000002"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-580, -240], "id": "464658db-5ca0-4b3f-822d-28baaa3695d3", "name": "Sticky Note4"}, {"parameters": {"content": "返回示例：\n[\n    {\n        \"id\": \"cuid_1vc7xb0mith\",\n        \"companyFilterId\": \"cmcvxajuq0001hvkgq75ovzro\",\n        \"investorCode\": \"510300.SH\",\n        \"tagName\": \"持有本司(000002.SZ)\",\n        \"tagCategory\": \"system\",\n        \"tagMetadata\": {\n            \"reportDate\": \"2025-03-31\"\n        },\n        \"modifiedAt\": \"2025-07-14T12:01:53.824Z\"\n    },\n    {\n        \"id\": \"cuid_itf9jbxpb3l\",\n        \"companyFilterId\": \"cmcvxajuq0001hvkgq75ovzro\",\n        \"investorCode\": \"512200.SH\",\n        \"tagName\": \"持有本司(000002.SZ)\",\n        \"tagCategory\": \"system\",\n        \"tagMetadata\": {\n            \"reportDate\": \"2025-03-31\"\n        },\n        \"modifiedAt\": \"2025-07-14T12:01:53.824Z\"\n    }\n ]", "height": 480, "width": 460}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1540, -180], "id": "a40c67d8-e0a1-4b0e-9d91-d3215341b4be", "name": "Sticky Note1"}], "connections": {"提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "智能日期计算": {"main": [[{"node": "智能机构持股查询（网络请求）", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "成功数据格式化": {"main": [[{"node": "查询公司筛选配置", "type": "main", "index": 0}]]}, "查询公司筛选配置": {"main": [[{"node": "检查筛选配置结果", "type": "main", "index": 0}]]}, "缓存acc token": {"main": [[{"node": "智能日期计算", "type": "main", "index": 0}]]}, "固定同花顺refresh": {"main": [[{"node": "获取（短期）access token", "type": "main", "index": 0}]]}, "获取（短期）access token": {"main": [[{"node": "缓存acc token", "type": "main", "index": 0}]]}, "检查已存在的投资人标签": {"main": [[{"node": "准备sql1", "type": "main", "index": 0}]]}, "智能机构持股查询（网络请求）": {"main": [[{"node": "判断查询的投资人数据", "type": "main", "index": 0}]]}, "查询所有标签": {"main": [[]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "成功数据格式化", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "检查筛选配置结果": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "固定同花顺refresh", "type": "main", "index": 0}], [{"node": "配置错误响应", "type": "main", "index": 0}]]}, "判断查询的投资人数据": {"main": [[{"node": "检查已存在的投资人标签", "type": "main", "index": 0}], [{"node": "没有数据响应", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "创建标签1", "type": "main", "index": 0}], [{"node": "创建后检查存在的标签1", "type": "main", "index": 0}]]}, "创建后检查存在的标签1": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}, "准备sql1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "创建标签1": {"main": [[{"node": "判断查询的投资人数据3", "type": "main", "index": 0}]]}, "判断查询的投资人数据3": {"main": [[{"node": "创建后检查存在的标签2", "type": "main", "index": 0}], [{"node": "没有数据响应2", "type": "main", "index": 0}]]}, "创建后检查存在的标签2": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}