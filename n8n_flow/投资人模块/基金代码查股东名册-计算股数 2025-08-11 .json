{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "extract-keywords", "name": "fund_code", "value": "={{ $json.query?.fund_code || $json.body?.fund_code || $json.fund_code || '' }}", "type": "string"}, {"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "c9d07d43-50b9-4aec-adfb-a303e3571969", "name": "organizationId", "value": "={{ $json.query?.organizationId || $json.body?.organizationId || $json.organizationId || '' }}", "type": "string"}, {"id": "1b85575d-599d-4632-a614-cf84c9b0cd53", "name": "unified_account_number", "value": "={{ $json.query?.unified_account_number || $json.body?.unified_account_number || $json.unified_account_number || '' }}", "type": "string"}]}, "options": {}}, "id": "3083568c-3512-41f0-9ab4-a0f35d5ff90b", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-1460, 360], "typeVersion": 3.4, "notesInFlow": false}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.pattern && !options.pattern.test(val)) return false;\n      if (options.notEmpty && val.trim() === '') return false;\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) return false;\n      if (options.min !== undefined && num < options.min) return false;\n      if (options.max !== undefined && num > options.max) return false;\n      if (options.integer && !Number.isInteger(num)) return false;\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'organizationId',\n      type: 'string',\n      required: true,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    },\n    {\n      field: 'fund_code',\n      type: 'string',\n      required: false,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    },\n    {\n      field: 'unified_account_number',\n      type: 'string',\n      required: false,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    }\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value);\n    } else if (type === 'boolean') {\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1;\n      }\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}\n"}, "id": "0af77a4e-c340-4eb9-966a-3cd4af44688d", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-1240, 360], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "bef8c294-9795-40c7-bb9c-f9db5d31dbf0", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1020, 360], "typeVersion": 2}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "ee903703-187e-42d6-bcae-8c500fd92e82", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "position": [2120, -120], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "fundcode_sharehold", "responseMode": "responseNode", "options": {}}, "id": "0303bea9-7c35-47e1-9871-40a60144fc76", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-1680, 360], "typeVersion": 2, "webhookId": "737d1017-057f-4799-8185-b6bf9cff8dc8"}, {"parameters": {"content": "提取设计api所需要的参数：如关键字、日期、分页等", "height": 260, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1560, 260], "id": "12f28691-c0f2-4fad-a08c-a9d5a12a2cc7", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：如关键字是否字符串、日期是否是日期、分页是否是整数", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1380, 260], "id": "f22b670f-7b47-4929-b85e-2037517d0418", "name": "Sticky Note1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 根据基金代码查询基金信息\n-- <AUTHOR>\n-- @created 2025-08-05 10:24:01\n-- @updated 2025-08-05 10:42:30 hayden 添加organizationId字段传递给下一个节点\n-- @description 根据基金代码查询ths_fund_management_custody表获取基金详细信息，并传递organizationId\n-- @example 输入: fund_code=\"001384.OF\", organizationId=\"Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36\"\n-- @example 输出: 返回基金代码、基金名称、基金全称、托管人、组织ID等信息\n\nSELECT \n  fund_code,\n  fund_name,\n  fund_full_name,\n  custodian,\n  management_company_name,\n  table_updated_at,\n  '{{ $json.organizationId }}' as organizationId\nFROM ths_fund_management_custody\nWHERE fund_code = '{{ $json.fund_code }}'\nLIMIT 1;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-360, 60], "id": "0e0552e6-f4e5-428d-bea5-4a00f9971aa9", "name": "查询同花顺基金名称", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "同花顺数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 基金股东持仓分析查询 - 包含最新持仓分析和历史对比\n * <AUTHOR>\n * @created 2025-01-27 10:30:00\n * @updated 2025-01-27 10:30:00 hayden 添加上一期排名和排名变动计算\n * @description 通过基金信息查询股东持仓详情，包含最新期分析、历史对比、行为判断、排名变动等\n */\nWITH fund_shareholders AS (\n  -- 查询所有相关的基金股东记录\n  SELECT \n    s.id,\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\",\n    s.\"contactNumber\",\n    s.\"contactAddress\",\n    s.\"cashAccount\",\n    s.\"sharesInCashAccount\",\n    s.\"marginAccount\", \n    s.\"sharesInMarginAccount\",\n    s.\"shareholderType\",\n    s.\"shareholderCategory\",\n    -- 解析托管人部分\n    CASE \n      WHEN POSITION('－' IN s.\"securitiesAccountName\") > 0 \n      THEN TRIM(SUBSTRING(s.\"securitiesAccountName\" FROM 1 FOR POSITION('－' IN s.\"securitiesAccountName\") - 1))\n      ELSE NULL \n    END AS custodian_part,\n    -- 解析基金全称部分\n    CASE \n      WHEN POSITION('－' IN s.\"securitiesAccountName\") > 0 \n      THEN TRIM(SUBSTRING(s.\"securitiesAccountName\" FROM POSITION('－' IN s.\"securitiesAccountName\") + 1))\n      ELSE NULL \n    END AS fund_name_part\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationid\"] }}'\n    AND s.\"securitiesAccountName\" LIKE '%－%'\n),\nmatched_fund AS (\n  -- 匹配目标基金的所有期数记录\n  SELECT *\n  FROM fund_shareholders fs\n  WHERE fs.fund_name_part = '{{ $json[\"fund_full_name\"] }}'\n    AND fs.custodian_part ILIKE '%{{ $json[\"custodian\"] }}%'\n),\nlatest_period AS (\n  -- 获取最新期数\n  SELECT MAX(\"registerDate\") as latest_date\n  FROM matched_fund\n),\nprevious_period AS (\n  -- 获取上一期数据\n  SELECT \n    mf.\"numberOfShares\" as prev_shares,\n    mf.\"shareholdingRatio\" as prev_ratio,\n    mf.\"registerDate\" as prev_date\n  FROM matched_fund mf\n  WHERE mf.\"registerDate\" < (SELECT latest_date FROM latest_period)\n  ORDER BY mf.\"registerDate\" DESC\n  LIMIT 1\n),\ncurrent_ranking AS (\n  -- 计算当前期排名\n  SELECT \n    s.\"shareholderId\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as current_rank\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationid\"] }}'\n    AND s.\"registerDate\" = (SELECT latest_date FROM latest_period)\n),\nprevious_ranking AS (\n  -- 计算上一期排名\n  SELECT \n    s.\"shareholderId\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as previous_rank\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationid\"] }}'\n    AND s.\"registerDate\" = (SELECT prev_date FROM previous_period)\n),\nall_periods_data AS (\n  -- 获取该基金所有期数的持股数据\n  SELECT \n    \"registerDate\",\n    \"numberOfShares\",\n    \"shareholdingRatio\"\n  FROM matched_fund\n  ORDER BY \"registerDate\" DESC\n)\nSELECT \n  '{{ $json.organizationid }}' as organizationId,\n  -- 基本信息\n  mf.id,\n  mf.\"shareholderId\" as certificate_number,\n  mf.\"unifiedAccountNumber\" as unified_account_number,\n  mf.\"securitiesAccountName\" as account_name,\n  mf.\"contactNumber\" as phone_number,\n  mf.\"shareholderType\" as shareholder_type,\n  mf.\"shareholderCategory\" as shareholder_category,\n  mf.\"contactAddress\" as contact_address,\n  mf.\"cashAccount\" as cash_account,\n  mf.\"sharesInCashAccount\" as cash_account_shares,\n  mf.\"marginAccount\" as margin_account,\n  mf.\"sharesInMarginAccount\" as margin_account_shares,\n  \n  -- 最新期数信息\n  mf.\"registerDate\" as latest_period_date,\n  mf.\"numberOfShares\" as current_shares,\n  mf.\"shareholdingRatio\" as current_ratio,\n  \n  -- 上期对比信息\n  pp.prev_shares,\n  pp.prev_ratio,\n  pp.prev_date as previous_period_date,\n  \n  -- 股东行为判断\n  CASE \n    WHEN pp.prev_shares IS NULL THEN '新增'\n    WHEN mf.\"numberOfShares\" > pp.prev_shares THEN '增持'\n    WHEN mf.\"numberOfShares\" < pp.prev_shares THEN '减持'\n    WHEN mf.\"numberOfShares\" = pp.prev_shares THEN '不变'\n    ELSE '未知'\n  END AS shareholder_behavior,\n  \n  -- 持股变化计算\n  CASE \n    WHEN pp.prev_shares IS NOT NULL \n    THEN mf.\"numberOfShares\" - pp.prev_shares\n    ELSE mf.\"numberOfShares\"\n  END AS shares_change,\n  \n  CASE \n    WHEN pp.prev_ratio IS NOT NULL \n    THEN mf.\"shareholdingRatio\" - pp.prev_ratio\n    ELSE mf.\"shareholdingRatio\"\n  END AS ratio_change,\n  \n  -- 持股排名信息\n  cr.current_rank as holding_rank,\n  pr.previous_rank as previous_holding_rank,\n  \n  -- 排名变动计算（正数表示排名上升，负数表示排名下降）\n  CASE \n    WHEN pr.previous_rank IS NULL THEN NULL  -- 新增股东无排名变动\n    ELSE pr.previous_rank - cr.current_rank  -- 上期排名 - 当期排名\n  END AS rank_change,\n  \n  -- 排名变动描述\n  CASE \n    WHEN pr.previous_rank IS NULL THEN '新增股东'\n    WHEN pr.previous_rank - cr.current_rank > 0 THEN '排名上升'\n    WHEN pr.previous_rank - cr.current_rank < 0 THEN '排名下降'\n    WHEN pr.previous_rank - cr.current_rank = 0 THEN '排名不变'\n    ELSE '排名未知'\n  END AS rank_change_description,\n  \n  -- 所有期数数据（JSON格式）\n  (\n    SELECT JSON_AGG(\n      JSON_BUILD_OBJECT(\n        'registerDate', \"registerDate\",\n        'numberOfShares', \"numberOfShares\", \n        'shareholdingRatio', \"shareholdingRatio\"\n      ) ORDER BY \"registerDate\" DESC\n    )\n    FROM all_periods_data\n  ) as all_periods_holdings,\n   \n  -- 匹配状态\n  'EXACT_MATCH' as match_status\n\nFROM matched_fund mf\nCROSS JOIN latest_period lp\nLEFT JOIN previous_period pp ON 1=1\nLEFT JOIN current_ranking cr ON cr.\"shareholderId\" = mf.\"shareholderId\"\nLEFT JOIN previous_ranking pr ON pr.\"shareholderId\" = mf.\"shareholderId\"\nWHERE mf.\"registerDate\" = lp.latest_date\nORDER BY mf.\"numberOfShares\" DESC;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [40, -80], "id": "95c8df85-741e-437a-a52c-5f2ac346fb2c", "name": "基金名称匹配股东名称", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"code\":0,\n  \"message\": \"没有找到该基金\"\n}", "options": {"responseCode": 200}}, "id": "b39ba362-4049-4b56-a55b-b2a8cbaf5934", "name": "错误响应2", "type": "n8n-nodes-base.respondToWebhook", "position": [60, 80], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "4510bcc5-b779-4752-9700-4d12b83dace2", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-580, 460], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5a3920f0-4491-4493-9c41-b391f8a71f3e", "leftValue": "={{ $json.fund_code }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, 60], "id": "0f785a22-dfec-4567-b309-9eabe21ee3f0", "name": "检查基金代码存在"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\":0,\n  \"message\": \"没有找到该基金\"\n}", "options": {"responseCode": 200}}, "id": "e44786bb-69d5-4554-9a3a-8785cd09d0de", "name": "错误响应3", "type": "n8n-nodes-base.respondToWebhook", "position": [460, 20], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 冻结股和限售股分析查询（简化版）\n-- <AUTHOR>\n-- @created 2025-01-27 14:45:00\n-- @updated 2025-01-27 14:45:00 hayden 简化查询结果，只返回必要的4个字段\n-- @description 根据organizationId查询冻结股、限售股数据及其占总股本比例\n-- @returns 冻结股数、限售股数、冻结占比、限售占比\n\nWITH latest_company_info AS (\n  SELECT \n    \"totalShares\",\n    \"registerDate\"\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json.organizationid }}'\n  ORDER BY \"registerDate\" DESC\n  LIMIT 1\n),\nshareholder_summary AS (\n  SELECT \n    SUM(s.\"frozenShares\") as total_frozen_shares,\n    SUM(s.\"lockedUpShares\") as total_locked_shares\n  FROM shareholder s\n  INNER JOIN latest_company_info lci ON s.\"registerDate\" = lci.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json.organizationid }}'\n)\nSELECT \n  ss.total_frozen_shares as frozen_shares,\n  ss.total_locked_shares as locked_shares,\n  CASE \n    WHEN lci.\"totalShares\" > 0 THEN \n      ROUND((ss.total_frozen_shares / lci.\"totalShares\") * 100, 4)\n    ELSE 0 \n  END as frozen_ratio_percent,\n  CASE \n    WHEN lci.\"totalShares\" > 0 THEN \n      ROUND((ss.total_locked_shares / lci.\"totalShares\") * 100, 4)\n    ELSE 0 \n  END as locked_ratio_percent\nFROM latest_company_info lci\nCROSS JOIN shareholder_summary ss;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [900, 0], "id": "c9ef9634-1800-409a-a377-3d97b4ecf75c", "name": "计算冻结股数", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d6cf3028-c4a0-4949-b476-53dbfd497341", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [260, -80], "id": "2e8841d4-b7c9-4c6b-9474-5b7e8f0ea58c", "name": "查询股东是否存在"}, {"parameters": {"jsCode": "/**\n * n8n Code节点 - 基金股东数据与冻结股限售股数据整合（支持双查询模式）\n * \n * <AUTHOR>\n * @created 2025-01-27 14:50:00\n * @updated 2025-01-27 15:10:00 hayden 整合基金代码查询和一码通查询两种模式\n * @description 智能判断查询模式，将股东数据与冻结股限售股数据进行合并\n * @version 2.0.0\n */\n\n// 获取冻结股数据\nconst frozenData = $('计算冻结股数').all();\n\n// 智能获取股东数据：优先检查哪个节点有数据\nlet shareholderData = [];\nlet queryMode = '';\n\ntry {\n  // 尝试获取基金名称匹配的数据\n  const fundMatchData = $('基金名称匹配股东名称').all();\n  if (fundMatchData && fundMatchData.length > 0) {\n    shareholderData = fundMatchData;\n    queryMode = 'FUND_CODE_QUERY';\n  }\n} catch (error) {\n  console.log('基金名称匹配节点未执行或无数据');\n}\n\ntry {\n  // 尝试获取一码通查询的数据\n  const unifiedAccountData = $('一码通查询股东').all();\n  if (unifiedAccountData && unifiedAccountData.length > 0) {\n    // 如果已有基金查询数据，则合并；否则直接使用\n    if (shareholderData.length > 0) {\n      shareholderData = [...shareholderData, ...unifiedAccountData];\n      queryMode = 'MIXED_QUERY';\n    } else {\n      shareholderData = unifiedAccountData;\n      queryMode = 'UNIFIED_ACCOUNT_QUERY';\n    }\n  }\n} catch (error) {\n  console.log('一码通查询节点未执行或无数据');\n}\n\n// 检查是否有有效的股东数据\nif (shareholderData.length === 0) {\n  return [{\n    json: {\n      error: '未找到有效的股东数据',\n      message: '基金名称匹配和一码通查询节点均未返回数据',\n      query_mode: 'NO_DATA',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// 提取冻结股数据（取第一条记录，因为是汇总数据）\nconst frozenStats = frozenData.length > 0 ? frozenData[0].json : {\n  frozen_shares: \"0.00\",\n  locked_shares: \"0.00\", \n  frozen_ratio_percent: \"0.0000\",\n  locked_ratio_percent: \"0.0000\"\n};\n\n// 数据去重处理（基于unified_account_number和certificate_number）\nconst uniqueShareholderMap = new Map();\n\nshareholderData.forEach(item => {\n  const data = item.json;\n  // 使用一码通账号作为主键，证件号码作为备用键\n  const uniqueKey = data.unified_account_number || data.certificate_number || data.id;\n  \n  if (!uniqueShareholderMap.has(uniqueKey)) {\n    uniqueShareholderMap.set(uniqueKey, data);\n  } else {\n    // 如果存在重复，保留数据更完整的记录\n    const existing = uniqueShareholderMap.get(uniqueKey);\n    const current = data;\n    \n    // 比较数据完整性（非空字段数量）\n    const existingFields = Object.values(existing).filter(v => v !== null && v !== undefined && v !== '').length;\n    const currentFields = Object.values(current).filter(v => v !== null && v !== undefined && v !== '').length;\n    \n    if (currentFields > existingFields) {\n      uniqueShareholderMap.set(uniqueKey, current);\n    }\n  }\n});\n\n// 整合数据：为每个股东记录添加冻结股统计信息和查询模式标识\nconst integratedData = Array.from(uniqueShareholderMap.values()).map(shareholderRecord => {\n  return {\n    // 保留原有股东数据\n    ...shareholderRecord,\n    \n    // 添加冻结股限售股统计数据\n    company_frozen_shares: frozenStats.frozen_shares,\n    company_locked_shares: frozenStats.locked_shares,\n    company_frozen_ratio_percent: frozenStats.frozen_ratio_percent,\n    company_locked_ratio_percent: frozenStats.locked_ratio_percent,\n    \n    // 添加查询模式和处理信息\n    query_mode: queryMode,\n    data_source: shareholderRecord.match_status || 'UNKNOWN',\n    processed_at: new Date().toISOString(),\n    total_records_found: uniqueShareholderMap.size\n  };\n});\n\n// 按持股数量降序排序\nintegratedData.sort((a, b) => {\n  const sharesA = parseFloat(a.current_shares || a.numberOfShares || 0);\n  const sharesB = parseFloat(b.current_shares || b.numberOfShares || 0);\n  return sharesB - sharesA;\n});\n\n// 返回整合后的数据\nreturn integratedData.map(data => ({ json: data }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1300, 0], "id": "3c5e0688-47b6-4015-9842-eef79585e57b", "name": "合并数据"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indipara\":[{\"indicator\":\"ths_avg_close_int_stock\",\"indiparams\":[\"{{ $('检查数据库结果').item.json.startdate }}\",\"{{ $('检查数据库结果').item.json.enddate }}\",\"100\",\"\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 800], "id": "1e2b1d48-adb4-461c-ac6c-b9e73d4fcafc", "name": "获取区间股价（收盘价）"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/real_time_quotation", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indicators\":\"latest\"}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1160, 1000], "id": "518ebe0d-960c-43b7-89f2-f0c51c3d8ac5", "name": "最新股价"}, {"parameters": {"jsCode": "// 数据库查询结果处理\nconst inputData = $input.all();\nif (!inputData || inputData.length === 0) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'NO_DATABASE_RESULT',\n      message: '数据库查询未返回任何结果',\n      field: 'database',\n      value: null,\n      timestamp: new Date().toISOString(),\n      type: 'DATABASE_ERROR'\n    }\n  };\n}\n\n// 获取查询结果\nconst dbResult = inputData[0];\nif (!dbResult || !dbResult.json || dbResult.json.length === 0) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'ORGANIZATION_NOT_FOUND',\n      message: '未找到对应的组织ID，请检查organizationcode是否正确',\n      field: 'organizationcode',\n      value: $('参数验证逻辑').item.json.organizationcode,\n      timestamp: new Date().toISOString(),\n      type: 'VALIDATION_ERROR'\n    }\n  };\n}\n\nconst result = dbResult.json;\nconst companyCode = result?.companyCode;\nconst recentDates = result?.recent_dates;\n\nif (!companyCode) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'COMPANY_CODE_NOT_FOUND',\n      message: '未找到对应的公司代码',\n      field: 'companyCode',\n      value: null,\n      timestamp: new Date().toISOString(),\n      type: 'DATABASE_ERROR'\n    }\n  };\n}\n\n// 获取验证后的参数\nlet startDate = $('参数验证逻辑').item.json.startdate;\nlet endDate = $('参数验证逻辑').item.json.enddate;\n\n// 检查是否已经传入了时间参数\nconst hasInputDates = startDate && startDate !== \"\" && endDate && endDate !== \"\";\n\nif (hasInputDates) {\n  // 如果已经传入了时间参数，直接使用传入的参数\n  // 注意：参数验证逻辑已经验证了30天限制，这里不需要重复验证\n  return {\n    validationResult: 'success',\n    companyCode: companyCode,\n    startdate: startDate,\n    enddate: endDate,\n    timestamp: new Date().toISOString(),\n    source: 'input'\n  };\n} else {\n  // 如果没有传入时间参数，从数据库获取最新两期时间\n  if (!recentDates || !Array.isArray(recentDates) || recentDates.length < 2) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INSUFFICIENT_DATE_DATA',\n        message: '数据库中日期数据不足，无法自动获取时间范围',\n        field: 'recent_dates',\n        value: recentDates,\n        timestamp: new Date().toISOString(),\n        type: 'DATABASE_ERROR'\n      }\n    };\n  }\n  \n  const latestDate = recentDates[0];\n  const secondLatestDate = recentDates[1];\n  \n  if (!latestDate || !secondLatestDate) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_DATE_DATA',\n        message: '数据库中的日期数据格式无效',\n        field: 'recent_dates',\n        value: recentDates,\n        timestamp: new Date().toISOString(),\n        type: 'DATABASE_ERROR'\n      }\n    };\n  }\n  \n  // 将日期转换为YYYYMMDD格式\n  const formatDate = (dateStr) => {\n    const date = new Date(dateStr);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}/${month}/${day}`;\n  };\n  \n  startDate = formatDate(secondLatestDate);\n  endDate = formatDate(latestDate);\n  \n  \n  return {\n    validationResult: 'success',\n    companyCode: companyCode,\n    startdate: startDate,\n    enddate: endDate,\n    timestamp: new Date().toISOString(),\n    source: 'database'\n  };\n}"}, "id": "a449c348-217c-4c9b-928f-0e5c395d35d1", "name": "数据库结果处理", "type": "n8n-nodes-base.code", "position": [-700, 1140], "typeVersion": 2}, {"parameters": {"jsCode": "const accessToken = $json.data?.access_token;\nconst expiredTimeStr = $json.data?.expired_time;\n\nif (!expiredTimeStr) {\n  throw new Error(\"expired_time 不存在，无法判断是否过期\");\n}\n\n// 将 '2025-07-10 18:42:42' 转为 ISO 格式 '2025-07-10T18:42:42'\nconst expiredAt = new Date(expiredTimeStr.replace(' ', 'T')).getTime();\nconst now = Date.now();\n\nconst isExpired = now >= expiredAt;\n\nreturn [{\n  json: {\n    access_token: accessToken,\n    expired_time: expiredTimeStr,\n    expired_timestamp: expiredAt,\n    is_expired: isExpired\n  }\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, 820], "id": "1d1e527d-41a9-45e8-a1df-1b9344a82fc4", "name": "判断access_token是否过期"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8f0cf283-33cf-4847-a5d1-2163874c054b", "leftValue": "={{ $json.is_expired === true }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [680, 820], "id": "d8b49051-c57c-4191-adc3-81bc358207a3", "name": "If"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [140, 820], "id": "8cf6c229-1fa3-47fd-b312-baa3303d08ae", "name": "获取access token"}, {"parameters": {"assignments": {"assignments": [{"id": "5f906a29-c6c7-4b73-b4cc-9f8b05f9e71d", "name": "fundcode", "value": "={{ $json.fundcode }}", "type": "string"}, {"id": "70187f95-81a0-447c-aa5f-e88bb37d9aaf", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "cf972d25-2aff-4c15-af40-579db61ca064", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}, {"id": "bba9fdf9-135e-473c-903a-b6c62eb42c74", "name": "refresh_token", "value": "=eyJzaWduX3RpbWUiOiIyMDI1LTA3LTIzIDE0OjExOjQ4In0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9C04C75508BBC8C1DE508E43E7EA749369F1E1D15B1681C6D27898F5A0A72E8D", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 820], "id": "45029d38-8b71-42b9-abfb-e911637f8562", "name": "固定refresh"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "819f3dc7-cb7d-4a9e-8ae4-3840982cf1ed", "name": "检查数据库结果", "type": "n8n-nodes-base.if", "position": [-500, 1080], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indipara\":[{\"indicator\":\"ths_td_datesint_stock\",\"indiparams\":[\"{{ $('检查数据库结果').item.json.startdate }}\",\"{{ $('检查数据库结果').item.json.enddate }}\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 800], "id": "2891fa83-ac98-4033-8726-230f50287fe8", "name": "区间交易天数"}, {"parameters": {"content": "同花顺循环获取可用token", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, 940], "id": "7c035420-9210-4aa7-acd5-0b81da36fd7d", "name": "Sticky Note2"}, {"parameters": {"content": "验证id是否可在数据库中查询到相关组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1020, 1020], "id": "077b7b5f-65bf-475d-a2f1-04274ba7d733", "name": "Sticky Note4"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "c6ea262d-462f-4e81-9d60-e74c5ac9d3bf", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-300, 1240], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "4342b005-7456-4437-91fa-d502aa89e8eb", "name": "average_stock_price", "value": "={{ $json.tables[0].table.ths_avg_close_int_stock[0] }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1360, 800], "id": "2190b2f6-6876-45c3-a0a5-e576e37e736b", "name": "拿到区间均价"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 根据公司代码查询股票行业分类信息\n-- <AUTHOR>\n-- @created 2025-08-05 17:05:21\n-- @description 通过无后缀的公司代码模糊匹配ths_stock_industry_classification表中有后缀的stock_code\n-- @example 输入: companyCode=\"300723\" 输出: 匹配到\"300723.SZ\"等带后缀的股票代码\n-- @note 使用LIKE模糊匹配，支持.SZ、.SH等各种后缀格式\n\nSELECT \n  stock_code,\n  stock_name,\n  csrc_industry_new,\n  ths_industry_new,\n  sw_industry,\n  table_updated_at\nFROM ths_stock_industry_classification\nWHERE stock_code LIKE '{{ $json.companyCode }}%'\nORDER BY stock_code ASC\nLIMIT 10;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-300, 760], "id": "a43a38e4-4683-4047-aac2-a4d97c446da7", "name": "同花顺-查找股票代码", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "同花顺数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    ci.\"companyCode\",\n    ARRAY(\n        SELECT sr.\"registerDate\"\n        FROM shareholder_registry sr\n        WHERE sr.\"organizationId\" = ci.\"organizationId\"\n        ORDER BY sr.\"registerDate\" DESC\n        LIMIT 2\n    ) AS recent_dates\nFROM company_info ci\nWHERE ci.\"organizationId\" = '{{ $json.organizationid }}'\nLIMIT 1;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1120, 1140], "id": "f7ee40a4-3f5a-4cae-9b21-4b9d2c696079", "name": "查找开始日期和结束日期", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"assignments": {"assignments": [{"id": "7ff75585-ad49-4ffa-823a-07830f4953c0", "name": "latest_price", "value": "={{ $json.tables[0].table.latest[0] }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1360, 1000], "id": "fc62e3d8-7a33-4925-b5ec-e359db4db840", "name": "拿到最新股价"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1560, 900], "id": "0820334e-d7d6-47d9-897e-607021e1db77", "name": "合并区间和最新股价"}, {"parameters": {"jsCode": "/**\n * 计算最新持仓收益和最新持仓市值\n * <AUTHOR>\n * @created 2025-01-27 15:15:00\n * @updated 2025-01-27 15:15:00 hayden 修复未执行节点引用错误，增加安全检测\n * @description 基于最新股价和持股数量计算投资收益指标，支持多种数据源\n */\n\n// 获取股价数据\nconst priceDataNodes = $('合并区间和最新股价').all();\n\n// 验证股价数据有效性\nif (!priceDataNodes || priceDataNodes.length === 0) {\n  return [{\n    json: {\n      error: 'PRICE_DATA_MISSING',\n      message: '合并区间和最新股价数据为空',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// 安全获取持股数量的函数\nfunction getSafeCurrentShares() {\n  let currentShares = 0;\n  let dataSource = '';\n  \n  try {\n    // 尝试获取第一个节点的数据\n    const node1Data = $('查询股东是否存在').first();\n    if (node1Data && node1Data.json && node1Data.json.current_shares) {\n      currentShares = Number(node1Data.json.current_shares) || 0;\n      dataSource = '查询股东是否存在';\n      return { currentShares, dataSource };\n    }\n  } catch (error) {\n    console.log('节点\"查询股东是否存在\"未执行或无数据');\n  }\n  \n  try {\n    // 尝试获取第二个节点的数据\n    const node2Data = $('查询股东是否存在1').first();\n    if (node2Data && node2Data.json && node2Data.json.current_shares) {\n      currentShares = Number(node2Data.json.current_shares) || 0;\n      dataSource = '查询股东是否存在1';\n      return { currentShares, dataSource };\n    }\n  } catch (error) {\n    console.log('节点\"查询股东是否存在1\"未执行或无数据');\n  }\n  \n  // 尝试从其他可能的数据源获取\n  try {\n    const shareholderNodes = $('基金名称匹配股东名称').all();\n    if (shareholderNodes && shareholderNodes.length > 0) {\n      const shareholderData = shareholderNodes[0].json;\n      currentShares = Number(shareholderData.current_shares || shareholderData.numberOfShares) || 0;\n      dataSource = '基金名称匹配股东名称';\n      return { currentShares, dataSource };\n    }\n  } catch (error) {\n    console.log('节点\"基金名称匹配股东名称\"未执行或无数据');\n  }\n  \n  try {\n    const unifiedAccountNodes = $('一码通查询股东').all();\n    if (unifiedAccountNodes && unifiedAccountNodes.length > 0) {\n      const unifiedData = unifiedAccountNodes[0].json;\n      currentShares = Number(unifiedData.current_shares || unifiedData.numberOfShares) || 0;\n      dataSource = '一码通查询股东';\n      return { currentShares, dataSource };\n    }\n  } catch (error) {\n    console.log('节点\"一码通查询股东\"未执行或无数据');\n  }\n  \n  return { currentShares: 0, dataSource: 'NONE' };\n}\n\n// 获取持股数量\nconst { currentShares, dataSource } = getSafeCurrentShares();\n\n// 验证持股数量有效性\nif (!currentShares || currentShares <= 0) {\n  return [{\n    json: {\n      error: 'SHARES_DATA_INVALID',\n      message: '当前持股数量无效或为零',\n      data_source_attempted: dataSource,\n      current_shares: currentShares,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// 提取股价数据\nconst priceData = priceDataNodes[0].json;\nconst latestPrice = Number(priceData.latest_price) || 0;\nconst averageStockPrice = Number(priceData.average_stock_price) || 0;\nconst currentSharesNum = Number(currentShares) || 0;\n\n// 验证股价数据\nif (latestPrice <= 0) {\n  return [{\n    json: {\n      error: 'PRICE_DATA_INVALID',\n      message: '最新股价数据无效',\n      latest_price: latestPrice,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// 计算最新持仓市值（元）\nconst latestMarketValue = latestPrice * currentSharesNum;\n\n// 计算预估持仓总成本（元）\nconst estimatedTotalCost = averageStockPrice * currentSharesNum;\n\n// 计算最新持仓收益（元）\nconst latestHoldingProfit = latestMarketValue - estimatedTotalCost;\n\n// 计算收益率\nconst profitRate = estimatedTotalCost > 0 ? (latestHoldingProfit / estimatedTotalCost * 100) : 0;\n\n// 返回计算结果\nreturn [{\n  json: {\n    // 计算结果\n    latest_market_value: latestMarketValue, // 最新持仓市值（元）\n    latest_holding_profit: latestHoldingProfit, // 最新持仓收益（元）\n    estimated_total_cost: estimatedTotalCost, // 预估持仓总成本（元）\n    profit_rate: profitRate, // 收益率（%）\n    \n    // 基础数据\n    latest_price: latestPrice, // 最新股价\n    average_stock_price: averageStockPrice, // 平均股价\n    current_shares: currentSharesNum, // 当前持股数量\n    \n    // 元数据\n    // data_source: dataSource, // 数据来源节点\n    // calculation_time: new Date().toISOString(), // 计算时间\n    // status: 'SUCCESS'\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 900], "id": "70d78d16-b667-40e8-9900-c1a5d85b3eb5", "name": "计算最新持仓市值+最新持仓收益"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1900, -120], "id": "19871d89-f1dc-4e9c-a9ff-d529ca39227d", "name": "合并最终数据"}, {"parameters": {"content": "基础信息字段\norganizationid: 组织机构ID\n id: 记录唯一标识符\ncertificate_number: 证件号码\nunified_account_number: 一码通账户号码\naccount_name: 证券账户名称/股东名称\nphone_number: 联系电话号码\nshareholder_type: 股东类型分类\nshareholder_category: 持有人类别代码\ncontact_address: 通讯地址\n账户持股信息字段\ncash_account: 普通证券账户号码\ncash_account_shares: 普通账户持股数量\nmargin_account: 信用证券账户号码\nmargin_account_shares: 信用账户持股数量\n持股变动分析字段\nlatest_period_date: 最新报告期日期\ncurrent_shares: 当前持股数量\ncurrent_ratio: 当前持股比例\nprev_shares: 上期持股数量\nprev_ratio: 上期持股比例\nprevious_period_date: 上期报告期日期\nshareholder_behavior: 股东行为（增持/减持/不变）\nshares_change: 持股数量变化\nratio_change: 持股比例变化\nholding_rank: 持股排名\n历史持股记录字段\nprevious_holding_rank: 上一期排名\nrank_change: 排名变化\nrank_change_description: 排名描述\nall_periods_holdings: 所有期间持股记录数组\n registerDate: 名册登记日期\n numberOfShares: 持股数量\n shareholdingRatio: 持股比例\n匹配状态字段\nmatch_status: 基金名称匹配状态\n公司整体统计字段\ncompany_frozen_shares: 公司冻结股总数\ncompany_locked_shares: 公司限售股总数\ncompany_frozen_ratio_percent: 公司冻结股比例\ncompany_locked_ratio_percent: 公司限售股比例\n投资收益分析字段\nlatest_market_value: 最新持仓市值（元）\nlatest_holding_profit: 最新持仓收益（元）\nestimated_total_cost: 预估持仓总成本（元）\nlatest_price: 最新股价\naverage_stock_price: 平均股价\ncalculation_time: 计算时间戳", "height": 900, "width": 480}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1760, 20], "id": "2c48ff5f-1e78-4f31-85de-1b099978c377", "name": "Sticky Note3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8acb4939-32ce-4662-9bfa-7f29f9aeab8a", "leftValue": "={{ $json.fund_code }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-800, 260], "id": "85e3d7da-97f1-480b-a924-cc082224f242", "name": "If1"}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\":0,\n  \"message\": \"没有找到该基金\"\n}", "options": {"responseCode": 200}}, "id": "ff403898-af4d-45f9-b5e1-68d99be4d4ee", "name": "错误响应4", "type": "n8n-nodes-base.respondToWebhook", "position": [-80, 420], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d6cf3028-c4a0-4949-b476-53dbfd497341", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-320, 280], "id": "b0fd59b2-2359-458f-b3b2-ab8f314d4289", "name": "查询股东是否存在1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 基金股东持仓分析查询 - 基于一码通账号直接查询\n * <AUTHOR>\n * @created 2025-01-27 10:30:00\n * @updated 2025-01-27 10:30:00 hayden 添加上一期排名和排名变动计算\n * @updated 2025-07-01 10:30:00 hayden 修改使用sharesInCashAccount字段并排除null值\n * @updated 2025-07-01 11:00:00 hayden 优化退出股东判断逻辑，提高退出判断优先级\n * @updated 2025-08-11 hayden 修正最新期和上一期识别逻辑，排除sharesInCashAccount为0和null的情况\n */\nWITH target_shareholder AS (\n  -- 查询指定一码通账号的所有期数记录，排除sharesInCashAccount为0和null\n  SELECT \n    s.id,\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\",\n    s.\"contactNumber\",\n    s.\"contactAddress\",\n    s.\"cashAccount\",\n    s.\"sharesInCashAccount\",\n    s.\"marginAccount\", \n    s.\"sharesInMarginAccount\",\n    s.\"shareholderType\",\n    s.\"shareholderCategory\"\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"unifiedAccountNumber\" = '{{ $json.unified_account_number }}'\n    AND s.\"sharesInCashAccount\" IS NOT NULL \n    AND s.\"sharesInCashAccount\" > 0\n),\nlatest_period AS (\n  -- 获取最新期数\n  SELECT MAX(\"registerDate\") as latest_date\n  FROM target_shareholder\n),\nprevious_period AS (\n  -- 获取上一期数据\n  SELECT \n    ts.\"sharesInCashAccount\" as prev_shares,\n    ts.\"shareholdingRatio\" as prev_ratio,\n    ts.\"registerDate\" as prev_date\n  FROM target_shareholder ts\n  WHERE ts.\"registerDate\" < (SELECT latest_date FROM latest_period)\n  ORDER BY ts.\"registerDate\" DESC\n  LIMIT 1\n),\ncurrent_period_exists AS (\n  -- 判断当前期是否存在该股东记录\n  SELECT COUNT(*) > 0 as exists_in_current\n  FROM target_shareholder ts\n  WHERE ts.\"registerDate\" = (SELECT latest_date FROM latest_period)\n),\nprevious_period_exists AS (\n  -- 判断上期是否存在该股东记录\n  SELECT COUNT(*) > 0 as exists_in_previous\n  FROM target_shareholder ts\n  WHERE ts.\"registerDate\" = (SELECT prev_date FROM previous_period)\n),\ncurrent_ranking AS (\n  -- 计算当前期排名（排除sharesInCashAccount为0和null）\n  SELECT \n    s.\"shareholderId\",\n    ROW_NUMBER() OVER (ORDER BY s.\"sharesInCashAccount\" DESC) as current_rank\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = (SELECT latest_date FROM latest_period)\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n),\nprevious_ranking AS (\n  -- 计算上一期排名（排除sharesInCashAccount为0和null）\n  SELECT \n    s.\"shareholderId\",\n    ROW_NUMBER() OVER (ORDER BY s.\"sharesInCashAccount\" DESC) as previous_rank\n  FROM \"shareholder\" s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = (SELECT prev_date FROM previous_period)\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n),\nall_periods_data AS (\n  -- 获取该股东所有期数的持股数据（排除sharesInCashAccount为0和null）\n  SELECT \n    \"registerDate\",\n    \"sharesInCashAccount\" as \"numberOfShares\",\n    \"shareholdingRatio\"\n  FROM target_shareholder\n  ORDER BY \"registerDate\" DESC\n)\nSELECT \n  '{{ $json.organizationId }}' as organizationId,\n  -- 基本信息\n  ts.id,\n  ts.\"shareholderId\" as certificate_number,\n  ts.\"unifiedAccountNumber\" as unified_account_number,\n  ts.\"securitiesAccountName\" as account_name,\n  ts.\"contactNumber\" as phone_number,\n  ts.\"shareholderType\" as shareholder_type,\n  ts.\"shareholderCategory\" as shareholder_category,\n  ts.\"contactAddress\" as contact_address,\n  ts.\"cashAccount\" as cash_account,\n  ts.\"sharesInCashAccount\" as cash_account_shares,\n  ts.\"marginAccount\" as margin_account,\n  ts.\"sharesInMarginAccount\" as margin_account_shares,\n  \n  -- 最新期数信息\n  ts.\"registerDate\" as latest_period_date,\n  ts.\"sharesInCashAccount\" as current_shares,\n  ts.\"shareholdingRatio\" as current_ratio,\n  \n  -- 上期对比信息\n  pp.prev_shares,\n  pp.prev_ratio,\n  pp.prev_date as previous_period_date,\n  \n  -- 股东行为判断（优化逻辑，提高退出判断优先级）\n  CASE \n    -- 优先判断退出：上期存在但当前期不存在\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = false THEN '退出'\n    -- 判断新进：上期不存在但当前期存在\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = false \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN '新进'\n    -- 判断增持、减持、不动（只有两期都存在时才判断）\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN\n        CASE \n          WHEN ts.\"sharesInCashAccount\" > pp.prev_shares THEN '增持'\n          WHEN ts.\"sharesInCashAccount\" < pp.prev_shares THEN '减持'\n          WHEN ts.\"sharesInCashAccount\" = pp.prev_shares THEN '不动'\n          ELSE '未知'\n        END\n    ELSE '未知'\n  END AS shareholder_behavior,\n  \n  -- 持股变化计算\n  CASE \n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN\n        ts.\"sharesInCashAccount\" - pp.prev_shares\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = false \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN\n        ts.\"sharesInCashAccount\"\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = false THEN\n        -pp.prev_shares\n    ELSE NULL\n  END AS shares_change,\n  \n  CASE \n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN\n        ts.\"shareholdingRatio\" - pp.prev_ratio\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = false \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN\n        ts.\"shareholdingRatio\"\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = false THEN\n        -pp.prev_ratio\n    ELSE NULL\n  END AS ratio_change,\n  \n  -- 持股排名信息\n  cr.current_rank as holding_rank,\n  pr.previous_rank as previous_holding_rank,\n  \n  -- 排名变动计算\n  CASE \n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = false \n      OR (SELECT exists_in_current FROM current_period_exists) = false THEN NULL\n    ELSE pr.previous_rank - cr.current_rank\n  END AS rank_change,\n  \n  -- 排名变动描述\n  CASE \n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = false \n      AND (SELECT exists_in_current FROM current_period_exists) = true THEN '新进股东'\n    WHEN (SELECT exists_in_previous FROM previous_period_exists) = true \n      AND (SELECT exists_in_current FROM current_period_exists) = false THEN '退出股东'\n    WHEN pr.previous_rank - cr.current_rank > 0 THEN '排名上升'\n    WHEN pr.previous_rank - cr.current_rank < 0 THEN '排名下降'\n    WHEN pr.previous_rank - cr.current_rank = 0 THEN '排名不动'\n    ELSE '排名未知'\n  END AS rank_change_description,\n  \n  -- 所有期数数据（JSON格式）\n  (\n    SELECT JSON_AGG(\n      JSON_BUILD_OBJECT(\n        'registerDate', \"registerDate\",\n        'numberOfShares', \"numberOfShares\", \n        'shareholdingRatio', \"shareholdingRatio\"\n      ) ORDER BY \"registerDate\" DESC\n    )\n    FROM all_periods_data\n  ) as all_periods_holdings,\n   \n  -- 匹配状态\n  'UNIFIED_ACCOUNT_MATCH' as match_status\nFROM target_shareholder ts\nCROSS JOIN latest_period lp\nLEFT JOIN previous_period pp ON 1=1\nLEFT JOIN current_ranking cr ON cr.\"shareholderId\" = ts.\"shareholderId\"\nLEFT JOIN previous_ranking pr ON pr.\"shareholderId\" = ts.\"shareholderId\"\nWHERE ts.\"registerDate\" = lp.latest_date\nORDER BY ts.\"sharesInCashAccount\" DESC;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-520, 280], "id": "03b17ad7-7437-40d3-9cdb-df6786836c8a", "name": "一码通查询股东", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}], "connections": {"提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "If1", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "查询同花顺基金名称": {"main": [[{"node": "检查基金代码存在", "type": "main", "index": 0}]]}, "基金名称匹配股东名称": {"main": [[{"node": "查询股东是否存在", "type": "main", "index": 0}]]}, "检查基金代码存在": {"main": [[{"node": "基金名称匹配股东名称", "type": "main", "index": 0}], [{"node": "错误响应2", "type": "main", "index": 0}]]}, "计算冻结股数": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "查询股东是否存在": {"main": [[{"node": "查找开始日期和结束日期", "type": "main", "index": 0}, {"node": "计算冻结股数", "type": "main", "index": 0}], [{"node": "错误响应3", "type": "main", "index": 0}]]}, "合并数据": {"main": [[{"node": "合并最终数据", "type": "main", "index": 0}]]}, "获取区间股价（收盘价）": {"main": [[{"node": "拿到区间均价", "type": "main", "index": 0}]]}, "最新股价": {"main": [[{"node": "拿到最新股价", "type": "main", "index": 0}]]}, "数据库结果处理": {"main": [[{"node": "检查数据库结果", "type": "main", "index": 0}]]}, "判断access_token是否过期": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "区间交易天数", "type": "main", "index": 0}], [{"node": "固定refresh", "type": "main", "index": 0}]]}, "获取access token": {"main": [[{"node": "判断access_token是否过期", "type": "main", "index": 0}]]}, "固定refresh": {"main": [[{"node": "获取access token", "type": "main", "index": 0}]]}, "检查数据库结果": {"main": [[{"node": "同花顺-查找股票代码", "type": "main", "index": 0}], [{"node": "错误响应1", "type": "main", "index": 0}]]}, "区间交易天数": {"main": [[{"node": "最新股价", "type": "main", "index": 0}, {"node": "获取区间股价（收盘价）", "type": "main", "index": 0}]]}, "拿到区间均价": {"main": [[{"node": "合并区间和最新股价", "type": "main", "index": 0}]]}, "同花顺-查找股票代码": {"main": [[{"node": "固定refresh", "type": "main", "index": 0}]]}, "查找开始日期和结束日期": {"main": [[{"node": "数据库结果处理", "type": "main", "index": 0}]]}, "拿到最新股价": {"main": [[{"node": "合并区间和最新股价", "type": "main", "index": 1}]]}, "合并区间和最新股价": {"main": [[{"node": "计算最新持仓市值+最新持仓收益", "type": "main", "index": 0}]]}, "计算最新持仓市值+最新持仓收益": {"main": [[{"node": "合并最终数据", "type": "main", "index": 1}]]}, "合并最终数据": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "查询同花顺基金名称", "type": "main", "index": 0}], [{"node": "一码通查询股东", "type": "main", "index": 0}]]}, "查询股东是否存在1": {"main": [[{"node": "查找开始日期和结束日期", "type": "main", "index": 0}, {"node": "计算冻结股数", "type": "main", "index": 0}], [{"node": "错误响应4", "type": "main", "index": 0}]]}, "一码通查询股东": {"main": [[{"node": "查询股东是否存在1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}