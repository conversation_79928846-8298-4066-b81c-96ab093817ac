{"nodes": [{"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "c8bf22d8-ab7b-4f87-9244-87c513fa388d", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-2260, 2120], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 404}}, "id": "915ec63c-e638-43a8-b4c5-1d55fdecc726", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1940, 2540], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "5f906a29-c6c7-4b73-b4cc-9f8b05f9e71d", "name": "fundcode", "value": "={{ $json.fundcode }}", "type": "string"}, {"id": "70187f95-81a0-447c-aa5f-e88bb37d9aaf", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "cf972d25-2aff-4c15-af40-579db61ca064", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}, {"id": "bba9fdf9-135e-473c-903a-b6c62eb42c74", "name": "refresh_token", "value": "=eyJzaWduX3RpbWUiOiIyMDI1LTA3LTEwIDEwOjQ2OjAxIn0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6529EA27B119F50E6966447B09E7214EF92C1DFD560ABA582055425A090DB61B", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1900, 2100], "id": "0fd952e5-0294-479c-94ff-5aed81625a14", "name": "固定refresh"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1680, 2100], "id": "d61167de-6902-47c8-b3ef-c832b07c901a", "name": "获取access token"}, {"parameters": {"jsCode": "/**\n * Token过期时间验证器\n * <AUTHOR>\n * @description 检查access_token的过期时间，判断token是否仍然有效\n * 主要功能：\n * 1. 提取access_token和过期时间\n * 2. 将过期时间字符串转换为时间戳\n * 3. 与当前时间比较判断是否过期\n * 4. 返回token状态信息供后续节点使用\n */\n\n// 从输入数据中提取access_token和过期时间\n// 使用可选链操作符(?.)安全访问嵌套属性，避免undefined错误\nconst accessToken = $json.data?.access_token;\nconst expiredTimeStr = $json.data?.expired_time;\n\n// 验证过期时间是否存在\n// 如果expired_time不存在，无法进行过期判断，抛出异常\nif (!expiredTimeStr) {\n  throw new Error(\"expired_time 不存在，无法判断是否过期\");\n}\n\n// 时间格式转换和过期判断\n// 将 '2025-07-10 18:42:42' 格式转为 ISO 格式 '2025-07-10T18:42:42'\n// 这是因为Date构造函数需要标准的ISO时间格式才能正确解析\nconst expiredAt = new Date(expiredTimeStr.replace(' ', 'T')).getTime();\n\n// 获取当前时间戳（毫秒）\nconst now = Date.now();\n\n// 比较当前时间和过期时间，判断token是否已过期\n// 如果当前时间大于等于过期时间，则认为token已过期\nconst isExpired = now >= expiredAt;\n\n// 返回n8n标准格式的数据\n// 包含原始token信息、过期时间戳和过期状态\nreturn [{\n  json: {\n    access_token: accessToken,        // 原始access_token\n    expired_time: expiredTimeStr,     // 原始过期时间字符串\n    expired_timestamp: expiredAt,     // 过期时间戳（毫秒）\n    is_expired: isExpired            // 是否已过期的布尔值\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1400, 2100], "id": "849aeb06-9102-43ef-8d39-0787dcba2ec7", "name": "Code"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8f0cf283-33cf-4847-a5d1-2163874c054b", "leftValue": "={{ $json.is_expired === true }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1140, 2100], "id": "9b5d1aa6-fff2-4c43-9444-3c2085879532", "name": "If"}, {"parameters": {"httpMethod": "POST", "path": "fund_details", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3020, 2120], "id": "7eb807df-b04a-4dba-bf51-b69f474e8ac3", "name": "基金详情展示1", "webhookId": "34d8fd1e-ef14-46b0-a801-697201ce8b4c"}, {"parameters": {"assignments": {"assignments": [{"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "7f2e0ad0-aa7d-446a-9f01-9032decbcb5c", "name": "stockCode", "value": "={{ $json.query?.stockCode || '' }}", "type": "string"}, {"id": "a767eb0b-9f5c-46a3-a347-fcdac928f5a5", "name": "fundcode", "value": "={{ $json.query?.fundcode || ''  }}", "type": "string"}, {"id": "6d68b9d5-45f0-41d7-8bab-615d6b1df6f4", "name": "", "value": "", "type": "string"}]}, "options": {}}, "id": "fc889d18-dbdf-40b5-a9f9-db75502ef443", "name": "提取输入参数2", "type": "n8n-nodes-base.set", "position": [-2800, 2120], "typeVersion": 3.4, "notesInFlow": false}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n \n * <AUTHOR>  // 添加注释和说明\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n * \n * 功能说明：\n * 这是一个高度可配置的通用参数验证器，主要用于验证n8n工作流中的输入参数\n * 支持多种数据类型验证，包括字符串、数字、数组、对象、布尔值、邮箱、URL等\n * 通过配置化的方式定义验证规则，避免重复编写验证逻辑\n * 提供详细的错误信息和统一的返回格式，便于调试和错误处理\n */\n\ntry {\n  // 安全的数据获取 - 从n8n输入节点获取所有数据\n  const inputData = $input.all();\n  \n  // 输入数据为空的异常处理\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 提取第一个输入项的JSON数据，这是n8n的标准数据格式\n  const jsonData = inputData[0]?.json;\n  \n  // 验证JSON数据格式的有效性\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数集合 - 包含各种数据类型的验证逻辑\n  const validators = {\n    // 字符串验证器 - 支持长度、正则表达式、非空等验证\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false; // 类型检查\n      if (options.minLength && val.length < options.minLength) return false; // 最小长度\n      if (options.maxLength && val.length > options.maxLength) return false; // 最大长度\n      if (options.pattern && !options.pattern.test(val)) return false; // 正则表达式匹配\n      if (options.notEmpty && val.trim() === '') return false; // 非空检查（去除空白字符）\n      return true;\n    },\n    \n    // 数字验证器 - 支持范围、整数等验证\n    number: (val, options = {}) => {\n      const num = Number(val); // 尝试转换为数字\n      if (isNaN(num)) return false; // NaN检查\n      if (options.min !== undefined && num < options.min) return false; // 最小值\n      if (options.max !== undefined && num > options.max) return false; // 最大值\n      if (options.integer && !Number.isInteger(num)) return false; // 整数检查\n      return true;\n    },\n    \n    // 数组验证器 - 支持长度和元素类型验证\n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false; // 数组类型检查\n      if (options.minLength && val.length < options.minLength) return false; // 最小长度\n      if (options.maxLength && val.length > options.maxLength) return false; // 最大长度\n      // 递归验证数组元素类型\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    // 对象验证器 - 支持必需键验证\n    object: (val, options = {}) => {\n      // 对象类型检查（排除null和数组）\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      // 检查必需的键是否存在\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    // 布尔值验证器 - 支持多种布尔值表示形式\n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    // 邮箱验证器 - 使用正则表达式验证邮箱格式\n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    // URL验证器 - 使用URL构造函数验证URL格式\n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  // 验证配置数组 - 定义每个字段的验证规则\n  const validationConfig = [\n    {\n      field: 'fundcode', // 字段名\n      type: 'string', // 数据类型\n      required: true, // 是否必需\n      options: { \n        notEmpty: true, // 不能为空\n        minLength: 1, // 最小长度\n        pattern: /^[0-9]{6}\\.(OF|SH|SZ)$/i, // 基金代码格式：6位数字.OF/SH/SZ\n        // 自定义验证函数 - 用于复杂的业务逻辑验证\n        customValidator: (val) => {\n          // 基金代码格式验证\n          const fundCodePattern = /^[0-9]{6}\\.(OF|SH|SZ)$/i;\n          return fundCodePattern.test(val);\n        }\n      },\n      defaultValue: \"\" // 默认值\n    },\n    {\n      field: 'organizationId', // 组织ID字段\n      type: 'string', \n      required: false, // 可选参数\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: \"\"\n    }\n  ];\n\n  // 通用验证执行器 - 处理验证逻辑的核心部分\n  const processedData = {\n    validationResult: 'success', // 验证结果标识\n    timestamp: jsonData.timestamp || new Date().toISOString() // 时间戳\n  };\n\n  // 遍历验证配置，逐个验证字段\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field]; // 获取字段值\n\n    // 处理缺失值的情况\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        // 必需字段缺失，返回错误\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 可选字段缺失，使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理 - 将字符串转换为目标类型\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value); // 字符串转数字\n    } else if (type === 'boolean') {\n      // 字符串转布尔值\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1; // 数字转布尔值\n      }\n    }\n\n    // 执行验证 - 获取对应类型的验证器\n    const validator = validators[type];\n    if (!validator) {\n      // 不支持的数据类型\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    // 调用验证器进行验证\n    const isValid = validator(value, options);\n    if (!isValid) {\n      // 针对fundcode提供更明确的错误信息\n      if (field === 'fundcode') {\n        return {\n          validationResult: 'error',\n          error: {\n            code: 'INVALID_FUND_CODE_FORMAT',\n            message: '基金代码格式不符合规范。正确格式：6位数字.OF/SH/SZ（如：000001.OF）',\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      }\n      \n      // 通用验证失败错误信息\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 自定义验证器检查 - 执行业务逻辑验证\n    if (options.customValidator && !options.customValidator(value)) {\n      if (field === 'fundcode') {\n        return {\n          validationResult: 'error',\n          error: {\n            code: 'INVALID_FUND_CODE_FORMAT',\n            message: '基金代码格式不符合规范。正确格式：6位数字.OF/SH/SZ（如：000001.OF）',\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      }\n    }\n\n    // 验证通过，保存处理后的值到结果对象\n    processedData[field] = value;\n  }\n\n  // 返回验证成功的结果，包含所有处理后的数据\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获 - 处理意外错误\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}"}, "id": "ce7cb8f5-1ab1-44e5-aa39-199bb840db33", "name": "参数验证逻辑1", "type": "n8n-nodes-base.code", "position": [-2520, 2120], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/data_pool", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"reportname\":\"p00407\",\"functionpara\":{\"jjlb\":\"{{ $('提取输入参数2').item.json.fundcode }}\"},\"outputpara\":\"jydm,jydm_mc,p00407_f002,p00407_f004,p00407_f009,p00407_f011,p00407_f013,p00407_f015,p00407_f023,p00407_f019\"}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, -320], "id": "9094bead-74eb-4b4c-ab30-50344b1e90a8", "name": "基金基本资料"}, {"parameters": {"assignments": {"assignments": [{"id": "df5460e1-3a9e-4d6f-8542-ca40f5cecf9a", "name": "Fund_type", "value": "={{ $json.tables[0].table.p00407_f019 }}", "type": "string"}, {"id": "22cecda5-0b9f-4533-b6e3-027a06be4802", "name": "Fund_Name", "value": "={{ $json.tables[0].table.jydm_mc }}", "type": "string"}, {"id": "8f8ba8c4-6285-48b5-9fb0-f44cf75fde0b", "name": "Fund_code", "value": "={{ $json.tables[0].table.jydm }}", "type": "string"}, {"id": "ed643734-7fba-478a-8bfe-149075b1d2b5", "name": "成立时间", "value": "={{ $json.tables[0].table.p00407_f015 }}", "type": "string"}, {"id": "a1fc9fe6-5de3-419f-ae39-64da05844e6f", "name": "指数名称", "value": "={{ $json.tables[0].table.p00407_f023 }}", "type": "string"}, {"id": "08e05446-8570-4be3-ab36-1ad285db42ae", "name": "基金公司", "value": "={{ $json.tables[0].table.p00407_f013 }}", "type": "string"}, {"id": "180e5522-ca20-4f9a-8588-b16c6e12a040", "name": "基金经理", "value": "={{ $json.tables[0].table.p00407_f011 }}", "type": "string"}, {"id": "18723600-6ae1-4ddd-823c-a2056f6864ee", "name": "联接基金", "value": "={{ $json.tables[0].table.ths_related_thscode_fund }}", "type": "string"}, {"id": "e3e58504-ca3c-4f6a-9f3a-8f0c082ffd42", "name": "总资产规模（亿元）", "value": "={{ $json.tables[0].table.p00407_f009 }}", "type": "string"}, {"id": "059e8c60-2ed0-43fe-a3fa-11c1862aba07", "name": "基金场内规模", "value": "", "type": "string"}, {"id": "9552f049-3236-4724-9068-d616aa11db9a", "name": "管理费用率", "value": "={{ $json.tables[0].table.p00412_f001 }}", "type": "string"}, {"id": "ff722652-e151-4471-9af6-f32bc64716a5", "name": "托管费用率", "value": "={{ $json.tables[0].table.p00412_f002 }}", "type": "string"}, {"id": "0bd78ad9-1cc9-4e7b-8eca-ed6dc8cbaca6", "name": "持仓换手率", "value": "=[\"{{ $json.tables[0].table.ths_turnover_ratio_fund }}\"]", "type": "string"}, {"id": "f7fdef8a-2816-4fa4-bf6b-655c1da64bd3", "name": "基金净值", "value": "={{ $json.tables[0].table.p00407_f002 }}", "type": "string"}, {"id": "4a87c64d-5e96-4ef1-8f86-56b42c1ef371", "name": "收盘价格", "value": "={{ $json.tables[0].table.p00407_f004 }}", "type": "string"}, {"id": "115c0453-cd36-4a4e-a4ba-9ab6822245c9", "name": "收盘价涨跌幅", "value": "={{ $json.tables[0].table.ths_unit_nvg_rate_fund }}", "type": "string"}, {"id": "96aa4d18-3535-4e7f-be45-2dfbf0e38585", "name": "成交金额", "value": "={{ $json.tables[0].table.ths_amt_fund }}", "type": "string"}, {"id": "c0bd6023-643b-4f0d-974f-9d44b415aae5", "name": "折价率", "value": "={{ $json.tables[0].table.ths_premium_rate_to_nv_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1660, 1020], "id": "41aba488-5312-4a13-aafa-78b0f3cf8d64", "name": "基金基础数据"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_related_thscode_fund\"}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, -120], "id": "75291dc7-5ba8-45e5-b4b9-90b48835839b", "name": "联接基金"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/data_pool", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"reportname\":\"p00412\",\"functionpara\":{\"jjlb\":\"{{ $('提取输入参数2').item.json.fundcode }}\"},\"outputpara\":\"jydm,jydm_mc,p00412_f001,p00412_f002\"}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 80], "id": "9bebb1d4-3595-4e15-b4aa-b0e5ebf9c7c5", "name": "费用率"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 7, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1080, 560], "id": "e18c536b-cb93-4755-b53c-49e9c9ec4e38", "name": "<PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_turnover_ratio_fund\",\"indiparams\":[\"{{ $json.lastTradingDay }}\"]}]}\n ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 260], "id": "5cb967b2-5e2c-4234-b0ab-bd35487827ce", "name": "持仓换手率"}, {"parameters": {"jsCode": "// 获取当前日期（n8n运行时的“当天”）\nconst today = new Date();\n\n// 计算上一天日期\nconst yesterday = new Date(today);\nyesterday.setDate(yesterday.getDate() - 1);\n\n// 处理周末：如果上一天是周日（0），则再减1天到周六；如果是周六（6），减2天到周五\nconst day = yesterday.getDay();\nif (day === 0) { // 周日\n  yesterday.setDate(yesterday.getDate() - 1);\n} else if (day === 6) { // 周六\n  yesterday.setDate(yesterday.getDate() - 2);\n}\n\n// 格式化为YYYYMMDD\nconst year = yesterday.getFullYear();\nconst month = String(yesterday.getMonth() + 1).padStart(2, '0');\nconst date = String(yesterday.getDate()).padStart(2, '0');\nconst lastTradingDay = `${year}${month}${date}`;\n\n// 输出到下一个节点\nreturn [{\n  json: {\n    lastTradingDay: lastTradingDay\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, 260], "id": "ca54d2a8-2e39-4abd-b01b-56943e051847", "name": "日期处理"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_unit_nvg_rate_fund\",\"indiparams\":[\"{{ $json.lastTradingDay }}\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 460], "id": "e8756e97-a83c-4fed-980a-c6853d779de7", "name": "收盘价涨跌幅"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_amt_fund\",\"indiparams\":[\"{{ $json.lastTradingDay }}\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 660], "id": "63b1c887-ce20-4d3d-8971-d2717350be1c", "name": "成交金额"}, {"parameters": {"jsCode": "// 获取当前日期（n8n运行时的“当天”）\nconst today = new Date();\n\n// 计算上一天日期\nconst yesterday = new Date(today);\nyesterday.setDate(yesterday.getDate() - 1);\n\n// 处理周末：如果上一天是周日（0），则再减1天到周六；如果是周六（6），减2天到周五\nconst day = yesterday.getDay();\nif (day === 0) { // 周日\n  yesterday.setDate(yesterday.getDate() - 1);\n} else if (day === 6) { // 周六\n  yesterday.setDate(yesterday.getDate() - 2);\n}\n\n// 格式化为YYYYMMDD\nconst year = yesterday.getFullYear();\nconst month = String(yesterday.getMonth() + 1).padStart(2, '0');\nconst date = String(yesterday.getDate()).padStart(2, '0');\nconst lastTradingDay = `${year}${month}${date}`;\n\n// 输出到下一个节点\nreturn [{\n  json: {\n    lastTradingDay: lastTradingDay\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, 460], "id": "8ea65a56-7954-4068-b41d-dce1ce3a18fc", "name": "日期处理1"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_premium_rate_to_nv_fund\",\"indiparams\":[\"{{ $json.lastTradingDay }}\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 880], "id": "945cb3e9-c09a-4599-a21a-52c942c84508", "name": "折价率"}, {"parameters": {"jsCode": "// 获取当前日期（n8n运行时的“当天”）\nconst today = new Date();\n\n// 计算上一天日期\nconst yesterday = new Date(today);\nyesterday.setDate(yesterday.getDate() - 1);\n\n// 处理周末：如果上一天是周日（0），则再减1天到周六；如果是周六（6），减2天到周五\nconst day = yesterday.getDay();\nif (day === 0) { // 周日\n  yesterday.setDate(yesterday.getDate() - 1);\n} else if (day === 6) { // 周六\n  yesterday.setDate(yesterday.getDate() - 2);\n}\n\n// 格式化为YYYYMMDD\nconst year = yesterday.getFullYear();\nconst month = String(yesterday.getMonth() + 1).padStart(2, '0');\nconst date = String(yesterday.getDate()).padStart(2, '0');\nconst lastTradingDay = `${year}${month}${date}`;\n\n// 输出到下一个节点\nreturn [{\n  json: {\n    lastTradingDay: lastTradingDay\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, 660], "id": "37367b21-4740-4d09-aba1-25284bc7b5bd", "name": "日期处理2"}, {"parameters": {"jsCode": "// 获取当前日期（n8n运行时的“当天”）\nconst today = new Date();\n\n// 计算上一天日期\nconst yesterday = new Date(today);\nyesterday.setDate(yesterday.getDate() - 1);\n\n// 处理周末：如果上一天是周日（0），则再减1天到周六；如果是周六（6），减2天到周五\nconst day = yesterday.getDay();\nif (day === 0) { // 周日\n  yesterday.setDate(yesterday.getDate() - 1);\n} else if (day === 6) { // 周六\n  yesterday.setDate(yesterday.getDate() - 2);\n}\n\n// 格式化为YYYYMMDD\nconst year = yesterday.getFullYear();\nconst month = String(yesterday.getMonth() + 1).padStart(2, '0');\nconst date = String(yesterday.getDate()).padStart(2, '0');\nconst lastTradingDay = `${year}${month}${date}`;\n\n// 输出到下一个节点\nreturn [{\n  json: {\n    lastTradingDay: lastTradingDay\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, 880], "id": "c8cd1f4b-d2f7-4551-acbd-6fdb6a9af38d", "name": "日期处理3"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "contentType": "raw", "rawContentType": "=formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_yeild_1m_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_3m_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_6m_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_1y_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_2y_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_3y_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_5y_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_10y_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\"]},{\"indicator\":\"ths_yeild_ytd_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]},{\"indicator\":\"ths_yeild_std_fund\",\"indiparams\":[\"{{ $json.dynamicDate }}\",\"101\",\"100\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 1140], "id": "d5fae386-da3f-4503-ad31-ca2dcada2a60", "name": "基金收益率表现"}, {"parameters": {"assignments": {"assignments": [{"id": "22bb8f4d-3315-4ec4-baef-08c0d94884d7", "name": "今年以来收益率", "value": "={{ $json.tables[0].table.ths_yeild_ytd_fund }}", "type": "string"}, {"id": "f58b3628-d4d6-4345-9719-98f297b279fb", "name": "近1个月收益率", "value": "={{ $json.tables[0].table.ths_yeild_1m_fund }}", "type": "string"}, {"id": "762bbe18-7819-4d0a-a78a-1b7ab8322f29", "name": "近3个月收益率", "value": "={{ $json.tables[0].table.ths_yeild_3m_fund }}", "type": "string"}, {"id": "7e5f2fd5-25b1-4786-9453-d1f881d0af77", "name": "近6个月收益率", "value": "={{ $json.tables[0].table.ths_yeild_6m_fund }}", "type": "string"}, {"id": "03e779f3-7782-4840-bcb9-cde67fa3b1e2", "name": "近1年收益率", "value": "={{ $json.tables[0].table.ths_yeild_1y_fund }}", "type": "string"}, {"id": "f0d7781c-2b6e-4416-8880-91c022ffc62d", "name": "近2年收益率", "value": "={{ $json.tables[0].table.ths_yeild_2y_fund }}", "type": "string"}, {"id": "9e578780-82f5-4d73-8282-978df69a96c5", "name": "近3年收益率", "value": "={{ $json.tables[0].table.ths_yeild_3y_fund }}", "type": "string"}, {"id": "59727542-6be2-45f1-934d-8ce152ba97cc", "name": "近5年收益率", "value": "={{ $json.tables[0].table.ths_yeild_5y_fund }}", "type": "string"}, {"id": "2d7efd42-0113-471a-9c99-548640a90e8b", "name": "近10年收益率", "value": "={{ $json.tables[0].table.ths_yeild_10y_fund }}", "type": "string"}, {"id": "60edc207-32fb-4281-b6ca-8142ca669b5c", "name": "成立以来收益率", "value": "={{ $json.tables[0].table.ths_yeild_std_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1700, 1320], "id": "bbce98de-2bbf-45f8-9483-87efacebab1e", "name": "固定各区间收益率"}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "1fdc5cce-a6a4-4ece-a0a5-6c6310737847", "name": "成功响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [3840, 2000], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "2573c00a-50fb-424d-a573-2f9b5245782c", "name": "基金代码", "value": "={{ $json.tables[0].thscode }}", "type": "string"}, {"id": "a3c90849-a420-4b0d-abb4-b2cf4917d164", "name": "行业配置", "value": "={{ $json.tables[0].table.ths_top_industry_name_fund }}", "type": "string"}, {"id": "e64e45d1-91c5-4520-bfed-0a361b7795e3", "name": "行业占净值比", "value": "={{ $json.tables[0].table.ths_top_industry_mv_to_fnv_fund }}", "type": "string"}, {"id": "32a4737c-fd29-4921-917f-a535b0c755d8", "name": "较上期", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1740, 1560], "id": "585f0c31-09d3-4c39-861c-1c99b824a065", "name": "固定行业配置"}, {"parameters": {"assignments": {"assignments": [{"id": "2573c00a-50fb-424d-a573-2f9b5245782c", "name": "重仓股票代码", "value": "={{ $json.tables[0].table.ths_top_held_stock_code_fund }}", "type": "string"}, {"id": "a3c90849-a420-4b0d-abb4-b2cf4917d164", "name": "重仓股票简称", "value": "={{ $json.tables[0].table.ths_top_held_stock_name_fund }}", "type": "string"}, {"id": "e64e45d1-91c5-4520-bfed-0a361b7795e3", "name": "持仓市值", "value": "={{ $json.tables[0].table.ths_top_held_mv_fund }}", "type": "string"}, {"id": "32a4737c-fd29-4921-917f-a535b0c755d8", "name": "占基金净值比", "value": "={{ $json.tables[0].table.ths_top_stock_mv_to_fnv_fund }}", "type": "string"}, {"id": "fe1ca704-8dc0-4bdc-8cd6-2efa98c0f2ff", "name": "持仓数量", "value": "={{ $json.tables[0].table.ths_top_held_num_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1740, 1840], "id": "3592ba36-1a0a-4dc8-ba40-589face11154", "name": "固定重仓股票（TOP10）"}, {"parameters": {"assignments": {"assignments": [{"id": "2573c00a-50fb-424d-a573-2f9b5245782c", "name": "重仓债券代码", "value": "={{ $json.tables[0].table.ths_top_bond_code_fund }}", "type": "string"}, {"id": "a3c90849-a420-4b0d-abb4-b2cf4917d164", "name": "重仓债券简称", "value": "={{ $json.tables[0].table.ths_top_bond_name_fund }}", "type": "string"}, {"id": "e64e45d1-91c5-4520-bfed-0a361b7795e3", "name": "持仓市值", "value": "={{ $json.tables[0].table.ths_top_bond_held_mv_fund }}", "type": "string"}, {"id": "32a4737c-fd29-4921-917f-a535b0c755d8", "name": "占基金净值比", "value": "={{ $json.tables[0].table.ths_top_bond_mv_to_stock_invest_mv_fund }}", "type": "string"}, {"id": "fe1ca704-8dc0-4bdc-8cd6-2efa98c0f2ff", "name": "持仓数量", "value": "={{ $json.tables[0].table.ths_top_bond_held_num_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1740, 2080], "id": "6316fec3-fc70-43f8-bf9b-00327a5ce5a9", "name": "固定重仓债券（TOP5）"}, {"parameters": {"fieldToSplitOut": "access_token", "include": "allOtherFields", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-880, 2080], "id": "999fd342-a3d2-4e47-a321-cb693de32566", "name": "Split Out"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "contentType": "raw", "rawContentType": "formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_rzts_by_tenure_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_name_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_managerid_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_gender_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_education_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_age_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_fund_manager_max_ages_fund\"},{\"indicator\":\"ths_rzjjzgm_fund\",\"indiparams\":[\"100\",\"1\",\"101\",\"1\"]},{\"indicator\":\"ths_zrjjs_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_fund_manager_invest_style_fund\",\"indiparams\":[\"100\",\"1\",\"20250713\",\"101\"]},{\"indicator\":\"ths_theme_style_fund\",\"indiparams\":[\"100\",\"1\",\"20250713\",\"101\"]},{\"indicator\":\"ths_service_funds_num_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_managerid_fund\",\"indiparams\":[\"100\",\"1\"]},{\"indicator\":\"ths_jlhjcs_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]},{\"indicator\":\"ths_resume_fund\",\"indiparams\":[\"100\",\"1\",\"101\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [320, 2640], "id": "dd917ad5-d908-496d-be33-f4278cafbc37", "name": "基金经理信息"}, {"parameters": {"assignments": {"assignments": [{"id": "bb728591-623b-4710-80e7-0ce12b0c70ba", "name": "姓名", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_name_fund }}", "type": "string"}, {"id": "3c872209-8a65-42a4-a78c-a280b5a740b0", "name": "性别", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_gender_fund }}", "type": "string"}, {"id": "2a0e5940-7bd9-419d-a3e4-9027502561da", "name": "年龄", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_age_fund }}", "type": "string"}, {"id": "444e7d7a-76e0-4712-815e-d50fd5e7e058", "name": "学历", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_education_fund }}", "type": "string"}, {"id": "38e43711-eeb4-40f9-8d7a-2d07e746a91a", "name": "简历", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_resume_fund }}", "type": "string"}, {"id": "86467108-6849-4856-b15d-ff5ba4667223", "name": "投资经理年限", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_fund_manager_max_ages_fund }}", "type": "string"}, {"id": "3f3b26d3-5f67-4961-aac6-04670ac435ac", "name": "在任基金总规模", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_rzjjzgm_fund }}", "type": "string"}, {"id": "76de5531-7ff8-42e6-ba19-0b916f544c6d", "name": "在任基金管理数", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_zrjjs_fund }}", "type": "string"}, {"id": "0b07b6e7-e108-4738-b785-a61ca240982b", "name": "现任公司年限", "value": "={{ $json.managementYears }}", "type": "string"}, {"id": "966aca88-8fa8-4c95-8330-3475a870511b", "name": "历任管理基金数", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_service_funds_num_fund }}", "type": "string"}, {"id": "e816a46d-a877-450c-aa32-3cabf3dd8fbd", "name": "获奖数", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_jlhjcs_fund }}", "type": "string"}, {"id": "bb9dd92a-de93-4e32-93c5-2ee9fa18e705", "name": "投资风格", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_fund_manager_invest_style_fund }}", "type": "string"}, {"id": "785f622e-554d-43c1-a0b7-4dad63324803", "name": "主题风格", "value": "={{ $('基金经理信息').item.json.tables[0].table.ths_theme_style_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1960, 2340], "id": "daf3265e-2960-4868-b70b-76532ccf05f0", "name": "固定基金经理信息"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "contentType": "raw", "rawContentType": "formData", "body": "={\"codes\":\"{{ $('提取输入参数2').item.json.fundcode }}\",\"indipara\":[{\"indicator\":\"ths_fs_cn_name_fund\"},{\"indicator\":\"ths_establish_date_gp_fund\"},{\"indicator\":\"ths_registered_capital_gp_fund\"},{\"indicator\":\"ths_fs_general_manager_fund\"},{\"indicator\":\"ths_funds_num_fund\"},{\"indicator\":\"ths_nav_gp_fund\",\"indiparams\":[\"104\"]},{\"indicator\":\"ths_fund_manager_num_fund\"},{\"indicator\":\"ths_fs_tel_fund\"},{\"indicator\":\"ths_fs_fax_fund\"},{\"indicator\":\"ths_web_gp_fund\"},{\"indicator\":\"ths_fs_office_address_fund\"}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 3200], "id": "026a9f35-2101-419f-9440-58ae12b91b15", "name": "基金公司信息"}, {"parameters": {"assignments": {"assignments": [{"id": "270d3d0d-d2f5-4f6c-a80d-fd8a99dae327", "name": "基金公司名称", "value": "={{ $json.tables[0].table.ths_fs_cn_name_fund }}", "type": "string"}, {"id": "eff6d105-d315-4358-a65d-a375ad00d954", "name": "成立日期", "value": "={{ $json.tables[0].table.ths_establish_date_gp_fund }}", "type": "string"}, {"id": "b73022a5-9383-4933-be18-d820cae5cb48", "name": "注册资本", "value": "={{ $json.tables[0].table.ths_registered_capital_gp_fund }}", "type": "string"}, {"id": "a3a7b749-2984-4ef7-8ff0-834bf854721d", "name": "总经理", "value": "={{ $json.tables[0].table.ths_fs_general_manager_fund }}", "type": "string"}, {"id": "c0b60e32-a2a5-4d9a-92ca-44a93a681f85", "name": "旗下基金数", "value": "={{ $json.tables[0].table.ths_funds_num_fund }}", "type": "string"}, {"id": "e2aa698c-5021-47d6-a5d0-a5bd39a9bf9e", "name": "基金资产管理规模", "value": "={{ $json.tables[0].table.ths_nav_gp_fund }}", "type": "string"}, {"id": "bca61194-e29c-4e16-8f0c-082b2bb2d9f9", "name": "基金经理人数", "value": "={{ $json.tables[0].table.ths_fund_manager_num_fund }}", "type": "string"}, {"id": "e8137825-db3d-4868-9be5-4a398a68fd4c", "name": "股东名称", "value": "-", "type": "string"}, {"id": "58409beb-7685-4927-bebb-4366e1dfc8e6", "name": "持股比例", "value": "-", "type": "string"}, {"id": "e0967aaa-eb7a-4f2e-bb76-b3f7841848c3", "name": "电话", "value": "={{ $json.tables[0].table.ths_fs_tel_fund }}", "type": "string"}, {"id": "660cb0ef-e488-4034-b501-33efa83cdbab", "name": "传真", "value": "={{ $json.tables[0].table.ths_fs_fax_fund }}", "type": "string"}, {"id": "3be76489-6fbe-47a1-a564-73045f259390", "name": "网址", "value": "={{ $json.tables[0].table.ths_web_gp_fund }}", "type": "string"}, {"id": "db1da7e5-2f7f-4263-9700-29f7822a08c7", "name": "办公地址", "value": "={{ $json.tables[0].table.ths_fs_office_address_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1920, 3180], "id": "22cef9fd-3e6c-4041-9530-c9dfb857945f", "name": "固定基金公司信息"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/data_pool", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "contentType": "raw", "rawContentType": "formData", "body": "={\"reportname\":\"p00512\",\"functionpara\":{\"thscode\":\"{{ $json.tables[0].thscode }}\",\"p0\":\"{{ $json.tables[0].table.ths_managerid_fund }}\"},\"outputpara\":\"jydm,jydm_mc,p00512_f009\"}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 2860], "id": "3d8414bc-1a56-4f97-8907-45857036dc8c", "name": "基金经理管理基金（TOP10）"}, {"parameters": {"jsCode": "// 获取输入数据（兼容n8n的输入格式）\nconst inputData = $input.all().map(item => item.json || {});\n\n// 安全解析JSON字符串的工具函数\nfunction safeJsonParse(str) {\n    try {\n        // 如果已经是对象或数组，直接返回\n        if (typeof str === 'object') {\n            return Array.isArray(str) ? str : [str];\n        }\n        \n        // 先判断是否为有效字符串\n        if (typeof str !== 'string' || str.trim() === '') {\n            return [];\n        }\n        const parsed = JSON.parse(str);\n        // 确保返回结果是数组（兼容可能的非数组格式）\n        return Array.isArray(parsed) ? parsed : [parsed];\n    } catch (error) {\n        console.error(`JSON解析失败: ${str}，错误: ${error.message}`);\n        return []; // 解析失败时返回空数组，避免中断流程\n    }\n}\n\n// 处理数据：提取管理基金规模信息\nconst processedData = inputData.map(fund => {\n    // 尝试多种可能的字段名称\n    let code = fund[\"管理基金代码\"] || fund[\"jydm\"] || \"\";\n    let name = fund[\"管理基金名称\"] || fund[\"jydm_mc\"] || \"\";\n    let sizeStr = fund[\"管理基金规模\"] || fund[\"p00407_f009\"] || \"0\";\n    \n    // 如果是数组或JSON字符串，尝试解析\n    if (typeof code === 'string' && (code.startsWith('[') || code.startsWith('{'))) {\n        const parsed = safeJsonParse(code);\n        code = parsed[0] || \"\";\n    }\n    \n    if (typeof name === 'string' && (name.startsWith('[') || name.startsWith('{'))) {\n        const parsed = safeJsonParse(name);\n        name = parsed[0] || \"\";\n    }\n    \n    if (typeof sizeStr === 'string' && (sizeStr.startsWith('[') || sizeStr.startsWith('{'))) {\n        const parsed = safeJsonParse(sizeStr);\n        sizeStr = parsed[0] || \"0\";\n    }\n    \n    // 转换规模为数字（处理特殊值）\n    let size = 0;\n    if (sizeStr !== \"--\" && sizeStr !== \"N/A\" && sizeStr !== \"\") {\n        // 移除可能的单位和逗号\n        const cleanSizeStr = String(sizeStr).replace(/,|亿元|万元|元/g, '');\n        size = parseFloat(cleanSizeStr) || 0; // 无法转换时默认为0\n    }\n    \n    return {\n        基金代码: code,\n        基金名称: name,\n        基金规模: size,\n        基金规模原始值: sizeStr\n    };\n});\n\n// 按基金规模降序排序（排除规模为0的无效数据）\nconst validFunds = processedData.filter(fund => fund.基金规模 > 0);\nconst sortedData = validFunds.sort((a, b) => b.基金规模 - a.基金规模);\n\n// 筛选前十（若不足10个则返回全部有效数据）\nconst topTenFunds = sortedData.slice(0, 10).map((fund, index) => ({\n    排名: index + 1,\n    基金代码: fund.基金代码,\n    基金名称: fund.基金名称,\n    基金规模: fund.基金规模.toFixed(2), // 保留两位小数\n    基金规模原始值: fund.基金规模原始值\n}));\n\n// 输出结果（符合n8n的输出格式）\nreturn topTenFunds.map(item => ({ json: item }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1600, 2860], "id": "19d50f93-c2b3-4b7c-b9fa-aa67e4041bdc", "name": "筛选管理基金（TOP10）"}, {"parameters": {"assignments": {"assignments": [{"id": "2d278585-837b-43bf-9816-484ae8f8691f", "name": "排名", "value": "={{ $json[\"排名\"] }}", "type": "string"}, {"id": "07645ca8-9f2f-46aa-a284-b45d2b3735db", "name": "基金代码", "value": "={{ $json[\"基金代码\"] }}", "type": "string"}, {"id": "7fb16563-c2ed-4f82-9df1-0566db70e20e", "name": "基金名称", "value": "={{ $json[\"基金名称\"] }}", "type": "string"}, {"id": "5dbf9035-e411-447b-a6fa-cce452881329", "name": "基金规模", "value": "={{ $json[\"基金规模\"] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1960, 2880], "id": "88881ee8-9f32-4b2d-becd-d122744efea9", "name": "固定管理前十基金"}, {"parameters": {"jsCode": "// 从n8n输入获取数据（兼容不同嵌套层级）\nconst inputData = $input.all()[0].json;\n\n// 尝试提取表格数据（处理可能的结构变化）\nlet tableData = null;\nif (inputData && inputData.tables && inputData.tables.length > 0) {\n    tableData = inputData.tables[0].table;\n} else if (inputData && inputData[0] && inputData[0].tables && inputData[0].tables.length > 0) {\n    // 兼容数组包裹的情况\n    tableData = inputData[0].tables[0].table;\n}\n\n// 验证核心数据是否存在\nif (!tableData || !tableData.jydm || !tableData.jydm_mc) {\n    throw new Error('未找到有效的jydm或jydm_mc数据');\n}\n\n// 提取数组并确保长度一致\nconst jydmList = Array.isArray(tableData.jydm) ? tableData.jydm : [];\nconst jydmMcList = Array.isArray(tableData.jydm_mc) ? tableData.jydm_mc : [];\nconst itemCount = Math.max(jydmList.length, jydmMcList.length);\n\n// 构建扁平化结果\nconst flattenedResult = [];\nfor (let i = 0; i < itemCount; i++) {\n    flattenedResult.push({\n        jydm: i < jydmList.length ? jydmList[i] : null,\n        jydm_mc: i < jydmMcList.length ? jydmMcList[i] : null,\n        // 可选：如果需要基金公司信息可以加上\n        // fund_company: tableData.p00512_f009 ? (i < tableData.p00512_f009.length ? tableData.p00512_f009[i] : null) : null\n    });\n}\n\n// 输出扁平化数据（n8n要求数组形式）\nreturn flattenedResult.map(item => ({ json: item }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1040, 2860], "id": "301fc871-c8e3-419b-96ae-e7e1824e17d7", "name": "数据扁平化"}, {"parameters": {"jsCode": "// 初始化结果数组\nconst results = [];\n\n// 获取访问令牌\nconst accessToken = $('If').item.json.access_token;\n\n// 循环10次\nfor (let i = 1; i <= 10; i++) {\n    // 生成当前批次的stockCode\n    const stockCode = i.toString();\n    \n    // 构建请求体\n    const requestBody = {\n        \"codes\": $('提取输入参数2').item.json.fundcode,\n        \"indipara\": [\n            {\"indicator\": \"ths_top_industry_name_fund\", \"indiparams\": [\"104\", stockCode]},\n            {\"indicator\": \"ths_top_industry_mv_to_fnv_fund\", \"indiparams\": [\"104\", stockCode]}\n        ]\n    };\n    \n    try {\n        // 使用n8n内置的httpRequest方法发送请求\n        const response = await this.helpers.httpRequest({\n            method: 'POST',\n            url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',\n            headers: {\n                'Content-Type': 'application/json',\n                'access_token': accessToken,\n                'ifindlang': 'cn'\n            },\n            body: requestBody,\n            json: true\n        });\n        \n        // 将响应添加到结果数组\n        results.push({ json: response });\n    } catch (error) {\n        // 错误处理\n        console.error(`请求 ${stockCode} 失败:`, error.message);\n        results.push({ json: { error: error.message } });\n    }\n}\n\n// 返回结果\nreturn results;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, 1440], "id": "e48f87d9-92be-4ce4-8c35-6ccf6b21bc75", "name": "行业配置循环请求"}, {"parameters": {"jsCode": "// 生成包含1到10的数组\nconst numbers = Array.from({ length: 10 }, (_, i) => i + 1);\n\n// 为每个数字创建HTTP请求并获取结果\nconst results = [];\n\nfor (const num of numbers) {\n  const code = num.toString();\n  \n  // 构建与原HTTP请求相同的参数\n  const headers = {\n    \"Content-Type\": \"application/json\",\n    \"access_token\": $('If').first().json.access_token,\n    \"ifindlang\": \"cn\"\n  };\n  \n  const body = {\n    \"codes\": $('提取输入参数2').first().json.fundcode,\n    \"indipara\": [\n      {\n        \"indicator\": \"ths_top_held_stock_code_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_held_stock_name_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_held_mv_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_stock_mv_to_fnv_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_held_num_fund\",\n        \"indiparams\": [\"104\", code]\n      }\n    ]\n  };\n  \n  // 使用this.helpers.httpRequest发送请求\n  try {\n    const response = await this.helpers.httpRequest({\n      url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',\n      method: 'POST',\n      headers: headers,\n      body: body,\n      json: true\n    });\n    \n    // 添加结果到数组\n    results.push({ json: response });\n  } catch (error) {\n    results.push({ json: { error: error.message, Code: code } });\n  }\n}\n\nreturn results;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, 1720], "id": "d6fe3cec-31e3-4219-bcbb-8f73a9725c5b", "name": "重仓股票循环请求"}, {"parameters": {"jsCode": "// 生成包含1到5的数组\nconst numbers = Array.from({ length: 5 }, (_, i) => i + 1);\n\n// 为每个数字创建HTTP请求并获取结果\nconst results = [];\n\nfor (const num of numbers) {\n  const code = num.toString();\n  \n  // 构建与原HTTP请求相同的参数\n  const headers = {\n    \"Content-Type\": \"application/json\",\n    \"access_token\": $('If').first().json.access_token,\n    \"ifindlang\": \"cn\"\n  };\n  \n  const body = {\n    \"codes\": $('提取输入参数2').first().json.fundcode,\n    \"indipara\": [\n      {\n        \"indicator\": \"ths_top_bond_code_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_bond_name_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_bond_mv_to_stock_invest_mv_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_bond_held_num_fund\",\n        \"indiparams\": [\"104\", code]\n      },\n      {\n        \"indicator\": \"ths_top_bond_held_mv_fund\",\n        \"indiparams\": [\"9\", code]\n      }\n    ]\n  };\n  \n  // 使用this.helpers.httpRequest发送请求\n  try {\n    const response = await this.helpers.httpRequest({\n      url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',\n      method: 'POST',\n      headers: headers,\n      body: body,\n      json: true\n    });\n    \n    // 添加结果到数组\n    results.push({ json: response });\n  } catch (error) {\n    results.push({ json: { error: error.message, Code: code } });\n  }\n}\n\nreturn results;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-20, 2060], "id": "85c42ec4-e354-4ce5-96dc-1d3f7ff31402", "name": "重仓债券循环请求"}, {"parameters": {"jsCode": "// 获取输入项\nconst inputItems = items;\nlet results = [];\n\n// 假设输入项中包含jydm字段\nfor (const item of inputItems) {\n  const jydm = item.json.jydm;\n  \n  if (!jydm) {\n    results.push({\n      json: {\n        error: \"缺少jydm字段\",\n        originalItem: item.json\n      }\n    });\n    continue;\n  }\n  \n  // 构建与原HTTP请求相同的参数\n  const headers = {\n    \"Content-Type\": \"application/json\",\n    \"access_token\": $('If').first().json.access_token,\n    \"ifindlang\": \"cn\"\n  };\n  \n  const body = {\n    \"reportname\": \"p00407\",\n    \"functionpara\": {\n      \"jjlb\": jydm\n    },\n    \"outputpara\": \"jydm,jydm_mc,p00407_f009\"\n  };\n  \n  // 使用this.helpers.httpRequest发送请求\n  try {\n    const response = await this.helpers.httpRequest({\n      url: 'https://quantapi.51ifind.com/api/v1/data_pool',\n      method: 'POST',\n      headers: headers,\n      body: body,\n      json: true\n    });\n    \n    // 从响应中提取所需字段\n    if (response && response.tables && response.tables[0] && response.tables[0].table) {\n      const table = response.tables[0].table;\n      \n      // 添加处理后的结果到数组\n      results.push({\n        json: {\n          // 保留原始项的所有字段\n          ...item.json,\n          // 添加新的字段\n          管理基金代码: table.jydm,\n          管理基金名称: table.jydm_mc,\n          管理基金规模: table.p00407_f009\n        }\n      });\n    } else {\n      // 如果响应中没有所需数据，返回原始项和错误信息\n      results.push({\n        json: {\n          ...item.json,\n          error: \"API响应中没有找到所需数据\",\n          apiResponse: response\n        }\n      });\n    }\n  } catch (error) {\n    // 处理请求错误\n    results.push({\n      json: {\n        ...item.json,\n        error: error.message\n      }\n    });\n  }\n}\n\nreturn results;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1320, 2860], "id": "9ccb9fed-e005-4d93-bade-9c809cf5b543", "name": "基金规模循环"}, {"parameters": {"numberInputs": 9}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [2760, 1860], "id": "bceafc5c-8456-4105-9565-f700e0d51b2a", "name": "Merge1"}, {"parameters": {"jsCode": "// 获取输入数据\nconst inputData = $input.all();\n\n// 完整的中英文字段映射表\nconst fieldMapping = {\n  // 基金基本信息\n  'Fund_type': 'fundType',\n  'Fund_Name': 'fundName', \n  'Fund_code': 'fundCode',\n  '基金代码': 'fundCode',\n  '基金名称': 'fundName',\n  '成立时间': 'establishDate',\n  '成立日期': 'establishDate',\n  '指数名称': 'indexName',\n  '基金公司': 'fundCompany',\n  '基金经理': 'fundManager',\n  '联接基金': 'linkedFund',\n  '总资产规模（亿元）': 'totalAssetScale',\n  '基金场内规模': 'onMarketScale',\n  '基金规模': 'fundScale',\n  '管理费用率': 'managementFeeRate',\n  '托管费用率': 'custodyFeeRate',\n  '持仓换手率': 'turnoverRate',\n  \n  // 净值和价格\n  '基金净值': 'fundNetValue',\n  '收盘价格': 'closingPrice',\n  '收盘价涨跌幅': 'priceChange',\n  '成交金额': 'tradingAmount',\n  '折价率': 'discountRate',\n  \n  // 收益率相关 - 根据实际输入源字段\n  '今年以来收益率': 'ytdReturn',\n  '近1个月收益率': 'oneMonthReturn',\n  '近3个月收益率': 'threeMonthReturn',\n  '近6个月收益率': 'sixMonthReturn',\n  '近1年收益率': 'oneYearReturn',\n  '近2年收益率': 'twoYearReturn',\n  '近3年收益率': 'threeYearReturn',\n  '近5年收益率': 'fiveYearReturn',\n  '近10年收益率': 'tenYearReturn',\n  '成立以来收益率': 'totalReturn',\n  \n  // 基金经理收益率数据 - 保持原有字段\n  '最近一月收益(%)': 'recentOneMonthReturn',\n  '今年以来收益(%)': 'ytdReturnPercent',\n  '最近三月收益(%)': 'recentThreeMonthReturn',\n  '最近六月收益(%)': 'recentSixMonthReturn',\n  '最近一年收益(%)': 'recentOneYearReturn',\n  '最近两年收益(%)': 'recentTwoYearReturn',\n  '最近三年收益(%)': 'recentThreeYearReturn',\n  '任职年化收益(%)': 'tenureAnnualizedReturn',\n  \n  // 行业配置\n  '行业配置': 'industryAllocation',\n  '行业占净值比': 'industryNetValueRatio',\n  '较上期': 'comparedToPrevious',\n  \n  // 重仓股票\n  '重仓股票代码': 'topStockCode',\n  '重仓股票简称': 'topStockName',\n  '持仓市值': 'holdingMarketValue',\n  '占基金净值比': 'fundNetValueRatio',\n  '持仓数量': 'holdingQuantity',\n  \n  // 重仓债券\n  '重仓债券代码': 'topBondCode',\n  '重仓债券简称': 'topBondName',\n  '重仓债券持仓市值': 'topBondHoldingMarketValue',\n  '重仓债券占基金净值比': 'topBondFundNetValueRatio', \n  '重仓债券持仓数量': 'topBondHoldingQuantity',\n  \n  // 基金经理信息\n  '姓名': 'managerName',\n  '性别': 'gender',\n  '年龄': 'age',\n  '学历': 'education',\n  '简历': 'resume',\n  '投资经理年限': 'managementYears',\n  '在任基金总规模': 'totalFundScale',\n  '在任基金管理数': 'currentFundCount',\n  '现任公司年限': 'currentCompanyYears',\n  '历任管理基金数': 'historicalFundCount',\n  '历任基金公司数': 'historicalCompanyCount',\n  '获奖数': 'awardCount',\n  '投资风格': 'investmentStyle',\n  '主题风格': 'themeStyle',\n  '经理代码': 'managerCode',\n  '经理名称': 'managerDisplayName', // 改为不同的字段名\n  '在管基金类型': 'managedFundType',\n\n  // 基金公司信息\n  '基金公司名称': 'fundCompanyName',\n  '注册资本': 'registeredCapital',\n  '总经理': 'generalManager',\n  '旗下基金数': 'totalFundsCount',\n  '基金资产管理规模': 'assetManagementScale',\n  '基金经理人数': 'fundManagerCount',\n  '股东名称': 'shareholderName',\n  '持股比例': 'shareholdingRatio',\n  '电话': 'phone',\n  '传真': 'fax',\n  '网址': 'website',\n  '办公地址': 'officeAddress',\n  \n  // 基金排名相关\n  '排名': 'ranking'\n};\n\n// 整合所有数据到一个对象\nconst result = {};\nconst industryAllocations = [];\nconst topStocks = [];\nconst topBonds = [];\nconst topFunds = [];\n\ninputData.forEach((item, index) => {\n  const json = item.json || {};\n  \n  for (const [key, value] of Object.entries(json)) {\n    let processedValue = value;\n    \n    // 处理JSON数组格式的字符串\n    if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {\n      try {\n        const parsed = JSON.parse(value);\n        if (Array.isArray(parsed)) {\n          const filteredArray = parsed.filter(item => \n            item !== null && item !== undefined && item !== \"\" && item !== \"null\"\n          );\n          processedValue = filteredArray.length > 0 ? filteredArray[0] : \"-\";\n        } else {\n          processedValue = parsed;\n        }\n      } catch (error) {\n        processedValue = value.replace(/^\\[\"|\"\\]$/g, '').replace(/^\\[\"|\\]$/g, '').replace(/^\\[|\\]$/g, '');\n      }\n    } else if (typeof value === 'string') {\n      processedValue = value.replace(/^\"|\"$/g, '');\n    }\n    \n    // 检查空值并替换为\"-\"\n    if (processedValue === null || processedValue === undefined || \n        processedValue === \"\" || processedValue === \"null\" || \n        processedValue === \"--\" || processedValue === \"N/A\") {\n      processedValue = \"-\";\n    }\n    // 数值字段处理\nif (key.includes('规模') || key.includes('金额') || key.includes('净值') || \n    key.includes('收益率') || key.includes('比例') || key.includes('占比') || \n    key.includes('费率') || key.includes('增长率') || key.includes('价格') ||\n    key.includes('收益(%)') || key.includes('市值')) {\n  if (typeof processedValue === 'string' && processedValue !== \"-\") {\n    const cleanStr = String(processedValue).replace(/,|亿元|万元|元|%/g, '');\n    const num = parseFloat(cleanStr);\n    if (!isNaN(num)) {\n      // 投资经理年限保留整数\n      if (key === '投资经理年限') {\n        processedValue = Math.round(num).toString();\n      } \n      // 占基金净值比、基金业绩、占净值比保留两位小数\n      else if (key.includes('占基金净值比') || key.includes('占净值比') || \n               key.includes('收益率') || key.includes('收益(%)')) {\n        processedValue = num.toFixed(2);\n      }\n      else {\n        processedValue = num.toFixed(2);\n      }\n    } else {\n      processedValue = \"-\";\n    }\n  }\n}\n    \n    const englishKey = fieldMapping[key] || key;\n    \n    // 特殊处理行业配置数据\n    if (key === '行业配置' || key === '行业占净值比' || key === '较上期') {\n      if (!industryAllocations[index]) {\n        industryAllocations[index] = {};\n      }\n      industryAllocations[index][englishKey] = processedValue;\n    }\n    // 特殊处理重仓股票数据\n    else if (key.includes('重仓股票') || (key === '持仓市值' && json['重仓股票代码']) || \n             (key === '持仓数量' && json['重仓股票代码']) || \n             (key === '占基金净值比' && json['重仓股票代码'])) {\n      if (!topStocks[index]) {\n        topStocks[index] = {};\n      }\n      topStocks[index][englishKey] = processedValue;\n    }\n    // 特殊处理重仓债券数据\n    else if (key.includes('重仓债券') || (key === '持仓市值' && json['重仓债券代码']) || \n             (key === '持仓数量' && json['重仓债券代码']) || \n             (key === '占基金净值比' && json['重仓债券代码'])) {\n      if (!topBonds[index]) {\n        topBonds[index] = {};\n      }\n      // 为债券相关的持仓数据添加特殊前缀\n      if (key === '持仓市值' && json['重仓债券代码']) {\n        topBonds[index]['holdingMarketValue'] = processedValue;\n      } else if (key === '持仓数量' && json['重仓债券代码']) {\n        topBonds[index]['holdingQuantity'] = processedValue;\n      } else if (key === '占基金净值比' && json['重仓债券代码']) {\n        topBonds[index]['fundNetValueRatio'] = processedValue;\n      } else {\n        topBonds[index][englishKey] = processedValue;\n      }\n    }\n    // 特殊处理基金排名数据\n    else if (key === '排名' || (key === '基金名称' && json['排名']) || (key === '基金规模' && json['排名'])) {\n      if (!topFunds[index]) {\n        topFunds[index] = {};\n      }\n      topFunds[index][englishKey] = processedValue;\n    }\n    // 特殊处理基金经理收益率数据\n    else if (key === '最近一月收益(%)' || key === '今年以来收益(%)' || \n             key === '最近三月收益(%)' || key === '最近六月收益(%)' || \n             key === '最近一年收益(%)' || key === '最近两年收益(%)' || \n             key === '最近三年收益(%)' || key === '任职年化收益(%)' ||\n             key === '经理代码' || key === '经理名称' || key === '在管基金类型') {\n      // 直接将这些值添加到主结果对象中，保持原值\n      if (processedValue === null || processedValue === \"null\") {\n        processedValue = \"-\";\n      }\n      result[englishKey] = processedValue;\n    }\n    // 其他数据直接合并\n    else {\n      // 优先保留非空值，避免空值覆盖有效数据\n      if (result[englishKey] === undefined || result[englishKey] === \"-\" || \n          (processedValue !== \"-\" && processedValue !== \"\" && processedValue !== null)) {\n        result[englishKey] = processedValue;\n      }\n    }\n  }\n});\n\n// 过滤并添加数组数据\nresult.industryAllocations = industryAllocations.filter(item => item && Object.keys(item).length > 0);\nresult.topStocks = topStocks.filter(item => item && Object.keys(item).length > 0);\nresult.topBonds = topBonds.filter(item => item && Object.keys(item).length > 0);\nresult.topFunds = topFunds.filter(item => item && Object.keys(item).length > 0);\n\n// 确保基金经理收益率字段存在，即使值为\"-\"\nconst managerReturnFields = [\n  'recentOneMonthReturn',\n  'ytdReturnPercent', \n  'recentThreeMonthReturn',\n  'recentSixMonthReturn',\n  'recentOneYearReturn',\n  'recentTwoYearReturn',\n  'recentThreeYearReturn',\n  'tenureAnnualizedReturn'\n];\n\nmanagerReturnFields.forEach(field => {\n  if (result[field] === undefined) {\n    result[field] = \"-\";\n  }\n});\n\n// 最终数据格式化处理\nObject.keys(result).forEach(key => {\n  let value = result[key];\n  \n  // 跳过数组字段\n  if (Array.isArray(value)) return;\n  \n  // 处理投资经理年限 - 取整\n  if (key === 'managementYears' && value !== \"-\" && value !== null && value !== undefined) {\n    const num = parseFloat(value);\n    if (!isNaN(num)) {\n      result[key] = Math.round(num).toString();\n    }\n  }\n  \n  // 处理年龄 - 取整\n  else if (key === 'age' && value !== \"-\" && value !== null && value !== undefined) {\n    const num = parseFloat(value);\n    if (!isNaN(num)) {\n      result[key] = Math.round(num).toString();\n    }\n  }\n  \n  // 处理所有占比字段 - 保留两位小数\n  else if ((key.includes('Ratio') || key.includes('Rate') || key.includes('Return') || \n            key === 'discountRate' || key === 'priceChange') && \n           value !== \"-\" && value !== null && value !== undefined) {\n    const num = parseFloat(value);\n    if (!isNaN(num)) {\n      result[key] = num.toFixed(2);\n    }\n  }\n});\n\n// 处理数组中的占比字段\n['industryAllocations', 'topStocks', 'topBonds'].forEach(arrayKey => {\n  if (result[arrayKey] && Array.isArray(result[arrayKey])) {\n    result[arrayKey].forEach(item => {\n      Object.keys(item).forEach(key => {\n        let value = item[key];\n        if ((key.includes('Ratio') || key.includes('Rate')) && \n            value !== \"-\" && value !== null && value !== undefined) {\n          const num = parseFloat(value);\n          if (!isNaN(num)) {\n            item[key] = num.toFixed(2);\n          }\n        }\n      });\n    });\n  }\n});\n\nreturn [{ json: result }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3080, 2000], "id": "e5325830-a0d7-4249-a7e0-7eebca5ef628", "name": "数据标准化"}, {"parameters": {"html": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <title>基金数据分析</title>\n</head>\n<body>\n  <div class=\"container\">\n    <!-- 上半部分：基金基本信息 -->\n    <div class=\"fund-info-section\">\n      <div class=\"fund-header\">\n        <div class=\"fund-type\">基金类型：{{ $json.fundType || '-' }}</div>\n        <div class=\"fund-date\">成立日期：\n          {{ $json.establishDate ? \n          $json.establishDate.toString().replace(/(\\d{4})(\\d{2})(\\d{2})/, '$1年$2月$3日') : '-' }}</div>\n        <div class=\"fund-index\">指数名称：{{ $json.indexName || '-' }}</div>\n        <div class=\"fund-company\">基金公司：<span class=\"value\">{{ $json.fundCompany || '-' }}</span></div>\n        <div class=\"fund-manager\">基金经理：<span class=\"value\">{{ $json.fundManager || '-' }}</span></div>\n        <div class=\"fund-connection\">联接基金：{{ $json.linkedFund || '-' }}</div>\n      </div>\n\n      <div class=\"fund-metrics\">\n        <div class=\"metrics-row\">\n          <div class=\"metric\">总资产规模：<span class=\"value\">{{ $json.totalAssetScale && $json.totalAssetScale !== '-' ? $json.totalAssetScale + '亿元' : '-' }}</span></div>\n          <div class=\"metric\">基金净值：<span class=\"value\">{{ $json.fundNetValue || '-' }}</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">基金场内规模：<span class=\"value\">{{ $json.onMarketScale && $json.onMarketScale !== '-' ? $json.onMarketScale + '亿元' : '-' }}</span></div>\n          <div class=\"metric\">收盘价格：<span class=\"value\">{{ $json.closingPrice || '-' }}</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">管理费用率：<span class=\"value\">{{ $json.managementFeeRate && $json.managementFeeRate !== '-' ? $json.managementFeeRate + '%' : '-' }}</span></div>\n          <div class=\"metric\">收盘价涨跌幅：<span class=\"value\" style=\"color: {{ parseFloat($json.priceChange || 0) >= 0 ? 'red' : 'green' }}\">{{ $json.priceChange && $json.priceChange !== '-' ? $json.priceChange : '-' }}</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">托管费用率：<span class=\"value\">{{ $json.custodyFeeRate && $json.custodyFeeRate !== '-' ? $json.custodyFeeRate + '%' : '-' }}</span></div>\n          <div class=\"metric\">成交金额：<span class=\"value\">{{ $json.tradingAmount && $json.tradingAmount !== '-' ? $json.tradingAmount + '亿元' : '-' }}</span></div>\n        </div>\n        <div class=\"metrics-row\">\n          <div class=\"metric\">持仓换手率：<span class=\"value\">{{ $json.turnoverRate && $json.turnoverRate !== '-' ? $json.turnoverRate + '%' : '-' }}</span></div>\n          <div class=\"metric\">折价率：<span class=\"value positive\">{{ $json.discountRate && $json.discountRate !== '-' ? $json.discountRate + '%' : '-' }}</span></div>\n        </div>\n      </div>\n\n      <!-- 收益表现和行业配置 -->\n      <div class=\"performance-industry\">\n        <div class=\"performance-section\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>基金业绩</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来：</td>\n              <td style=\"color: {{ parseFloat($json.ytdReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.ytdReturn ? (parseFloat($json.ytdReturn) >= 0 ? '+' + $json.ytdReturn : $json.ytdReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近一个月：</td>\n              <td style=\"color: {{ parseFloat($json.oneMonthReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.oneMonthReturn ? (parseFloat($json.oneMonthReturn) >= 0 ? '+' + $json.oneMonthReturn : $json.oneMonthReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近三个月：</td>\n              <td style=\"color: {{ parseFloat($json.threeMonthReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.threeMonthReturn ? (parseFloat($json.threeMonthReturn) >= 0 ? '+' + $json.threeMonthReturn : $json.threeMonthReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近六个月：</td>\n              <td style=\"color: {{ parseFloat($json.sixMonthReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.sixMonthReturn ? (parseFloat($json.sixMonthReturn) >= 0 ? '+' + $json.sixMonthReturn : $json.sixMonthReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近1年：</td>\n              <td style=\"color: {{ parseFloat($json.oneYearReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.oneYearReturn ? (parseFloat($json.oneYearReturn) >= 0 ? '+' + $json.oneYearReturn : $json.oneYearReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近2年：</td>\n              <td style=\"color: {{ parseFloat($json.twoYearReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.twoYearReturn ? (parseFloat($json.twoYearReturn) >= 0 ? '+' + $json.twoYearReturn : $json.twoYearReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近3年：</td>\n              <td style=\"color: {{ parseFloat($json.threeYearReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.threeYearReturn ? (parseFloat($json.threeYearReturn) >= 0 ? '+' + $json.threeYearReturn : $json.threeYearReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近5年：</td>\n              <td style=\"color: {{ parseFloat($json.fiveYearReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.fiveYearReturn ? (parseFloat($json.fiveYearReturn) >= 0 ? '+' + $json.fiveYearReturn : $json.fiveYearReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近10年：</td>\n              <td style=\"color: {{ parseFloat($json.tenYearReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.tenYearReturn ? (parseFloat($json.tenYearReturn) >= 0 ? '+' + $json.tenYearReturn : $json.tenYearReturn) + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>成立以来：</td>\n              <td style=\"color: {{ parseFloat($json.totalReturn || 0) >= 0 ? 'red' : 'green' }}\">\n                {{ $json.totalReturn ? (parseFloat($json.totalReturn) >= 0 ? '+' + $json.totalReturn : $json.totalReturn) + '%' : '-' }}\n              </td>\n            </tr>\n          </table>\n        </div>\n\n        <div class=\"industry-section\">\n          <h3>行业配置</h3>\n          <table class=\"industry-table\">\n            <tr>\n              <th>行业名称</th>\n              <th>占净值比</th>\n            </tr>\n            {{ ($json.industryAllocations || [])\n                .filter(industry => industry && industry.industryAllocation && industry.industryAllocation !== '-')\n                .slice(0, 10)\n                .map((industry, index) => `\n            <tr>\n              <td>${industry.industryAllocation}</td>\n              <td>${industry.industryNetValueRatio ? industry.industryNetValueRatio + '%' : '-'}</td>\n            </tr>\n            `).join('') }}\n          </table>\n        </div>\n      </div>\n\n      <!-- 重仓股票和重仓债券 -->\n      <div class=\"holdings-section\">\n        <!-- 重仓股票 -->\n        <h3>重仓股票（TOP10）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>证券代码</th>\n            <th>证券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          {{ ($json.topStocks || [])\n              .filter(stock => stock && stock.topStockCode && stock.topStockName && stock.topStockCode !== '-' && stock.topStockName !== '-')\n              .slice(0, 10)\n              .map(stock => `\n          <tr>\n            <td>${stock.topStockCode}</td>\n            <td>${stock.topStockName}</td>\n            <td>${stock.holdingMarketValue || '-'}</td>\n            <td>${stock.fundNetValueRatio ? stock.fundNetValueRatio + '%' : '-'}</td>\n            <td>${stock.holdingQuantity || '-'}</td>\n          </tr>\n          `).join('') }}\n        </table>\n\n        <!-- 重仓债券 -->\n        <h3>重仓债券（TOP5）</h3>\n        <table class=\"holdings-table\">\n          <tr>\n            <th>债券代码</th>\n            <th>债券简称</th>\n            <th>持仓市值(元)</th>\n            <th>占基金净值比</th>\n            <th>持仓数量</th>\n          </tr>\n          {{ ($json.topBonds || [])\n              .filter(bond => bond && bond.topBondCode && bond.topBondName && bond.topBondCode !== '-' && bond.topBondName !== '-')\n              .slice(0, 5)\n              .map(bond => `\n          <tr>\n            <td>${bond.topBondCode}</td>\n            <td>${bond.topBondName}</td>\n            <td>${bond.holdingMarketValue || '-'}</td>\n            <td>${bond.fundNetValueRatio ? bond.fundNetValueRatio + '%' : '-'}</td>\n            <td>${bond.holdingQuantity || '-'}</td>\n          </tr>\n          `).join('') }}\n        </table>\n      </div>\n    </div>\n\n    <!-- 下半部分：基金经理信息 -->\n    <div class=\"manager-section\">\n      <h3 class=\"section-title\">基金经理-现任</h3>\n      \n      <div class=\"manager-profile\">\n        <div class=\"manager-name-section\">\n          <div class=\"manager-name\">\n            <span>{{ $json.managerName || '-' }}</span>\n            <span class=\"gender\">{{ $json.gender || '-' }}</span>\n          \n            <span class=\"education\">{{ $json.education || '-' }}</span>\n          </div>\n          \n          <div class=\"manager-content\">\n            <div class=\"manager-stats\">\n              <div class=\"stats-grid\">\n                <div class=\"stat-label\">投资经理年限</div>\n                <div class=\"stat-value\">{{ $json.managementYears ? $json.managementYears + ' 年' : '-' }}</div>\n\n                <div class=\"stat-label\">历任管理基金数</div>\n                <div class=\"stat-value\">{{ $json.historicalFundCount ? $json.historicalFundCount + ' 只' : '-' }}</div>\n\n                <div class=\"stat-label\">在任基金总规模</div>\n                <div class=\"stat-value\">{{ $json.totalFundScale ? $json.totalFundScale + ' 元' : '-' }}</div>\n\n                <div class=\"stat-label\">现任公司年限</div>\n                <div class=\"stat-value\">{{ $json.currentCompanyYears ? $json.currentCompanyYears + ' 年' : '-' }}</div>\n\n                <div class=\"stat-label\">在任管理基金数</div>\n                <div class=\"stat-value\">{{ $json.currentFundCount ? $json.currentFundCount + ' 只' : '-' }}</div>\n\n                <div class=\"stat-label\">获奖数</div>\n                <div class=\"stat-value\">{{ $json.awardCount ? $json.awardCount + ' 个' : '-' }}</div>\n              </div>\n            </div>\n            \n            <div class=\"manager-bio-section\">\n              <h4 class=\"bio-title\">基金经理简介:</h4>\n              <div class=\"manager-bio\">\n                <p>{{ $json.resume || '-' }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 收益率表现和前十大管理基金 -->\n      <div class=\"performance-funds\">\n        <div class=\"manager-performance\">\n          <h3>收益率表现</h3>\n          <table class=\"performance-table\">\n            <tr>\n              <th>任职间收益率</th>\n              <th></th>\n            </tr>\n            <tr>\n              <td>今年以来</td>\n              <td class=\"{{ $json.ytdReturnPercent && parseFloat($json.ytdReturnPercent) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.ytdReturnPercent ? $json.ytdReturnPercent + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近1个月</td>\n              <td class=\"{{ $json.recentOneMonthReturn && parseFloat($json.recentOneMonthReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentOneMonthReturn ? $json.recentOneMonthReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近3个月</td>\n              <td class=\"{{ $json.recentThreeMonthReturn && parseFloat($json.recentThreeMonthReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentThreeMonthReturn ? $json.recentThreeMonthReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近6个月</td>\n              <td class=\"{{ $json.recentSixMonthReturn && parseFloat($json.recentSixMonthReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentSixMonthReturn ? $json.recentSixMonthReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近1年</td>\n              <td class=\"{{ $json.recentOneYearReturn && parseFloat($json.recentOneYearReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentOneYearReturn ? $json.recentOneYearReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近2年</td>\n              <td class=\"{{ $json.recentTwoYearReturn && parseFloat($json.recentTwoYearReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentTwoYearReturn ? $json.recentTwoYearReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>近3年</td>\n              <td class=\"{{ $json.recentThreeYearReturn && parseFloat($json.recentThreeYearReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.recentThreeYearReturn ? $json.recentThreeYearReturn + '%' : '-' }}\n              </td>\n            </tr>\n            <tr>\n              <td>管理基金以来年化收益率</td>\n              <td class=\"{{ $json.tenureAnnualizedReturn && parseFloat($json.tenureAnnualizedReturn) >= 0 ? 'positive' : 'negative' }}\">\n                {{ $json.tenureAnnualizedReturn ? $json.tenureAnnualizedReturn + '%' : '-' }}\n              </td>\n            </tr>\n          </table>\n          <div class=\"calculation-note\">\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\n          </div>\n        </div>\n        \n        <div class=\"top-funds\">\n          <h3>前十大管理基金</h3>\n          <table class=\"funds-table\">\n            <tr>\n              <th>基金名称</th>\n              <th>总资产规模</th>\n            </tr>\n            {{ ($json.topFunds || [])\n                .filter(fund => fund && fund.fundName && fund.fundName !== '-' && fund.fundName.trim() !== '')\n                .slice(0, 10)\n                .map(fund => `\n            <tr>\n              <td><a href=\"#\" class=\"fund-link\">${fund.fundName}</a></td>\n              <td>${fund && fund.fundScale ? fund.fundScale + '亿元' : '-'}</td>\n            </tr>\n            `).join('') }}\n          </table>\n        </div>\n      </div>\n      \n      <!-- 基金公司信息 -->\n      <div class=\"company-section\">\n        <h3>{{ $json.fundCompanyName || '基金公司' }}</h3>\n        <div class=\"company-info-grid\">\n          <!-- 基本信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">基本资料</h4>\n            <div class=\"company-content company-basic-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">成立日期</td>\n                  <td class=\"info-value text-overflow\">{{ $json.establishDate ? $json.establishDate.toString().replace(/(\\d{4})(\\d{2})(\\d{2})/, '$1-$2-$3') : '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">注册资本(万)</td>\n                  <td class=\"info-value text-overflow\">{{ $json.registeredCapital || '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">总经理</td>\n                  <td class=\"info-value text-overflow\">{{ $json.generalManager || '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">管理资产规模</td>\n                  <td class=\"info-value text-overflow\">{{ $json.assetManagementScale ? (parseFloat($json.assetManagementScale) / 100000000).toFixed(2) + '亿元' : '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">旗下基金数量</td>\n                  <td class=\"info-value text-overflow\">{{ $json.totalFundsCount || '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">基金经理人数</td>\n                  <td class=\"info-value text-overflow\">{{ $json.fundManagerCount || '-' }}</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          <!-- 股东信息 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">股东列表</h4>\n            <div class=\"company-content shareholders-content\">\n              <table class=\"inner-table shareholders-table\">\n                <tr>\n                  <th class=\"shareholder-name\">股东名称</th>\n                  <th class=\"shareholder-ratio\">持股比例</th>\n                </tr>\n                {{ ($json.shareholders || [\n                  {name: '开发中', ratio: '-'},                                   \n                ]).map(shareholder => `\n                <tr>\n                  <td class=\"shareholder-name text-overflow\">${shareholder.name}</td>\n                  <td class=\"shareholder-ratio text-overflow\">${shareholder.ratio}</td>\n                </tr>\n                `).join('') }}\n              </table>\n            </div>\n          </div>\n\n          <!-- 联系方式 -->\n          <div class=\"company-info-block\">\n            <h4 class=\"section-subtitle\">联系方式</h4>\n            <div class=\"company-content company-contact-info\">\n              <table class=\"inner-table compact-table\">\n                <tr>\n                  <td class=\"info-label\">电话</td>\n                  <td class=\"info-value text-overflow\">{{ $json.phone || '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">传真</td>\n                  <td class=\"info-value text-overflow\">{{ $json.fax || '-' }}</td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">网址</td>\n                  <td class=\"info-value text-overflow\"><a href=\"{{ $json.website ? 'http://' + $json.website.replace(/^https?:\\/\\//, '') : '#' }}\" target=\"_blank\" class=\"website-link\">{{ $json.website || '-' }}</a></td>\n                </tr>\n                <tr>\n                  <td class=\"info-label\">办公地址</td>\n                  <td class=\"info-value text-overflow\">{{ $json.officeAddress || '-' }}</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <style>\n    /* 全局样式 */\n    body {\n      font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\n      margin: 0;\n      padding: 0;\n      color: #333;\n      line-height: 1.5;\n    }\n    \n    .container {\n      margin: 0 auto;\n      padding: 20px;\n    }\n    \n    h3 {\n      color: #1e293b;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 16px 0;\n      padding-left: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n    \n    /* 基金信息部分 */\n    .fund-header {\n      margin-bottom: 16px;\n    }\n\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .fund-index {\n      font-weight: normal;\n    }\n\n    .fund-connection {\n      color: #555;\n      margin-bottom: 16px;\n    }\n    \n    .fund-metrics {\n      margin-bottom: 20px;\n    }\n    \n    .metrics-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n    }\n    \n    .metric {\n      flex: 1;\n      font-size: 14px;\n    }\n    \n    .value {\n      font-weight: 600;\n    }\n    \n    .positive {\n      color: #dc2626;\n    }\n    \n    .negative {\n      color: #16a34a;\n    }\n    \n    /* 表格样式 */\n    table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-bottom: 20px;\n      font-size: 14px;\n    }\n    \n    th, td {\n      padding: 10px;\n      text-align: left;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    th {\n      font-weight: 600;\n    }\n    \n    /* 布局 */\n    .performance-industry {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .performance-section, .industry-section {\n      flex: 1;\n    }\n    \n    /* 标签页导航样式 */\n    .tab-navigation {\n      display: flex;\n      margin-bottom: 16px;\n      border-bottom: 2px solid #e2e8f0;\n    }\n\n    .tab-button {\n      padding: 12px 24px;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 14px;\n      font-weight: 500;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n    }\n\n    .tab-button:hover {\n      color: #0ea5e9;\n      background-color: #f8fafc;\n    }\n\n    .tab-button.active {\n      color: #0ea5e9;\n      border-bottom-color: #0ea5e9;\n      font-weight: 600;\n    }\n\n    /* 标签内容样式 */\n    .tab-content {\n      display: none;\n    }\n\n    .tab-content.active {\n      display: block;\n    }\n\n    /* 持仓表格样式优化 */\n    .holdings-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin-top: 0;\n    }\n\n    .holdings-table th {\n      background-color: #f8fafc;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    /* 基金经理部分 */\n    .manager-section {\n      margin-top: 30px;\n      border-top: 1px solid #e2e8f0;\n      padding-top: 20px;\n    }\n    \n    .section-title {\n      padding: 10px;\n      border-radius: 4px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-profile {\n      margin-bottom: 20px;\n    }\n    \n    .manager-name-section {\n      width: 100%;\n    }\n    \n    .manager-name {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n    \n    .manager-content {\n      display: flex;\n      gap: 20px;\n      align-items: flex-start;\n    }\n    \n    .manager-stats {\n      flex: 1;\n      min-width: 300px;\n    }\n    \n    .manager-bio-section {\n      flex: 1.5;\n    }\n    \n    .bio-title {\n      margin: 0 0 8px 0;\n      font-size: 15px;\n      font-weight: 600;\n      color: #1e293b;\n    }\n    \n    .manager-bio {\n      padding: 8px 12px;\n      border-radius: 4px;\n      background-color: #f8fafc;\n      max-height: 160px;\n      overflow-y: auto;\n    }\n    \n    .manager-bio p {\n      margin: 0;\n      font-size: 13px;\n      line-height: 1.4;\n      color: #475569;\n      text-align: justify;\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: 150px auto;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .stat-label {\n      color: #64748b;\n    }\n    \n    .stat-value {\n      font-weight: 600;\n    }\n    \n    .performance-funds {\n      display: flex;\n      gap: 20px;\n      margin-bottom: 20px;\n    }\n    \n    .manager-performance, .top-funds {\n      flex: 1;\n    }\n    \n    .calculation-note {\n      font-size: 12px;\n      color: #64748b;\n      margin-top: 10px;\n      padding: 8px;\n      background-color: #f1f5f9;\n      border-radius: 4px;\n    }\n    \n    .fund-link {\n      color: #0ea5e9;\n      text-decoration: none;\n    }\n    \n    .fund-link:hover {\n      text-decoration: underline;\n    }\n    \n    /* 基金公司信息样式 */\n    .company-section {\n      margin-top: 20px;\n    }\n    \n    .company-info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1.2fr 1fr;\n      gap: 15px;\n      margin-top: 15px;\n      align-items: start;\n    }\n    \n    .company-info-block {\n      display: flex;\n      flex-direction: column;\n      min-height: 280px;\n      height: auto;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      background-color: #ffffff;\n    }\n    \n    .section-subtitle {\n      color: #1e293b;\n      font-size: 16px;\n      font-weight: 600;\n      margin: 0;\n      padding: 12px 15px;\n      background-color: #f8fafc;\n      border-bottom: 1px solid #e2e8f0;\n      border-radius: 6px 6px 0 0;\n    }\n    \n    .company-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: 0;\n    }\n    \n    .shareholders-content {\n      max-height: 230px;\n    }\n    \n    .inner-table {\n      width: 100%;\n      border-collapse: collapse;\n      margin: 0;\n    }\n    \n    .inner-table td {\n      padding: 10px 15px;\n      border-bottom: 1px solid #f1f5f9;\n      border-left: none;\n      border-right: none;\n      font-size: 13px;\n    }\n    \n    .inner-table tr:last-child td {\n      border-bottom: none;\n    }\n    \n    .info-label {\n      color: #64748b;\n      width: 45%;\n      font-weight: 500;\n    }\n    \n    .info-value {\n      font-weight: 500;\n      color: #1e293b;\n    }\n    /* 基金公司和基金经理的value显示为蓝色 */\n    .fund-company .value,\n    .fund-manager .value {\n      color: #0ea5e9;\n    }\n    .shareholders-table {\n      width: 100%;\n    }\n\n    .shareholder-name {\n      text-align: left;\n      color: #475569;\n      width: 70%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholder-ratio {\n      text-align: right;\n      font-weight: 600;\n      color: #1e293b;\n      width: 30%;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .shareholders-table th {\n      background-color: #ffffff;\n      border-bottom: 1px solid #e2e8f0;\n      padding: 10px 15px;\n      font-size: 14px;\n      color: #1e293b;\n      font-weight: 600;\n      position: sticky;\n      top: 0;\n      z-index: 1;\n      box-shadow: none;\n      text-shadow: none;\n    }\n\n    .website-link {\n      color: #0ea5e9;\n      text-decoration: none;\n      word-break: break-all;\n      line-height: 1.4;\n    }\n\n    .website-link:hover {\n      text-decoration: underline;\n    }\n\n    /* 滚动条样式 */\n    .company-content::-webkit-scrollbar {\n      width: 6px;\n    }\n\n    .company-content::-webkit-scrollbar-track {\n      background: #f1f5f9;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb {\n      background: #cbd5e1;\n      border-radius: 3px;\n    }\n\n    .company-content::-webkit-scrollbar-thumb:hover {\n      background: #94a3b8;\n    }\n    /* 移除重仓债券表格底部边框 */\n    .holdings-section .holdings-table:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n    }\n    \n    .holdings-section .holdings-table:last-child tr:last-child td {\n      border-bottom: none;\n    }\n    /* 基本资料和联系方式取消滚动 */\n    .company-basic-info,\n    .company-contact-info {\n      overflow-y: visible !important;\n    }\n    \n    /* 紧凑表格样式 */\n    .compact-table {\n      table-layout: fixed;\n      width: 100%;\n    }\n    \n    .compact-table td {\n      padding: 6px 15px !important;\n      word-wrap: break-word;\n      word-break: break-all;\n      white-space: normal;\n      vertical-align: top;\n    }\n    \n    .compact-table .info-label {\n      width: 35% !important;\n      font-weight: 500;\n      color: #64748b;\n    }\n    \n    .compact-table .info-value {\n      width: 65% !important;\n      font-weight: 500;\n      color: #1e293b;\n      line-height: 1.4;\n    }\n    \n    /* 移除text-overflow类的截断效果 */\n    .text-overflow {\n      overflow: visible !important;\n      text-overflow: unset !important;\n      white-space: normal !important;\n    }\n  </style>\n\n  <script>\n    function switchTab(tabName) {\n      // 隐藏所有标签内容\n      const allTabs = document.querySelectorAll('.tab-content');\n      allTabs.forEach(tab => {\n        tab.classList.remove('active');\n      });\n      \n      // 移除所有按钮的active状态\n      const allButtons = document.querySelectorAll('.tab-button');\n      allButtons.forEach(button => {\n        button.classList.remove('active');\n      });\n      \n      // 显示选中的标签内容\n      const selectedTab = document.getElementById(tabName + '-tab');\n      if (selectedTab) {\n        selectedTab.classList.add('active');\n      }\n      \n      // 激活对应的按钮\n      event.target.classList.add('active');\n    }\n  </script>\n</body>\n</html>\n"}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [3360, 2000], "id": "efcca605-a11c-472c-a01f-fc4d93a95b4d", "name": "HTML1"}, {"parameters": {"jsCode": "const list = [\n  {\n    \"Fund_type\": \"[\\\"灵活配置型基金\\\"]\",\n    \"Fund_Name\": \"[\\\"东方新思路灵活配置混合A\\\"]\",\n    \"Fund_code\": \"[\\\"001384.OF\\\"]\",\n    \"成立时间\": \"[\\\"2015/06/25\\\"]\",\n    \"指数名称\": \"[\\\"沪深300,中债总指数(总值)全价指数\\\"]\",\n    \"基金公司\": \"[\\\"东方基金管理股份有限公司\\\"]\",\n    \"基金经理\": \"[\\\"曲华锋\\\"]\",\n    \"联接基金\": \"[\\\"001385.OF\\\"]\",\n    \"总资产规模（亿元）\": \"[\\\"0.61023332940000000000\\\"]\",\n    \"基金场内规模\": \"\",\n    \"管理费用率\": \"[\\\"1.0000\\\"]\",\n    \"托管费用率\": \"[\\\"0.2000\\\"]\",\n    \"持仓换手率\": \"[\\\"\\\"]\",\n    \"基金净值\": \"[\\\"1.0986\\\"]\",\n    \"收盘价格\": \"[\\\"--\\\"]\",\n    \"收盘价涨跌幅\": \"[-0.29043383554183]\",\n    \"成交金额\": \"[null]\",\n    \"折价率\": \"[null]\"\n  },\n  {\n    \"今年以来收益率\": \"[-3.0789589766211]\",\n    \"近1个月收益率\": \"[2.5483057966956]\",\n    \"近3个月收益率\": \"[-3.7160385626643]\",\n    \"近6个月收益率\": \"[-0.93778178539224]\",\n    \"近1年收益率\": \"[3.7785754770452]\",\n    \"近2年收益率\": \"[-21.634924031671]\",\n    \"近3年收益率\": \"[-27.379693283977]\",\n    \"近5年收益率\": \"[-8.7617307532597]\",\n    \"近10年收益率\": \"[20.262725779967]\",\n    \"成立以来收益率\": \"[9.86]\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"制造业\\\"]\",\n    \"行业占净值比\": \"[49.631101733445]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"房地产业\\\"]\",\n    \"行业占净值比\": \"[33.952013828346]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"信息传输、软件和信息技术服务业\\\"]\",\n    \"行业占净值比\": \"[2.5565262265231]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"租赁和商务服务业\\\"]\",\n    \"行业占净值比\": \"[2.3255418393899]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"农、林、牧、渔业\\\"]\",\n    \"行业占净值比\": \"[1.2500067707579]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"水利、环境和公共设施管理业\\\"]\",\n    \"行业占净值比\": \"[0.099677612032017]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"科学研究和技术服务业\\\"]\",\n    \"行业占净值比\": \"[0.0090010175383426]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"\\\"]\",\n    \"行业占净值比\": \"[null]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"\\\"]\",\n    \"行业占净值比\": \"[null]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"基金代码\": \"001384.OF\",\n    \"行业配置\": \"[\\\"\\\"]\",\n    \"行业占净值比\": \"[null]\",\n    \"较上期\": \"\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"600383\\\"]\",\n    \"重仓股票简称\": \"[\\\"金地集团\\\"]\",\n    \"持仓市值\": \"[8588000]\",\n    \"占基金净值比\": \"[7.703665695923]\",\n    \"持仓数量\": \"[1900000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"603833\\\"]\",\n    \"重仓股票简称\": \"[\\\"欧派家居\\\"]\",\n    \"持仓市值\": \"[7497600]\",\n    \"占基金净值比\": \"[6.7255477319227]\",\n    \"持仓数量\": \"[120000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"600048\\\"]\",\n    \"重仓股票简称\": \"[\\\"保利发展\\\"]\",\n    \"持仓市值\": \"[7434000]\",\n    \"占基金净值比\": \"[6.6684968308677]\",\n    \"持仓数量\": \"[900000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"001979\\\"]\",\n    \"重仓股票简称\": \"[\\\"招商蛇口\\\"]\",\n    \"持仓市值\": \"[7336000]\",\n    \"占基金净值比\": \"[6.5805882097451]\",\n    \"持仓数量\": \"[800000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"000002\\\"]\",\n    \"重仓股票简称\": \"[\\\"万科A\\\"]\",\n    \"持仓市值\": \"[7050000]\",\n    \"占基金净值比\": \"[6.3240385603466]\",\n    \"持仓数量\": \"[1000000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"600519\\\"]\",\n    \"重仓股票简称\": \"[\\\"贵州茅台\\\"]\",\n    \"持仓市值\": \"[7024500]\",\n    \"占基金净值比\": \"[6.3011643783199]\",\n    \"持仓数量\": \"[4500]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"002244\\\"]\",\n    \"重仓股票简称\": \"[\\\"滨江集团\\\"]\",\n    \"持仓市值\": \"[6264000]\",\n    \"占基金净值比\": \"[5.618975537874]\",\n    \"持仓数量\": \"[600000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"002821\\\"]\",\n    \"重仓股票简称\": \"[\\\"凯莱英\\\"]\",\n    \"持仓市值\": \"[4725000]\",\n    \"占基金净值比\": \"[4.2384513755515]\",\n    \"持仓数量\": \"[60000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"000858\\\"]\",\n    \"重仓股票简称\": \"[\\\"五粮液\\\"]\",\n    \"持仓市值\": \"[4597250]\",\n    \"占基金净值比\": \"[4.123856208731]\",\n    \"持仓数量\": \"[35000]\"\n  },\n  {\n    \"重仓股票代码\": \"[\\\"000568\\\"]\",\n    \"重仓股票简称\": \"[\\\"泸州老窖\\\"]\",\n    \"持仓市值\": \"[3891600]\",\n    \"占基金净值比\": \"[3.4908692853113]\",\n    \"持仓数量\": \"[30000]\"\n  },\n  {\n    \"重仓债券代码\": \"[\\\"019723\\\"]\",\n    \"重仓债券简称\": \"[\\\"23国债20\\\"]\",\n    \"持仓市值\": \"[7103141.64]\",\n    \"占基金净值比\": \"[6.3717080327608]\",\n    \"持仓数量\": \"[70000]\"\n  },\n  {\n    \"重仓债券代码\": \"[\\\"\\\"]\",\n    \"重仓债券简称\": \"[\\\"\\\"]\",\n    \"持仓市值\": \"[null]\",\n    \"占基金净值比\": \"[null]\",\n    \"持仓数量\": \"[null]\"\n  },\n  {\n    \"重仓债券代码\": \"[\\\"\\\"]\",\n    \"重仓债券简称\": \"[\\\"\\\"]\",\n    \"持仓市值\": \"[null]\",\n    \"占基金净值比\": \"[null]\",\n    \"持仓数量\": \"[null]\"\n  },\n  {\n    \"重仓债券代码\": \"[\\\"\\\"]\",\n    \"重仓债券简称\": \"[\\\"\\\"]\",\n    \"持仓市值\": \"[null]\",\n    \"占基金净值比\": \"[null]\",\n    \"持仓数量\": \"[null]\"\n  },\n  {\n    \"重仓债券代码\": \"[\\\"\\\"]\",\n    \"重仓债券简称\": \"[\\\"\\\"]\",\n    \"持仓市值\": \"[null]\",\n    \"占基金净值比\": \"[null]\",\n    \"持仓数量\": \"[null]\"\n  },\n  {\n    \"姓名\": \"[\\\"曲华锋\\\"]\",\n    \"性别\": \"[\\\"男\\\"]\",\n    \"年龄\": \"[\\\"\\\"]\",\n    \"学历\": \"[\\\"硕士\\\"]\",\n    \"简历\": \"[\\\"曲华锋，吉林大学信用经济与管理专业硕士。2012年加盟东方基金管理有限责任公司，曾任权益投资部研究员，东方策略成长混合型开放式证券投资基金基金经理助理、东方新兴成长混合型证券投资基金基金经理助理。2020年4月30日起任东方新思路灵活配置混合型证券投资基金基金经理。自2020年9月2日起至2022年06月29日任东方民丰回报赢安混合型证券投资基金基金经理。自2021年8月11日起任东方盛世灵活配置混合型证券投资基金基金经理。自2022年06月29日起任东方支柱产业灵活配置混合型证券投资基金基金经理。自2023年8月23日起任东方中国红利混合型证券投资基金基金经理。\\\"]\",\n    \"投资经理年限\": \"[5.2191780821918]\",\n    \"在任基金总规模\": \"[\\\"255228791.96\\\"]\",\n    \"在任基金管理数\": \"[\\\"4\\\"]\",\n    \"现任公司年限\": \"5.22\",\n    \"历任管理基金数\": \"[\\\"5\\\"]\",\n    \"获奖数\": \"[\\\"0\\\"]\",\n    \"投资风格\": \"[\\\"大盘平衡\\\"]\",\n    \"主题风格\": \"[\\\"均衡\\\"]\"\n  },\n  {\n    \"经理代码\": \"\",\n    \"经理名称\": \"\",\n    \"在管基金类型\": \"\",\n    \"最近一月收益(%)\": \"1.586\",\n    \"今年以来收益(%)\": \"-1.03\",\n    \"最近三月收益(%)\": \"0.5115\",\n    \"最近六月收益(%)\": \"2.1751\",\n    \"最近一年收益(%)\": \"4.7051\",\n    \"最近两年收益(%)\": \"\",\n    \"最近三年收益(%)\": \"\",\n    \"任职年化收益(%)\": \"\"\n  },\n  {\n    \"排名\": \"1\",\n    \"基金代码\": \"[\\\"004005.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方民丰回报赢安混合A\\\"]\",\n    \"基金规模\": \"2.51\"\n  },\n  {\n    \"排名\": \"2\",\n    \"基金代码\": \"[\\\"009590.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方盛世灵活配置混合C\\\"]\",\n    \"基金规模\": \"0.72\"\n  },\n  {\n    \"排名\": \"3\",\n    \"基金代码\": \"[\\\"001384.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方新思路灵活配置混合A\\\"]\",\n    \"基金规模\": \"0.61\"\n  },\n  {\n    \"排名\": \"4\",\n    \"基金代码\": \"[\\\"001385.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方新思路灵活配置混合C\\\"]\",\n    \"基金规模\": \"0.50\"\n  },\n  {\n    \"排名\": \"5\",\n    \"基金代码\": \"[\\\"004205.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方支柱产业灵活配置混合\\\"]\",\n    \"基金规模\": \"0.41\"\n  },\n  {\n    \"排名\": \"6\",\n    \"基金代码\": \"[\\\"009999.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方中国红利混合\\\"]\",\n    \"基金规模\": \"0.31\"\n  },\n  {\n    \"排名\": \"7\",\n    \"基金代码\": \"[\\\"004006.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方民丰回报赢安混合C\\\"]\",\n    \"基金规模\": \"0.01\"\n  },\n  {\n    \"排名\": \"8\",\n    \"基金代码\": \"[\\\"002497.OF\\\"]\",\n    \"基金名称\": \"[\\\"东方盛世灵活配置混合A\\\"]\",\n    \"基金规模\": \"0.00\"\n  },\n  {\n    \"基金公司名称\": \"[\\\"东方基金管理股份有限公司\\\"]\",\n    \"成立日期\": \"[\\\"20040611\\\"]\",\n    \"注册资本\": \"[\\\"33333.0000万人民币\\\"]\",\n    \"总经理\": \"[\\\"刘鸿鹏\\\"]\",\n    \"旗下基金数\": \"[68]\",\n    \"基金资产管理规模\": \"[104673185861.84]\",\n    \"基金经理人数\": \"[24]\",\n    \"股东名称\": \"-\",\n    \"持股比例\": \"-\",\n    \"电话\": \"[\\\"86-010-66295888\\\"]\",\n    \"传真\": \"[\\\"86-010-66578700\\\"]\",\n    \"网址\": \"[\\\"www.orient-fund.com\\\"]\",\n    \"办公地址\": \"[\\\"北京市丰台区金泽路161号院1号楼远洋锐中心26层\\\"]\"\n  }\n]\nreturn list"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2840, 2360], "id": "bee39fe0-9a74-4ad2-b356-bc66d3b6056d", "name": "测试数据"}, {"parameters": {"assignments": {"assignments": [{"id": "1d2de67a-b852-4570-acc0-d1f236afe4ec", "name": "经理代码", "value": "=", "type": "string"}, {"id": "912084e3-f156-4830-a863-0124c006456a", "name": "经理名称", "value": "=", "type": "string"}, {"id": "37020c14-cb9b-42c0-9fc2-c8da553ef9b9", "name": "在管基金类型", "value": "=", "type": "string"}, {"id": "4aff6e0d-a243-4151-b302-b136ea56a2cb", "name": "最近一月收益(%)", "value": "={{ $json.results[1].ths_yeild_human_fund }}", "type": "string"}, {"id": "ccfca1a6-479c-446e-bae0-50910112e6bd", "name": "今年以来收益(%)", "value": "={{ $json.results[0].ths_yeild_human_fund }}", "type": "string"}, {"id": "7ddc5471-a2e7-4faf-adb2-ea6e493d2731", "name": "最近三月收益(%)", "value": "={{ $json.results[2].ths_yeild_human_fund }}", "type": "string"}, {"id": "f956f182-1cd8-4a42-bb37-956ce9ec1780", "name": "最近六月收益(%)", "value": "={{ $json.results[3].ths_yeild_human_fund }}", "type": "string"}, {"id": "fa539e64-d14d-4ccc-91d2-b7fe7e920ac8", "name": "最近一年收益(%)", "value": "={{ $json.results[4].ths_yeild_human_fund }}", "type": "string"}, {"id": "a67572fd-0d85-4e21-b5c3-49ae9b511a9e", "name": "最近两年收益(%)", "value": "={{ $json.results[5].ths_yeild_human_fund }}", "type": "string"}, {"id": "59126a88-2b35-45cb-aebe-c051a9a41e60", "name": "最近三年收益(%)", "value": "={{ $json.results[6].ths_yeild_human_fund }}", "type": "string"}, {"id": "f07b6fc5-c220-4438-be60-9fd7f3c95613", "name": "任职年化收益(%)", "value": "={{ $json.results[7].ths_yeild_human_fund }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1960, 2560], "id": "35b60173-19b1-4944-9478-ccb7d3655ab1", "name": "固定基金经理收益1"}, {"parameters": {"content": "数据消耗量51"}, "type": "n8n-nodes-base.stickyNote", "position": [700, 2800], "typeVersion": 1, "id": "46d021b6-d34f-4c66-9f3d-9955bb88e855", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// 获取当前日期并格式化为YYYYMMDD格式\nconst today = new Date();\nconst year = today.getFullYear();\nconst month = String(today.getMonth() + 1).padStart(2, '0');\nconst day = String(today.getDate()).padStart(2, '0');\nconst currentDate = `${year}${month}${day}`;\n\n// 返回格式化的日期\nreturn [{ json: { dynamicDate: currentDate } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, 1160], "id": "2aa4ca4f-0059-4d12-994f-b26893d4aac3", "name": "获取日期"}, {"parameters": {"content": "消耗数据量10"}, "type": "n8n-nodes-base.stickyNote", "position": [340, 1080], "typeVersion": 1, "id": "c73511a9-a1a0-4f8f-8855-fad93239354d", "name": "Sticky Note1"}, {"parameters": {"content": "数据消耗量20"}, "type": "n8n-nodes-base.stickyNote", "position": [-120, 1380], "typeVersion": 1, "id": "1f04847e-08cc-46fc-8e12-68ba78557fe1", "name": "Sticky Note2"}, {"parameters": {"content": "数据消耗量50"}, "type": "n8n-nodes-base.stickyNote", "position": [-100, 1660], "typeVersion": 1, "id": "a0664412-e9ec-4aa7-af7d-58d0fe552543", "name": "Sticky Note3"}, {"parameters": {"content": "数据消耗量25"}, "type": "n8n-nodes-base.stickyNote", "position": [-80, 2000], "typeVersion": 1, "id": "5324ed9f-80ba-4a60-88c1-de4f18e5c824", "name": "Sticky Note4"}, {"parameters": {"content": "消耗数据量14"}, "type": "n8n-nodes-base.stickyNote", "position": [200, 2620], "typeVersion": 1, "id": "e0b62dba-200b-42e4-baa5-75a18610d03a", "name": "Sticky Note5"}, {"parameters": {"content": "数据消耗量9\n"}, "type": "n8n-nodes-base.stickyNote", "position": [980, 2540], "typeVersion": 1, "id": "f53c2075-432d-4e38-b249-3cddb28b20fc", "name": "Sticky Note6"}, {"parameters": {"jsCode": "// 获取动态参数\nconst fundCode = $('提取输入参数2').first().json.fundcode;\nconst accessToken = $('If').first().json.access_token\n\n// 获取当前日期\nconst now = new Date();\nconst currentDate = now.toISOString().slice(0, 10).replace(/-/g, '');\n\n// 计算各个时间段的起始日期\nfunction getDateString(date) {\n  return date.toISOString().slice(0, 10).replace(/-/g, '');\n}\n\n// 今年以来\nconst yearStart = new Date(now.getFullYear(), 0, 1);\nconst ytdStartDate = getDateString(yearStart);\n\n// 最近一个月\nconst oneMonthAgo = new Date(now);\noneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);\nconst oneMonthStartDate = getDateString(oneMonthAgo);\n\n// 最近三个月\nconst threeMonthsAgo = new Date(now);\nthreeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);\nconst threeMonthStartDate = getDateString(threeMonthsAgo);\n\n// 最近六个月\nconst sixMonthsAgo = new Date(now);\nsixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);\nconst sixMonthStartDate = getDateString(sixMonthsAgo);\n\n// 最近一年\nconst oneYearAgo = new Date(now);\noneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);\nconst oneYearStartDate = getDateString(oneYearAgo);\n\n// 最近两年\nconst twoYearsAgo = new Date(now);\ntwoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);\nconst twoYearStartDate = getDateString(twoYearsAgo);\n\n// 最近三年\nconst threeYearsAgo = new Date(now);\nthreeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);\nconst threeYearStartDate = getDateString(threeYearsAgo);\n\n// 首先获取基金经理任职起始日期\nlet tenureStartDate = null;\ntry {\n  const tenureResponse = await this.helpers.httpRequest({\n    method: 'POST',\n    url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',\n    headers: {\n      'Content-Type': 'application/json',\n      'access_token': accessToken,\n      'ifindlang': 'cn'\n    },\n    body: JSON.stringify({\n      codes: fundCode,\n      indipara: [{\n        indicator: \"ths_service_sd_fund\",\n        indiparams: [\"100\", \"1\", \"101\"]\n      }]\n    }),\n    json: true\n  });\n\n  if (tenureResponse && tenureResponse.tables && tenureResponse.tables[0] && \n      tenureResponse.tables[0].table && tenureResponse.tables[0].table.ths_service_sd_fund) {\n    const serviceDate = tenureResponse.tables[0].table.ths_service_sd_fund[0];\n    tenureStartDate = serviceDate.replace(/-/g, '');\n  }\n} catch (error) {\n  console.error('获取任职日期失败:', error);\n}\n\n// 构造请求数据（包含任职期间）\nconst requests = [\n  { period: \"今年以来\", startDate: ytdStartDate },\n  { period: \"最近一个月\", startDate: oneMonthStartDate },\n  { period: \"最近三个月\", startDate: threeMonthStartDate },\n  { period: \"最近六个月\", startDate: sixMonthStartDate },\n  { period: \"最近一年\", startDate: oneYearStartDate },\n  { period: \"最近两年\", startDate: twoYearStartDate },\n  { period: \"最近三年\", startDate: threeYearStartDate }\n];\n\n// 如果获取到任职日期，添加任职期间的请求\nif (tenureStartDate) {\n  requests.push({ period: \"任职期间\", startDate: tenureStartDate });\n}\n\n// 发送所有请求\nconst results = [];\n\nfor (const req of requests) {\n  const requestBody = {\n    codes: fundCode,\n    indipara: [{\n      indicator: \"ths_yeild_human_fund\",\n      indiparams: [req.startDate, currentDate, \"1\", \"100\", \"1\", \"101\", \"101\"]\n    }]\n  };\n\n  try {\n    const response = await this.helpers.httpRequest({\n      method: 'POST',\n      url: 'https://quantapi.51ifind.com/api/v1/basic_data_service',\n      headers: {\n        'Content-Type': 'application/json',\n        'access_token': accessToken,\n        'ifindlang': 'cn'\n      },\n      body: JSON.stringify(requestBody),\n      json: true\n    });\n\n    // 只提取需要的数据\n    if (response && response.tables && response.tables[0] && \n        response.tables[0].table && response.tables[0].table.ths_yeild_human_fund) {\n      results.push({\n        period: req.period,\n        startDate: req.startDate,\n        endDate: currentDate,\n        ths_yeild_human_fund: response.tables[0].table.ths_yeild_human_fund[0]\n      });\n    } else {\n      results.push({\n        period: req.period,\n        startDate: req.startDate,\n        endDate: currentDate,\n        ths_yeild_human_fund: \"-\"\n      });\n    }\n  } catch (error) {\n    results.push({\n      period: req.period,\n      startDate: req.startDate,\n      endDate: currentDate,\n      ths_yeild_human_fund: \"-\",\n      error: error.message\n    });\n  }\n}\n\nreturn [{ json: { results, tenureStartDate } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 2580], "id": "ca6a4de5-0dd0-4d78-abad-b46cb3c8c0ba", "name": "区间收益率"}, {"parameters": {"content": "数据消耗量11\n"}, "type": "n8n-nodes-base.stickyNote", "position": [320, 3140], "typeVersion": 1, "id": "f093106a-4c35-45a8-8597-980406f282bf", "name": "Sticky Note7"}, {"parameters": {"content": "数据消耗量10\n"}, "type": "n8n-nodes-base.stickyNote", "position": [-80, -380], "typeVersion": 1, "id": "d1b32973-52f8-43e5-a55c-442b5d02acc5", "name": "Sticky Note8"}, {"parameters": {"content": "数据消耗量1\n"}, "type": "n8n-nodes-base.stickyNote", "position": [-80, -160], "typeVersion": 1, "id": "f66a945b-16fa-4414-9a38-6c00df1b4d1a", "name": "Sticky Note9"}, {"parameters": {"content": "数据消耗量4\n"}, "type": "n8n-nodes-base.stickyNote", "position": [-80, 40], "typeVersion": 1, "id": "06c69e54-53db-4498-aeca-dc285b51c32c", "name": "Sticky Note10"}, {"parameters": {"content": "数据消耗量1\n", "height": 760, "width": 380}, "type": "n8n-nodes-base.stickyNote", "position": [-100, 240], "typeVersion": 1, "id": "7ab15cde-7977-4937-b3e2-6534d41d70e5", "name": "Sticky Note11"}, {"parameters": {"jsCode": "// 获取工作天数并计算年限\nconst workDays = $input.first().json.tables[0].table.ths_rzts_by_tenure_fund[0];\nconst managementYears = Math.round((parseInt(workDays) / 365) * 100) / 100;\n\nreturn [{ json: { managementYears: managementYears.toString()} }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [840, 2300], "id": "3a820ac6-22d0-40d4-8b98-59e093c2e1bd", "name": "计算任职年限"}, {"parameters": {"content": "路由地址：/fund_details\n\n记录时间：2025-07-31\n\n相关人：hayden jason\n\n", "height": 260, "width": 340}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3300, 2020], "id": "9e587abf-8020-4b7e-a2f4-fccd9edd7282", "name": "Sticky Note12"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：验证investors数据", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2560, 2020], "id": "bc622a3e-c5b7-4188-99e2-d9b8c0a6ca13", "name": "Sticky Note13"}, {"parameters": {"content": "提取设计api所需要的参数：investors数组", "height": 260, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2860, 2020], "id": "43c2bfe3-b182-4059-8cba-b6a1d4a09bf5", "name": "Sticky Note14"}, {"parameters": {"content": "返回数据示例：\n\n[\n  {\n    \"html\": \"<!DOCTYPE html>\\n<html>\\n<head>\\n  <meta charset=\\\"UTF-8\\\" />\\n  <title>基金数据分析</title>\\n</head>\\n<body>\\n  <div class=\\\"container\\\">\\n    <!-- 上半部分：基金基本信息 -->\\n    <div class=\\\"fund-info-section\\\">\\n      <div class=\\\"fund-header\\\">\\n        <div class=\\\"fund-type\\\">基金类型：灵活配置型基金</div>\\n        <div class=\\\"fund-date\\\">成立日期：\\n          2004年06月11日</div>\\n        <div class=\\\"fund-index\\\">指数名称：沪深300,中债总指数(总值)全价指数</div>\\n        <div class=\\\"fund-company\\\">基金公司：<span class=\\\"value\\\">东方基金管理股份有限公司</span></div>\\n        <div class=\\\"fund-manager\\\">基金经理：<span class=\\\"value\\\">曲华锋</span></div>\\n        <div class=\\\"fund-connection\\\">联接基金：001385.OF</div>\\n      </div>\\n\\n      <div class=\\\"fund-metrics\\\">\\n        <div class=\\\"metrics-row\\\">\\n          <div class=\\\"metric\\\">总资产规模：<span class=\\\"value\\\">0.61亿元</span></div>\\n          <div class=\\\"metric\\\">基金净值：<span class=\\\"value\\\">1.10</span></div>\\n        </div>\\n        <div class=\\\"metrics-row\\\">\\n          <div class=\\\"metric\\\">基金场内规模：<span class=\\\"value\\\">-</span></div>\\n          <div class=\\\"metric\\\">收盘价格：<span class=\\\"value\\\">-</span></div>\\n        </div>\\n        <div class=\\\"metrics-row\\\">\\n          <div class=\\\"metric\\\">管理费用率：<span class=\\\"value\\\">1.00%</span></div>\\n          <div class=\\\"metric\\\">收盘价涨跌幅：<span class=\\\"value\\\" style=\\\"color: green\\\">-0.29</span></div>\\n        </div>\\n        <div class=\\\"metrics-row\\\">\\n          <div class=\\\"metric\\\">托管费用率：<span class=\\\"value\\\">0.20%</span></div>\\n          <div class=\\\"metric\\\">成交金额：<span class=\\\"value\\\">-</span></div>\\n        </div>\\n        <div class=\\\"metrics-row\\\">\\n          <div class=\\\"metric\\\">持仓换手率：<span class=\\\"value\\\">-</span></div>\\n          <div class=\\\"metric\\\">折价率：<span class=\\\"value positive\\\">-</span></div>\\n        </div>\\n      </div>\\n\\n      <!-- 收益表现和行业配置 -->\\n      <div class=\\\"performance-industry\\\">\\n        <div class=\\\"performance-section\\\">\\n          <h3>收益率表现</h3>\\n          <table class=\\\"performance-table\\\">\\n            <tr>\\n              <th>基金业绩</th>\\n              <th></th>\\n            </tr>\\n            <tr>\\n              <td>今年以来：</td>\\n              <td style=\\\"color: green\\\">\\n                -3.08%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近一个月：</td>\\n              <td style=\\\"color: red\\\">\\n                +2.55%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近三个月：</td>\\n              <td style=\\\"color: green\\\">\\n                -3.72%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近六个月：</td>\\n              <td style=\\\"color: green\\\">\\n                -0.94%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近1年：</td>\\n              <td style=\\\"color: red\\\">\\n                +3.78%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近2年：</td>\\n              <td style=\\\"color: green\\\">\\n                -21.63%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近3年：</td>\\n              <td style=\\\"color: green\\\">\\n                -27.38%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近5年：</td>\\n              <td style=\\\"color: green\\\">\\n                -8.76%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近10年：</td>\\n              <td style=\\\"color: red\\\">\\n                +20.26%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>成立以来：</td>\\n              <td style=\\\"color: red\\\">\\n                +9.86%\\n              </td>\\n            </tr>\\n          </table>\\n        </div>\\n\\n        <div class=\\\"industry-section\\\">\\n          <h3>行业配置</h3>\\n          <table class=\\\"industry-table\\\">\\n            <tr>\\n              <th>行业名称</th>\\n              <th>占净值比</th>\\n            </tr>\\n            \\n            <tr>\\n              <td>制造业</td>\\n              <td>49.63%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>房地产业</td>\\n              <td>33.95%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>信息传输、软件和信息技术服务业</td>\\n              <td>2.56%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>租赁和商务服务业</td>\\n              <td>2.33%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>农、林、牧、渔业</td>\\n              <td>1.25%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>水利、环境和公共设施管理业</td>\\n              <td>0.10%</td>\\n            </tr>\\n            \\n            <tr>\\n              <td>科学研究和技术服务业</td>\\n              <td>0.01%</td>\\n            </tr>\\n            \\n          </table>\\n        </div>\\n      </div>\\n\\n      <!-- 重仓股票和重仓债券 -->\\n      <div class=\\\"holdings-section\\\">\\n        <!-- 重仓股票 -->\\n        <h3>重仓股票（TOP10）</h3>\\n        <table class=\\\"holdings-table\\\">\\n          <tr>\\n            <th>证券代码</th>\\n            <th>证券简称</th>\\n            <th>持仓市值(元)</th>\\n            <th>占基金净值比</th>\\n            <th>持仓数量</th>\\n          </tr>\\n          \\n          <tr>\\n            <td>600383</td>\\n            <td>金地集团</td>\\n            <td>8588000</td>\\n            <td>7.70%</td>\\n            <td>1900000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>603833</td>\\n            <td>欧派家居</td>\\n            <td>7497600</td>\\n            <td>6.73%</td>\\n            <td>120000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>600048</td>\\n            <td>保利发展</td>\\n            <td>7434000</td>\\n            <td>6.67%</td>\\n            <td>900000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>001979</td>\\n            <td>招商蛇口</td>\\n            <td>7336000</td>\\n            <td>6.58%</td>\\n            <td>800000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>000002</td>\\n            <td>万科A</td>\\n            <td>7050000</td>\\n            <td>6.32%</td>\\n            <td>1000000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>600519</td>\\n            <td>贵州茅台</td>\\n            <td>7024500</td>\\n            <td>6.30%</td>\\n            <td>4500</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>002244</td>\\n            <td>滨江集团</td>\\n            <td>6264000</td>\\n            <td>5.62%</td>\\n            <td>600000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>002821</td>\\n            <td>凯莱英</td>\\n            <td>4725000</td>\\n            <td>4.24%</td>\\n            <td>60000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>000858</td>\\n            <td>五粮液</td>\\n            <td>4597250</td>\\n            <td>4.12%</td>\\n            <td>35000</td>\\n          </tr>\\n          \\n          <tr>\\n            <td>000568</td>\\n            <td>泸州老窖</td>\\n            <td>3891600</td>\\n            <td>3.49%</td>\\n            <td>30000</td>\\n          </tr>\\n          \\n        </table>\\n\\n        <!-- 重仓债券 -->\\n        <h3>重仓债券（TOP5）</h3>\\n        <table class=\\\"holdings-table\\\">\\n          <tr>\\n            <th>债券代码</th>\\n            <th>债券简称</th>\\n            <th>持仓市值(元)</th>\\n            <th>占基金净值比</th>\\n            <th>持仓数量</th>\\n          </tr>\\n          \\n          <tr>\\n            <td>019723</td>\\n            <td>23国债20</td>\\n            <td>7103141.64</td>\\n            <td>6.37%</td>\\n            <td>70000</td>\\n          </tr>\\n          \\n        </table>\\n      </div>\\n    </div>\\n\\n    <!-- 下半部分：基金经理信息 -->\\n    <div class=\\\"manager-section\\\">\\n      <h3 class=\\\"section-title\\\">基金经理-现任</h3>\\n      \\n      <div class=\\\"manager-profile\\\">\\n        <div class=\\\"manager-name-section\\\">\\n          <div class=\\\"manager-name\\\">\\n            <span>曲华锋</span>\\n            <span class=\\\"gender\\\">男</span>\\n          \\n            <span class=\\\"education\\\">硕士</span>\\n          </div>\\n          \\n          <div class=\\\"manager-content\\\">\\n            <div class=\\\"manager-stats\\\">\\n              <div class=\\\"stats-grid\\\">\\n                <div class=\\\"stat-label\\\">投资经理年限</div>\\n                <div class=\\\"stat-value\\\">5 年</div>\\n\\n                <div class=\\\"stat-label\\\">历任管理基金数</div>\\n                <div class=\\\"stat-value\\\">5 只</div>\\n\\n                <div class=\\\"stat-label\\\">在任基金总规模</div>\\n                <div class=\\\"stat-value\\\">255228791.96 元</div>\\n\\n                <div class=\\\"stat-label\\\">现任公司年限</div>\\n                <div class=\\\"stat-value\\\">5.22 年</div>\\n\\n                <div class=\\\"stat-label\\\">在任管理基金数</div>\\n                <div class=\\\"stat-value\\\">4 只</div>\\n\\n                <div class=\\\"stat-label\\\">获奖数</div>\\n                <div class=\\\"stat-value\\\">0 个</div>\\n              </div>\\n            </div>\\n            \\n            <div class=\\\"manager-bio-section\\\">\\n              <h4 class=\\\"bio-title\\\">基金经理简介:</h4>\\n              <div class=\\\"manager-bio\\\">\\n                <p>曲华锋，吉林大学信用经济与管理专业硕士。2012年加盟东方基金管理有限责任公司，曾任权益投资部研究员，东方策略成长混合型开放式证券投资基金基金经理助理、东方新兴成长混合型证券投资基金基金经理助理。2020年4月30日起任东方新思路灵活配置混合型证券投资基金基金经理。自2020年9月2日起至2022年06月29日任东方民丰回报赢安混合型证券投资基金基金经理。自2021年8月11日起任东方盛世灵活配置混合型证券投资基金基金经理。自2022年06月29日起任东方支柱产业灵活配置混合型证券投资基金基金经理。自2023年8月23日起任东方中国红利混合型证券投资基金基金经理。</p>\\n              </div>\\n            </div>\\n          </div>\\n        </div>\\n      </div>\\n      \\n      <!-- 收益率表现和前十大管理基金 -->\\n      <div class=\\\"performance-funds\\\">\\n        <div class=\\\"manager-performance\\\">\\n          <h3>收益率表现</h3>\\n          <table class=\\\"performance-table\\\">\\n            <tr>\\n              <th>任职间收益率</th>\\n              <th></th>\\n            </tr>\\n            <tr>\\n              <td>今年以来</td>\\n              <td class=\\\"negative\\\">\\n                -1.03%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近1个月</td>\\n              <td class=\\\"positive\\\">\\n                1.59%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近3个月</td>\\n              <td class=\\\"positive\\\">\\n                0.51%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近6个月</td>\\n              <td class=\\\"positive\\\">\\n                2.18%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近1年</td>\\n              <td class=\\\"positive\\\">\\n                4.71%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近2年</td>\\n              <td class=\\\"negative\\\">\\n                -%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>近3年</td>\\n              <td class=\\\"negative\\\">\\n                -%\\n              </td>\\n            </tr>\\n            <tr>\\n              <td>管理基金以来年化收益率</td>\\n              <td class=\\\"negative\\\">\\n                -%\\n              </td>\\n            </tr>\\n          </table>\\n          <div class=\\\"calculation-note\\\">\\n            收益率计算方法：基金经理收益率是根据所管理的基金涨跌幅来计算的，并且以基金的资产规模为权重。\\n          </div>\\n        </div>\\n        \\n        <div class=\\\"top-funds\\\">\\n          <h3>前十大管理基金</h3>\\n          <table class=\\\"funds-table\\\">\\n            <tr>\\n              <th>基金名称</th>\\n              <th>总资产规模</th>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方民丰回报赢安混合A</a></td>\\n              <td>2.51亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方盛世灵活配置混合C</a></td>\\n              <td>0.72亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方新思路灵活配置混合A</a></td>\\n              <td>0.61亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方新思路灵活配置混合C</a></td>\\n              <td>0.50亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方支柱产业灵活配置混合</a></td>\\n              <td>0.41亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方中国红利混合</a></td>\\n              <td>0.31亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方民丰回报赢安混合C</a></td>\\n              <td>0.01亿元</td>\\n            </tr>\\n            \\n            <tr>\\n              <td><a href=\\\"#\\\" class=\\\"fund-link\\\">东方盛世灵活配置混合A</a></td>\\n              <td>0.00亿元</td>\\n            </tr>\\n            \\n          </table>\\n        </div>\\n      </div>\\n      \\n      <!-- 基金公司信息 -->\\n      <div class=\\\"company-section\\\">\\n        <h3>东方基金管理股份有限公司</h3>\\n        <div class=\\\"company-info-grid\\\">\\n          <!-- 基本信息 -->\\n          <div class=\\\"company-info-block\\\">\\n            <h4 class=\\\"section-subtitle\\\">基本资料</h4>\\n            <div class=\\\"company-content company-basic-info\\\">\\n              <table class=\\\"inner-table compact-table\\\">\\n                <tr>\\n                  <td class=\\\"info-label\\\">成立日期</td>\\n                  <td class=\\\"info-value text-overflow\\\">2004-06-11</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">注册资本(万)</td>\\n                  <td class=\\\"info-value text-overflow\\\">33333.0000万人民币</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">总经理</td>\\n                  <td class=\\\"info-value text-overflow\\\">刘鸿鹏</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">管理资产规模</td>\\n                  <td class=\\\"info-value text-overflow\\\">1046.73亿元</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">旗下基金数量</td>\\n                  <td class=\\\"info-value text-overflow\\\">68</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">基金经理人数</td>\\n                  <td class=\\\"info-value text-overflow\\\">24</td>\\n                </tr>\\n              </table>\\n            </div>\\n          </div>\\n\\n          <!-- 股东信息 -->\\n          <div class=\\\"company-info-block\\\">\\n            <h4 class=\\\"section-subtitle\\\">股东列表</h4>\\n            <div class=\\\"company-content shareholders-content\\\">\\n              <table class=\\\"inner-table shareholders-table\\\">\\n                <tr>\\n                  <th class=\\\"shareholder-name\\\">股东名称</th>\\n                  <th class=\\\"shareholder-ratio\\\">持股比例</th>\\n                </tr>\\n                \\n                <tr>\\n                  <td class=\\\"shareholder-name text-overflow\\\">开发中</td>\\n                  <td class=\\\"shareholder-ratio text-overflow\\\">-</td>\\n                </tr>\\n                \\n              </table>\\n            </div>\\n          </div>\\n\\n          <!-- 联系方式 -->\\n          <div class=\\\"company-info-block\\\">\\n            <h4 class=\\\"section-subtitle\\\">联系方式</h4>\\n            <div class=\\\"company-content company-contact-info\\\">\\n              <table class=\\\"inner-table compact-table\\\">\\n                <tr>\\n                  <td class=\\\"info-label\\\">电话</td>\\n                  <td class=\\\"info-value text-overflow\\\">86-010-66295888</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">传真</td>\\n                  <td class=\\\"info-value text-overflow\\\">86-010-66578700</td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">网址</td>\\n                  <td class=\\\"info-value text-overflow\\\"><a href=\\\"http://www.orient-fund.com\\\" target=\\\"_blank\\\" class=\\\"website-link\\\">www.orient-fund.com</a></td>\\n                </tr>\\n                <tr>\\n                  <td class=\\\"info-label\\\">办公地址</td>\\n                  <td class=\\\"info-value text-overflow\\\">北京市丰台区金泽路161号院1号楼远洋锐中心26层</td>\\n                </tr>\\n              </table>\\n            </div>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n  </div>\\n\\n  <style>\\n    /* 全局样式 */\\n    body {\\n      font-family: \\\"PingFang SC\\\", \\\"Microsoft YaHei\\\", sans-serif;\\n      margin: 0;\\n      padding: 0;\\n      color: #333;\\n      line-height: 1.5;\\n    }\\n    \\n    .container {\\n      margin: 0 auto;\\n      padding: 20px;\\n    }\\n    \\n    h3 {\\n      color: #1e293b;\\n      font-size: 18px;\\n      font-weight: 600;\\n      margin: 16px 0;\\n      padding-left: 8px;\\n      border-left: 4px solid #0ea5e9;\\n    }\\n    \\n    /* 基金信息部分 */\\n    .fund-header {\\n      margin-bottom: 16px;\\n    }\\n\\n    .fund-type, .fund-date, .fund-index, .fund-company, .fund-manager, .fund-connection, .metric {\\n      font-size: 14px;\\n      margin-bottom: 8px;\\n    }\\n\\n    .fund-index {\\n      font-weight: normal;\\n    }\\n\\n    .fund-connection {\\n      color: #555;\\n      margin-bottom: 16px;\\n    }\\n    \\n    .fund-metrics {\\n      margin-bottom: 20px;\\n    }\\n    \\n    .metrics-row {\\n      display: flex;\\n      justify-content: space-between;\\n      margin-bottom: 8px;\\n    }\\n    \\n    .metric {\\n      flex: 1;\\n      font-size: 14px;\\n    }\\n    \\n    .value {\\n      font-weight: 600;\\n    }\\n    \\n    .positive {\\n      color: #dc2626;\\n    }\\n    \\n    .negative {\\n      color: #16a34a;\\n    }\\n    \\n    /* 表格样式 */\\n    table {\\n      width: 100%;\\n      border-collapse: collapse;\\n      margin-bottom: 20px;\\n      font-size: 14px;\\n    }\\n    \\n    th, td {\\n      padding: 10px;\\n      text-align: left;\\n      border-bottom: 1px solid #e2e8f0;\\n    }\\n    \\n    th {\\n      font-weight: 600;\\n    }\\n    \\n    /* 布局 */\\n    .performance-industry {\\n      display: flex;\\n      gap: 20px;\\n      margin-bottom: 20px;\\n    }\\n    \\n    .performance-section, .industry-section {\\n      flex: 1;\\n    }\\n    \\n    /* 标签页导航样式 */\\n    .tab-navigation {\\n      display: flex;\\n      margin-bottom: 16px;\\n      border-bottom: 2px solid #e2e8f0;\\n    }\\n\\n    .tab-button {\\n      padding: 12px 24px;\\n      background: none;\\n      border: none;\\n      cursor: pointer;\\n      font-size: 14px;\\n      font-weight: 500;\\n      color: #64748b;\\n      border-bottom: 2px solid transparent;\\n      transition: all 0.3s ease;\\n    }\\n\\n    .tab-button:hover {\\n      color: #0ea5e9;\\n      background-color: #f8fafc;\\n    }\\n\\n    .tab-button.active {\\n      color: #0ea5e9;\\n      border-bottom-color: #0ea5e9;\\n      font-weight: 600;\\n    }\\n\\n    /* 标签内容样式 */\\n    .tab-content {\\n      display: none;\\n    }\\n\\n    .tab-content.active {\\n      display: block;\\n    }\\n\\n    /* 持仓表格样式优化 */\\n    .holdings-table {\\n      width: 100%;\\n      border-collapse: collapse;\\n      margin-top: 0;\\n    }\\n\\n    .holdings-table th {\\n      background-color: #f8fafc;\\n      font-weight: 600;\\n      color: #1e293b;\\n    }\\n    \\n    /* 基金经理部分 */\\n    .manager-section {\\n      margin-top: 30px;\\n      border-top: 1px solid #e2e8f0;\\n      padding-top: 20px;\\n    }\\n    \\n    .section-title {\\n      padding: 10px;\\n      border-radius: 4px;\\n      margin-bottom: 20px;\\n    }\\n    \\n    .manager-profile {\\n      margin-bottom: 20px;\\n    }\\n    \\n    .manager-name-section {\\n      width: 100%;\\n    }\\n    \\n    .manager-name {\\n      font-size: 16px;\\n      font-weight: 600;\\n      margin-bottom: 20px;\\n    }\\n    \\n    .manager-content {\\n      display: flex;\\n      gap: 20px;\\n      align-items: flex-start;\\n    }\\n    \\n    .manager-stats {\\n      flex: 1;\\n      min-width: 300px;\\n    }\\n    \\n    .manager-bio-section {\\n      flex: 1.5;\\n    }\\n    \\n    .bio-title {\\n      margin: 0 0 8px 0;\\n      font-size: 15px;\\n      font-weight: 600;\\n      color: #1e293b;\\n    }\\n    \\n    .manager-bio {\\n      padding: 8px 12px;\\n      border-radius: 4px;\\n      background-color: #f8fafc;\\n      max-height: 160px;\\n      overflow-y: auto;\\n    }\\n    \\n    .manager-bio p {\\n      margin: 0;\\n      font-size: 13px;\\n      line-height: 1.4;\\n      color: #475569;\\n      text-align: justify;\\n    }\\n    \\n    .stats-grid {\\n      display: grid;\\n      grid-template-columns: 150px auto;\\n      gap: 8px;\\n      margin-bottom: 16px;\\n    }\\n    \\n    .stat-label {\\n      color: #64748b;\\n    }\\n    \\n    .stat-value {\\n      font-weight: 600;\\n    }\\n    \\n    .performance-funds {\\n      display: flex;\\n      gap: 20px;\\n      margin-bottom: 20px;\\n    }\\n    \\n    .manager-performance, .top-funds {\\n      flex: 1;\\n    }\\n    \\n    .calculation-note {\\n      font-size: 12px;\\n      color: #64748b;\\n      margin-top: 10px;\\n      padding: 8px;\\n      background-color: #f1f5f9;\\n      border-radius: 4px;\\n    }\\n    \\n    .fund-link {\\n      color: #0ea5e9;\\n      text-decoration: none;\\n    }\\n    \\n    .fund-link:hover {\\n      text-decoration: underline;\\n    }\\n    \\n    /* 基金公司信息样式 */\\n    .company-section {\\n      margin-top: 20px;\\n    }\\n    \\n    .company-info-grid {\\n      display: grid;\\n      grid-template-columns: 1fr 1.2fr 1fr;\\n      gap: 15px;\\n      margin-top: 15px;\\n      align-items: start;\\n    }\\n    \\n    .company-info-block {\\n      display: flex;\\n      flex-direction: column;\\n      min-height: 280px;\\n      height: auto;\\n      border: 1px solid #e2e8f0;\\n      border-radius: 6px;\\n      background-color: #ffffff;\\n    }\\n    \\n    .section-subtitle {\\n      color: #1e293b;\\n      font-size: 16px;\\n      font-weight: 600;\\n      margin: 0;\\n      padding: 12px 15px;\\n      background-color: #f8fafc;\\n      border-bottom: 1px solid #e2e8f0;\\n      border-radius: 6px 6px 0 0;\\n    }\\n    \\n    .company-content {\\n      flex: 1;\\n      overflow-y: auto;\\n      padding: 0;\\n    }\\n    \\n    .shareholders-content {\\n      max-height: 230px;\\n    }\\n    \\n    .inner-table {\\n      width: 100%;\\n      border-collapse: collapse;\\n      margin: 0;\\n    }\\n    \\n    .inner-table td {\\n      padding: 10px 15px;\\n      border-bottom: 1px solid #f1f5f9;\\n      border-left: none;\\n      border-right: none;\\n      font-size: 13px;\\n    }\\n    \\n    .inner-table tr:last-child td {\\n      border-bottom: none;\\n    }\\n    \\n    .info-label {\\n      color: #64748b;\\n      width: 45%;\\n      font-weight: 500;\\n    }\\n    \\n    .info-value {\\n      font-weight: 500;\\n      color: #1e293b;\\n    }\\n    /* 基金公司和基金经理的value显示为蓝色 */\\n    .fund-company .value,\\n    .fund-manager .value {\\n      color: #0ea5e9;\\n    }\\n    .shareholders-table {\\n      width: 100%;\\n    }\\n\\n    .shareholder-name {\\n      text-align: left;\\n      color: #475569;\\n      width: 70%;\\n      box-shadow: none;\\n      text-shadow: none;\\n    }\\n\\n    .shareholder-ratio {\\n      text-align: right;\\n      font-weight: 600;\\n      color: #1e293b;\\n      width: 30%;\\n      box-shadow: none;\\n      text-shadow: none;\\n    }\\n\\n    .shareholders-table th {\\n      background-color: #ffffff;\\n      border-bottom: 1px solid #e2e8f0;\\n      padding: 10px 15px;\\n      font-size: 14px;\\n      color: #1e293b;\\n      font-weight: 600;\\n      position: sticky;\\n      top: 0;\\n      z-index: 1;\\n      box-shadow: none;\\n      text-shadow: none;\\n    }\\n\\n    .website-link {\\n      color: #0ea5e9;\\n      text-decoration: none;\\n      word-break: break-all;\\n      line-height: 1.4;\\n    }\\n\\n    .website-link:hover {\\n      text-decoration: underline;\\n    }\\n\\n    /* 滚动条样式 */\\n    .company-content::-webkit-scrollbar {\\n      width: 6px;\\n    }\\n\\n    .company-content::-webkit-scrollbar-track {\\n      background: #f1f5f9;\\n      border-radius: 3px;\\n    }\\n\\n    .company-content::-webkit-scrollbar-thumb {\\n      background: #cbd5e1;\\n      border-radius: 3px;\\n    }\\n\\n    .company-content::-webkit-scrollbar-thumb:hover {\\n      background: #94a3b8;\\n    }\\n    /* 移除重仓债券表格底部边框 */\\n    .holdings-section .holdings-table:last-child {\\n      border-bottom: none;\\n      margin-bottom: 0;\\n    }\\n    \\n    .holdings-section .holdings-table:last-child tr:last-child td {\\n      border-bottom: none;\\n    }\\n    /* 基本资料和联系方式取消滚动 */\\n    .company-basic-info,\\n    .company-contact-info {\\n      overflow-y: visible !important;\\n    }\\n    \\n    /* 紧凑表格样式 */\\n    .compact-table {\\n      table-layout: fixed;\\n      width: 100%;\\n    }\\n    \\n    .compact-table td {\\n      padding: 6px 15px !important;\\n      word-wrap: break-word;\\n      word-break: break-all;\\n      white-space: normal;\\n      vertical-align: top;\\n    }\\n    \\n    .compact-table .info-label {\\n      width: 35% !important;\\n      font-weight: 500;\\n      color: #64748b;\\n    }\\n    \\n    .compact-table .info-value {\\n      width: 65% !important;\\n      font-weight: 500;\\n      color: #1e293b;\\n      line-height: 1.4;\\n    }\\n    \\n    /* 移除text-overflow类的截断效果 */\\n    .text-overflow {\\n      overflow: visible !important;\\n      text-overflow: unset !important;\\n      white-space: normal !important;\\n    }\\n  </style>\\n\\n  <script>\\n    function switchTab(tabName) {\\n      // 隐藏所有标签内容\\n      const allTabs = document.querySelectorAll('.tab-content');\\n      allTabs.forEach(tab => {\\n        tab.classList.remove('active');\\n      });\\n      \\n      // 移除所有按钮的active状态\\n      const allButtons = document.querySelectorAll('.tab-button');\\n      allButtons.forEach(button => {\\n        button.classList.remove('active');\\n      });\\n      \\n      // 显示选中的标签内容\\n      const selectedTab = document.getElementById(tabName + '-tab');\\n      if (selectedTab) {\\n        selectedTab.classList.add('active');\\n      }\\n      \\n      // 激活对应的按钮\\n      event.target.classList.add('active');\\n    }\\n  </script>\\n</body>\\n</html>\\n\"\n  }\n]\n]", "height": 960, "width": 360}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4000, 1660], "id": "da8693a1-6ef6-49a7-b0fa-fbac7644e29e", "name": "Sticky Note15"}, {"parameters": {"assignments": {"assignments": [{"id": "20b5e6eb-4d4d-454a-8682-6a8b58270780", "name": "html", "value": "={{ $json.html }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [3640, 2000], "id": "8f849e24-0c23-457f-8e57-4048abf98289", "name": "固定页面"}, {"parameters": {"content": "固定从API接口获取到的各部分数据", "height": 280}, "type": "n8n-nodes-base.stickyNote", "position": [1600, 880], "typeVersion": 1, "id": "84e1f42d-63b0-46f4-8d09-d138bab6b9ca", "name": "Sticky Note16"}, {"parameters": {"content": "将合并后的数据标准化处理，方便之后的数据渲染和传入"}, "type": "n8n-nodes-base.stickyNote", "position": [2960, 1860], "typeVersion": 1, "id": "c912147e-741f-499e-b1b1-b961965354c2", "name": "Sticky Note17"}, {"parameters": {"content": "获取同花顺长期tocken和短期tocken验证是否会过期\n", "width": 960}, "type": "n8n-nodes-base.stickyNote", "position": [-1960, 1940], "typeVersion": 1, "id": "36d58a02-bd09-47da-b07c-66b71aa055fb", "name": "Sticky Note18"}, {"parameters": {"content": "将数据写入HTML界面，进行展示\n"}, "type": "n8n-nodes-base.stickyNote", "position": [3300, 1860], "typeVersion": 1, "id": "c05bd584-c932-490b-aaea-842a55641b03", "name": "Sticky Note19"}], "connections": {"检查验证结果": {"main": [[{"node": "固定refresh", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "固定refresh": {"main": [[{"node": "获取access token", "type": "main", "index": 0}]]}, "获取access token": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Split Out", "type": "main", "index": 0}], [{"node": "固定refresh", "type": "main", "index": 0}]]}, "基金详情展示1": {"main": [[{"node": "提取输入参数2", "type": "main", "index": 0}]]}, "提取输入参数2": {"main": [[{"node": "参数验证逻辑1", "type": "main", "index": 0}]]}, "参数验证逻辑1": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "基金基本资料": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "基金基础数据": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "联接基金": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "费用率": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "基金基础数据", "type": "main", "index": 0}]]}, "持仓换手率": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "日期处理": {"main": [[{"node": "持仓换手率", "type": "main", "index": 0}]]}, "收盘价涨跌幅": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 4}]]}, "成交金额": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 5}]]}, "日期处理1": {"main": [[{"node": "收盘价涨跌幅", "type": "main", "index": 0}]]}, "折价率": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 6}]]}, "日期处理2": {"main": [[{"node": "成交金额", "type": "main", "index": 0}]]}, "日期处理3": {"main": [[{"node": "折价率", "type": "main", "index": 0}]]}, "基金收益率表现": {"main": [[{"node": "固定各区间收益率", "type": "main", "index": 0}]]}, "固定各区间收益率": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "固定行业配置": {"main": [[{"node": "Merge1", "type": "main", "index": 2}]]}, "固定重仓股票（TOP10）": {"main": [[{"node": "Merge1", "type": "main", "index": 3}]]}, "固定重仓债券（TOP5）": {"main": [[{"node": "Merge1", "type": "main", "index": 4}]]}, "Split Out": {"main": [[{"node": "基金基本资料", "type": "main", "index": 0}, {"node": "联接基金", "type": "main", "index": 0}, {"node": "费用率", "type": "main", "index": 0}, {"node": "日期处理", "type": "main", "index": 0}, {"node": "日期处理1", "type": "main", "index": 0}, {"node": "日期处理2", "type": "main", "index": 0}, {"node": "日期处理3", "type": "main", "index": 0}, {"node": "获取日期", "type": "main", "index": 0}, {"node": "行业配置循环请求", "type": "main", "index": 0}, {"node": "重仓股票循环请求", "type": "main", "index": 0}, {"node": "重仓债券循环请求", "type": "main", "index": 0}, {"node": "基金经理信息", "type": "main", "index": 0}, {"node": "基金公司信息", "type": "main", "index": 0}]]}, "基金经理信息": {"main": [[{"node": "基金经理管理基金（TOP10）", "type": "main", "index": 0}, {"node": "区间收益率", "type": "main", "index": 0}, {"node": "计算任职年限", "type": "main", "index": 0}]]}, "固定基金经理信息": {"main": [[{"node": "Merge1", "type": "main", "index": 5}]]}, "基金公司信息": {"main": [[{"node": "固定基金公司信息", "type": "main", "index": 0}]]}, "固定基金公司信息": {"main": [[{"node": "Merge1", "type": "main", "index": 8}]]}, "基金经理管理基金（TOP10）": {"main": [[{"node": "数据扁平化", "type": "main", "index": 0}]]}, "筛选管理基金（TOP10）": {"main": [[{"node": "固定管理前十基金", "type": "main", "index": 0}]]}, "固定管理前十基金": {"main": [[{"node": "Merge1", "type": "main", "index": 7}]]}, "数据扁平化": {"main": [[{"node": "基金规模循环", "type": "main", "index": 0}]]}, "行业配置循环请求": {"main": [[{"node": "固定行业配置", "type": "main", "index": 0}]]}, "重仓股票循环请求": {"main": [[{"node": "固定重仓股票（TOP10）", "type": "main", "index": 0}]]}, "重仓债券循环请求": {"main": [[{"node": "固定重仓债券（TOP5）", "type": "main", "index": 0}]]}, "基金规模循环": {"main": [[{"node": "筛选管理基金（TOP10）", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "数据标准化", "type": "main", "index": 0}]]}, "数据标准化": {"main": [[{"node": "HTML1", "type": "main", "index": 0}]]}, "HTML1": {"main": [[{"node": "固定页面", "type": "main", "index": 0}]]}, "测试数据": {"main": [[]]}, "固定基金经理收益1": {"main": [[{"node": "Merge1", "type": "main", "index": 6}]]}, "获取日期": {"main": [[{"node": "基金收益率表现", "type": "main", "index": 0}]]}, "区间收益率": {"main": [[{"node": "固定基金经理收益1", "type": "main", "index": 0}]]}, "计算任职年限": {"main": [[{"node": "固定基金经理信息", "type": "main", "index": 0}]]}, "固定页面": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}