{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "extract-keywords", "name": "ana", "value": "={{ $json.query?.ana || $json.body?.ana || $json.ana || '' }}", "type": "string"}, {"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "0e1ff7d0-cf41-492f-8e84-bbc2428f9166", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-1820, 200], "typeVersion": 3.4, "notesInFlow": false}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.pattern && !options.pattern.test(val)) return false;\n      if (options.notEmpty && val.trim() === '') return false;\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) return false;\n      if (options.min !== undefined && num < options.min) return false;\n      if (options.max !== undefined && num > options.max) return false;\n      if (options.integer && !Number.isInteger(num)) return false;\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'ana',\n      type: 'string',\n      required: true,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    }\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value);\n    } else if (type === 'boolean') {\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1;\n      }\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}\n"}, "id": "a7aed224-f6ea-4441-878d-d0c00c373101", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-1600, 200], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "2daac27e-2951-43cc-9186-3a19d2de0501", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1380, 200], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "success-keywords", "name": "ana", "value": "={{ $json.ana }}", "type": "string"}, {"id": "success-timestamp", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}]}, "options": {}}, "id": "4925c1e8-310b-41f4-a1fe-28f5b7428575", "name": "成功数据格式化", "type": "n8n-nodes-base.set", "position": [-1160, 100], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "9ad41045-3a45-4d9f-afd7-aed81493d149", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1160, 300], "typeVersion": 1.1}, {"parameters": {"options": {"responseCode": 200}}, "id": "a886282a-f542-4370-9be7-bde5db7e0a15", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "position": [976, 0], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "recommend", "responseMode": "responseNode", "options": {}}, "id": "5515be48-4c1e-4b56-84ce-29e1f4befc02", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-2040, 200], "typeVersion": 2, "webhookId": "4debb3f1-37a4-4e28-8f30-6f13ab46c656"}, {"parameters": {"content": "提取设计api所需要的参数：ana字段为推荐语", "height": 260, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1920, 100], "id": "acc0f0a9-ad19-4284-8523-3caed1d5418d", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：ana字段", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1740, 100], "id": "3e555f71-126d-425b-9433-80aab87a5e6c", "name": "Sticky Note1"}, {"parameters": {"content": "优化用户上传推荐语，大模型判断基金维护相关，提问是ai则使用finance字段输出格式，精炼用户推荐语。\n\n如果不是基金相关则使用err_finance返回大模型判断", "height": 200, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, -160], "id": "7c5db30b-dba1-4f38-8e5e-3335e0021705", "name": "Sticky Note2"}, {"parameters": {"promptType": "define", "text": "=## 角色定义\n\n你现在是一位\"**基金内容检测与优化专家**\"，只对\"基金\"相关内容做处理，其余全部拒绝。\n\n## 1. 角色与任务\n\n### 1.1 基金相关智能判定\n使用你的专业知识判断文本是否与基金相关，包括但不限于：\n\n**基金产品类型**：\n- 公募基金、私募基金、对冲基金\n- ETF、LOF、QDII、FOF、REITs等各类基金\n- 指数基金、主动基金、被动基金\n- 股票基金、债券基金、混合基金、货币基金\n- 商品基金、黄金基金、原油基金\n- 养老目标基金、科创板基金、北交所基金\n- 分级基金、封闭基金、开放基金\n\n**基金业务操作**：\n- 基金投资、申购、赎回、转换、定投\n- 基金分红、拆分、合并、清盘\n- 基金估值、净值、费率、评级\n\n**基金相关概念**：\n- 基金经理、基金公司、基金托管\n- 基金持仓、基金规模、基金业绩\n- 基金风险、基金策略、基金组合\n- 基金投顾、智能投顾、基金销售\n\n**新兴基金概念**：\n- 你应该能识别任何与基金投资相关的新概念、新产品、新策略\n\n### 1.2 核心提炼规则\n若判定为\"涉及基金\"，用一句话（≤20字）提炼核心：\n```\n标的/工具 + 关键动作 + 核心指标/风险\n```\n\n### 1.3 排除规则\n- 纯粹的股票、债券、期货、外汇等非基金投资\n- 银行理财、保险产品、信托产品（除非明确提及基金）\n- 一般性财经新闻（除非涉及基金）\n- 与投资无关的日常话题\n\n## 2. 输入检测与反馈规则\n\n### 2.1 无关基金\n```json\n{\n  \"err_finance\": \"您输入的内容与基金无关，请提供基金、ETF、REITs等基金相关问题。\"\n}\n```\n\n### 2.2 涉及基金\n```json\n{\n  \"finance\": \"<≤20字的基金核心提炼>\"\n}\n```\n\n## 3. 示例\n\n| 用户输入 | 正确输出 |\n|---------|---------|\n| 如何选择一只好的基金？ | `{\"finance\":\"选基看业绩经理费率风险匹配目标\"}` |\n| 今天天气真好。 | `{\"err_finance\":\"您输入的内容与基金无关，请提供基金、ETF、REITs等基金相关问题。\"}` |\n| 一品红的股票代码是多少？ | `{\"err_finance\":\"您输入的内容与基金无关，请提供基金、ETF、REITs等基金相关问题。\"}` |\n| 中证500ETF值得定投吗？ | `{\"finance\":\"中证500ETF可定投关注估值与回撤\"}` |\n| 买REITs要注意什么？ | `{\"finance\":\"REITs看分红率现金流与利率风险\"}` |\n| 科创50基金怎么样？ | `{\"finance\":\"科创50基金关注科技成长与波动风险\"}` |\n| 智能投顾的基金组合靠谱吗？ | `{\"finance\":\"智能投顾基金组合看算法与风控能力\"}` |\n\n## 4. 处理指令\n\n对用户输入的文本 `{{ $json.ana }}` 进行智能检测，运用你的专业知识判断是否与基金相关，并严格按照以上规则返回 JSON。\n\n**判断原则**：\n1. 优先考虑文本的核心意图和主要内容\n2. 即使没有明确提及\"基金\"二字，但明显涉及基金投资的内容也应识别\n3. 对于边界情况，倾向于更宽泛的基金相关判定\n4. 保持专业性和准确性\n\n## 使用说明\n\n### 4.1 优势\n- **全面覆盖**: 不受固定关键词限制，能识别新兴基金概念\n- **智能判断**: 基于语义理解，而非简单的关键词匹配\n- **灵活适应**: 能够适应基金行业的发展和变化\n- **准确识别**: 减少误判和漏判\n\n### 4.2 技术要求\n- 基于语义理解进行判断\n- 输出格式必须为标准JSON\n- 核心提炼不超过20个字符\n- 保持判断的一致性和专业性\n\n### 4.3 质量标准\n- **准确性**: 基于专业知识的准确判断\n- **简洁性**: 核心提炼≤20字\n- **一致性**: 相似输入产生一致输出\n- **完整性**: 覆盖所有基金相关场景，包括新兴概念\n\n---", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-940, 100], "id": "3423d0d3-8e88-4cd9-a382-82bcca1b1be2", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-852, 320], "id": "ce189646-63bb-4344-9eb0-1dd189d9ccd7", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// 读取输入\nconst raw = $input.first().json.output;\n\n// 去掉 Markdown 代码块标记，只保留中间 JSON\nconst cleaned = raw\n  .replace(/^```json\\s*/i, '') // 去掉开头的 ```json\n  .replace(/\\s*```$/i, '');    // 去掉结尾的 ```\n\n// 解析\nconst output = JSON.parse(cleaned);\n\n// 返回给后续节点\nreturn { output };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-564, 100], "id": "5fb82520-4346-40cc-aa20-b4fa6ec63dc9", "name": "格式化ai响应的json数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "13e77625-5a8d-415f-a6af-55b654925da8", "leftValue": "={{ $json.output.finance }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-344, 100], "id": "8737ba8b-0580-4597-bbf8-eb0a90bbdc68", "name": "If"}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "2ca576e2-8e2a-4801-9c99-da7afa8b5ee3", "name": "不相关金融反馈", "type": "n8n-nodes-base.respondToWebhook", "position": [-124, 200], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "20b5e6eb-4d4d-454a-8682-6a8b58270780", "name": "finance", "value": "={{ $json.output.finance }}", "type": "string"}, {"id": "23124581-5a78-4da4-b897-2f3d9ab56694", "name": "refresh_token", "value": "eyJzaWduX3RpbWUiOiIyMDI1LTA3LTIzIDE0OjExOjQ4In0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9C04C75508BBC8C1DE508E43E7EA749369F1E1D15B1681C6D27898F5A0A72E8D", "type": "string"}]}, "options": {}}, "id": "9ed2bce8-3545-4f7b-a5ee-586bac8e4af7", "name": "正确金融数据整理", "type": "n8n-nodes-base.set", "position": [-124, 0], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "23c8a3ec-6809-4606-80c0-84d110da16e3", "name": "access_token", "value": "={{ $json.data.access_token }}", "type": "string"}]}, "options": {}}, "id": "6c017b5a-af96-4e38-8e36-187aa06d3569", "name": "缓存acc token", "type": "n8n-nodes-base.set", "position": [316, 0], "typeVersion": 3.4}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "id": "48ea066f-ad2e-47b0-9774-284d6641b7f4", "name": "获取（短期）access token", "type": "n8n-nodes-base.httpRequest", "position": [96, 0], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/smart_stock_picking", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\"} ", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"searchstring\":\"{{ $('If').item.json.output.finance }}\",\"searchtype\":\"fund\"}", "options": {}}, "id": "05e7a49e-3d7d-4ead-b6b9-9dad561b38b1", "name": "获取（短期）access token1", "type": "n8n-nodes-base.httpRequest", "position": [540, 0], "typeVersion": 4.2}, {"parameters": {"jsCode": "/**\n * 过滤基金数据，提取基金代码并转换为英文键名\n * <AUTHOR>\n * @created 2025-01-31 16:47:31\n * @description 从输入的基金数据中提取基金代码，转换为英文键名格式，并添加报告日期\n * @modification 2025-01-31 - 初始创建，过滤基金代码数据并格式化输出\n * @modification 2025-01-31 16:47:31 - 修改逻辑：当没有提取到数据时，报告日期显示为\"-\"\n * @modification 2025-01-31 16:47:31 - 适配HTTP请求节点返回errorcode -4001的情况\n */\n\n// 获取输入数据\nconst inputData = $input.first().json;\n\n// 检查是否为HTTP请求错误响应（errorcode: -4001, no data）\nif (inputData && inputData.errorcode === -4001) {\n  return [{\n    json: {\n      fundCodes: [],\n      reportDate: \"-\",\n      totalCount: 0,\n      timestamp: new Date().toISOString(),\n      error: `HTTP请求无数据: ${inputData.errmsg || 'no data'}`\n    }\n  }];\n}\n\n// 检查数据结构是否正确\nif (!inputData || !inputData.tables || !Array.isArray(inputData.tables) || inputData.tables.length === 0) {\n  return [{\n    json: {\n      fundCodes: [],\n      reportDate: \"-\",\n      totalCount: 0,\n      timestamp: new Date().toISOString(),\n      error: \"数据结构不正确，缺少必要的tables字段或tables为空数组\"\n    }\n  }];\n}\n\n// 检查第一个table是否存在\nif (!inputData.tables[0] || !inputData.tables[0].table) {\n  return [{\n    json: {\n      fundCodes: [],\n      reportDate: \"-\",\n      totalCount: 0,\n      timestamp: new Date().toISOString(),\n      error: \"tables[0]不存在或缺少table字段\"\n    }\n  }];\n}\n\n// 提取表格数据\nconst tableData = inputData.tables[0].table;\n\n// 检查是否存在基金代码字段\nif (!tableData[\"基金代码\"]) {\n  return [{\n    json: {\n      fundCodes: [],\n      reportDate: \"-\",\n      totalCount: 0,\n      timestamp: new Date().toISOString(),\n      error: \"未找到基金代码字段\"\n    }\n  }];\n}\n\n// 提取基金代码数组\nconst fundCodes = tableData[\"基金代码\"];\n\n// 检查基金代码数组是否为空或无效\nif (!Array.isArray(fundCodes) || fundCodes.length === 0) {\n  return [{\n    json: {\n      fundCodes: [],\n      reportDate: \"-\",\n      totalCount: 0,\n      timestamp: new Date().toISOString(),\n      error: \"基金代码数据为空\"\n    }\n  }];\n}\n\n// 转换为英文键名格式并添加报告日期\nconst result = {\n  fundCodes: fundCodes,\n  reportDate: \"20250630\",\n  totalCount: fundCodes.length,\n  timestamp: new Date().toISOString()\n};\n\n// 返回处理后的数据\nreturn [{\n  json: result\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [756, 0], "id": "bee6b61e-9021-482a-a681-66a9b7be4611", "name": "整理基金代码"}, {"parameters": {"content": "返回示例：\n\n[\n  {\n    \"fundCodes\": [\n      \"000220.OF\",\n      \"000242.OF\",\n      \"000339.OF\",\n      \"000418.OF\",\n      \"000586.OF\",\n      \"000634.OF\",\n      \"000739.OF\",\n      \"001106.OF\"\n    ],\n    \"reportDate\": \"20250630\",\n    \"totalCount\": 117,\n    \"timestamp\": \"2025-08-04T06:03:04.970Z\"\n  }\n]", "height": 400, "width": 360}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1280, -80], "id": "a07a3a39-b857-48cb-baa0-3436622a13c5", "name": "Sticky Note3"}, {"parameters": {"content": "api路径：/recommend\n\n创作人：hayden\n\n创建时间：2025-08-04 14:30:02", "height": 180}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2400, 160], "id": "5f849de8-af8e-4c96-b906-7e57e255d666", "name": "Sticky Note4"}], "connections": {"提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "成功数据格式化", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "成功数据格式化": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "格式化ai响应的json数据", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "格式化ai响应的json数据": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "正确金融数据整理", "type": "main", "index": 0}], [{"node": "不相关金融反馈", "type": "main", "index": 0}]]}, "正确金融数据整理": {"main": [[{"node": "获取（短期）access token", "type": "main", "index": 0}]]}, "缓存acc token": {"main": [[{"node": "获取（短期）access token1", "type": "main", "index": 0}]]}, "获取（短期）access token": {"main": [[{"node": "缓存acc token", "type": "main", "index": 0}]]}, "获取（短期）access token1": {"main": [[{"node": "整理基金代码", "type": "main", "index": 0}]]}, "整理基金代码": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}