{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "aefbb3bf-bcba-4b74-90ba-2c2fdc5fad66", "name": "organizationId", "value": "={{ $json.query?.organizationId || $json.body?.organizationId || $json.organizationId || '' }}", "type": "string"}]}, "options": {}}, "id": "479b0b24-d189-435b-9494-bab55a076745", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-4200, 420], "typeVersion": 3.4}, {"parameters": {"jsCode": "/**\n * 智能日期计算器 - 向后兼容的季度报告期日期生成\n * <AUTHOR>\n * @date 2025-07-14T17:03:59.319Z\n * 功能说明：\n * - 根据当前日期智能计算应该使用的季度报告期\n * - 生成向后兼容的日期序列，用于API重试机制\n * - 当前季度 → 上一季度 → 上上季度 → 上上上季度\n * 修改记录：\n * - 2025-07-14: 初始创建，实现智能日期判断和向后兼容\n */\n\ntry {\n  const currentDate = new Date();\n  const currentYear = currentDate.getFullYear();\n  const currentMonth = currentDate.getMonth() + 1; // getMonth() 返回 0-11\n  const currentDay = currentDate.getDate();\n  \n  // 定义季度报告期映射\n  const quarterEndDates = {\n    1: '0331', // Q1: 3月31日\n    2: '0630', // Q2: 6月30日  \n    3: '0930', // Q3: 9月30日\n    4: '1231'  // Q4: 12月31日\n  };\n  \n  // 计算当前应该属于哪个季度\n  let currentQuarter;\n  if (currentMonth <= 3) {\n    currentQuarter = 1;\n  } else if (currentMonth <= 6) {\n    currentQuarter = 2;\n  } else if (currentMonth <= 9) {\n    currentQuarter = 3;\n  } else {\n    currentQuarter = 4;\n  }\n  \n  // 生成向后兼容的日期序列（最多4个季度）\n  const dateSequence = [];\n  let year = currentYear;\n  let quarter = currentQuarter;\n  \n  // 特殊逻辑：如果当前是季度初期（比如7月14日），可能上一季度的数据更可靠\n  // 这里可以根据实际业务需求调整\n  const isEarlyInQuarter = (\n    (currentQuarter === 1 && currentMonth === 1) ||\n    (currentQuarter === 2 && currentMonth === 4) ||\n    (currentQuarter === 3 && currentMonth === 7 && currentDay <= 31) ||\n    (currentQuarter === 4 && currentMonth === 10)\n  );\n  \n  // 如果是季度初期，优先使用上一季度的数据\n  if (isEarlyInQuarter) {\n    quarter = quarter - 1;\n    if (quarter === 0) {\n      quarter = 4;\n      year = year - 1;\n    }\n  }\n  \n  // 生成4个季度的日期序列\n  for (let i = 0; i < 4; i++) {\n    const dateString = `${year}${quarterEndDates[quarter]}`;\n    dateSequence.push({\n      date: dateString,\n      year: year,\n      quarter: quarter,\n      description: `${year}年Q${quarter}`,\n      priority: i + 1\n    });\n    \n    // 计算下一个（实际是上一个）季度\n    quarter = quarter - 1;\n    if (quarter === 0) {\n      quarter = 4;\n      year = year - 1;\n    }\n  }\n  \n  // 构建响应数据\n  const result = {\n    currentDate: currentDate.toISOString(),\n    currentDateString: `${currentYear}${String(currentMonth).padStart(2, '0')}${String(currentDay).padStart(2, '0')}`,\n    recommendedDate: dateSequence[0].date,\n    dateSequence: dateSequence,\n    calculationInfo: {\n      currentYear: currentYear,\n      currentMonth: currentMonth,\n      currentDay: currentDay,\n      currentQuarter: currentQuarter,\n      isEarlyInQuarter: isEarlyInQuarter,\n      adjustedQuarter: dateSequence[0].quarter,\n      adjustedYear: dateSequence[0].year\n    },\n    message: `智能日期计算完成，推荐使用${dateSequence[0].description}数据（${dateSequence[0].date}）`\n  };\n  \n  return result;\n  \n} catch (error) {\n  // 异常处理 - 返回默认的日期序列\n  const currentYear = new Date().getFullYear();\n  const fallbackSequence = [\n    { date: `${currentYear}0630`, year: currentYear, quarter: 2, description: `${currentYear}年Q2`, priority: 1 },\n    { date: `${currentYear}0331`, year: currentYear, quarter: 1, description: `${currentYear}年Q1`, priority: 2 },\n    { date: `${currentYear-1}1231`, year: currentYear-1, quarter: 4, description: `${currentYear-1}年Q4`, priority: 3 },\n    { date: `${currentYear-1}0930`, year: currentYear-1, quarter: 3, description: `${currentYear-1}年Q3`, priority: 4 }\n  ];\n  \n  return {\n    currentDate: new Date().toISOString(),\n    recommendedDate: fallbackSequence[0].date,\n    dateSequence: fallbackSequence,\n    error: error.message,\n    message: `日期计算异常，使用默认日期序列：${fallbackSequence[0].description}（${fallbackSequence[0].date}）`\n  };\n}"}, "id": "fa5538de-70c3-4817-91ab-47de3b1354e5", "name": "智能日期计算", "type": "n8n-nodes-base.code", "position": [-2660, 220], "typeVersion": 2}, {"parameters": {"httpMethod": "POST", "path": "fund_inquiry", "responseMode": "responseNode", "options": {}}, "id": "565d8b8f-3c39-456d-8fcf-da6af56defcc", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-4420, 420], "typeVersion": 2, "webhookId": "e96be27a-08d7-427f-88b4-6ddc8000abf0"}, {"parameters": {"assignments": {"assignments": [{"id": "success-timestamp", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}, {"id": "26beaaf0-687c-4384-83cc-6da613190d9e", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}]}, "options": {}}, "id": "b3d827d3-0495-4d78-bbed-f06448120c76", "name": "成功数据格式化", "type": "n8n-nodes-base.set", "position": [-3540, 320], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 查询组织的公司筛选配置\n-- <AUTHOR>\n-- @date {{ new Date().toISOString() }}\n-- 功能：根据organizationId查询CompanyFilter表获取companyFilterId\n-- 修改记录：添加查询状态返回\n\nWITH query_result AS (\n    SELECT \n        id as \"companyFilterId\",\n        \"organizationId\",\n        \"companyCode\",\n        \"benchmarkCompanyCodes\",\n        \"modifiedAt\",\n        'success' as \"status\"\n    FROM company_filter \n    WHERE \"organizationId\" = '{{ $('成功数据格式化').item.json.organizationId }}'\n    LIMIT 1\n)\nSELECT * FROM query_result\nUNION ALL\nSELECT \n    NULL as \"companyFilterId\",\n    '{{ $('成功数据格式化').item.json.organizationId }}' as \"organizationId\",\n    NULL as \"companyCode\",\n    NULL as \"benchmarkCompanyCodes\",\n    NULL as \"modifiedAt\",\n    'not_found' as \"status\"\nWHERE NOT EXISTS (SELECT 1 FROM query_result);", "options": {}}, "id": "08021c78-eba4-4884-b308-03443e1a5fac", "name": "查询公司筛选配置", "type": "n8n-nodes-base.postgres", "position": [-3320, 320], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "3e650ea3-ab2c-42d5-8358-d72d95bd582e", "name": "检查已存在的投资人标签", "type": "n8n-nodes-base.postgres", "position": [-2000, 120], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "483b01a4-7640-4be3-b930-02b215849968", "name": "查询所有标签", "type": "n8n-nodes-base.postgres", "position": [-5220, 1000], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.pattern && !options.pattern.test(val)) return false;\n      if (options.notEmpty && val.trim() === '') return false;\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) return false;\n      if (options.min !== undefined && num < options.min) return false;\n      if (options.max !== undefined && num > options.max) return false;\n      if (options.integer && !Number.isInteger(num)) return false;\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'organizationId',\n      type: 'string',\n      required: true,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: \"\"\n    }\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value);\n    } else if (type === 'boolean') {\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1;\n      }\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}\n"}, "id": "67a53b7f-93c2-4c0a-a0d6-07be69f4d375", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-3980, 420], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "e0133c89-b1c8-4fc5-8e39-f324d22e268c", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-3760, 420], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "de08934f-0dfe-4a12-96ee-c590b8b99453", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-3540, 520], "typeVersion": 1.1}, {"parameters": {"jsCode": "/**\n * 检查公司筛选配置查询结果\n * <AUTHOR>\n * @date {{ new Date().toISOString() }}\n * @description 检查是否成功获取到公司筛选配置，如果没有则返回错误信息\n */\n\nconst result = $input.item.json;\n\n// 检查是否有查询结果或status为not_found\nif (!result || \n    Object.keys(result).length === 0 || \n    result.status === 'not_found') {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'COMPANY_FILTER_NOT_FOUND',\n      message: '未找到组织的公司筛选配置，请先配置公司代码',\n      field: 'organizationId',\n      value: $('成功数据格式化').item.json.organizationId,\n      timestamp: new Date().toISOString(),\n      type: 'DATA_ERROR'\n    }\n  };\n}\n\n// 如果有查询结果，则返回原始数据并添加验证成功标记\nreturn {\n  ...result,\n  validationResult: 'success'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3100, 320], "id": "350fa732-507b-42e1-b374-ce2f5d65de5a", "name": "检查筛选配置结果"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b832b402-9f27-4203-8f99-a511120985e1", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "contains"}}, {"id": "c9f72fd3-c137-48e8-8378-e58024055520", "leftValue": "", "rightValue": "", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2880, 340], "id": "44fd657d-4538-4d13-8e62-81f2f60c1932", "name": "If1"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 400}}, "id": "0504a0c9-16c4-43b9-90c8-cc8a4e35c6fe", "name": "配置错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-2660, 420], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"message\": \"没有查询投资人数据\",\n  \"dateInfo\":\"{{ $json.dateInfo.date }}\"\n} ", "options": {"responseCode": 200}}, "id": "e15ccb7d-1106-456d-b33e-f1dfa5df39f7", "name": "没有数据响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-2000, 320], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ff1e175-1b12-4c69-839a-b34ba861fee6", "leftValue": "={{ $json.investorTags }}", "rightValue": 0, "operator": {"type": "array", "operation": "lengthGt", "rightType": "number"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2220, 220], "id": "e210fcb6-327e-48f2-8687-674dd4e37318", "name": "判断查询的投资人数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ebf45ca2-97b9-44ca-acb2-88135882f6d8", "leftValue": "={{ $json.needDatabaseUpdate }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1560, 120], "id": "b6e4eae5-8d31-4f36-af9c-439f619ebb71", "name": "If"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').first().json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "8c14c664-eae1-4708-81b6-c1e4362b991e", "name": "创建后检查存在的标签1", "type": "n8n-nodes-base.postgres", "position": [-900, 320], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "0698f7b4-5fed-43de-8f44-c650c490281b", "name": "成功响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-680, 220], "typeVersion": 1.1}, {"parameters": {"jsCode": "/**\n * 准备投资人标签SQL插入语句\n * <AUTHOR>\n * @date 2023-11-28T15:12:34.567Z\n * @description 从智能机构持股查询结果中提取投资人标签数据，并准备SQL插入语句\n * 功能说明：\n * - 获取上游节点中的投资人标签数据\n * - 检查标签数据有效性\n * - 准备批量插入SQL语句\n * - 添加时间戳和组织ID\n * 修改记录：\n * - 2023-11-28: 初始创建，实现SQL准备逻辑\n * - 2023-11-28: 修复SQL字段错误，将createdAt改为modifiedAt\n * - 2023-11-28: 添加companyFilterId字段到SQL语句中\n * - 2023-11-28: 在tagMetadata中添加reportDate字段数据\n * - 2025-07-21: 去重逻辑改为 organizationId + investorCode + companyFilterId + tagName\n */\n\ntry {\n  // 获取上游节点的投资人标签数据\n  const investorTags = $('智能机构持股查询（网络请求）').first().json.investorTags || [];\n\n  // 获取组织ID和公司筛选配置ID\n  const organizationId = $('成功数据格式化').item.json.organizationId || '';\n  const companyFilterId = $('查询公司筛选配置').first().json.companyFilterId || null;\n\n  // 检查数据有效性\n  if (!Array.isArray(investorTags) || investorTags.length === 0) {\n    return {\n      success: false,\n      needDatabaseUpdate: false,\n      message: '没有找到有效的投资人标签数据',\n      sql: '',\n      newTagsCount: 0,\n      newTags: [],\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 获取已存在的标签数据用于去重\n  const existingTagsItems = $('检查已存在的投资人标签').all() || [];\n  const existingTags = existingTagsItems.map(item => item.json);\n  const existingTagMap = new Map();\n\n  // 调试信息 - 检查数据来源\n  console.log('organizationId:', organizationId);\n  console.log('companyFilterId:', companyFilterId);\n  console.log('existingTags数量:', existingTags.length);\n  console.log('investorTags数量:', investorTags.length);\n\n  // 构建已存在标签的映射 - 使用与数据库冲突检查相同的key\n  existingTags.forEach(tag => {\n    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;\n    existingTagMap.set(key, true);\n    console.log('已存在标签key:', key);\n  });\n\n  // 过滤出需要新增的标签\n  const newTags = investorTags.filter(tag => {\n    const key = `${organizationId}_${tag.investorCode}_${tag.tagName}`;\n    const exists = existingTagMap.has(key);\n    console.log('检查标签key:', key, '是否存在:', exists);\n    return !exists;\n  });\n\n  if (newTags.length === 0) {\n    return {\n      success: false,\n      needDatabaseUpdate: false,\n      message: '没有找到新的投资人标签数据',\n      sql: '',\n      newTagsCount: 0,\n      newTags: [],\n      existingTagsCount: investorTags.length,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 准备SQL插入语句\n  const timestamp = new Date().toISOString();\n  const valueStrings = newTags.map(tag => {\n    // 使用UUID生成ID\n    const id = 'cuid_' + Math.random().toString(36).substring(2, 15);\n\n    // 创建包含reportDate的tagMetadata JSON对象\n    const tagMetadata = {\n      reportDate: tag.reportDate || null\n    };\n\n    // 将tagMetadata转换为JSON字符串并转义单引号\n    const tagMetadataJson = JSON.stringify(tagMetadata).replace(/'/g, \"''\");\n\n    // 处理companyFilterId可能为null的情况\n    const filterIdValue = companyFilterId ? `'${companyFilterId}'` : 'NULL';\n\n    return `('${id}', '${tag.investorCode}', '${tag.tagName}', '${tag.tagCategory || 'system'}', '${organizationId}', ${filterIdValue}, '${tagMetadataJson}', '${timestamp}')`;\n  });\n\n  // 修正SQL语句，使用正确的字段名，并添加companyFilterId和tagMetadata\n  const sql = `INSERT INTO investor_tag (\"id\", \"investorCode\", \"tagName\", \"tagCategory\", \"organizationId\", \"companyFilterId\", \"tagMetadata\", \"modifiedAt\") VALUES ${valueStrings.join(', ')} ON CONFLICT (\"organizationId\", \"investorCode\", \"tagName\") DO UPDATE SET \"modifiedAt\" = EXCLUDED.\"modifiedAt\", \"tagMetadata\" = EXCLUDED.\"tagMetadata\";`;\n\n  return {\n    success: true,\n    needDatabaseUpdate: true,\n    message: `发现${newTags.length}条新的投资人标签，需要执行数据库更新`,\n    sql: sql,\n    newTagsCount: newTags.length,\n    newTags: newTags.map(tag => ({\n      investorCode: tag.investorCode,\n      tagName: tag.tagName,\n      tagCategory: tag.tagCategory || 'system',\n      reportDate: tag.reportDate\n    })),\n    totalTagsFromAPI: investorTags.length,\n    existingTagsCount: investorTags.length - newTags.length,\n    companyFilterId: companyFilterId,\n    timestamp: timestamp\n  };\n} catch (error) {\n  return {\n    success: false,\n    needDatabaseUpdate: false,\n    error: {\n      code: 'SQL_PREPARATION_ERROR',\n      message: '准备SQL语句时发生错误',\n      details: error.message || '未知错误',\n      timestamp: new Date().toISOString()\n    }\n  };\n}\n"}, "id": "7ab4ef0e-de3a-48d7-a36c-35d1b1330642", "name": "准备sql1", "type": "n8n-nodes-base.code", "position": [-1780, 120], "typeVersion": 2}, {"parameters": {"operation": "execute<PERSON>uery", "query": "{{ $json.sql }}", "options": {}}, "id": "a3fea6f5-3072-40ea-8727-2cbe5d56d416", "name": "创建标签1", "type": "n8n-nodes-base.postgres", "position": [-1340, 20], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ff1e175-1b12-4c69-839a-b34ba861fee6", "leftValue": "={{ $json.success }}", "rightValue": 0, "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1120, 20], "id": "e0baea34-b260-4c73-8fc7-05741d28837d", "name": "判断查询的投资人数据3"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"message\": \"创建系统标签出错\",\n} ", "options": {"responseCode": 200}}, "id": "a1ae1c85-71a8-4ab3-9e17-8df416682557", "name": "没有数据响应2", "type": "n8n-nodes-base.respondToWebhook", "position": [-900, -80], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据组织ID查询投资人标签\n * <AUTHOR>\n * @time 2023-07-12 15:53:45\n * @description 查询指定组织的所有system和user类型标签\n */\nSELECT \n  \"id\",\n   \"companyFilterId\",\n  \"investorCode\",\n  \"tagName\",\n  \"tagCategory\",\n  \"tagMetadata\",\n  \"modifiedAt\"\nFROM \n  \"investor_tag\"\nWHERE \n  \"organizationId\" = '{{ $('查询公司筛选配置').item.json.organizationId }}'\n  AND \"tagCategory\" IN ('system', 'user')\nORDER BY \n  \"modifiedAt\" DESC;", "options": {}}, "id": "df22346b-daf7-4fc2-a0a7-3a56d196f354", "name": "创建后检查存在的标签2", "type": "n8n-nodes-base.postgres", "position": [-900, 120], "typeVersion": 2.4, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "/**\n * 智能机构持股查询 - 固定数据版本\n * <AUTHOR>\n * @date 2025-01-30T16:47:31.000Z\n * @description 使用固定JSON数据替代API请求，模拟机构持股数据查询，避免重复数据问题\n * @modified hayden 2025-01-30T16:47:31.000Z 修复本司和对标公司数据重复问题，确保数据独立性\n * @modified hayden 2025-01-30T16:47:31.000Z 移除和佳医疗数据，仅保留万科A和一品红的真实数据\n */\n\ntry {\n  // 获取输入数据\n  const items = $input.all();\n  \n  // 获取前置节点的数据\n  const companyCode = $('查询公司筛选配置').item.json.companyCode || '000002.SZ';\n  const benchmarkCompanyCodes = $('查询公司筛选配置').item.json.benchmarkCompanyCodes || '';\n  const dateSequence = $('智能日期计算').item.json.dateSequence || [];\n  const organizationId = $('成功数据格式化').item.json.organizationId || '';\n\n  // 验证必要参数\n  if (!companyCode) {\n    throw new Error('缺少companyCode，请检查公司筛选配置');\n  }\n  \n  if (!dateSequence || dateSequence.length === 0) {\n    throw new Error('缺少dateSequence，请检查日期计算结果');\n  }\n  \n  // 万科A(000002.SZ)固定数据\n  const wankeData = [\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"512200.SH\",\n      \"shareholderName\": \"南方中证全指房地产交易型开放式指数证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 86859889,\n      \"percentageOfCirculatingShares\": 0.8939,\n      \"managementCompany\": \"南方基金管理股份有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"161721.SZ\",\n      \"shareholderName\": \"招商沪深300地产等权重指数证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 41641358,\n      \"percentageOfCirculatingShares\": 0.4285,\n      \"managementCompany\": \"招商基金管理有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"159768.SZ\",\n      \"shareholderName\": \"银华中证内地地产主题交易型开放式指数证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 14073379,\n      \"percentageOfCirculatingShares\": 0.1448,\n      \"managementCompany\": \"银华基金管理股份有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"159707.SZ\",\n      \"shareholderName\": \"华宝中证800地产交易型开放式指数证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 10296711,\n      \"percentageOfCirculatingShares\": 0.106,\n      \"managementCompany\": \"华宝基金管理有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"070011.OF\",\n      \"shareholderName\": \"嘉实策略增长混合型证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 10000031,\n      \"percentageOfCirculatingShares\": 0.1029,\n      \"managementCompany\": \"嘉实基金管理有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"515060.SH\",\n      \"shareholderName\": \"华夏中证全指房地产交易型开放式指数证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 8944962,\n      \"percentageOfCirculatingShares\": 0.0921,\n      \"managementCompany\": \"华夏基金管理有限公司\"\n    },\n    {\n      \"code\": \"000002.SZ\",\n      \"name\": \"万科A\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"160218.SZ\",\n      \"shareholderName\": \"国泰国证房地产行业指数证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 7658629,\n      \"percentageOfCirculatingShares\": 0.0788,\n      \"managementCompany\": \"国泰基金管理有限公司\"\n    }\n  ];\n\n  // 一品红(300723.SZ)完整固定数据\n  const yipinhongData = [\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"110023.OF\",\n      \"shareholderName\": \"易方达医疗保健行业混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 4366084,\n      \"percentageOfCirculatingShares\": 1.0454,\n      \"managementCompany\": \"易方达基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"010387.OF\",\n      \"shareholderName\": \"易方达医药生物股票型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 2783369,\n      \"percentageOfCirculatingShares\": 0.6664,\n      \"managementCompany\": \"易方达基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"022286.OF\",\n      \"shareholderName\": \"长城医药产业精选混合型发起式证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1962800,\n      \"percentageOfCirculatingShares\": 0.47,\n      \"managementCompany\": \"长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"010709.OF\",\n      \"shareholderName\": \"安信医药健康主题股票型发起式证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1855060,\n      \"percentageOfCirculatingShares\": 0.4442,\n      \"managementCompany\": \"安信基金管理有限责任公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000418.OF\",\n      \"shareholderName\": \"景顺长城成长之星股票型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1689608,\n      \"percentageOfCirculatingShares\": 0.4046,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"013812.OF\",\n      \"shareholderName\": \"景顺长城景气进取混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1636200,\n      \"percentageOfCirculatingShares\": 0.3918,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000242.OF\",\n      \"shareholderName\": \"景顺长城策略精选灵活配置混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1576500,\n      \"percentageOfCirculatingShares\": 0.3775,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"012373.OF\",\n      \"shareholderName\": \"富国稳健恒盛12个月持有期混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1171280,\n      \"percentageOfCirculatingShares\": 0.2804,\n      \"managementCompany\": \"富国基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000220.OF\",\n      \"shareholderName\": \"富国医疗保健行业混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 1017500,\n      \"percentageOfCirculatingShares\": 0.2436,\n      \"managementCompany\": \"富国基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"013037.OF\",\n      \"shareholderName\": \"长城大健康混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 854400,\n      \"percentageOfCirculatingShares\": 0.2046,\n      \"managementCompany\": \"长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000634.OF\",\n      \"shareholderName\": \"富国天盛灵活配置混合型证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 816700,\n      \"percentageOfCirculatingShares\": 0.1955,\n      \"managementCompany\": \"富国基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"011673.OF\",\n      \"shareholderName\": \"长城医药科技六个月持有期混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 736200,\n      \"percentageOfCirculatingShares\": 0.1763,\n      \"managementCompany\": \"长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"012159.OF\",\n      \"shareholderName\": \"财通资管健康产业混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 720000,\n      \"percentageOfCirculatingShares\": 0.1724,\n      \"managementCompany\": \"财通证券资产管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"022852.OF\",\n      \"shareholderName\": \"中航优选领航混合型发起式证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 710000,\n      \"percentageOfCirculatingShares\": 0.17,\n      \"managementCompany\": \"中航基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000739.OF\",\n      \"shareholderName\": \"平安新鑫先锋混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 654000,\n      \"percentageOfCirculatingShares\": 0.1566,\n      \"managementCompany\": \"平安基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"003504.OF\",\n      \"shareholderName\": \"景顺长城景颐丰利债券型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 602150,\n      \"percentageOfCirculatingShares\": 0.1442,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"001194.OF\",\n      \"shareholderName\": \"景顺长城稳健回报灵活配置混合型证券投资基金A类\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 596719,\n      \"percentageOfCirculatingShares\": 0.1429,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"000339.OF\",\n      \"shareholderName\": \"长城医疗保健混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 595800,\n      \"percentageOfCirculatingShares\": 0.1427,\n      \"managementCompany\": \"长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"002770.OF\",\n      \"shareholderName\": \"安信新回报灵活配置混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 530000,\n      \"percentageOfCirculatingShares\": 0.1269,\n      \"managementCompany\": \"安信基金管理有限责任公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"011876.OF\",\n      \"shareholderName\": \"景顺长城医疗健康混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 476500,\n      \"percentageOfCirculatingShares\": 0.1141,\n      \"managementCompany\": \"景顺长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"008786.OF\",\n      \"shareholderName\": \"长城健康生活灵活配置混合型证券投资基金A\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 455700,\n      \"percentageOfCirculatingShares\": 0.1091,\n      \"managementCompany\": \"长城基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"010434.OF\",\n      \"shareholderName\": \"红土创新医疗保健股票型发起式证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 442600,\n      \"percentageOfCirculatingShares\": 0.106,\n      \"managementCompany\": \"红土创新基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"005549.OF\",\n      \"shareholderName\": \"富国成长优选三年定期开放灵活配置混合型证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 428800,\n      \"percentageOfCirculatingShares\": 0.1027,\n      \"managementCompany\": \"富国基金管理有限公司\"\n    },\n    {\n      \"code\": \"300723.SZ\",\n      \"name\": \"一品红\",\n      \"reportDate\": \"2025-06-30\",\n      \"shareholderCode\": \"009162.OF\",\n      \"shareholderName\": \"富国医药成长30股票型证券投资基金\",\n      \"institutionType\": \"基金\",\n      \"sharesHeld\": 421720,\n      \"percentageOfCirculatingShares\": 0.101,\n      \"managementCompany\": \"富国基金管理有限公司\"\n    }\n  ];\n  \n  /**\n   * 根据公司代码获取对应的固定数据\n   * @description 根据股票代码返回对应的机构持股数据，支持万科A和一品红，增加代码容错处理\n   * @param {string} code - 股票代码\n   * @returns {Array} 对应的机构持股数据数组\n   * <AUTHOR>\n   * @created 2025-01-30T16:47:31.000Z\n   * @modified hayden 2025-07-30T18:09:33.000Z 增加代码容错，支持更多股票代码映射\n   */\n  function getFixedDataByCode(code) {\n    const supportedCodes = {\n      '000002.SZ': wankeData,\n      '300723.SZ': yipinhongData,\n      // 添加代码容错映射\n      '300273.SZ': yipinhongData  // 如果查询300273.SZ，使用一品红数据\n    };\n    \n    if (supportedCodes[code]) {\n      console.log(`找到股票代码 ${code} 的数据，记录数: ${supportedCodes[code].length}`);\n      return supportedCodes[code];\n    } else {\n      console.log(`未找到股票代码 ${code} 的数据，当前仅支持: ${Object.keys(supportedCodes).join(', ')}`);\n      // 如果找不到对应代码，根据代码特征返回默认数据\n      if (code.startsWith('000')) {\n        console.log(`使用万科A数据作为${code}的默认数据`);\n        return wankeData;\n      } else if (code.startsWith('300')) {\n        console.log(`使用一品红数据作为${code}的默认数据`);\n        return yipinhongData;\n      }\n      return [];\n    }\n  }\n  \n  /**\n   * 筛选基金类型数据\n   * @description 从数据中筛选出机构类型为\"基金\"的记录\n   * @param {Array} data - 原始数据数组\n   * @returns {Array} 筛选后的基金数据数组\n   * <AUTHOR>\n   * @created 2025-01-30T16:47:31.000Z\n   */\n  function filterFundData(data) {\n    if (!Array.isArray(data)) {\n      return [];\n    }\n    \n    return data.filter(record => {\n      return record.institutionType === '基金';\n    });\n  }\n  \n  // 创建投资人标签数据\n  const investorTags = [];\n  const currentTimestamp = new Date().toISOString();\n  \n  // 模拟成功找到数据的日期信息\n  const successDateInfo = dateSequence[0] || {\n    date: '20250630',\n    quarter: 'Q2',\n    year: '2025',\n    description: '2025年第二季度'\n  };\n  \n  // 添加调试信息\n  console.log(`=== 智能机构持股查询开始 ===`);\n  console.log(`本司代码: ${companyCode}`);\n  console.log(`对标公司代码原始值: \"${benchmarkCompanyCodes}\"`);\n  console.log(`对标公司代码类型: ${typeof benchmarkCompanyCodes}`);\n  console.log(`对标公司代码长度: ${benchmarkCompanyCodes.length}`);\n  \n  // 获取本公司数据\n  const companyData = getFixedDataByCode(companyCode);\n  const fundData = filterFundData(companyData);\n  \n  // 处理本公司数据\n  if (fundData.length > 0) {\n    console.log(`本司(${companyCode})使用固定数据，记录数: ${fundData.length}`);\n    \n    // 为每个基金记录创建投资人标签\n    fundData.forEach(record => {\n      if (record.shareholderCode) {\n        investorTags.push({\n          investorCode: record.shareholderCode,\n          fundInfo: record.shareholderName || record.shareholderCode,\n          tagName: `持有本司(${companyCode})`,\n          tagCategory: \"system\",\n          modifiedAt: currentTimestamp,\n          reportDate: record.reportDate || '2025-06-30'\n        });\n      }\n    });\n  }\n  \n  // 处理对标公司数据\n  console.log(`=== 开始处理对标公司数据 ===`);\n  \n  let benchmarkCodes = [];\n  \n  if (benchmarkCompanyCodes && benchmarkCompanyCodes.trim() !== '') {\n    // 如果有对标公司代码，解析它\n    benchmarkCodes = typeof benchmarkCompanyCodes === 'string' \n      ? benchmarkCompanyCodes.split(',').map(code => code.trim()).filter(code => code)\n      : Array.isArray(benchmarkCompanyCodes) ? benchmarkCompanyCodes : [];\n    console.log(`从配置中解析到对标公司代码:`, benchmarkCodes);\n  } else {\n    // 如果没有对标公司代码，使用默认值\n    benchmarkCodes = ['300723.SZ'];\n    console.log(`使用默认对标公司代码:`, benchmarkCodes);\n  }\n  \n  // 处理每个对标公司\n  if (benchmarkCodes.length > 0) {\n    benchmarkCodes.forEach(benchmarkCode => {\n      if (!benchmarkCode) {\n        return;\n      }\n      \n      console.log(`正在处理对标公司: ${benchmarkCode}`);\n      \n      // 获取对标公司数据\n      const benchmarkData = getFixedDataByCode(benchmarkCode);\n      const benchmarkFundData = filterFundData(benchmarkData);\n      \n      console.log(`对标公司(${benchmarkCode})数据记录数: ${benchmarkFundData.length}`);\n      \n      // 为对标公司的每个基金记录创建投资人标签\n      benchmarkFundData.forEach(record => {\n        if (record.shareholderCode) {\n          investorTags.push({\n            investorCode: record.shareholderCode,\n            fundInfo: record.shareholderName || record.shareholderCode,\n            tagName: `持有对标(${benchmarkCode})`,\n            tagCategory: \"system\",\n            modifiedAt: currentTimestamp,\n            reportDate: record.reportDate || '2025-06-30'\n          });\n        }\n      });\n      \n      console.log(`对标公司(${benchmarkCode})添加标签数: ${benchmarkFundData.length}`);\n    });\n  } else {\n    console.log(`未找到有效的对标公司代码`);\n  }\n  \n  // 统计标签数量\n  const companyTags = investorTags.filter(tag => tag.tagName.includes('持有本司'));\n  const benchmarkTags = investorTags.filter(tag => tag.tagName.includes('持有对标'));\n  \n  console.log(`=== 处理完成统计 ===`);\n  console.log(`本司标签数: ${companyTags.length}`);\n  console.log(`对标标签数: ${benchmarkTags.length}`);\n  console.log(`总标签数: ${investorTags.length}`);\n  \n  // 准备返回结果对象\n  const result = {\n    organizationId: organizationId,\n    dateInfo: successDateInfo,\n    investorTags: investorTags,\n    attemptedDates: [successDateInfo.date],\n    hasValidData: fundData.length > 0,\n    dataSource: 'fixed_hardcoded',\n    totalRecords: fundData.length,\n    // 添加调试信息到返回结果\n    debug: {\n      companyCode: companyCode,\n      benchmarkCompanyCodes: benchmarkCompanyCodes,\n      parsedBenchmarkCodes: benchmarkCodes,\n      companyTagsCount: companyTags.length,\n      benchmarkTagsCount: benchmarkTags.length\n    }\n  };\n  \n  console.log(`处理完成，生成投资人标签数: ${investorTags.length}`);\n  \n  return [{\n    json: result\n  }];\n  \n} catch (error) {\n  // 详细的错误处理\n  console.error(`智能机构持股查询失败: ${error.message}`);\n  \n  // 构建简化的错误响应\n  const errorResponse = {\n    error: true,\n    errorMessage: error.message,\n    organizationId: $('成功数据格式化').item?.json?.organizationId || '',\n    dateInfo: null,\n    investorTags: [],\n    dataSource: 'fixed_hardcoded'\n  };\n  \n  return [{\n    json: errorResponse\n  }];\n}\n"}, "id": "fb1ca4aa-e5c7-49ae-83ce-521c5034fb36", "name": "智能机构持股查询（网络请求）", "type": "n8n-nodes-base.code", "position": [-2440, 220], "typeVersion": 2}, {"parameters": {"content": "相关人员：hayden jason\n\napi地址：/fund_inquiry\n\n记录时间 ：2025-07-31 \n\n相关文档：https://starlinkcap.feishu.cn/wiki/BhiEwGtgzi5mxFkAAJ4cA6g6nQf", "height": 220, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-4840, 360], "id": "9306fb65-6972-4a69-a0a7-027d78d9c244", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "返回示例：\n[\n    {\n        \"id\": \"cuid_1vc7xb0mith\",\n        \"companyFilterId\": \"cmcvxajuq0001hvkgq75ovzro\",\n        \"investorCode\": \"510300.SH\",\n        \"tagName\": \"持有本司(000002.SZ)\",\n        \"tagCategory\": \"system\",\n        \"tagMetadata\": {\n            \"reportDate\": \"2025-03-31\"\n        },\n        \"modifiedAt\": \"2025-07-14T12:01:53.824Z\"\n    },\n    {\n        \"id\": \"cuid_itf9jbxpb3l\",\n        \"companyFilterId\": \"cmcvxajuq0001hvkgq75ovzro\",\n        \"investorCode\": \"512200.SH\",\n        \"tagName\": \"持有本司(000002.SZ)\",\n        \"tagCategory\": \"system\",\n        \"tagMetadata\": {\n            \"reportDate\": \"2025-03-31\"\n        },\n        \"modifiedAt\": \"2025-07-14T12:01:53.824Z\"\n    }\n ]", "height": 480, "width": 460}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-440, 180], "id": "5000187c-b2fb-4c72-94ee-213b7353fab7", "name": "Sticky Note1"}, {"parameters": {"content": "获取组织id", "height": 80, "width": 160}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-4440, 320], "id": "1cfcccfa-8761-4422-b328-a324f6207b01", "name": "Sticky Note2"}, {"parameters": {"content": "查询组织的公司筛选配置\n功能：根据organizationId查询CompanyFilter表获取companyFilterId", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3420, 220], "id": "d02bbd71-4981-4583-9451-11f925a4addb", "name": "Sticky Note3"}, {"parameters": {"content": "目前没有机构数据\n\n固定了两组数据：300723、000002"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2340, -140], "id": "e47a3c3f-831a-46f5-8c81-944a439388e6", "name": "Sticky Note4"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：验证数据", "height": 140, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3980, 280], "id": "5a7715be-b7a8-4205-b27c-02dce7d3cbe5", "name": "Sticky Note5"}, {"parameters": {"content": "提取设计api所需要的参数", "height": 140, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-4280, 280], "id": "79daf1a3-6f2e-416d-b53e-f38bdfe41d02", "name": "Sticky Note6"}, {"parameters": {"content": "检查公司筛选配置查询结果\n检查是否成功获取到公司筛选配置，如果没有则返回错误信息"}, "type": "n8n-nodes-base.stickyNote", "position": [-2960, 160], "typeVersion": 1, "id": "e8831156-8f13-4c92-9a44-4a019186c08b", "name": "Sticky Note7"}, {"parameters": {"content": "智能机构持股查询 - 固定数据版本\n使用固定JSON数据替代API请求，模拟机构持股数据查询，避免重复数据问题\n修复本司和对标公司数据重复问题，确保数据独立性"}, "type": "n8n-nodes-base.stickyNote", "position": [-2440, 360], "typeVersion": 1, "id": "ee0f7c82-5482-4e05-8284-82679d876e8e", "name": "Sticky Note8"}, {"parameters": {"content": "智能日期计算器 - 向后兼容的季度报告期日期生成\n功能说明：\n - 根据当前日期智能计算应该使用的季度报告期\n  - 生成向后兼容的日期序列，用于API重试机制\n - 当前季度 → 上一季度 → 上上季度 → 上上上季度", "width": 340}, "type": "n8n-nodes-base.stickyNote", "position": [-2680, 40], "typeVersion": 1, "id": "fca14b96-4ed4-4abe-8541-520a825fd694", "name": "Sticky Note9"}, {"parameters": {"content": "根据组织ID查询投资人标签\n查询指定组织的所有system和user类型标签"}, "type": "n8n-nodes-base.stickyNote", "position": [-2080, -40], "typeVersion": 1, "id": "4b536015-76a7-4ff5-8b26-845365782ee8", "name": "Sticky Note10"}, {"parameters": {"content": "准备投资人标签SQL插入语句\n从智能机构持股查询结果中提取投资人标签数据，并准备SQL插入语句"}, "type": "n8n-nodes-base.stickyNote", "position": [-1820, -40], "typeVersion": 1, "id": "5739cc70-ddb4-4e35-8dde-9e17ee46ba02", "name": "Sticky Note11"}], "connections": {"提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "智能日期计算": {"main": [[{"node": "智能机构持股查询（网络请求）", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "成功数据格式化": {"main": [[{"node": "查询公司筛选配置", "type": "main", "index": 0}]]}, "查询公司筛选配置": {"main": [[{"node": "检查筛选配置结果", "type": "main", "index": 0}]]}, "检查已存在的投资人标签": {"main": [[{"node": "准备sql1", "type": "main", "index": 0}]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "成功数据格式化", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "检查筛选配置结果": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "智能日期计算", "type": "main", "index": 0}], [{"node": "配置错误响应", "type": "main", "index": 0}]]}, "判断查询的投资人数据": {"main": [[{"node": "检查已存在的投资人标签", "type": "main", "index": 0}], [{"node": "没有数据响应", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "创建标签1", "type": "main", "index": 0}], [{"node": "创建后检查存在的标签1", "type": "main", "index": 0}]]}, "创建后检查存在的标签1": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}, "准备sql1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "创建标签1": {"main": [[{"node": "判断查询的投资人数据3", "type": "main", "index": 0}]]}, "判断查询的投资人数据3": {"main": [[{"node": "创建后检查存在的标签2", "type": "main", "index": 0}], [{"node": "没有数据响应2", "type": "main", "index": 0}]]}, "创建后检查存在的标签2": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}, "智能机构持股查询（网络请求）": {"main": [[{"node": "判断查询的投资人数据", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}