{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "extract-keywords", "name": "fund_code", "value": "={{ $json.query?.fund_code || $json.body?.fund_code || $json.fund_code || '' }}", "type": "string"}, {"id": "extract-webhook-url", "name": "webhookUrl", "value": "={{ $json.webhookUrl || '' }}", "type": "string"}, {"id": "add-timestamp", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "7f2e0ad0-aa7d-446a-9f01-9032decbcb5c", "name": "shareholder_name", "value": "={{ $json.query?.shareholder_name || $json.body?.shareholder_name || $json.shareholder_name || '' }}", "type": "string"}, {"id": "c9d07d43-50b9-4aec-adfb-a303e3571969", "name": "organizationId", "value": "={{ $json.query?.organizationId || $json.body?.organizationId || $json.organizationId || '' }}", "type": "string"}]}, "options": {}}, "id": "2f82b4e0-ecc5-44ec-bba3-7def144793f4", "name": "提取输入参数", "type": "n8n-nodes-base.set", "position": [-1560, -60], "typeVersion": 3.4, "notesInFlow": false}, {"parameters": {"jsCode": "/**\n * 通用参数验证器 - 支持多种数据类型的灵活验证\n * <AUTHOR>\n * @date 2025-07-04T15:22:15.935Z\n * 修改记录：\n * - 设计通用验证规则配置\n * - 支持string、number、array、object、boolean等类型\n * - 支持可选参数和默认值\n * - 支持自定义验证函数\n * - 极简配置，高度复用\n */\n\ntry {\n  // 安全的数据获取\n  const inputData = $input.all();\n  if (!inputData || inputData.length === 0) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'NO_INPUT_DATA',\n        message: '未接收到任何输入数据',\n        field: 'input',\n        value: null,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  const jsonData = inputData[0]?.json;\n  if (!jsonData || typeof jsonData !== 'object') {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_INPUT_FORMAT',\n        message: '输入数据格式无效，期望JSON对象',\n        field: 'input',\n        value: jsonData,\n        timestamp: new Date().toISOString(),\n        type: 'SYSTEM_ERROR'\n      }\n    };\n  }\n\n  // 通用验证器函数\n  const validators = {\n    string: (val, options = {}) => {\n      if (typeof val !== 'string') return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.pattern && !options.pattern.test(val)) return false;\n      if (options.notEmpty && val.trim() === '') return false;\n      return true;\n    },\n    \n    number: (val, options = {}) => {\n      const num = Number(val);\n      if (isNaN(num)) return false;\n      if (options.min !== undefined && num < options.min) return false;\n      if (options.max !== undefined && num > options.max) return false;\n      if (options.integer && !Number.isInteger(num)) return false;\n      return true;\n    },\n    \n    array: (val, options = {}) => {\n      if (!Array.isArray(val)) return false;\n      if (options.minLength && val.length < options.minLength) return false;\n      if (options.maxLength && val.length > options.maxLength) return false;\n      if (options.itemType) {\n        return val.every(item => validators[options.itemType](item, options.itemOptions || {}));\n      }\n      return true;\n    },\n    \n    object: (val, options = {}) => {\n      if (typeof val !== 'object' || val === null || Array.isArray(val)) return false;\n      if (options.requiredKeys) {\n        return options.requiredKeys.every(key => key in val);\n      }\n      return true;\n    },\n    \n    boolean: (val) => {\n      return typeof val === 'boolean' || val === 'true' || val === 'false' || val === 1 || val === 0;\n    },\n    \n    email: (val) => {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      return typeof val === 'string' && emailRegex.test(val);\n    },\n    \n    url: (val) => {\n      try {\n        new URL(val);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n  };\n\n  // 🔥 核心配置区域 - 只需要在这里配置参数规则 🔥\n  const validationConfig = [\n    {\n      field: 'organizationId',\n      type: 'string',\n      required: true,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    },\n    {\n      field: 'fund_code',\n      type: 'string',\n      required: false,\n      options: { notEmpty: true, minLength: 1 },\n      defaultValue: null\n    },\n    {\n      field: 'shareholder_name',\n      type: 'string', \n      required: false,\n      options: { notEmpty: true },\n      defaultValue: null\n    }\n  ];\n\n  // 通用验证执行器\n  const processedData = {\n    validationResult: 'success',\n    timestamp: jsonData.timestamp || new Date().toISOString()\n  };\n\n  for (const config of validationConfig) {\n    const { field, type, required, options = {}, defaultValue } = config;\n    let value = jsonData[field];\n\n    // 处理缺失值\n    if (value === undefined || value === null || value === '') {\n      if (required) {\n        return {\n          validationResult: 'error',\n          error: {\n            code: `MISSING_${field.toUpperCase()}`,\n            message: `缺少必需参数：${field}。请提供${field}参数。`,\n            field: field,\n            value: value,\n            timestamp: processedData.timestamp,\n            type: 'VALIDATION_ERROR'\n          }\n        };\n      } else {\n        // 使用默认值\n        processedData[field] = defaultValue;\n        continue;\n      }\n    }\n\n    // 类型转换处理\n    if (type === 'number' && typeof value === 'string') {\n      value = Number(value);\n    } else if (type === 'boolean') {\n      if (typeof value === 'string') {\n        value = value.toLowerCase() === 'true' || value === '1';\n      } else if (typeof value === 'number') {\n        value = value === 1;\n      }\n    }\n\n    // 执行验证\n    const validator = validators[type];\n    if (!validator) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: 'UNSUPPORTED_TYPE',\n          message: `不支持的参数类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'SYSTEM_ERROR'\n        }\n      };\n    }\n\n    const isValid = validator(value, options);\n    if (!isValid) {\n      return {\n        validationResult: 'error',\n        error: {\n          code: `INVALID_${field.toUpperCase()}`,\n          message: `${field}参数验证失败。期望类型：${type}`,\n          field: field,\n          value: value,\n          timestamp: processedData.timestamp,\n          type: 'VALIDATION_ERROR'\n        }\n      };\n    }\n\n    // 验证通过，保存处理后的值\n    processedData[field] = value;\n  }\n\n  return processedData;\n\n} catch (error) {\n  // 全局异常捕获\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'UNEXPECTED_ERROR',\n      message: '系统发生未预期的错误',\n      field: 'system',\n      value: error.message || '未知错误',\n      timestamp: new Date().toISOString(),\n      type: 'SYSTEM_ERROR'\n    }\n  };\n}\n"}, "id": "7d4e6bab-8cce-4a3b-b08d-02262b09a5d5", "name": "参数验证逻辑", "type": "n8n-nodes-base.code", "position": [-1340, -60], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.validationResult }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "6094fa58-92ab-461a-9371-b4e836a50d16", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1120, -60], "typeVersion": 2}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "45500002-daad-41f3-8ade-3039b5d00574", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "position": [200, -360], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "fund_institution", "responseMode": "responseNode", "options": {}}, "id": "442771dc-fa94-466e-9bca-3c61821e8003", "name": "api统一错误响应模板1", "type": "n8n-nodes-base.webhook", "position": [-1780, -60], "typeVersion": 2, "webhookId": "737d1017-057f-4799-8185-b6bf9cff8dc8"}, {"parameters": {"content": "提取设计api所需要的参数：如关键字、日期、分页等", "height": 260, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1700, -160], "id": "cf40bcf3-83ca-41b9-a29d-74ebc31f454b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "添加验证字段参数api所需要的参数：如关键字是否字符串、日期是否是日期、分页是否是整数", "height": 260, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1380, -160], "id": "4936c3de-96f1-468b-b01e-b6898d12f750", "name": "Sticky Note1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据基金信息动态查询对应的股东名册记录 - n8n版本（优化托管人匹配）\n * <AUTHOR>\n * @created 2025-01-27 10:30:00\n * @updated 2025-01-27 14:30:00 hayden 优化托管人匹配策略，支持更灵活的双向模糊匹配\n * @description 通过基金全称和托管人信息动态匹配股东名册中的基金股东记录，增强托管人匹配兼容性\n */\nWITH input_params AS (\n  -- 输入参数预处理\n  SELECT \n    '{{ $json[\"organizationid\"] }}' as organization_id,\n    '{{ $json[\"fund_full_name\"] }}' as target_fund_name,\n    '{{ $json[\"custodian\"] }}' as target_custodian,\n    -- 提取托管人核心名称（去除\"股份有限公司\"等后缀）\n    CASE \n      WHEN '{{ $json[\"custodian\"] }}' LIKE '%银行%' THEN\n        REGEXP_REPLACE(\n          REGEXP_REPLACE('{{ $json[\"custodian\"] }}', '股份有限公司$', ''),\n          '有限公司$', ''\n        )\n      ELSE '{{ $json[\"custodian\"] }}'\n    END as custodian_core_name\n),\nfund_shareholders AS (\n  SELECT \n    s.id,\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\",\n    s.\"contactNumber\",\n    s.\"contactAddress\",\n    -- 解析托管人部分（－之前的部分）\n    CASE \n      WHEN POSITION('－' IN s.\"securitiesAccountName\") > 0 \n      THEN TRIM(SUBSTRING(s.\"securitiesAccountName\" FROM 1 FOR POSITION('－' IN s.\"securitiesAccountName\") - 1))\n      ELSE NULL \n    END AS custodian_part,\n    -- 解析基金全称部分（－之后的部分）\n    CASE \n      WHEN POSITION('－' IN s.\"securitiesAccountName\") > 0 \n      THEN TRIM(SUBSTRING(s.\"securitiesAccountName\" FROM POSITION('－' IN s.\"securitiesAccountName\") + 1))\n      ELSE NULL \n    END AS fund_name_part\n  FROM \"shareholder\" s\n  CROSS JOIN input_params ip\n  WHERE s.\"organizationId\" = ip.organization_id\n    AND s.\"securitiesAccountName\" LIKE '%－%'  -- 只查询包含分隔符的基金股东\n),\nmatched_shareholders AS (\n  SELECT \n    fs.*,\n    ip.target_fund_name,\n    ip.target_custodian,\n    ip.custodian_core_name,\n    -- 计算托管人匹配分数\n    CASE \n      -- 精确匹配\n      WHEN fs.custodian_part = ip.target_custodian THEN 100\n      -- 输入托管人包含股东名册中的托管人（如：中国光大银行股份有限公司 包含 光大银行）\n      WHEN ip.target_custodian ILIKE '%' || fs.custodian_part || '%' THEN 95\n      -- 股东名册中的托管人包含输入托管人\n      WHEN fs.custodian_part ILIKE '%' || ip.target_custodian || '%' THEN 90\n      -- 核心名称匹配（如：中国光大银行 匹配 光大银行）\n      WHEN ip.custodian_core_name ILIKE '%' || fs.custodian_part || '%' THEN 88\n      WHEN fs.custodian_part ILIKE '%' || ip.custodian_core_name || '%' THEN 85\n      -- 关键词部分匹配（至少3个字符匹配）\n      WHEN LENGTH(fs.custodian_part) >= 3 \n           AND (ip.target_custodian ILIKE '%' || SUBSTRING(fs.custodian_part FROM 1 FOR 3) || '%'\n                OR ip.custodian_core_name ILIKE '%' || SUBSTRING(fs.custodian_part FROM 1 FOR 3) || '%') THEN 80\n      ELSE 0\n    END as custodian_match_score,\n    -- 基金名称匹配分数\n    CASE \n      WHEN fs.fund_name_part = ip.target_fund_name THEN 100\n      ELSE 0\n    END as fund_match_score\n  FROM fund_shareholders fs\n  CROSS JOIN input_params ip\n  WHERE \n    -- 基金全称必须精确匹配\n    fs.fund_name_part = ip.target_fund_name\n    AND (\n      -- 托管人多层级模糊匹配\n      fs.custodian_part = ip.target_custodian\n      OR ip.target_custodian ILIKE '%' || fs.custodian_part || '%'\n      OR fs.custodian_part ILIKE '%' || ip.target_custodian || '%'\n      OR ip.custodian_core_name ILIKE '%' || fs.custodian_part || '%'\n      OR fs.custodian_part ILIKE '%' || ip.custodian_core_name || '%'\n      OR (LENGTH(fs.custodian_part) >= 3 \n          AND (ip.target_custodian ILIKE '%' || SUBSTRING(fs.custodian_part FROM 1 FOR 3) || '%'\n               OR ip.custodian_core_name ILIKE '%' || SUBSTRING(fs.custodian_part FROM 1 FOR 3) || '%'))\n    )\n)\nSELECT \n  ms.id,\n  ms.\"shareholderId\",\n  ms.\"unifiedAccountNumber\", \n  ms.\"securitiesAccountName\",\n  ms.\"numberOfShares\",\n  ms.\"shareholdingRatio\",\n  ms.\"registerDate\",\n  ms.\"contactNumber\",\n  ms.\"contactAddress\",\n  ms.custodian_part,\n  ms.fund_name_part,\n  ms.custodian_match_score,\n  ms.fund_match_score,\n  (ms.custodian_match_score + ms.fund_match_score) as total_match_score,\n  -- 优化后的匹配状态标识\n  CASE \n    WHEN ms.fund_match_score = 100 AND ms.custodian_match_score >= 95 THEN 'EXACT_MATCH'\n    WHEN ms.fund_match_score = 100 AND ms.custodian_match_score >= 85 THEN 'HIGH_CONFIDENCE_MATCH'\n    WHEN ms.fund_match_score = 100 AND ms.custodian_match_score >= 80 THEN 'GOOD_MATCH'\n    WHEN ms.fund_match_score = 100 THEN 'FUND_MATCH_ONLY'\n    ELSE 'PARTIAL_MATCH'\n  END AS match_status,\n  ms.target_custodian as input_custodian,\n  ms.target_fund_name as input_fund_name\nFROM matched_shareholders ms\nWHERE ms.custodian_match_score >= 80  -- 只返回托管人匹配度较高的结果\nORDER BY \n  total_match_score DESC,\n  ms.\"numberOfShares\" DESC,\n  ms.\"registerDate\" DESC;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-240, -260], "id": "0444095f-8889-47a2-9570-033decfa2aea", "name": "基金名称匹配股东名称", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"code\":0,\n  \"message\": \"没有找到该基金\"\n}", "options": {"responseCode": 200}}, "id": "4e4d2d0d-5387-4bc7-b1b3-a7d20b232b3e", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [200, -160], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"code\":0,\n  \"message\": \"没有找到该基金\"\n}", "options": {"responseCode": 200}}, "id": "9ff8ae64-ce19-42b0-9b45-a90fb81c655f", "name": "错误响应2", "type": "n8n-nodes-base.respondToWebhook", "position": [-240, -460], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "d696bb8a-56ce-4e74-943d-11970c4ee26a", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-900, 40], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "check-validation-result", "leftValue": "={{ $json.fund_code }}", "rightValue": "success", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "fc4ce6f9-85b9-4ba0-8eb2-fcecac3a8902", "name": "查基金还是查股东", "type": "n8n-nodes-base.if", "position": [-900, -160], "typeVersion": 2}, {"parameters": {"jsCode": "/**\n * 股东名称分段解析器\n * <AUTHOR>\n * @created 2025-01-27 14:30:00\n * @updated 2025-01-27 14:30:00 jason 创建股东名称分段解析功能，支持托管人和基金全称的提取\n * @description 解析股东名称格式\"托管人全称－基金全称\"，提取托管人和基金信息用于后续查询\n * @param {string} shareholderName - 输入的股东名称\n * @returns {Object} 包含解析后的托管人和基金信息\n */\n\n// 获取输入数据\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  try {\n    // 获取股东名称\n    const shareholderName = item.json.shareholderName || item.json.shareholder_name || '';\n    \n    // 参数验证\n    if (!shareholderName || typeof shareholderName !== 'string') {\n      throw new Error('股东名称不能为空且必须为字符串类型');\n    }\n    \n    const trimmedName = shareholderName.trim();\n    if (trimmedName === '') {\n      throw new Error('股东名称不能为空字符串');\n    }\n    \n    // 检查是否包含分隔符\n    const separator = '－';\n    const separatorIndex = trimmedName.indexOf(separator);\n    \n    if (separatorIndex === -1) {\n      throw new Error(`股东名称格式错误，应包含分隔符\"${separator}\"，格式为\"托管人全称${separator}基金全称\"`);\n    }\n    \n    // 分段解析\n    const custodianPart = trimmedName.substring(0, separatorIndex).trim();\n    const fundPart = trimmedName.substring(separatorIndex + 1).trim();\n    \n    // 验证分段结果\n    if (custodianPart === '') {\n      throw new Error('托管人部分不能为空');\n    }\n    \n    if (fundPart === '') {\n      throw new Error('基金全称部分不能为空');\n    }\n    \n    // 构建输出结果\n    const result = {\n      // 原始数据\n      original_shareholder_name: shareholderName,\n      \n      // 解析结果\n      custodian_name: custodianPart,\n      fund_full_name: fundPart,\n      \n      // 元数据\n      parse_success: true,\n      parse_timestamp: new Date().toISOString(),\n      separator_used: separator,\n      separator_position: separatorIndex,\n      \n      // 用于后续查询的字段\n      query_custodian: custodianPart,\n      query_fund_name: fundPart,\n      \n      // 保留其他原始字段\n      ...item.json\n    };\n    \n    results.push({ json: result });\n    \n  } catch (error) {\n    // 错误处理\n    const errorResult = {\n      original_shareholder_name: item.json.shareholderName || item.json.shareholder_name || '',\n      parse_success: false,\n      error_message: error.message,\n      error_timestamp: new Date().toISOString(),\n      custodian_name: null,\n      fund_full_name: null,\n      \n      // 保留其他原始字段\n      ...item.json\n    };\n    \n    results.push({ json: errorResult });\n  }\n}\n\nreturn results;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-680, 140], "id": "11f288e6-6618-4b0c-a8bb-af4b2fba252f", "name": "股东名称分段"}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseCode": 200}}, "id": "b06e990c-658b-44af-af58-f246997da2f5", "name": "成功响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [420, 40], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 根据解析后的托管人和基金信息查询基金代码（n8n兼容版-优化托管人匹配）\n * <AUTHOR>\n * @created 2025-01-27 14:30:00\n * @updated 2025-01-27 14:30:00 hayden 优化托管人模糊匹配策略，支持更灵活的名称匹配\n * @description 在ths_fund_management_custody表中根据托管人和基金全称查询对应的基金代码，增强托管人匹配兼容性\n * @param {string} query_custodian - 托管人名称（支持多层级模糊匹配）\n * @param {string} query_fund_name - 基金全称（精确匹配）\n * @example 输入: query_custodian=\"中国光大银行股份有限公司\", query_fund_name=\"建信健康民生混合型证券投资基金\"\n * @example 输出: 返回匹配的基金代码、基金名称等信息\n */\n\nWITH query_params AS (\n  -- 参数预处理和验证\n  SELECT \n    TRIM('{{ $json.query_custodian }}') as input_custodian,\n    TRIM('{{ $json.query_fund_name }}') as input_fund_name,\n    CASE \n      WHEN TRIM('{{ $json.query_custodian }}') = '' OR TRIM('{{ $json.query_fund_name }}') = '' \n           OR '{{ $json.query_custodian }}' IS NULL OR '{{ $json.query_fund_name }}' IS NULL THEN FALSE\n      ELSE TRUE\n    END as params_valid\n),\ncustodian_keywords AS (\n  -- 从输入的托管人名称中提取关键词\n  SELECT \n    qp.input_custodian,\n    qp.input_fund_name,\n    qp.params_valid,\n    -- 提取银行关键词（去除\"股份有限公司\"等后缀）\n    CASE \n      WHEN qp.input_custodian LIKE '%银行%' THEN\n        REGEXP_REPLACE(\n          REGEXP_REPLACE(qp.input_custodian, '股份有限公司$', ''),\n          '有限公司$', ''\n        )\n      ELSE qp.input_custodian\n    END as custodian_core_name\n  FROM query_params qp\n),\nexact_match AS (\n  -- 第一优先级：基金全称精确匹配 + 托管人精确匹配\n  SELECT \n    f.fund_code,\n    f.fund_name,\n    f.fund_full_name,\n    f.custodian,\n    ck.input_custodian,\n    ck.input_fund_name,\n    'exact_both' as match_type,\n    100 as match_score,\n    1 as priority_level\n  FROM ths_fund_management_custody f\n  CROSS JOIN custodian_keywords ck\n  WHERE \n    ck.params_valid = TRUE\n    AND f.fund_full_name = ck.input_fund_name\n    AND f.custodian = ck.input_custodian\n),\nfund_exact_custodian_fuzzy AS (\n  -- 第二优先级：基金全称精确匹配 + 托管人多层级模糊匹配\n  SELECT \n    f.fund_code,\n    f.fund_name,\n    f.fund_full_name,\n    f.custodian,\n    ck.input_custodian,\n    ck.input_fund_name,\n    'fund_exact_custodian_fuzzy' as match_type,\n    CASE \n      -- 数据库托管人包含在输入托管人中（如：光大银行 包含在 中国光大银行股份有限公司）\n      WHEN ck.input_custodian ILIKE '%' || f.custodian || '%' THEN 95\n      -- 输入托管人包含在数据库托管人中\n      WHEN f.custodian ILIKE '%' || ck.input_custodian || '%' THEN 90\n      -- 核心名称匹配（如：中国光大银行 匹配 光大银行）\n      WHEN ck.custodian_core_name ILIKE '%' || f.custodian || '%' THEN 88\n      WHEN f.custodian ILIKE '%' || ck.custodian_core_name || '%' THEN 85\n      -- 关键词交叉匹配\n      WHEN EXISTS (\n        SELECT 1 \n        FROM unnest(string_to_array(f.custodian, '')) AS db_char\n        JOIN unnest(string_to_array(ck.custodian_core_name, '')) AS input_char\n        ON db_char = input_char\n        WHERE length(db_char) > 1\n        GROUP BY 1\n        HAVING count(*) >= 3\n      ) THEN 80\n      ELSE 75\n    END as match_score,\n    2 as priority_level\n  FROM ths_fund_management_custody f\n  CROSS JOIN custodian_keywords ck\n  WHERE \n    ck.params_valid = TRUE\n    AND f.fund_full_name = ck.input_fund_name\n    AND f.custodian != ck.input_custodian\n    AND (\n      -- 双向包含匹配\n      ck.input_custodian ILIKE '%' || f.custodian || '%'\n      OR f.custodian ILIKE '%' || ck.input_custodian || '%'\n      OR ck.custodian_core_name ILIKE '%' || f.custodian || '%'\n      OR f.custodian ILIKE '%' || ck.custodian_core_name || '%'\n    )\n    AND NOT EXISTS (SELECT 1 FROM exact_match)\n),\nfallback_match AS (\n  -- 第三优先级：基金全称模糊匹配 + 托管人模糊匹配（备用方案）\n  SELECT \n    f.fund_code,\n    f.fund_name,\n    f.fund_full_name,\n    f.custodian,\n    ck.input_custodian,\n    ck.input_fund_name,\n    'both_fuzzy' as match_type,\n    CASE \n      WHEN f.fund_full_name ILIKE '%' || ck.input_fund_name || '%' \n           AND ck.input_custodian ILIKE '%' || f.custodian || '%' THEN 70\n      WHEN ck.input_fund_name ILIKE '%' || f.fund_full_name || '%' \n           AND ck.input_custodian ILIKE '%' || f.custodian || '%' THEN 65\n      ELSE 60\n    END as match_score,\n    3 as priority_level\n  FROM ths_fund_management_custody f\n  CROSS JOIN custodian_keywords ck\n  WHERE \n    ck.params_valid = TRUE\n    AND f.fund_full_name != ck.input_fund_name\n    AND (\n      f.fund_full_name ILIKE '%' || ck.input_fund_name || '%'\n      OR ck.input_fund_name ILIKE '%' || f.fund_full_name || '%'\n    )\n    AND (\n      ck.input_custodian ILIKE '%' || f.custodian || '%'\n      OR f.custodian ILIKE '%' || ck.input_custodian || '%'\n      OR ck.custodian_core_name ILIKE '%' || f.custodian || '%'\n      OR f.custodian ILIKE '%' || ck.custodian_core_name || '%'\n    )\n    AND NOT EXISTS (SELECT 1 FROM exact_match)\n    AND NOT EXISTS (SELECT 1 FROM fund_exact_custodian_fuzzy)\n),\nall_matches AS (\n  SELECT * FROM exact_match\n  UNION ALL\n  SELECT * FROM fund_exact_custodian_fuzzy\n  UNION ALL\n  SELECT * FROM fallback_match\n)\n-- 返回最终结果\nSELECT \n  fund_code,\n  fund_name,\n  fund_full_name,\n  custodian,\n  input_custodian as query_custodian,\n  input_fund_name as query_fund_name,\n  match_type,\n  match_score,\n  priority_level,\n  CURRENT_TIMESTAMP as query_timestamp\nFROM all_matches\nWHERE match_score >= 75\nORDER BY \n  priority_level ASC,\n  match_score DESC,\n  fund_code ASC\nLIMIT 10;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-460, 140], "id": "364f0e49-a220-4f2f-a41f-d1e94cd5ef2b", "name": "股东名称查询基金代码", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "同花顺数据库"}}}, {"parameters": {"assignments": {"assignments": [{"id": "90b60346-ce26-4109-859b-ab42149ddce0", "name": "fund_code", "value": "={{ $json.fund_code }}", "type": "string"}, {"id": "80874354-86fa-49fc-ba39-9dddb14b5f7c", "name": "fund_name", "value": "={{ $json.fund_name }}", "type": "string"}, {"id": "1acb0afd-584f-4eb3-bb7f-fa005aeff021", "name": "fund_full_name", "value": "={{ $json.fund_full_name }}", "type": "string"}, {"id": "348435b9-d391-49e8-a188-48e70c94ee7f", "name": "query_timestamp", "value": "={{ $json.query_timestamp }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 40], "id": "f86d1cd3-c8bc-40fa-9491-3ecf73ac6b10", "name": "固定字段"}, {"parameters": {"jsCode": "/**\n * n8n Code节点 - 基金数据筛选逻辑（修复版）\n * \n * <AUTHOR>\n * @created 2024-12-19T10:30:00.000Z\n * @modified 2024-12-19T14:45:00.000Z - 修复fundArray数据结构问题\n * @version 1.2.1\n */\n\n// 获取输入数据\nconst items = $input.all();\n\n// 修复：直接使用items数组，因为输入数据本身就是基金数组\n// 原代码：const fundArray = items[0].json;\nconst fundArray = items.map(item => item.json);\n\n// 查找以\"A\"结尾的基金\nconst fundEndingWithA = fundArray.find(fund => fund.fund_name.endsWith('A'));\n\n// 返回结果\nif (fundEndingWithA) {\n  return [{ json: fundEndingWithA }];\n} else {\n  return [{ json: fundArray[0] }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 40], "id": "4877882c-3f4f-4106-9adc-fb0fbef05202", "name": "筛选出符合条件的单个基金对象"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"code\":0,\n  \"message\": \"没有找到该股东对应的基金\"\n}", "options": {"responseCode": 200}}, "id": "1a8f5594-bd69-4a35-bced-d9f5ec544d0f", "name": "错误响应3", "type": "n8n-nodes-base.respondToWebhook", "position": [-20, 240], "typeVersion": 1.1}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5a3920f0-4491-4493-9c41-b391f8a71f3e", "leftValue": "={{ $json.fund_code }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-240, 140], "id": "e1117527-9c69-435c-ab9f-5ed12907c3b9", "name": "检查查询存在"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5a3920f0-4491-4493-9c41-b391f8a71f3e", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-20, -260], "id": "54b1f85d-2ac0-4c16-bffc-4cf56c908ef9", "name": "检测股东存在"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5a3920f0-4491-4493-9c41-b391f8a71f3e", "leftValue": "={{ $json.fund_code }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-460, -360], "id": "cc762f12-14d9-483a-9755-931b6248b6f7", "name": "检查基金代码存在"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 根据基金代码查询基金信息\n-- <AUTHOR>\n-- @created 2025-08-05 10:24:01\n-- @updated 2025-08-05 10:42:30 hayden 添加organizationId字段传递给下一个节点\n-- @description 根据基金代码查询ths_fund_management_custody表获取基金详细信息，并传递organizationId\n-- @example 输入: fund_code=\"001384.OF\", organizationId=\"Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36\"\n-- @example 输出: 返回基金代码、基金名称、基金全称、托管人、组织ID等信息\n\nSELECT \n  fund_code,\n  fund_name,\n  fund_full_name,\n  custodian,\n  management_company_name,\n  table_updated_at,\n  '{{ $json.organizationId }}' as organizationId\nFROM ths_fund_management_custody\nWHERE fund_code = '{{ $json.fund_code }}'\nLIMIT 1;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-680, -360], "id": "e4c8df70-5f60-4509-89e6-7434a731d98c", "name": "同花顺-查询基金名称", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "同花顺数据库"}}}], "connections": {"提取输入参数": {"main": [[{"node": "参数验证逻辑", "type": "main", "index": 0}]]}, "参数验证逻辑": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "查基金还是查股东", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "api统一错误响应模板1": {"main": [[{"node": "提取输入参数", "type": "main", "index": 0}]]}, "基金名称匹配股东名称": {"main": [[{"node": "检测股东存在", "type": "main", "index": 0}]]}, "查基金还是查股东": {"main": [[{"node": "同花顺-查询基金名称", "type": "main", "index": 0}], [{"node": "股东名称分段", "type": "main", "index": 0}]]}, "股东名称分段": {"main": [[{"node": "股东名称查询基金代码", "type": "main", "index": 0}]]}, "股东名称查询基金代码": {"main": [[{"node": "检查查询存在", "type": "main", "index": 0}]]}, "固定字段": {"main": [[{"node": "筛选出符合条件的单个基金对象", "type": "main", "index": 0}]]}, "筛选出符合条件的单个基金对象": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}]]}, "检查查询存在": {"main": [[{"node": "固定字段", "type": "main", "index": 0}], [{"node": "错误响应3", "type": "main", "index": 0}]]}, "检测股东存在": {"main": [[{"node": "成功响应", "type": "main", "index": 0}], [{"node": "错误响应1", "type": "main", "index": 0}]]}, "检查基金代码存在": {"main": [[{"node": "基金名称匹配股东名称", "type": "main", "index": 0}], [{"node": "错误响应2", "type": "main", "index": 0}]]}, "同花顺-查询基金名称": {"main": [[{"node": "检查基金代码存在", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}