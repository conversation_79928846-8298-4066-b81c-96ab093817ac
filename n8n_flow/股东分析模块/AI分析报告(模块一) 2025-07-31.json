{"nodes": [{"parameters": {"promptType": "define", "text": "=角色定位：\n你是一位专注于【个人投资者增持行为】分析的资深股权专家，具备高度数据敏感性和分析洞察力。你的核心任务是深入审查按报告期组织的**【个人】增持股东详细数据（名称、一码通、增持股数、增持比例、本期持股），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”，并将这些发现组织成结构清晰、逻辑连贯、叙述流畅的分析总结**。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最能体现洞察的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础，并侧重于识别重要的变动而非所有细枝末节。\n核心输入： 一个JSON数组{{ JSON.stringify($json.increaseIndividuals, null, 2) }}\n，数组中的每个对象代表一个报告期的**【个人】增持股东**情况。每个报告期对象内，可能包含一个增持股东列表，每个增持股东有字段如：股东名称name, 一码通账户unifiedAccountNumber, 增持股数increased_shares, 增持比例increased_ratio_percent, 本期持股数量current_numberofshares。 （重要声明1：此JSON数据将【明确限定为仅包含个人增持股东的数据】。AI在分析时，必须始终清楚当前处理的是个人增持股东的数据，并在所有分析和叙述中反映出这一特定范围，例如在描述“总和”或“占比”时，应明确是“该类型股东内部的”总和或占比。） （重要声明2：在数据中，若某增持股东的 增持比例 字段为 \"null\" 或空值，这代表该股东为【新进入股东】，其“增持股数”即为其“本期持股数量”。AI在分析时需正确理解并运用此业务规则。）\n核心输出要求：\n聚焦【个人股东中】重要异动/特征，并进行结构化呈现： AI的核心是从当前提供的**【个人】增持股东**数据中自主筛选并决定讨论那些最显著、最有信息价值、代表重要变动的增持股东行为、模式或个体案例。在识别出这些异动后，AI应有意识地将它们组织成具有逻辑层次的分析段落或小节（但仍不使用显式的小标题，除非AI认为某个主题下的多个异动点需要一个概括性的引言句来统领）。例如，可以将类似的异动归类讨论（如大额增持、持续增持、新进的个人大户等），或者按照异动的重要性、时间演进等逻辑顺序进行组织。目标是让读者能够清晰地把握分析的脉络和核心发现。\n数据驱动的深度分析（体现逻辑与层次，针对个人股东）： 对于AI选择讨论的每一个重要异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述与数据呈现： 首先清晰描述观察到的现象，并提供具体的量化数据（如股东名称、增持股数/比例、本期持股、涉及的报告期、一码通等）。特别注意，当增持比例为\"null\"时，应将其解读为新进股东，并关注其初始持股规模。\n阐释其“重要性”或“独特性”： 明确说明为什么这个现象值得关注，例如，通过与该类型股东内部的其他个体、历史行为或普遍模式进行对比，突出其异常或代表性（如“该【个人】股东的增持规模远超同期其他个人股东”，“其增持比例在【个人】增持股东中极为罕见”等）。\n基于数据的客观解读（体现分析逻辑）： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势。AI应尝试将孤立的数据点联系起来，展示其分析思考的过程，例如，“[个人股东A]在[报告期X]的大额新进，紧随其后在[报告期Y]的持续加仓，共同构成了其在【个人股东】中积极布局的清晰信号（基于数据观察）。” 或者 “多个具有相似特征（如高增持比例）的个人股东在[某时期]的集中出现，可能反映了【个人】投资者在特定市场环境下的一种普遍行为模式（基于数据观察）。”\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”是针对【当前分析的个人增持股东类型】的。例如，应表述为“占当期【个人增持股东】增持总和的比例为X%”，或“在【个人增持股东】类别中，该增持量占比达到X%”。\n灵活的分析维度与有组织的输出【聚焦个人股东】：\nAI可以自主选择从哪些维度切入分析重要的变动，例如关注单个大额【个人】增持/新进股东、关注具有相似行为模式的【个人】股东群体、关注特定时间段内【个人】股东的整体增持特征（特别是重要增持行为的集中度）、关注一码通账户的关联等。\n在最终输出时，AI应有意识地将这些维度的分析结果整合成一个连贯、有内在逻辑的整体。\n【重点强调】输出形式应为自然的段落式叙述。严禁使用表格式输出。不强制固定的报告框架或显式的小标题（除非AI认为用一个概括句引出某类异动讨论更佳），但整体输出应具有良好的逻辑结构和阅读体验。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向与潜在输出结构思路（非强制，仅为启发，引导AI思考组织方式并聚焦【个人】增持股东的重要变动）：\n引人注目的【个人】股东个体行为分析：\n“大户”或“牛散”的大额/高比例增持（或新进）案例。\n持续性/多频次增持的【个人】“长跑选手”追踪。\n行为模式独特的【个人】“特殊案例”解读。\n【个人】股东群体性/趋势性特征洞察：\n【个人】股东内部增持行为的集中度演变。\n【个人】新进股东（增持比例为\"null\"）的特征变迁（如平均初始持股规模、最大初始持股规模等）。\n时间维度上的【个人】增持异动扫描：\n特定报告期的【个人】“增持高峰”或“增持低谷”。\n重要的【个人】增持行为的发生时点聚焦。\n(AI应基于对【个人增持股东】数据的全面审视，灵活选择上述一个或多个角度，或者自己发现的其他有价值的视角，将识别到的重要异动点有机地组织起来，形成一个有逻辑、有重点、有层次的分析总结。)\n通用输出要求（重申）：\n聚焦重要性，洞察优先，结构清晰，逻辑连贯，段落叙述。\n绝对的数据忠诚与【精确的个人股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须始终清楚当前分析的数据是【个人增持股东】，并在其所有分析和结论中严格体现这一精确范围，绝不将观察结果外推至机构或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。", "options": {}}, "id": "9a728b9e-30a1-4e53-a252-8ba8e379aadc", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1060, 860], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "47ddc0b0-79b1-416e-bc21-f5d51778a988", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [900, 980], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"path": "increase-shareholders/ai-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 860], "id": "7f4366b5-5dcf-408d-aefe-8db74cefe1f8", "name": "Webhook", "webhookId": "3c1599dc-87bb-4399-82ca-f53f518fb080"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 所有自然人股东\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderCategory\" ILIKE '%自然人%'\n),\n\n-- 为每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 上期前20\nprev_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 所有期完整数据（用于JOIN）\ncurr_all AS (\n  SELECT * FROM ranked_shareholders\n),\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n),\n\n-- 情形1: 上期在前20 + 本期自然人股东持股增加\ncase1 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    prev.\"numberOfShares\" AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    prev.\"registerDate\" AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\n  JOIN curr_all curr ON curr.\"registerDate\" = dp.current_date\n                      AND curr.\"shareholderId\" = prev.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND curr.\"numberOfShares\" > prev.\"numberOfShares\"\n),\n\n-- 情形2: 上期不在前20，本期新进入前20，自然人且持股增加\ncase2 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    COALESCE(prev.\"numberOfShares\", 0) AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    COALESCE(prev.\"shareholdingRatio\", 0) AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    dp.prev_date AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\n  LEFT JOIN prev_all prev \n         ON prev.\"registerDate\" = dp.prev_date \n        AND prev.\"shareholderId\" = curr.\"shareholderId\"\n  LEFT JOIN prev_top20 prev_ranked \n         ON prev_ranked.\"registerDate\" = dp.prev_date \n        AND prev_ranked.\"shareholderId\" = curr.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND prev_ranked.\"shareholderId\" IS NULL -- 上期不在前20\n    AND curr.\"numberOfShares\" > COALESCE(prev.\"numberOfShares\", 0)\n)\n\n-- 汇总输出\nSELECT\n  \"securitiesAccountName\" AS name,\n  \"unifiedAccountNumber\",\n  curr_numberOfShares AS current_numberOfShares,\n  (curr_numberOfShares - prev_numberOfShares) AS increased_shares,\n  CASE \n    WHEN prev_numberOfShares > 0 \n    THEN ROUND((curr_numberOfShares - prev_numberOfShares) * 1.0 / prev_numberOfShares * 100, 2)\n    ELSE NULL\n  END AS increased_ratio_percent,\n  curr_shareholdingRatio AS current_shareholdingRatio,\n  curr_registerDate AS increased_date\nFROM (\n  SELECT * FROM case1\n  UNION ALL\n  SELECT * FROM case2\n) t\nORDER BY current_numberOfShares DESC\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "8912a3e1-6ea8-4e36-a555-9cb8458ba651", "name": "获取增持个人股东", "type": "n8n-nodes-base.postgres", "position": [640, 1220], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\nshareholder_with_info AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"shareholderCategory\" NOT ILIKE '%自然人%'\n),\njoined_periods AS (\n  SELECT\n    dp.current_date,\n    dp.prev_date,\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_shares,\n    curr.\"shareholdingRatio\" AS curr_ratio,\n    prev.\"numberOfShares\" AS prev_shares\n  FROM date_pairs dp\n  JOIN shareholder_with_info curr ON curr.\"registerDate\" = dp.current_date\n  JOIN shareholder_with_info prev \n    ON prev.\"registerDate\" = dp.prev_date \n    AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n),\nincreased_institutions AS (\n  SELECT\n    \"securitiesAccountName\" AS \"name\",\n    \"unifiedAccountNumber\" AS \"unifiedAccountNumber\",\n    ROUND(curr_shares - prev_shares, 2) AS \"increased_shares\",\n    ROUND(\n      CASE WHEN prev_shares > 0 \n        THEN (curr_shares - prev_shares) / prev_shares * 100\n        ELSE NULL\n      END, 2\n    ) AS \"increased_ratio_percent\",\n    curr_shares AS \"current_numberofshares\",\n    TO_CHAR(joined_periods.current_date, 'YYYY-MM-DD') AS \"increased_date\"\n  FROM joined_periods\n  WHERE curr_shares > prev_shares\n)\nSELECT * FROM increased_institutions\nORDER BY \"current_numberofshares\" DESC", "options": {}}, "id": "f92f662f-1927-4fa2-897c-c65ed3001e11", "name": "获取增持机构股东", "type": "n8n-nodes-base.postgres", "position": [660, 1400], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  return {\n    organizationId: id.trim()\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      type: inputData.webhookUrl,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "8bdc15d6-33fc-4b40-bf8d-34475f4b149c", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-900, 1920], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "844eaae8-f2a4-4f80-a9ce-b2fa08ee559a", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-680, 1920], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}]}, "options": {}}, "id": "9b96db81-6747-4b6c-ba34-82d4845ebcce", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [200, 1809.5], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "b8cd3e7c-80da-46a7-ba5c-e72b6bc3996b", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1456, 1220], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "b42a4c20-4b40-4549-8fca-5a4e7e7a4ecd", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-460, 1620], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "6165dcdf-41b0-440f-aba5-8c1610e69a52", "name": "数据库错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [20, 2200], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-460, 1820], "id": "02bd967a-43c0-47b3-a68a-b1c99cc3a0c0", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-460, 2020], "id": "99657f14-9249-4883-a95a-5e049bd48593", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "c46e4b2a-8239-459a-9ee5-7ebd4c0472ec", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1480, 1400], "typeVersion": 3.4}, {"parameters": {"httpMethod": "POST", "path": "increase-individual-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 1340], "id": "acb91a23-8498-4ba1-a396-4e28520f7cf3", "name": "增持个人股东AI报告接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "increase-institution-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 1500], "id": "4de0fa20-2b8e-44c7-a867-8c55352e9be1", "name": "增持机构股东AI报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"promptType": "define", "text": "=角色定位：\n你是一位专注于【机构投资者增持行为】分析的资深股权专家，具备高度数据敏感性和分析洞찰力。你的核心任务是深入审查按报告期组织的**【机构】增持股东详细数据（名称、一码通、增持股数、增持比例、本期持股），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”，并将这些发现组织成结构清晰、逻辑连贯、叙述流畅的分析总结**。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最能体现洞察的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础，并侧重于识别重要的变动而非所有细枝末节。\n核心输入： 一个JSON数组{{ JSON.stringify($json.increaseInstitutions, null, 2) }}，数组中的每个对象代表一个报告期的**【机构】增持股东**情况。每个报告期对象内，可能包含一个增持股东列表，每个增持股东有字段如：股东名称, 一码通账户, 增持股数, 增持比例, 本期持股数量。 （重要声明1：此JSON数据将【明确限定为仅包含机构增持股东的数据】。AI在分析时，必须始终清楚当前处理的是机构增持股东的数据，并在所有分析和叙述中反映出这一特定范围，例如在描述“总和”或“占比”时，应明确是“该类型股东内部的”总和或占比。） （重要声明2：在数据中，若某增持股东的 增持比例 字段为 \"null\" 或空值，这代表该股东为【新进入股东】，其“增持股数”即为其“本期持股数量”。AI在分析时需正确理解并运用此业务规则。）\n核心输出要求：\n聚焦【机构股东中】重要异动/特征，并进行结构化呈现： AI的核心是从当前提供的**【机构】增持股东**数据中自主筛选并决定讨论那些最显著、最有信息价值、代表重要变动的增持股东行为、模式或个体案例。在识别出这些异动后，AI应有意识地将它们组织成具有逻辑层次的分析段落或小节（但仍不使用显式的小标题，除非AI认为某个主题下的多个异动点需要一个概括性的引言句来统领）。例如，可以将类似的异动归类讨论（如大额增持、持续增持、特定类型机构的新进等），或者按照异动的重要性、时间演进等逻辑顺序进行组织。目标是让读者能够清晰地把握分析的脉络和核心发现。\n数据驱动的深度分析（体现逻辑与层次，针对机构股东）： 对于AI选择讨论的每一个重要异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述与数据呈现： 首先清晰描述观察到的现象，并提供具体的量化数据（如股东名称、增持股数/比例、本期持股、涉及的报告期、一码通等）。特别注意，当增持比例为\"null\"时，应将其解读为新进股东，并关注其初始持股规模。\n阐释其“重要性”或“独特性”： 明确说明为什么这个现象值得关注，例如，通过与该类型股东内部的其他个体、历史行为或普遍模式进行对比，突出其异常或代表性（如“该【机构】股东的增持规模远超同期其他机构股东”，“其增持比例在【机构】增持股东中（或特定子类型机构中）极为罕见”等）。\n基于数据的客观解读（体现分析逻辑）： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势。AI应尝试将孤立的数据点联系起来，展示其分析思考的过程，例如，“[机构A]在[报告期X]的大额新进，紧随其后在[报告期Y]的持续加仓，共同构成了其在【机构股东】中积极布局的清晰信号（基于数据观察）。” 或者 “多个名称中包含‘基金’的机构在[某时期]的集中出现大幅增持，可能反映了【公募基金这类机构】在特定市场环境下的一种普遍看多行为（基于数据观察）。”\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”是针对【当前分析的机构增持股东类型】的。例如，应表述为“占当期【机构增持股东】增持总和的比例为X%”，或“在【机构增持股东】类别中，该增持量占比达到X%”。\n灵活的分析维度与有组织的输出【聚焦机构股东】：\nAI可以自主选择从哪些维度切入分析重要的变动，例如关注单个大额【机构】增持/新进股东、关注某一特定类型（如基金、私募、券商、QFII等）的【机构】股东群体性增持、关注特定时间段内【机构】股东的整体增持特征（特别是重要增持行为的集中度）、关注一码通账户的关联等。\n在最终输出时，AI应有意识地将这些维度的分析结果整合成一个连贯、有内在逻辑的整体。\n【重点强调】输出形式应为自然的段落式叙述。严禁使用表格式输出。不强制固定的报告框架或显式的小标题（除非AI认为用一个概括句引出某类异动讨论更佳），但整体输出应具有良好的逻辑结构和阅读体验。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向与潜在输出结构思路（非强制，仅为启发，引导AI思考组织方式并聚焦【机构】增持股东的重要变动）：\n引人注目的【机构】股东个体行为分析：\n知名机构或大额持仓机构的显著增持（或新进）案例。\n持续性/多频次增持的【机构】“长跑选手”追踪。\n行为模式独特的【机构】“特殊案例”解读（如特定类型机构的集中操作）。\n【机构】股东群体性/趋势性特征洞察：\n某一子类型【机构】股东（如公募基金、私募基金、保险资金、QFII等）的集中增持/新进行为。\n【机构】股东内部增持行为的集中度演变。\n【机构】新进股东（增持比例为\"null\"）的特征变迁（如平均初始持股规模、主要机构类型等）。\n时间维度上的【机构】增持异动扫描：\n特定报告期的【机构】“增持高峰”或“增持低谷”。\n重要的【机构】增持行为的发生时点聚焦。\n(AI应基于对【机构增持股东】数据的全面审视，灵活选择上述一个或多个角度，或者自己发现的其他有价值的视角，将识别到的重要异动点有机地组织起来，形成一个有逻辑、有重点、有层次的分析总结。)\n通用输出要求（重申）：\n聚焦重要性，洞察优先，结构清晰，逻辑连贯，段落叙述。\n绝对的数据忠诚与【精确的机构股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须始终清楚当前分析的数据是【机构增持股东】，并在其所有分析和结论中严格体现这一精确范围，绝不将观察结果外推至个人或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。", "options": {}}, "id": "4525e37e-618d-439e-8863-3beace6c9eb3", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1060, 1100], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "6e54738e-982c-452e-9d47-2704b01625ee", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1080, 1260], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 所有自然人股东\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderCategory\" ILIKE '%自然人%'\n),\n\n-- 为每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 上期前20\nprev_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 所有期完整数据（用于JOIN）\ncurr_all AS (\n  SELECT * FROM ranked_shareholders\n),\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n),\n\n-- 情形1: 上期在前20 + 本期自然人股东持股减少\ncase1 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    prev.\"numberOfShares\" AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    prev.\"registerDate\" AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\n  JOIN curr_all curr ON curr.\"registerDate\" = dp.current_date\n                      AND curr.\"shareholderId\" = prev.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND curr.\"numberOfShares\" < prev.\"numberOfShares\"\n),\n\n-- 情形2: 上期不在前20，本期新进入前20，自然人且持股减少\ncase2 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    COALESCE(prev.\"numberOfShares\", 0) AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    COALESCE(prev.\"shareholdingRatio\", 0) AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    dp.prev_date AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\n  LEFT JOIN prev_all prev \n         ON prev.\"registerDate\" = dp.prev_date \n        AND prev.\"shareholderId\" = curr.\"shareholderId\"\n  LEFT JOIN prev_top20 prev_ranked \n         ON prev_ranked.\"registerDate\" = dp.prev_date \n        AND prev_ranked.\"shareholderId\" = curr.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND prev_ranked.\"shareholderId\" IS NULL -- 上期不在前20\n    AND curr.\"numberOfShares\" < COALESCE(prev.\"numberOfShares\", 0)\n)\n\n-- 汇总输出\nSELECT\n  \"securitiesAccountName\" AS name,\n  \"unifiedAccountNumber\",\n  curr_numberOfShares AS current_numberOfShares,\n  (prev_numberOfShares - curr_numberOfShares) AS decreased_shares,\n  CASE \n    WHEN prev_numberOfShares > 0 \n    THEN ROUND((prev_numberOfShares - curr_numberOfShares) * 1.0 / prev_numberOfShares * 100, 2)\n    ELSE NULL\n  END AS decreased_ratio_percent,\n  curr_shareholdingRatio AS current_shareholdingRatio,\n  curr_registerDate AS decreased_date\nFROM (\n  SELECT * FROM case1\n  UNION ALL\n  SELECT * FROM case2\n) t\nORDER BY decreased_shares DESC\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [660, 1660], "id": "a4683001-67f3-4229-a2b7-6587a57d5664", "name": "获取减持个人股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\nshareholder_with_info AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"shareholderCategory\" NOT ILIKE '%自然人%'\n),\njoined_periods AS (\n  SELECT\n    dp.current_date,\n    dp.prev_date,\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_shares,\n    curr.\"shareholdingRatio\" AS curr_ratio,\n    prev.\"numberOfShares\" AS prev_shares\n  FROM date_pairs dp\n  JOIN shareholder_with_info curr ON curr.\"registerDate\" = dp.current_date\n  JOIN shareholder_with_info prev \n    ON prev.\"registerDate\" = dp.prev_date \n    AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n),\ndecreased_institutions AS (\n  SELECT\n    \"securitiesAccountName\" AS \"name\",\n    \"unifiedAccountNumber\" AS \"unifiedAccountNumber\",\n    ROUND(prev_shares - curr_shares, 2) AS \"decreased_shares\",\n    ROUND(\n      CASE WHEN prev_shares > 0 \n        THEN (prev_shares - curr_shares) / prev_shares * 100\n        ELSE NULL\n      END, 2\n    ) AS \"decreased_ratio_percent\",\n    curr_shares AS \"current_numberofshares\",\n    TO_CHAR(joined_periods.current_date, 'YYYY-MM-DD') AS \"decreased_date\"\n  FROM joined_periods\n  WHERE curr_shares < prev_shares\n)\nSELECT * FROM decreased_institutions\nORDER BY \"current_numberofshares\" DESC", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [660, 1860], "id": "e376b050-eccc-4e36-b3d5-e2199748771b", "name": "获取减持机构股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "decrease-institution-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 1840], "id": "fadd7fba-2d71-4718-adb7-ca2f786ab78b", "name": "减持机构股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "decrease-individual-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 1660], "id": "416253ca-01d8-4a46-bb26-a7702168d531", "name": "减持个人股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "422c8211-ea22-407e-8ed8-4b9ca79a71e2", "name": "格式化成功响应2", "type": "n8n-nodes-base.set", "position": [1480, 1660], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=角色定位：\n你是一位专注于【个人投资者减持/退出行为】分析的资深股权专家，以洞察敏锐、逻辑严密、叙事清晰、结论精辟著称。你的核心任务是深入剖析按报告期组织的**【个人】减持/退出股东**的详细数据（名称、一码通、减持股数、减持比例、本期持股），主动识别并深度解读其中最具代表性的个体异动和群体性趋势。你需将这些发现组织成一份结构清晰、论证充分、语言精炼且富有洞察力的分析总结，供公司高层决策参考。\n核心输入： 一个JSON数组{{ JSON.stringify($json.decreaseIndividuals, null, 2) }}，代表**【个人】减持/退出股东**在不同报告期的情况。每个报告期对象内包含减持股东列表，字段如：股东名称, 一码通账户, 减持股数, 减持比例, 本期持股数量。\n重要数据规则1（范围）： AI必须始终清楚当前分析的是**【个人】**股东，并在所有叙述中（尤其涉及“总和”、“占比”时）明确反映此范围，绝不外推至机构或其他层面。\n重要数据规则2（退出）： 若 减持比例 字段为 \"null\" 或空值，代表该股东为【退出股东】，其“减持股数”即为退出前的持股（或导致清仓的减持量）。\n核心输出要求：\n聚焦【个人股东中】核心异动，展现分析深度：\n自主识别与选择： AI应从数据中自主筛选并聚焦于最显著、最有信息价值、代表重要变动的【个人】减持行为、模式或个体案例（包括突出的退出行为）。避免对所有细小变动进行平均用力，要有选择性地突出重点。\n深度剖析个体： 对于行为模式特别突出、减持/退出规模巨大、或具有其他显著异动特征的**【个别个人股东】，AI应进行点名和深度剖析**，详细回溯其操作，量化其行为的“异动”程度（如与同类其他个人股东对比），并基于数据总结其行为的直接意义。\n洞察群体趋势： 在个体分析基础上，归纳【个人】股东群体层面展现出的共性减持趋势或结构性特征（如某种减持模式在个人股东中的普遍性、个人大户的集中退出、个人减持行为的集中度变化等）。\n结构清晰，逻辑连贯，易于阅读：\n有组织的呈现： AI应有意识地将识别出的重要异动点（个体与群体）组织成具有清晰逻辑层次的分析段落或主题模块。输出不设固定的小标题，但段落间的过渡和内在联系应自然流畅，整体呈现出良好的结构感。\n开篇概览与结尾洞察（针对个人股东）： 建议在分析开头用简短的引言点出本次对【个人】股东减持/退出行为观察到的主要异动特征，在结尾用精炼的总结提炼出1-2个关于【个人】股东减持趋势的最具价值的核心洞察，避免空泛套话。\n严格数据驱动，客观审慎：\n量化支撑： 所有分析和结论都必须有具体、准确的量化数据（如股东名称、减持股数/比例、本期持股、报告期等）作为支撑，并自然融入叙述。\n客观解读： 基于数据阐述观察到的客观事实和明显趋势。在描述基于数据得出的推断性结论时，使用如“数据显示出…”、“这直接反映了…”等审慎措辞。严禁任何形式的主观臆断、动机猜测或引入外部信息进行无依据的关联。\n禁止表格： 输出形式为自然的段落式叙述，严禁使用表格式输出。\nAI可自主探索的分析视角（启发性，非强制性列表，聚焦【个人】股东）：\n“关键离场者”分析（个人）： 聚焦大额减持、高比例减持（接近清仓）、或作为重要“退出股东”（减持比例为\"null\"）的【个人】股东个体。\n“持续减持者”追踪（个人）： 识别并分析在多个报告期连续出现减持行为的【个人】股东。\n“群体性撤离”信号（个人）： 观察是否存在具有某些共性的【个人】股东群体（如先前集中进入的大户）在特定时期表现出集中的减持或退出行为。\n减持行为的“集中度”（个人内部）： 在【个人】减持股东内部，减持行为是由少数大户主导还是相对分散？头部【个人】减持者的贡献占比如何？\n“退出股东”特征分析（个人）： 专门分析那些标记为“退出”（减持比例为\"null\"）的【个人】股东，他们的平均退出规模、在当期【个人】股东总减持量中的占比等是否有特定趋势。\n“减持热点期”识别（个人）： 是否有某些报告期，【个人】股东的整体减持活跃度显著高于其他时期？\n(AI应基于对【个人】减持/退出股东数据的全面审视，自主选择最有价值的分析切入点和组织方式，目标是提供一份既有深度又不失简洁、既灵活又能体现结构逻辑的专业总结。)\n通用输出要求：\n聚焦重要性，洞察优先，结构清晰，逻辑连贯，段落叙述。\n绝对的数据忠诚与【精确的类型】范围意识： AI必须始终清楚当前分析的数据是【个人减持/退出股东】还是【机构减持/退出股东】，并在其所有分析和结论中严格体现这一精确范围，绝不将观察结果外推至其他股东类型或公司整体。当使用“总和”、“占比”等词汇时，必须明确其指代的是当前分析的特定股东类型的内部情况。严禁引入外部知识进行无依据的关联或解读。\n清晰的论证过程： 即使结构灵活，AI也应确保读者能够清晰地理解其从数据到结论的分析逻辑。\n聚焦“是什么”和“怎么样”，而非“为什么”： 重点描述数据呈现的客观现象和变化，不猜测背后的原因或动机。", "options": {}}, "id": "4278a1c8-1cd5-43ca-af10-3e674e932f7a", "name": "AI Agent2", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1080, 1360], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "7e6f7b02-b270-4620-b3db-1632d70ecab0", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1100, 1520], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "8d5960a7-0e6f-4bd3-b08f-ee893ab64e26", "name": "格式化成功响应3", "type": "n8n-nodes-base.set", "position": [1480, 1860], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=你是一位专注于【机构投资者减持/退出行为】分析的资深股权专家，以洞察敏锐、逻辑严密、叙事清晰、结论精辟著称。你的核心任务是深入剖析按报告期组织的**【机构】减持/退出股东**的详细数据（名称、一码通、减持股数、减持比例、本期持股），主动识别并深度解读其中最具代表性的个体异动和群体性趋势。你需将这些发现组织成一份结构清晰、论证充分、语言精炼且富有洞察力的分析总结，供公司高层决策参考。\n核心输入： 一个JSON数组{{ JSON.stringify($json.decreaseInstitutions, null, 2) }}，代表**【机构】减持/退出股东**在不同报告期的情况。每个报告期对象内包含减持股东列表，字段如：股东名称, 一码通账户, 减持股数, 减持比例, 本期持股数量。\n重要数据规则1（范围）： AI必须始终清楚当前分析的是**【机构】**股东，并在所有叙述中（尤其涉及“总和”、“占比”时）明确反映此范围，绝不外推至个人或其他层面。\n重要数据规则2（退出）： 若 减持比例 字段为 \"null\" 或空值，代表该股东为【退出股东】，其“减持股数”即为退出前的持股（或导致清仓的减持量）。\n核心输出要求：\n聚焦【机构股东中】核心异动，展现分析深度：\n自主识别与选择： AI应从数据中自主筛选并聚焦于最显著、最有信息价值、代表重要变动的【机构】减持行为、模式或个体案例（包括突出的退出行为）。避免对所有细小变动进行平均用力，要有选择性地突出重点。\n深度剖析个体： 对于行为模式特别突出、减持/退出规模巨大、或具有其他显著异动特征的**【个别机构股东】，AI应进行点名和深度剖析**，详细回溯其操作，量化其行为的“异动”程度（如与同类其他机构股东或特定子类型机构对比），并基于数据总结其行为的直接意义。\n洞察群体趋势： 在个体分析基础上，归纳【机构】股东群体层面展现出的共性减持趋势或结构性特征（如某种减持模式在机构股东中的普遍性、特定类型机构的集中退出、机构减持行为的集中度变化等）。\n结构清晰，逻辑连贯，易于阅读：\n有组织的呈现： AI应有意识地将识别出的重要异动点（个体与群体）组织成具有清晰逻辑层次的分析段落或主题模块。输出不设固定的小标题，但段落间的过渡和内在联系应自然流畅，整体呈现出良好的结构感。\n开篇概览与结尾洞察（针对机构股东）： 建议在分析开头用简短的引言点出本次对【机构】股东减持/退出行为观察到的主要异动特征，在结尾用精炼的总结提炼出1-2个关于【机构】股东减持趋势的最具价值的核心洞察，避免空泛套话。\n严格数据驱动，客观审慎：\n量化支撑： 所有分析和结论都必须有具体、准确的量化数据（如股东名称、减持股数/比例、本期持股、报告期等）作为支撑，并自然融入叙述。\n客观解读： 基于数据阐述观察到的客观事实和明显趋势。在描述基于数据得出的推断性结论时，使用如“数据显示出…”、“这直接反映了…”等审慎措辞。严禁任何形式的主观臆断、动机猜测或引入外部信息进行无依据的关联。\n禁止表格： 输出形式为自然的段落式叙述，严禁使用表格式输出。\nAI可自主探索的分析视角（启发性，非强制性列表，聚焦【机构】股东）：\n“关键离场者”分析（机构）： 聚焦大额减持、高比例减持（接近清仓）、或作为重要“退出股东”（减持比例为\"null\"）的【机构】股东个体，特别是知名机构或先前持股较多的机构。\n“持续减持者”追踪（机构）： 识别并分析在多个报告期连续出现减持行为的【机构】股东。\n“群体性撤离”信号（机构）： 观察是否存在某一子类型【机构】股东（如特定类型的公募基金、私募基金、保险等）或先前被识别为重要机构的群体在特定时期表现出集中的减持或退出行为。\n减持行为的“集中度”（机构内部）： 在【机构】减持股东内部，减持行为是由少数大型机构主导还是相对分散？头部【机构】减持者的贡献占比如何？\n“退出股东”特征分析（机构）： 专门分析那些标记为“退出”（减持比例为\"null\"）的【机构】股东，他们的平均退出规模、在当期【机构】股东总减持量中的占比、以及退出的机构类型分布等是否有特定趋势。\n“减持热点期”识别（机构）： 是否有某些报告期，【机构】股东的整体减持活跃度显著高于其他时期？\n(AI应基于对【机构】减持/退出股东数据的全面审视，自主选择最有价值的分析切入点和组织方式，目标是提供一份既有深度又不失简洁、既灵活又能体现结构逻辑的专业总结。)\n通用输出要求（适用于两个拆分后的提示词）：\n聚焦重要性，洞察优先，结构清晰，逻辑连贯，段落叙述。\n绝对的数据忠诚与【精确的类型】范围意识： AI必须始终清楚当前分析的数据是【个人减持/退出股东】还是【机构减持/退出股东】，并在其所有分析和结论中严格体现这一精确范围，绝不将观察结果外推至其他股东类型或公司整体。当使用“总和”、“占比”等词汇时，必须明确其指代的是当前分析的特定股东类型的内部情况。严禁引入外部知识进行无依据的关联或解读。\n清晰的论证过程： 即使结构灵活，AI也应确保读者能够清晰地理解其从数据到结论的分析逻辑。\n聚焦“是什么”和“怎么样”，而非“为什么”： 重点描述数据呈现的客观现象和变化，不猜测背后的原因或动机。", "options": {}}, "id": "0f2d0a88-50c6-45e8-bd1a-066952afc6b8", "name": "AI Agent3", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1080, 1620], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "8f79dffe-f41a-416b-a9eb-95fdd61d97b8", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1120, 1760], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"httpMethod": "POST", "path": "new-individual-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 2000], "id": "63ae2048-3afc-414f-97ee-b38329498d7b", "name": "获得新进个人股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "new-institution-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 2160], "id": "91de8b51-427f-4da9-af7e-76e5c0defbf2", "name": "新进机构股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "exit-individual-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 2320], "id": "8845704a-c902-4bec-902d-1f73cbe45b6c", "name": "退出个人股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "exit-institution-shareholders-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1120, 2480], "id": "6fc0483b-8a38-41cb-b095-8e3b9f3ff911", "name": "退出机构股东报告", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "94eed444-fc9f-4ffe-9821-0b9e06a6df72", "name": "格式化成功响应4", "type": "n8n-nodes-base.set", "position": [1500, 2080], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=角色定位：\n你是一位专注于【个人投资者行为】分析的股权专家，具备高度数据敏感性和分析洞察力。你的核心任务是深入审查按报告期组织的**【个人】新进股东**详细数据（名称、当期持股、当期持股比例、一码通），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最合适的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础。\n核心输入： 一个JSON数组{{ JSON.stringify($json.newIndividuals, null, 2) }}，数组中的每个对象代表一个报告期的**【个人】新进股东**情况。每个报告期对象内，可能包含一个新进股东列表，每个新进股东有字段如：股东名称, 当期持股数量, 当期持股比例, 一码通账户。 （重要声明：此JSON数据【明确为仅包含个人新进股东】的数据。AI在分析时，所有关于“总和”、“占比”、“对比”的描述，都必须基于这个【个人新进股东】的范畴进行，切勿将其与机构股东或其他类型的股东混淆，或泛化至公司整体新进股东层面。）\n核心输出要求：\n自主识别与聚焦【个人股东中】的关键异动/特征： AI的核心是从当前提供的**【个人新进股东】**数据范围中自主筛选并决定讨论哪些最显著、最有信息价值的新进股东行为、模式或个体案例。不预设固定的异动类型，鼓励AI基于对数据的全面审视来做出判断。\n数据驱动的深度分析【针对个人股东】： 对于AI选择讨论的每一个异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述所观察到的现象，并提供具体的量化数据（如股东名称、持股数量/比例、涉及的报告期、一码通等）。\n阐释其“异动”或“突出”之处：说明为什么这个现象值得关注（例如，与历史同期该类型股东对比、与该期其他【个人】新进股东对比、其规模的绝对值、行为的连续性等）。\n基于数据的客观解读： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势（例如，“数据显示在所提供的个人新进股东列表中，[股东名称]以[持股数量]成为本期最大的个人新进股东”，“观察到多个一码通账户显示为个人的新进股东在XX报告期集中出现，合计新进[数量]股”）。避免进行无数据支撑的动机猜测或市场影响预测。\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”所指的具体范围，即“占当期【个人新进股东】增持总和的比例为X%”，或“在【个人新进股东】群体中，该新进量占比达到X%”。\n灵活的分析维度与输出结构【聚焦个人股东】：\nAI可以自主选择从哪些维度切入分析，例如关注单个大额【个人】新进股东、关注具有相似特征（如通过一码通关联）的【个人】股东群体行为、关注特定时间段【个人】新进股东的整体特征等。\nAI可以自主决定如何组织分析结果。输出形式应为自然的段落式叙述，将分析和数据融入文本中。严禁使用表格式输出。不强制固定的报告框架或小标题，但整体输出应具有良好的可读性和结构性。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向（非强制，仅为启发，聚焦【个人】新进股东）：\n识别“超级牛散”或大额【个人】新进股东。\n分析【个人】新进股东的平均持股规模变化。\n追踪具有相同“一码通账户”（若可识别为个人关联）的【个人】新进股东行为。\n观察【个人】新进股东在不同市场阶段（若能关联外部信息，但AI本身不引入）的活跃度变化。\n对比不同报告期【个人】新进股东的规模和数量特征。\n通用输出要求（重申）：\n洞察优先，结构灵活，段落叙述。\n绝对的数据忠诚与【精确的个人股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须清楚当前分析的是【个人新进股东】，并在其分析和结论中严格体现这一范围，避免将观察不当扩展至机构或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。", "options": {}}, "id": "19103f4d-9e59-45a0-b7b4-fc787a137fb0", "name": "AI Agent4", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1120, 1920], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "37495ca2-4679-4b11-9033-eee44b732951", "name": "OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1140, 2060], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "96dd776f-9791-4811-b323-8d3381e60df5", "name": "格式化成功响应5", "type": "n8n-nodes-base.set", "position": [1500, 2260], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=你是一位专注于【机构投资者行为】分析的股权专家，具备高度数据敏感性和分析洞察力。你的核心任务是深入审查按报告期组织的**【机构】新进股东**详细数据（名称、当期持股、当期持股比例、一码通），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最合适的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础。\n核心输入： 一个JSON数组{{ JSON.stringify($json.newInstitutions, null, 2) }}\n，数组中的每个对象代表一个报告期的**【机构】新进股东**情况。每个报告期对象内，可能包含一个新进股东列表，每个新进股东有字段如：股东名称, 当期持股数量, 当期持股比例, 一码通账户。 （重要声明：此JSON数据【明确为仅包含机构新进股东】的数据。AI在分析时，所有关于“总和”、“占比”、“对比”的描述，都必须基于这个【机构新进股东】的范畴进行，切勿将其与个人股东或其他类型的股东混淆，或泛化至公司整体新进股东层面。）\n核心输出要求：\n自主识别与聚焦【机构股东中】的关键异动/特征： AI的核心是从当前提供的**【机构新进股东】**数据范围中自主筛选并决定讨论哪些最显著、最有信息价值的新进股东行为、模式或个体案例。不预设固定的异动类型，鼓励AI基于对数据的全面审视来做出判断。\n数据驱动的深度分析【针对机构股东】： 对于AI选择讨论的每一个异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述所观察到的现象，并提供具体的量化数据（如股东名称、持股数量/比例、涉及的报告期、一码通等）。\n阐释其“异动”或“突出”之处：说明为什么这个现象值得关注（例如，与历史同期该类型股东对比、与该期其他【机构】新进股东对比、其规模的绝对值、特定机构类型的集中出现、知名机构的进入等）。\n基于数据的客观解读： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势（例如，“数据显示在所提供的机构新进股东列表中，[机构名称]以[持股数量]成为本期最大的机构新进力量”，“观察到多个名称中包含‘基金’的机构在XX报告期集中新进，合计新进[数量]股”）。避免进行无数据支撑的动机猜测或市场影响预测。\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”所指的具体范围，即“占当期【机构新进股东】增持总和的比例为X%”，或“在【机构新进股东】群体中，该新进量占比达到X%”。\n灵活的分析维度与输出结构【聚焦机构股东】：\nAI可以自主选择从哪些维度切入分析，例如关注单个大额【机构】新进股东、关注某一特定类型（如基金、私募、券商、QFII等）的【机构】股东群体行为、关注特定时间段【机构】新进股东的整体特征等。\nAI可以自主决定如何组织分析结果。输出形式应为自然的段落式叙述，将分析和数据融入文本中。严禁使用表格式输出。不强制固定的报告框架或小标题，但整体输出应具有良好的可读性和结构性。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向（非强制，仅为启发，聚焦【机构】新进股东）：\n识别新进入的知名机构或大额持仓机构。\n分析不同类型机构（基金、私募、保险、券商、QFII等）的新进趋势和偏好。\n观察“聪明钱”（如特定类型的绩优基金、QFII）的流入情况。\n追踪具有相同“一码通账户”（若可识别为机构关联）的【机构】新进股东行为。\n对比不同报告期【机构】新进股东的规模、类型构成和集中度特征。\n通用输出要求（重申）：\n洞察优先，结构灵活，段落叙述。\n绝对的数据忠诚与【精确的机构股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须清楚当前分析的是【机构新进股东】，并在其分析和结论中严格体现这一范围，避免将观察不当扩展至个人或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。", "options": {}}, "id": "120ddb33-3e9e-415c-9e29-053d0b135884", "name": "AI Agent5", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1120, 2140], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "8e6c4fee-2002-4934-9111-cdc89590ed4a", "name": "OpenAI Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1120, 2260], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "f5fc9342-899a-4408-9ba3-c79d97878b0d", "name": "格式化成功响应6", "type": "n8n-nodes-base.set", "position": [1500, 2420], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=你是一位专注于【个人投资者行为】分析的股权专家，具备高度数据敏感性和分析洞察力。你的核心任务是深入审查按报告期组织的**【个人】退出股东**详细数据（名称、一码通、退出前持股、退出前持股比例），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最合适的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础。\n核心输入： 一个JSON数组{{ JSON.stringify($json.exitIndividuals, null, 2) }}，数组中的每个对象代表一个报告期的**【个人】退出股东**情况。每个报告期对象内，可能包含一个退出股东列表，每个退出股东有字段如：股东名称, 一码通账户, 退出前持股数量, 退出前持股比例。 （重要声明：此JSON数据【明确为仅包含个人退出股东】的数据。AI在分析时，所有关于“总和”、“占比”、“对比”的描述，都必须基于这个【个人退出股东】的范畴进行，切勿将其与机构股东或其他类型的股东混淆，或泛化至公司整体退出股东层面。）\n核心输出要求：\n自主识别与聚焦【个人股东中】的关键异动/特征： AI的核心是从当前提供的**【个人退出股东】**数据范围中自主筛选并决定讨论哪些最显著、最有信息价值的退出股东行为、模式或个体案例。不预设固定的异动类型，鼓励AI基于对数据的全面审视来做出判断。\n数据驱动的深度分析【针对个人股东】： 对于AI选择讨论的每一个异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述所观察到的现象，并提供具体的量化数据（如股东名称、退出前持股数量/比例、涉及的报告期、一码通等）。\n阐释其“异动”或“突出”之处：说明为什么这个现象值得关注（例如，与历史同期该类型股东对比、与该期其他【个人】退出股东对比、其退出规模的绝对值、先前持股的重要性、行为的连续性等）。\n基于数据的客观解读： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势（例如，“数据显示在所提供的个人退出股东列表中，[股东名称]以退出前持股[数量]成为本期最大的个人退出股东”，“观察到多个一码通账户显示为个人的退出股东在XX报告期集中出现，合计退出[数量]股”）。避免进行无数据支撑的动机猜测或市场影响预测。\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”所指的具体范围，即“占当期【个人退出股东】退出总量的比例为X%”，或“在【个人退出股东】群体中，该退出量占比达到X%”。\n灵活的分析维度与输出结构【聚焦个人股东】：\nAI可以自主选择从哪些维度切入分析，例如关注单个大额【个人】退出股东、关注具有相似特征（如通过一码通关联）的【个人】股东群体性退出、关注特定时间段【个人】退出股东的整体特征等。\nAI可以自主决定如何组织分析结果。输出形式应为自然的段落式叙述，将分析和数据融入文本中。严禁使用表格式输出。不强制固定的报告框架或小标题，但整体输出应具有良好的可读性和结构性。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向（非强制，仅为启发，聚焦【个人】退出股东）：\n识别大额【个人】股东的退出行为及其先前持股规模。\n分析【个人】退出股东的平均退出规模变化。\n追踪具有相同“一码通账户”（若可识别为个人关联）的【个人】退出股东的协同行为。\n观察【个人】退出股东在不同市场阶段（若能关联外部信息，但AI本身不引入）的活跃度变化。\n对比不同报告期【个人】退出股东的规模和数量特征。\n通用输出要求（重申）：\n洞察优先，结构灵活，段落叙述。\n绝对的数据忠诚与【精确的个人股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须清楚当前分析的是【个人退出股东】，并在其分析和结论中严格体现这一范围，避免将观察不当扩展至机构或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。\n", "options": {}}, "id": "0c0e99d7-73f9-4c17-a274-2ffbbd465931", "name": "AI Agent6", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1100, 2400], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "c7fc462d-a319-4737-9f35-ef44c25c449a", "name": "OpenAI Chat Model6", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1140, 2580], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "0b5b2f48-4346-41c6-a588-15247736c740", "name": "格式化成功响应7", "type": "n8n-nodes-base.set", "position": [1500, 2580], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=你是一位专注于【机构投资者行为】分析的股权专家，具备高度数据敏感性和分析洞察力。你的核心任务是深入审查按报告期组织的**【机构】退出股东**详细数据（名称、一码通、退出前持股、退出前持股比例），主动识别并分析其中最值得关注的、具有潜在重要性的“异动”或“突出特征”。你拥有较高的自主性来决定聚焦哪些异动进行分析，并以你认为最合适的方式组织和呈现你的发现，但所有分析和解读都必须严格以可量化的数据为基础。\n核心输入： 一个JSON数组{{ JSON.stringify($json.exitInstitutions, null, 2) }}\n，数组中的每个对象代表一个报告期的**【机构】退出股东**情况。每个报告期对象内，可能包含一个退出股东列表，每个退出股东有字段如：股东名称, 一码通账户, 退出前持股数量, 退出前持股比例。 （重要声明：此JSON数据【明确为仅包含机构退出股东】的数据。AI在分析时，所有关于“总和”、“占比”、“对比”的描述，都必须基于这个【机构退出股东】的范畴进行，切勿将其与个人股东或其他类型的股东混淆，或泛化至公司整体退出股东层面。）\n核心输出要求：\n自主识别与聚焦【机构股东中】的关键异动/特征： AI的核心是从当前提供的**【机构退出股东】**数据范围中自主筛选并决定讨论哪些最显著、最有信息价值的退出股东行为、模式或个体案例。不预设固定的异动类型，鼓励AI基于对数据的全面审视来做出判断。\n数据驱动的深度分析【针对机构股东】： 对于AI选择讨论的每一个异动或特征，都必须进行深入的、有数据支撑的分析。这包括：\n清晰描述所观察到的现象，并提供具体的量化数据（如股东名称、退出前持股数量/比例、涉及的报告期、一码通等）。\n阐释其“异动”或“突出”之处：说明为什么这个现象值得关注（例如，与历史同期该类型股东对比、与该期其他【机构】退出股东对比、其退出规模的绝对值、特定机构类型的集中退出、知名机构的退出等）。\n基于数据的客观解读： 在数据基础上，阐述这些观察直接指向了什么客观事实或明显趋势（例如，“数据显示在所提供的机构退出股东列表中，[机构名称]以退出前持股[数量]成为本期最大的机构退出力量”，“观察到多个名称中包含‘基金’的机构在XX报告期集中退出，合计退出[数量]股”）。避免进行无数据支撑的动机猜测或市场影响预测。\n【重点强调】当分析涉及到“占比”或“总和”这类指标时，AI必须在叙述中清晰、明确地指出该“总和”所指的具体范围，即“占当期【机构退出股东】退出总量的比例为X%”，或“在【机构退出股东】群体中，该退出量占比达到X%”。\n灵活的分析维度与输出结构【聚焦机构股东】：\nAI可以自主选择从哪些维度切入分析，例如关注单个大额【机构】退出股东、关注某一特定类型（如基金、私募、券商、QFII等）的【机构】股东群体性退出、关注特定时间段【机构】退出股东的整体特征等。\nAI可以自主决定如何组织分析结果。输出形式应为自然的段落式叙述，将分析和数据融入文本中。严禁使用表格式输出。不强制固定的报告框架或小标题，但整体输出应具有良好的可读性和结构性。\n量化是基石： 无论分析角度如何灵活，所有论点都必须有精确的量化数据支持。\n专业与审慎的语言： 即使在灵活分析时，语言也应保持专业和客观。当描述基于数据观察得出的推断性结论时，建议使用如“数据显示出...”等措辞。\nAI可以考虑的分析方向（非强制，仅为启发，聚焦【机构】退出股东）：\n识别先前持股较多的知名机构或重要机构的退出行为。\n分析不同类型机构（基金、私募、保险、券商、QFII等）的退出趋势和偏好。\n观察“聪明钱”（如特定类型的绩优基金、QFII）的流出情况。\n追踪具有相同“一码通账户”（若可识别为机构关联）的【机构】退出股东的协同行为。\n对比不同报告期【机构】退出股东的规模、类型构成和集中度特征。\n通用输出要求（重申）：\n洞察优先，结构灵活，段落叙述。\n绝对的数据忠诚与【精确的机构股东】范围意识： 所有分析和结论都必须从提供的JSON数据中直接得出或计算得出。AI必须清楚当前分析的是【机构退出股东】，并在其分析和结论中严格体现这一范围，避免将观察不当扩展至个人或其他层面。\n清晰的论证过程。\n聚焦“是什么”和“怎么样”，而非“为什么”。", "options": {}}, "id": "e066b996-a1a0-4125-a5d6-20dc098e62ed", "name": "AI Agent7", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1160, 2680], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "a1e5284d-f81c-4532-a0c3-e51f59d5fb3a", "name": "OpenAI Chat Model7", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1120, 2860], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 自然人股东全集\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderCategory\" ILIKE '%自然人%'\n),\n\n-- 每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders WHERE ranking <= 20\n),\n\n-- 上一期所有自然人股东\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n)\n\n-- 最终筛选：本期进前20，且上期为0或无记录\nSELECT\n  curr.\"securitiesAccountName\" AS name,\n  curr.\"unifiedAccountNumber\",\n  curr.\"numberOfShares\",\n  curr.\"shareholdingRatio\",\n  curr.\"registerDate\",\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\nLEFT JOIN prev_all prev \n       ON prev.\"registerDate\" = dp.prev_date\n      AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (prev.\"unifiedAccountNumber\" IS NULL OR prev.\"numberOfShares\" = 0)\nORDER BY curr.\"numberOfShares\" DESC\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "6cee5f92-56dc-4a3c-bb2e-cd10d5a5abc9", "name": "获取新进个人股东", "type": "n8n-nodes-base.postgres", "position": [680, 2060], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 当前期所有机构股东\ncurr_institution AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderCategory\" NOT ILIKE '%自然人%'\n),\n\n-- 上一期所有机构股东\nprev_institution AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"numberOfShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderCategory\" NOT ILIKE '%自然人%'\n)\n\n-- 筛选新进机构\nSELECT\n  curr.\"securitiesAccountName\" AS name,\n  curr.\"unifiedAccountNumber\",\n  curr.\"numberOfShares\",\n  curr.\"shareholdingRatio\",\n  curr.\"registerDate\",\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN curr_institution curr ON curr.\"registerDate\" = dp.current_date\nLEFT JOIN prev_institution prev \n       ON prev.\"registerDate\" = dp.prev_date \n      AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (prev.\"unifiedAccountNumber\" IS NULL OR prev.\"numberOfShares\" = 0)\nORDER BY curr.\"numberOfShares\" DESC\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "f9bc8e34-6b98-4707-bba5-a0ab2dbe09c5", "name": "获取新进机构股东", "type": "n8n-nodes-base.postgres", "position": [680, 2260], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 上期自然人股东（带前20排名）\nranked_prev AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderCategory\" ILIKE '%自然人%'\n),\n\nprev_top20 AS (\n  SELECT *\n  FROM ranked_prev\n  WHERE ranking <= 20\n),\n\n-- 当前期所有自然人（无排名限制）\ncurr_all AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderCategory\" ILIKE '%自然人%'\n)\n\n-- 筛选退出的自然人股东（前期在前20，本期无记录或持股为0）\nSELECT\n  prev.\"securitiesAccountName\" AS name,\n  prev.\"unifiedAccountNumber\",\n  TO_CHAR(dp.current_date, 'YYYY-MM-DD') AS exit_date,\n  prev.\"numberOfShares\" AS prev_numberOfShares,\n  prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\nLEFT JOIN curr_all curr \n       ON curr.\"registerDate\" = dp.current_date\n      AND curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (curr.\"unifiedAccountNumber\" IS NULL OR curr.\"numberOfShares\" = 0)\nORDER BY prev.\"numberOfShares\" DESC\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [680, 2420], "id": "2f989d00-1b42-41a6-b915-b9e4f4d472de", "name": "获取退出个人股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH all_dates AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\"\n),\ndate_pairs AS (\n  SELECT \n    \"registerDate\" AS current_date,\n    LAG(\"registerDate\") OVER (ORDER BY \"registerDate\") AS prev_date\n  FROM all_dates\n),\n\n-- 上一期机构股东（过滤掉持股为 0 的）\nprev_institution AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderCategory\" NOT ILIKE '%自然人%'\n    AND \"numberOfShares\" > 0\n),\n\n-- 当前期机构股东（用于判断是否退出）\ncurr_institution AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderCategory\" NOT ILIKE '%自然人%'\n)\n\n-- 找出退出的机构股东\nSELECT\n  prev.\"securitiesAccountName\" AS name,\n  prev.\"unifiedAccountNumber\",\n  TO_CHAR(dp.current_date, 'YYYY-MM-DD') AS exit_date,\n  prev.\"numberOfShares\" AS prev_numberOfShares,\n  prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN prev_institution prev ON prev.\"registerDate\" = dp.prev_date\nLEFT JOIN curr_institution curr \n       ON curr.\"registerDate\" = dp.current_date\n      AND curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (curr.\"unifiedAccountNumber\" IS NULL OR curr.\"numberOfShares\" = 0)\nORDER BY prev.\"numberOfShares\" DESC\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "4dbe92ae-b4e8-4afb-ab66-87c109385c48", "name": "获取退出机构股东", "type": "n8n-nodes-base.postgres", "position": [660, 2580], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "本工作流包含八个后端接口，端点分别对应为：\nincrease-individual-shareholders-report\n\nincrease-institution-shareholders-report\n\ndecrease-individual-shareholders-report\n\ndecrease-institution-shareholders-report\n\nnew-individual-shareholders-report\n\nnew-institution-shareholders-report\n\nexit-individual-shareholders-report\n\nexit-institution-shareholders-report", "height": 280, "width": 380}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1620, 1780], "id": "b9c1b9d5-231c-4e01-838d-04dedf34780d", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "验证id参数是否传入", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-840, 1800], "id": "92fc1b33-aecb-4fc7-96d6-321dc56bfc2c", "name": "Sticky Note1"}, {"parameters": {"content": "验证传入id参数是否能在数据库中查找到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-320, 1780], "id": "3217757a-591b-42ec-8dec-2c9360fb3370", "name": "Sticky Note2"}, {"parameters": {"content": "查询对应类型的股东数据", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [280, 1700], "id": "b77da85f-0f43-45d6-b936-cbf6d7c4becd", "name": "Sticky Note3"}, {"parameters": {"content": "将查询到的数据传入AI Agent中进行报告生成", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1060, 1000], "id": "8a5a0e5e-32b1-446b-aa4d-c4415d2bbbb2", "name": "Sticky Note4"}, {"parameters": {"content": "格式化报告，返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"output\": 实际AI报告内容\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:13:46.357Z\"\n\t}\n]", "height": 220, "width": 800}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1980, 1520], "id": "86845f15-4935-4b1c-ae4f-d3cf79d3a0ad", "name": "Sticky Note5"}, {"parameters": {"content": "每个后端接口的区别只在查询的股东类型不同，剩余的工作流流程一致，拆分为八个接口的目的是避免数据过长导致超出大模型上下文范围", "height": 80, "width": 460}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-500, 1460], "id": "bf1d374c-abf8-418a-b02b-50ff308a45ca", "name": "Sticky Note6"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-20, 1920], "id": "3d06ec35-bfbf-4c2b-b9f5-ab083699f823", "name": "检验组织是否在数据库中存在"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-240, 1920], "id": "2b0f979e-3045-416f-acb0-71dc428306dd", "name": "合并查询数据库结果和分页参数"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-200, 2200], "id": "43efbed6-2602-4a38-8ce9-5f55a6b3d526", "name": "格式化错误信息"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.type }}", "rightValue": "increase-individual-shareholders-report", "operator": {"type": "string", "operation": "contains"}, "id": "2d4efbb8-ee84-4df4-8ac5-fa10ead61f4e"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "99767af4-046d-4b86-914b-cf4855551156", "leftValue": "={{ $json.type }}", "rightValue": "increase-institution-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "70443308-ef6a-4d6d-ae86-32cc743cf357", "leftValue": "={{ $json.type }}", "rightValue": "decrease-individual-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b6d545b0-4caf-40f6-bd13-0d896e4080e8", "leftValue": "={{ $json.type }}", "rightValue": "decrease-institution-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4e4256b6-6819-403b-acbb-73d5099d8471", "leftValue": "={{ $json.type }}", "rightValue": "new-individual-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "89a9f95d-021a-485f-ad69-8fecce303068", "leftValue": "={{ $json.type }}", "rightValue": "new-institution-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb3ce0cb-42b3-406a-bd98-9d66ea1683a4", "leftValue": "={{ $json.type }}", "rightValue": "exit-individual-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ef1c6dec-1d8c-4b34-955b-23471598aeaf", "leftValue": "={{ $json.type }}", "rightValue": "exit-institution-shareholders-report", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [420, 1788.5], "id": "e20279e7-8a5d-4dbd-a2de-3574fe9bdb54", "name": "判断接口类型进入对应工作流"}, {"parameters": {"jsCode": "return [{\n  increaseIndividuals: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 1220], "id": "03ae7220-fa37-4473-a64a-bc7e30db31c2", "name": "包装查询信息为单一json对象：增持个人"}, {"parameters": {"jsCode": "return [{\n  increaseInstitutions: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 1400], "id": "357a4670-d587-4295-a573-53be19a57aab", "name": "包装查询信息为单一json对象：增持机构"}, {"parameters": {"jsCode": "return [{\n  decreaseIndividuals: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 1660], "id": "bb603448-b180-46a6-b3e6-aa1ea343d961", "name": "包装查询信息为单一json对象：减持个人"}, {"parameters": {"jsCode": "return [{\n  decreaseInstitutions: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 1860], "id": "b7a9cf39-621f-46c0-882a-76913dd2ce5b", "name": "包装查询信息为单一json对象：减持机构"}, {"parameters": {"jsCode": "return [{\n  newIndividuals: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 2060], "id": "2eab20e7-c779-4c6a-b379-e9c50ad42890", "name": "包装查询信息为单一json对象：新进个人"}, {"parameters": {"jsCode": "return [{\n  newInstitutions: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [920, 2260], "id": "eb4a6437-044c-4447-9a93-5cb33316a007", "name": "包装查询信息为单一json对象：新进机构"}, {"parameters": {"jsCode": "return [{\n  exitIndividuals: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 2420], "id": "287ffbf5-61dc-4f89-9499-858cb42ea59d", "name": "包装查询信息为单一json对象：退出个人"}, {"parameters": {"jsCode": "return [{\n  exitInstitutions: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 2600], "id": "a2995a3c-1d51-406a-8ecb-c9384ac3029e", "name": "包装查询信息为单一json对象：退出机构"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "643c9300-5c32-4054-b384-b22b0d1dd625", "name": "增持个人股东AI报告接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1680, 1220], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "bff39557-6dae-4334-9566-ab0caa716e60", "name": "增持机构股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1700, 1400], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "80bf9e8c-2f6e-4036-a2d9-c2f6ac96413c", "name": "减持个人股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1700, 1660], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "7f105611-5412-4e94-92b5-061ae5<PERSON>feb", "name": "减持机构股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1700, 1860], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "741c390f-da95-4846-97cb-6f23a02adee4", "name": "新进个人股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1720, 2080], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "5e72c9ea-08c1-4321-973b-1e71a6c96811", "name": "新进机构股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1720, 2260], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "e2393234-248d-4432-8f8a-e601fcf4dd91", "name": "退出个人股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1720, 2420], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "88ff4daa-b680-4439-bb8a-c617021d87f4", "name": "退出机构股东AI报告接口", "type": "n8n-nodes-base.respondToWebhook", "position": [1980, 2660], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：<PERSON>\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1600, 2300], "id": "9c574a44-104f-446f-8864-5225c257e13d", "name": "Sticky Note7"}], "connections": {"AI Agent": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[]]}, "获取增持个人股东": {"main": [[{"node": "包装查询信息为单一json对象：增持个人", "type": "main", "index": 0}]]}, "获取增持机构股东": {"main": [[{"node": "包装查询信息为单一json对象：增持机构", "type": "main", "index": 0}]]}, "输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "设置组织ID": {"main": [[{"node": "判断接口类型进入对应工作流", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "增持个人股东AI报告接口响应", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "合并查询数据库结果和分页参数", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "合并查询数据库结果和分页参数", "type": "main", "index": 1}]]}, "格式化成功响应1": {"main": [[{"node": "增持机构股东AI报告接口", "type": "main", "index": 0}]]}, "增持个人股东AI报告接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "增持机构股东AI报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "获取减持个人股东": {"main": [[{"node": "包装查询信息为单一json对象：减持个人", "type": "main", "index": 0}]]}, "获取减持机构股东": {"main": [[{"node": "包装查询信息为单一json对象：减持机构", "type": "main", "index": 0}]]}, "减持机构股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "减持个人股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "格式化成功响应2": {"main": [[{"node": "减持个人股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent2": {"main": [[{"node": "格式化成功响应2", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "AI Agent2", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应3": {"main": [[{"node": "减持机构股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent3": {"main": [[{"node": "格式化成功响应3", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "AI Agent3", "type": "ai_languageModel", "index": 0}]]}, "获得新进个人股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "新进机构股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "退出个人股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "退出机构股东报告": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "格式化成功响应4": {"main": [[{"node": "新进个人股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent4": {"main": [[{"node": "格式化成功响应4", "type": "main", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "AI Agent4", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应5": {"main": [[{"node": "新进机构股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent5": {"main": [[{"node": "格式化成功响应5", "type": "main", "index": 0}]]}, "OpenAI Chat Model5": {"ai_languageModel": [[{"node": "AI Agent5", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应6": {"main": [[{"node": "退出个人股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent6": {"main": [[{"node": "格式化成功响应6", "type": "main", "index": 0}]]}, "OpenAI Chat Model6": {"ai_languageModel": [[{"node": "AI Agent6", "type": "ai_languageModel", "index": 0}]]}, "格式化成功响应7": {"main": [[{"node": "退出机构股东AI报告接口", "type": "main", "index": 0}]]}, "AI Agent7": {"main": [[{"node": "格式化成功响应7", "type": "main", "index": 0}]]}, "OpenAI Chat Model7": {"ai_languageModel": [[{"node": "AI Agent7", "type": "ai_languageModel", "index": 0}]]}, "获取新进个人股东": {"main": [[{"node": "包装查询信息为单一json对象：新进个人", "type": "main", "index": 0}]]}, "获取新进机构股东": {"main": [[{"node": "包装查询信息为单一json对象：新进机构", "type": "main", "index": 0}]]}, "获取退出个人股东": {"main": [[{"node": "包装查询信息为单一json对象：退出个人", "type": "main", "index": 0}]]}, "获取退出机构股东": {"main": [[{"node": "包装查询信息为单一json对象：退出机构", "type": "main", "index": 0}]]}, "检验组织是否在数据库中存在": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "合并查询数据库结果和分页参数": {"main": [[{"node": "检验组织是否在数据库中存在", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应", "type": "main", "index": 0}]]}, "判断接口类型进入对应工作流": {"main": [[{"node": "获取增持个人股东", "type": "main", "index": 0}], [{"node": "获取增持机构股东", "type": "main", "index": 0}], [{"node": "获取减持个人股东", "type": "main", "index": 0}], [{"node": "获取减持机构股东", "type": "main", "index": 0}], [{"node": "获取新进个人股东", "type": "main", "index": 0}], [{"node": "获取新进机构股东", "type": "main", "index": 0}], [{"node": "获取退出个人股东", "type": "main", "index": 0}], [{"node": "获取退出机构股东", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：增持个人": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：增持机构": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：减持个人": {"main": [[{"node": "AI Agent2", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：减持机构": {"main": [[{"node": "AI Agent3", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：新进个人": {"main": [[{"node": "AI Agent4", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：新进机构": {"main": [[{"node": "AI Agent5", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：退出个人": {"main": [[{"node": "AI Agent6", "type": "main", "index": 0}]]}, "包装查询信息为单一json对象：退出机构": {"main": [[{"node": "AI Agent7", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}