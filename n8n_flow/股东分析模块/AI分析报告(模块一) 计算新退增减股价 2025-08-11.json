{"nodes": [{"parameters": {"jsCode": "/**\n * 输入验证函数 - 检查 organizationId、startDate、endDate 参数\n * <AUTHOR>\n * @created 2025-01-27 16:47:31\n * @description 验证前端传入的查询参数，确保数据完整性和安全性，支持可选的日期范围参数\n * @modified 2025-01-27 16:47:31 - 添加startDate和endDate可选参数验证 - hayden\n */\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const startDate = inputData?.query?.startDate || inputData?.body?.startDate || inputData?.startDate;\n  const endDate = inputData?.query?.endDate || inputData?.body?.endDate || inputData?.endDate;\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 startDate（可选）\n  if (startDate !== undefined && startDate !== null && startDate !== '') {\n    if (typeof startDate !== 'string') {\n      throw new Error('INVALID_START_DATE_TYPE');\n    }\n    if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(startDate)) {\n      throw new Error('INVALID_START_DATE_FORMAT');\n    }\n    const startDateObj = new Date(startDate);\n    if (isNaN(startDateObj.getTime())) {\n      throw new Error('INVALID_START_DATE_VALUE');\n    }\n  }\n\n  // 验证 endDate（可选）\n  if (endDate !== undefined && endDate !== null && endDate !== '') {\n    if (typeof endDate !== 'string') {\n      throw new Error('INVALID_END_DATE_TYPE');\n    }\n    if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(endDate)) {\n      throw new Error('INVALID_END_DATE_FORMAT');\n    }\n    const endDateObj = new Date(endDate);\n    if (isNaN(endDateObj.getTime())) {\n      throw new Error('INVALID_END_DATE_VALUE');\n    }\n  }\n\n  // 验证日期范围逻辑\n  if (startDate && endDate) {\n    const startDateObj = new Date(startDate);\n    const endDateObj = new Date(endDate);\n    if (startDateObj > endDateObj) {\n      throw new Error('INVALID_DATE_RANGE');\n    }\n  }\n\n  return {\n    organizationId: id.trim(),\n    ...(startDate && { startDate: startDate.trim() }),\n    ...(endDate && { endDate: endDate.trim() })\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const validatedData = validateInput(inputData);\n\n  return [{\n    json: {\n      ...validatedData,\n      type: inputData.webhookUrl,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_START_DATE_TYPE': '无效的startDate类型：参数必须是字符串。',\n    'INVALID_START_DATE_FORMAT': '无效的startDate格式：日期格式必须为YYYY-MM-DD。',\n    'INVALID_START_DATE_VALUE': '无效的startDate值：请提供有效的日期。',\n    'INVALID_END_DATE_TYPE': '无效的endDate类型：参数必须是字符串。',\n    'INVALID_END_DATE_FORMAT': '无效的endDate格式：日期格式必须为YYYY-MM-DD。',\n    'INVALID_END_DATE_VALUE': '无效的endDate值：请提供有效的日期。',\n    'INVALID_DATE_RANGE': '无效的日期范围：开始日期不能晚于结束日期。'\n  };\n  return messages[code] || '输入验证失败';\n}"}, "id": "7c32bfd1-249f-4459-8bcf-80b5c32d86f6", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-2100, 380], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "f6ae4ce4-cda1-4690-81ad-073be409ff01", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1880, 380], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "1db7b403-681d-4ac0-940d-6648ace6fca6", "name": "数据库错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1720, 680], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1680, 400], "id": "b2bea59c-12d9-4e4b-9acd-10460f7407df", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "d959a1de-8890-4e5d-9c9c-864f9e502b74", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [480, 360], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=# 角色：\n你是一位顶尖的股权数据分析专家，以精准捕捉数据核心、凝练独到洞察、语言专业流畅著称。\n\n# 目标：\n基于一个包含多个独立数据对象的JSON数组（这些对象共同构成了对公司最新股东情况的多方面描述），严格按照预设的核心分析维度，为公司董秘办及管理层生成一份关于公司股东结构与动态的核心数据洞察与总结。你需要将关键数据指标自然地融入分析叙述中，展现出对数据背后趋势的专业理解，严禁任何形式的主观臆断或超出数据范围的臆想。\n核心输入： 一个JSON数组{{ JSON.stringify($('合并股东数据和股价').all(), null, 2)}}\n\n# 技能：\n- 数据精准定位与运用： AI必须准确地从JSON数组的不同对象和字段中提取并运用所需数据。\n- 量化支撑与趋势解读： 所有分析和解读都必须基于具体的量化数据，AI应能从数据变化中解读出客观的趋势。\n- 自然流畅的叙述，避免模板化： AI应自主组织语言，将关键数据和基于数据的判断自然地融入总结性段落中。避免严格套用固定句式，力求行文流畅、专业且易于理解，更像一位分析师的口述总结。\n- 客观中立，杜绝臆想： 严格基于输入数据进行总结，不进行任何无数据支撑的推测、原因分析或市场影响预测。\n- 结构清晰，标题规范： 最终输出应具有清晰的模块化结构，仅使用下面“分析维度与核心关注点指引”中指定的一级模块标题。\n\n# 分析维度与核心关注点指引：(AI需根据以下每个维度的核心关注点，结合JSON数据，自主构建分析段落。)\n一、股东结构总体概览：\n- 强制提取字段：companyName、companyCode、registerDate、totalShares、totalShareholders、institutionShares、totalInstitutions、top100SharesChange\n- 对比字段：prevTotalShareholders、shareholdersChange\n- 严格按照如下输出示例：[数据截止日期]，公司简称为[公司名称]，股票代码为[股票代码]，总股本[稳定保持/增加/减少]至[总股本数量]，股东户数[增长/下降/不变]至[数值]，前100名股东总持股数变化[前100股东持股变化数值]股,变化率为[top100持股变化率]。持股机构数量[增长/下降/稳定不变]至[数值]，[若增加：增加的持股机构为：[新进机构股东名称1][新进机构股东名称1]]等等。\n\n二、新进股东重点关注：\n- 首先，筛选包含\"behaviorType\": \"new\"的相关数据作为新进股东数据，对应提取如下字段：股东名称shareholdername、当期持股份额sharesincashaccount、最新持仓市值currentMarketValue、最新持仓收益currentProfit；其次，通过最新持仓收益正负判断盈亏状态，参考输出要求，列出新进个人、机构股东的持仓情况及对应持股分析变动。\n- 持股变动分析：从以下角度评价持股变动或提出未来该股东操作的可能性：从股东本期持仓数量、持仓比例、预估持仓成本、最新持仓数值、最新持仓收益等数据，分析该股东新进股票份额的盈亏情况、未来可能操作，例如新进股东最新持仓收益为正，未来有增持的可能。\n- 输出要求：\n**新进个人股东重点关注**：[股东名称1][当期持股份额]，持仓市值达到[最新持仓市值]，目前处于[盈利/亏损/持平]状态，预估最新持仓收益为[最新持仓收益]；[持股变动分析]\n**新进机构股东重点关注**：[股东名称2][当期持股份额]，持仓市值达到[最新持仓市值]，目前处于[盈利/亏损/持平]状态，预估最新持仓收益为[最新持仓收益]；[持股变动分析]\n- 要求分点列出每位股东\n\n 三、增持股东重点关注：\n- 首先，筛选包含behaviorType: increased的相关数据作为增持股东数据，对应提取如下字段：股东名称\"name\"、增持份额shares increase、最新持仓收益profitLoss；其次，通过最新持仓收益正负判断盈亏状态，参考输出要求，列出增持个人、机构股东的持仓情况及对应持股变动分析。\n- 持股变动分析：从以下角度评价持股变动或提出未来该股东操作的可能性：从股东增持股数、持股增幅、本期持仓数量、本期持仓比例、预估持仓成本、最新持仓数值、最新持仓收益等数据，分析该股东增持股票份额的盈亏情况、未来可能操作，例如增持后股数未超过1000万股，但增持股数超过200万股，且目前增持股份均处于盈利状态，未来有较高低位补仓得可能。\n- 输出要求：\n**增持个人股东重点关注**：[股东名称1]增持额达到[增持份额]，目前处于[盈利/亏损/持平]状态，预估[收益/亏损/保持]至[最新持仓收益]；[持股变动分析]\n**增持机构股东重点关注**：[股东名称2]增持额达到[增持份额]，目前处于[盈利/亏损/持平]状态，预估[收益/亏损/保持]至[最新持仓收益]；[持股变动分析]\n- 要求分点列出每位股东\n\n四、减持股东重点关注：\n- 首先，筛选包含behaviorType: decreased的相关数据作为减持股东数据，对应提取如下字段：股东名称\"name\"、减持股数shares decrease、减持实现收益decreaseProfit、最新持仓收益totalProfit；其次，通过最新持仓收益正负判断盈亏状态，参考输出要求，列出减持股东的持仓情况及持股变动分析。\n- 持股变动分析：从以下角度评价持股变动或提出未来该股东操作的可能性：从股东持股占比、持股减幅、减持次数、减持实现收益、剩余股票账面收益等数据，分析该股东减持的盈亏情况、未来可能操作，例如股东减持后持股份额低于5%大股东线，继续减持可能性较高；\n- 输出要求：\n**减持个人股东重点关注**：[股东名称1][减持股数]，减持预估出现[盈利/亏损/持平]状态，[盈利/亏损/保持][减持实现收益]，剩余股票账面[盈利/亏损/保持][最新持仓收益]；[持股变动分析]\n**减持机构股东重点关注**：[股东名称1][减持股数]，减持预估出现[盈利/亏损/持平]状态，[盈利/亏损/保持][减持实现收益]，剩余股票账面[盈利/亏损/保持][最新持仓收益]；[持股变动分析]\n- 要求分点列出每位股东\n\n五、退出股东重点关注：\n- 首先，筛选包含behaviorType: exited的相关数据作为退出股东数据，对应提取如下字段：股东名称\"name\"、退出股数shares in cash account、预估退出收益profitLoss；根据预估退出收益由大到小对股东进行排序；其次，通过预估退出损益{profitLoss}正负判断是盈利退出还是亏损退出，参考输出要求，列出退出股东的持仓情况及持股变动方向。\n- 持股变动分析：从以下角度评价持股变动或提出未来该股东操作的可能性：从股东原持股比例、退出股数、、预估退出收益、预估持仓成本、预估退出总额等数据，分析该股东退出的盈亏情况、未来可能操作，例如某股东盈利退出，在未来有较高低位重新建仓可能性。\n- 输出要求：\n**退出个人股东重点关注**：[股东名称1][退出股数]，[盈利退出/亏损退出]，预估损益达到[预估退出收益],；[持股变动分析]\n**退出机构股东重点关注**：[股东名称1][退出股数]，[盈利退出/亏损退出]，预估损益达到[预估退出收益]；[持股变动分析]\n- 要求分点列出每位股东\n\n六、不变股东重点关注：\n- 首先，筛选包含behaviorType: unchanged的相关数据作为不变股东数据，对应提取如下字段：股东名称\"name\"、最新持仓数值shares in cash account、最新持仓收益profitLoss；根据最新持仓收益由大到小对股东进行排序；其次，通过最新持仓收益正负判断盈利、亏损还是持平，参考输出要求，列出不变股东的持仓情况及持仓变动分析。\n- 持股变动分析：从以下角度评价持股变动或提出未来该股东操作的可能性：从股东持股数、持股比例、最新持股数值、最新持股收益等数据，分析该股东维持股票份额的盈亏情况、未来可能操作，例如某股东持股数量不变且实现盈利，在未来有继续看涨的可能性。\n- 输出要求：\n**不变个人股东重点关注**：[股东名称1]最新持仓市值达到[最新持仓数值]，预估[盈利/亏损/持平][最新持仓收益]；[持股变动分析]\n**不变机构股东重点关注**：[股东名称1]最新持仓市值达到[最新持仓数值]，预估[盈利/亏损/持平][最新持仓收益]；[持股变动分析]\n- 要求分点列出每位股东\n\n# 动态分析输出模板（数据驱动型表述）：\n- **股东总体变动**：\n- **新进股东重点关注**：重点关注以下新进股东持股份额前五的股东情况出及持股变动分析\n- **增持股东重点关注**：重点关注以下增持股数前五的股东情况及持股变动分析\n- **减持股东重点关注**：重点关注以下减持股数前五的股东情况及持股变动分析\n- **退出股东重点关注**：重点关注盈利前五的退出股东情况及持股变动分析\n- **不变股东重点关注**：重点关注盈利前五的不变股东情况及持股变动分析\n\n# 按照如下思维链进行思考：\n- 理解提示词以及输入的所有数据字段含义\n- 从behaviorType字段区分是新进股东、增持、减持、退出、不变股东，区分持股人字段name是个人还是机构，深入理解分析维度与核心关注点指引进行计算筛选\n- 深度了解股价波动与股东持股行为的动机，进行专业的持股变动分析，并将字段单位为元改为万元单位，根据动态分析输出模板完整结果，必须分点列出每位股东情况及对应的持股变动分析\n- 再次对比每位股东及对应字段数值，确保数据真实来源于输入且符合分析维度要求，直到数据完全准确为止\n\n# 限制\n- 不可随意编造数据，只能从输出数据中进行提取及计算", "options": {}}, "id": "4cb86762-7505-4819-80b8-6b4ee8d9d430", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [180, 380], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "0c281694-980a-416b-8115-851a22771208", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [180, 580], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "bae69290-103e-4e34-9a84-f51a65185e57", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1700, 200], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取最新两期公司完整信息（包括机构持股数和前100名股东持股统计）并附加最早日期\n-- <AUTHOR>\n-- @created 2025-01-27 10:24:01\n-- @updated 2025-01-27 10:30:15 hayden 修复GROUP BY错误，重构前100名股东查询逻辑\n-- @description 增加前100名股东持股变化计算，使用普通账户持股数量进行统计\nWITH latest_periods AS (\n  SELECT \n    \"companyName\",\n    \"companyCode\", \n    \"totalShares\",\n    \"totalShareholders\",\n    \"totalInstitutions\",\n    \"institutionShares\",\n    \"registerDate\",\n    \"registryId\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\n-- 计算当期前100名股东持股总数（使用普通账户持股数量）\ncurrent_top100_ranked AS (\n  SELECT \n    s.\"registryId\",\n    COALESCE(s.\"sharesInCashAccount\", 0) as shares,\n    ROW_NUMBER() OVER (ORDER BY COALESCE(s.\"sharesInCashAccount\", 0) DESC) as rank_num\n  FROM latest_periods lp\n  JOIN shareholder s ON s.\"registryId\" = lp.\"registryId\"\n  WHERE lp.rn = 1\n    AND s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ncurrent_top100 AS (\n  SELECT \n    COALESCE(SUM(shares), 0) as \"currentTop100Shares\"\n  FROM current_top100_ranked\n  WHERE rank_num <= 100\n),\n-- 计算上期前100名股东持股总数（使用普通账户持股数量）\nprev_top100_ranked AS (\n  SELECT \n    s.\"registryId\",\n    COALESCE(s.\"sharesInCashAccount\", 0) as shares,\n    ROW_NUMBER() OVER (ORDER BY COALESCE(s.\"sharesInCashAccount\", 0) DESC) as rank_num\n  FROM latest_periods lp\n  JOIN shareholder s ON s.\"registryId\" = lp.\"registryId\"\n  WHERE lp.rn = 2\n    AND s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nprev_top100 AS (\n  SELECT \n    COALESCE(SUM(shares), 0) as \"prevTop100Shares\"\n  FROM prev_top100_ranked\n  WHERE rank_num <= 100\n),\ncurrent_prev AS (\n  SELECT \n    MAX(CASE WHEN rn = 1 THEN \"companyName\" END) as \"currentCompanyName\",\n    MAX(CASE WHEN rn = 1 THEN \"companyCode\" END) as \"currentCompanyCode\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShares\" END) as \"currentTotalShares\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShareholders\" END) as \"currentTotalShareholders\",\n    MAX(CASE WHEN rn = 1 THEN \"totalInstitutions\" END) as \"currentTotalInstitutions\",\n    MAX(CASE WHEN rn = 1 THEN \"institutionShares\" END) as \"currentInstitutionShares\",\n    MAX(CASE WHEN rn = 1 THEN \"registerDate\" END) as \"currentRegisterDate\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShares\" END) as \"prevTotalShares\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShareholders\" END) as \"prevTotalShareholders\", \n    MAX(CASE WHEN rn = 2 THEN \"totalInstitutions\" END) as \"prevTotalInstitutions\",\n    MAX(CASE WHEN rn = 2 THEN \"institutionShares\" END) as \"prevInstitutionShares\"\n  FROM latest_periods\n  WHERE rn <= 2\n),\noldest_period AS (\n  SELECT MIN(\"registerDate\") AS \"oldestRegisterDate\"\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n)\nSELECT \n  cp.\"currentCompanyName\" as \"companyName\",\n  cp.\"currentCompanyCode\" as \"companyCode\",\n  cp.\"currentRegisterDate\" as \"registerDate\",\n  o.\"oldestRegisterDate\",\n  cp.\"currentTotalShares\" as \"totalShares\",\n  cp.\"currentTotalShareholders\" as \"totalShareholders\",\n  cp.\"currentTotalInstitutions\" as \"totalInstitutions\",\n  cp.\"currentInstitutionShares\" as \"institutionShares\",\n  (cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\") as \"individualShareholders\",\n  cp.\"prevTotalShares\",\n  cp.\"prevTotalShareholders\",\n  cp.\"prevTotalInstitutions\",\n  cp.\"prevInstitutionShares\",\n  (cp.\"prevTotalShareholders\" - cp.\"prevTotalInstitutions\") as \"prevIndividualShareholders\",\n  -- 原有变化计算\n  (cp.\"currentTotalShareholders\" - COALESCE(cp.\"prevTotalShareholders\", cp.\"currentTotalShareholders\")) as \"shareholdersChange\",\n  (cp.\"currentTotalInstitutions\" - COALESCE(cp.\"prevTotalInstitutions\", cp.\"currentTotalInstitutions\")) as \"institutionsChange\",\n  ((cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\") - \n   COALESCE((cp.\"prevTotalShareholders\" - cp.\"prevTotalInstitutions\"), \n            (cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\"))) as \"individualShareholdersChange\",\n  -- 原有百分比变化\n  CASE \n    WHEN cp.\"prevTotalShares\" IS NOT NULL AND cp.\"prevTotalShares\" > 0 \n    THEN ROUND(((cp.\"currentTotalShares\" - cp.\"prevTotalShares\") / cp.\"prevTotalShares\" * 100)::numeric, 2)\n    ELSE 0 \n  END as \"totalSharesChangePercent\",\n  CASE \n    WHEN cp.\"prevInstitutionShares\" IS NOT NULL AND cp.\"prevInstitutionShares\" > 0 \n    THEN ROUND(((cp.\"currentInstitutionShares\" - cp.\"prevInstitutionShares\") / cp.\"prevInstitutionShares\" * 100)::numeric, 2)\n    ELSE 0 \n  END as \"institutionSharesChangePercent\",\n  -- 新增前100名股东持股统计\n  ct.\"currentTop100Shares\",\n  pt.\"prevTop100Shares\",\n  -- 前100名股东持股变化数额\n  (ct.\"currentTop100Shares\" - COALESCE(pt.\"prevTop100Shares\", ct.\"currentTop100Shares\")) as \"top100SharesChange\",\n  -- 前100名股东持股变化率\n  CASE \n    WHEN pt.\"prevTop100Shares\" IS NOT NULL AND pt.\"prevTop100Shares\" > 0 \n    THEN ROUND(((ct.\"currentTop100Shares\" - pt.\"prevTop100Shares\") / pt.\"prevTop100Shares\" * 100)::numeric, 2)\n    ELSE 0 \n  END as \"top100SharesChangePercent\"\nFROM current_prev cp\nCROSS JOIN oldest_period o\nCROSS JOIN current_top100 ct\nCROSS JOIN prev_top100 pt;", "options": {}}, "id": "2eb725f5-78d0-4559-a76f-28bf6f63dc66", "name": "获取最新一期公司完整信息及较上期变化", "type": "n8n-nodes-base.postgres", "position": [-820, 80], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取最新两期信用数据\nWITH latest_dates AS (\n  SELECT \n    \"registerDate\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n),\ncurrent_period AS (\n  SELECT \n    COUNT(DISTINCT CASE WHEN \"marginAccount\" IS NOT NULL AND \"marginAccount\" != '' THEN \"shareholderId\" END) as \"currentCreditAccountCount\",\n    SUM(COALESCE(\"sharesInMarginAccount\", 0)) as \"currentTotalMarginShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = (SELECT \"registerDate\" FROM latest_dates WHERE rn = 1)\n),\nprev_period AS (\n  SELECT \n    COUNT(DISTINCT CASE WHEN \"marginAccount\" IS NOT NULL AND \"marginAccount\" != '' THEN \"shareholderId\" END) as \"prevCreditAccountCount\",\n    SUM(COALESCE(\"sharesInMarginAccount\", 0)) as \"prevTotalMarginShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = (SELECT \"registerDate\" FROM latest_dates WHERE rn = 2)\n)\nSELECT \n  c.\"currentCreditAccountCount\" as \"creditAccountCount\",\n  c.\"currentTotalMarginShares\" as \"totalMarginShares\",\n  COALESCE(p.\"prevCreditAccountCount\", c.\"currentCreditAccountCount\") as \"prevCreditAccountCount\",\n  COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\") as \"prevTotalMarginShares\",\n  -- 计算变化\n  (c.\"currentCreditAccountCount\" - COALESCE(p.\"prevCreditAccountCount\", c.\"currentCreditAccountCount\")) as \"creditAccountCountChange\",\n  CASE \n    WHEN COALESCE(p.\"prevTotalMarginShares\", 0) > 0 \n    THEN ROUND(((c.\"currentTotalMarginShares\" - COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\")) / COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\") * 100)::numeric, 2)\n    ELSE 0 \n  END as \"totalMarginSharesChangePercent\"\nFROM current_period c\nCROSS JOIN prev_period p", "options": {}}, "id": "ac72e38e-a108-4f91-a410-21d83d9431c9", "name": "获取最新一期信用账户数据及较上期变化", "type": "n8n-nodes-base.postgres", "position": [-820, 300], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取前十大、前二十大股东持股比例及户均持股数据（修复版）\nWITH latest_periods AS (\n  SELECT \n    \"registerDate\",\n    \"totalShares\",\n    \"totalShareholders\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\n-- 获取最新两期的基础数据\ncompany_data AS (\n  SELECT \n    MAX(CASE WHEN rn = 1 THEN \"registerDate\" END) as \"currentRegisterDate\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShares\" END) as \"currentTotalShares\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShareholders\" END) as \"currentTotalShareholders\",\n    MAX(CASE WHEN rn = 2 THEN \"registerDate\" END) as \"prevRegisterDate\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShares\" END) as \"prevTotalShares\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShareholders\" END) as \"prevTotalShareholders\"\n  FROM latest_periods\n  WHERE rn <= 2\n),\n-- 最新期股东持股比例数据\ncurrent_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as ranking\n  FROM shareholder s, company_data cd\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = cd.\"currentRegisterDate\"\n),\n-- 上一期股东持股比例数据\nprev_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as ranking\n  FROM shareholder s, company_data cd\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = cd.\"prevRegisterDate\"\n),\n-- 最新期集中度统计\ncurrent_stats AS (\n  SELECT \n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 10 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"currentTop10Ratio\",\n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 20 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"currentTop20Ratio\"\n  FROM current_period_data\n),\n-- 上一期集中度统计\nprev_stats AS (\n  SELECT \n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 10 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"prevTop10Ratio\",\n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 20 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"prevTop20Ratio\"\n  FROM prev_period_data\n)\nSELECT \n  -- 最新期数据\n  cs.\"currentTop10Ratio\" as \"top10ShareholdingRatio\",\n  cs.\"currentTop20Ratio\" as \"top20ShareholdingRatio\", \n  -- 使用company_info表直接计算户均持股数\n  ROUND(\n    CASE \n      WHEN NULLIF(cd.\"currentTotalShareholders\", 0) IS NULL THEN 0\n      ELSE (cd.\"currentTotalShares\" / cd.\"currentTotalShareholders\") \n    END::numeric, 2\n  ) as \"avgSharesPerHolder\",\n  \n  -- 上一期数据\n  COALESCE(ps.\"prevTop10Ratio\", cs.\"currentTop10Ratio\") as \"prevTop10ShareholdingRatio\",\n  COALESCE(ps.\"prevTop20Ratio\", cs.\"currentTop20Ratio\") as \"prevTop20ShareholdingRatio\",\n  \n  -- 上一期户均持股数\n  ROUND(\n    CASE \n      WHEN NULLIF(COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\"), 0) IS NULL THEN 0\n      ELSE (COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\"))\n    END::numeric, 2\n  ) as \"prevAvgSharesPerHolder\",\n  \n  -- 计算变化\n  ROUND((cs.\"currentTop10Ratio\" - COALESCE(ps.\"prevTop10Ratio\", cs.\"currentTop10Ratio\"))::numeric, 2) as \"top10RatioChange\",\n  ROUND((cs.\"currentTop20Ratio\" - COALESCE(ps.\"prevTop20Ratio\", cs.\"currentTop20Ratio\"))::numeric, 2) as \"top20RatioChange\",\n  \n  -- 户均持股数变化\n  ROUND(\n    CASE \n      WHEN NULLIF(cd.\"currentTotalShareholders\", 0) IS NULL OR NULLIF(cd.\"prevTotalShareholders\", 0) IS NULL THEN 0\n      ELSE ((cd.\"currentTotalShares\" / cd.\"currentTotalShareholders\") - \n            (COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\")))\n    END::numeric, 2\n  ) as \"avgSharesPerHolderChange\"\n  \nFROM company_data cd\nCROSS JOIN current_stats cs\nCROSS JOIN prev_stats ps;\n", "options": {}}, "id": "581de708-20e7-411e-9d71-18ca7124b33a", "name": "获取股东集中度及户均持股数据", "type": "n8n-nodes-base.postgres", "position": [-800, 480], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}]}, "options": {}}, "id": "bc5633a5-e39f-460a-a997-8370660f0a05", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [-1060, 380], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_period AS (\n  SELECT \n    \"registerDate\",\n    \"totalShares\"\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\" DESC\n  LIMIT 1\n),\ncurrent_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) as ranking\n  FROM shareholder s, latest_period lp\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = lp.\"registerDate\"\n)\nSELECT\n  -- 前1大\n  SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top1_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top1_shareholding_ratio,\n  -- 前10大\n  SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top10_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top10_shareholding_ratio,\n  -- 前100大\n  SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top100_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top100_shareholding_ratio\nFROM current_period_data\n;", "options": {}}, "id": "958eecaf-feee-4221-a99e-70682e446bdf", "name": "获取核心持股数据", "type": "n8n-nodes-base.postgres", "position": [-800, 680], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 股东类型统计查询（包含上期对比数据及数据验证）\n * <AUTHOR>\n * @created 2025-07-17 18:41:42\n * @description 查询最新报告期各机构股东类型的户数、持股数及与上期的对比变化，包含数据验证反馈\n * @update 2025-07-17 19:10:27 hayden 新增数据验证逻辑，防止只有一期名册数据的情况\n */\nWITH DateRange AS (\n    -- 获取最新和上期报告期日期\n    SELECT\n        MAX(\"registerDate\") as latest_date,\n        -- 获取上期日期（最新日期之前的最大日期）\n        (SELECT MAX(\"registerDate\")\n         FROM \"shareholder\"\n         WHERE \"organizationId\" = '{{ $json.organizationId }}'\n         AND \"registerDate\" < (SELECT MAX(\"registerDate\") FROM \"shareholder\" WHERE \"organizationId\" = '{{ $json.organizationId }}')\n        ) as prev_date,\n        -- 统计该组织的报告期数量\n        COUNT(DISTINCT \"registerDate\") as period_count\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n),\nDataValidation AS (\n    -- 数据验证：检查是否有足够的数据进行对比分析\n    SELECT\n        dr.latest_date,\n        dr.prev_date,\n        dr.period_count,\n        CASE\n            WHEN dr.period_count < 2 THEN 'INSUFFICIENT_DATA'\n            WHEN dr.prev_date IS NULL THEN 'NO_PREVIOUS_PERIOD'\n            ELSE 'VALID'\n        END as validation_status,\n        CASE\n            WHEN dr.period_count < 2 THEN '数据不足：该组织仅有一期股东名册数据，无法进行期间对比分析。请确保至少上传两期股东名册数据。'\n            WHEN dr.prev_date IS NULL THEN '缺少上期数据：无法获取上期报告期数据，请检查数据完整性。'\n            ELSE '数据验证通过'\n        END as validation_message\n    FROM DateRange dr\n),\nCurrentTypeStats AS (\n    -- 计算最新报告期每种机构股东类型的户数和持股数\n    SELECT\n        \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS typeCount,\n        -- 确保正确转换为数值类型后再求和\n        SUM(CAST(\"numberOfShares\" AS DECIMAL(17,2))) AS typeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    -- 使用模糊匹配排除个人股东类型和知名牛散\n    AND \"shareholderType\" NOT LIKE '%个人%'\n    AND \"shareholderType\" != '知名牛散'\n    -- 只查询最新日期的数据\n    AND \"registerDate\" = (SELECT latest_date FROM DateRange)\n    GROUP BY \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n    -- 只保留有股东的类型（typeCount > 0）\n    HAVING COUNT(DISTINCT \"shareholderId\") > 0\n),\nPrevTypeStats AS (\n    -- 计算上期报告期每种机构股东类型的户数和持股数\n    SELECT\n        \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS prevTypeCount,\n        SUM(CAST(\"numberOfShares\" AS DECIMAL(17,2))) AS prevTypeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    AND \"shareholderType\" NOT LIKE '%个人%'\n    AND \"shareholderType\" != '知名牛散'\n    AND \"registerDate\" = (SELECT prev_date FROM DateRange WHERE prev_date IS NOT NULL)\n    GROUP BY \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n    HAVING COUNT(DISTINCT \"shareholderId\") > 0\n),\nCurrentCompanyInfo AS (\n    -- 获取最新报告期的CompanyInfo数据作为分母\n    SELECT\n        sr.\"registerDate\",\n        ci.\"totalInstitutions\",\n        ci.\"institutionShares\"\n    FROM \"company_info\" ci\n    JOIN \"shareholder_registry\" sr ON ci.\"registryId\" = sr.id\n    WHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\n    -- 只查询最新日期的数据\n    AND sr.\"registerDate\" = (SELECT latest_date FROM DateRange)\n),\nPrevCompanyInfo AS (\n    -- 获取上期报告期的CompanyInfo数据作为分母\n    SELECT\n        sr.\"registerDate\",\n        ci.\"totalInstitutions\" as \"prevTotalInstitutions\",\n        ci.\"institutionShares\" as \"prevInstitutionShares\"\n    FROM \"company_info\" ci\n    JOIN \"shareholder_registry\" sr ON ci.\"registryId\" = sr.id\n    WHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\n    AND sr.\"registerDate\" = (SELECT prev_date FROM DateRange WHERE prev_date IS NOT NULL)\n),\nCombinedStats AS (\n    -- 合并当期和上期数据\n    SELECT\n        COALESCE(cts.shareholderType, pts.shareholderType) AS shareholderType,\n        COALESCE(cts.typeCount, 0) AS typeCount,\n        COALESCE(cts.typeShares, 0) AS typeShares,\n        COALESCE(pts.prevTypeCount, 0) AS prevTypeCount,\n        COALESCE(pts.prevTypeShares, 0) AS prevTypeShares\n    FROM CurrentTypeStats cts\n    FULL OUTER JOIN PrevTypeStats pts ON cts.shareholderType = pts.shareholderType\n    -- 只保留当期有数据的类型\n    WHERE cts.shareholderType IS NOT NULL\n)\n\n-- 构建最终结果\nSELECT\n    -- 数据验证信息\n    dv.validation_status as \"validationStatus\",\n    dv.validation_message as \"validationMessage\",\n    dv.period_count as \"periodCount\",\n    \n    -- 使用TO_CHAR将日期转换为ISO格式字符串\n    CASE\n        WHEN dv.validation_status = 'VALID' THEN\n            TO_CHAR(dv.latest_date AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"')\n        ELSE NULL\n    END AS \"registerDate\",\n    \n    CASE\n        WHEN dv.validation_status = 'VALID' AND dv.prev_date IS NOT NULL THEN\n            TO_CHAR(dv.prev_date AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"')\n        ELSE NULL\n    END AS \"prevRegisterDate\",\n    \n    -- 只有验证通过才返回股东类型数据\n    CASE\n        WHEN dv.validation_status = 'VALID' THEN\n            json_agg(\n                json_build_object(\n                    'shareholderType', cs.shareholderType,\n                    'typeCount', CAST(cs.typeCount AS TEXT),\n                    'typeShares', CAST(cs.typeShares AS TEXT),\n                    'typePercentage', CAST(CASE\n                        WHEN cci.\"totalInstitutions\" > 0 THEN\n                            CAST((cs.typeCount::numeric / cci.\"totalInstitutions\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    'sharesPercentage', CAST(CASE\n                        WHEN cci.\"institutionShares\" > 0 THEN\n                            CAST((cs.typeShares::numeric / cci.\"institutionShares\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    -- 上期数据\n                    'prevTypeCount', CAST(cs.prevTypeCount AS TEXT),\n                    'prevTypeShares', CAST(cs.prevTypeShares AS TEXT),\n                    'prevTypePercentage', CAST(CASE\n                        WHEN pci.\"prevTotalInstitutions\" > 0 THEN\n                            CAST((cs.prevTypeCount::numeric / pci.\"prevTotalInstitutions\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    'prevSharesPercentage', CAST(CASE\n                        WHEN pci.\"prevInstitutionShares\" > 0 THEN\n                            CAST((cs.prevTypeShares::numeric / pci.\"prevInstitutionShares\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    -- 变化数据\n                    'typeCountChange', CAST((cs.typeCount - cs.prevTypeCount) AS TEXT),\n                    'typeSharesChange', CAST((cs.typeShares - cs.prevTypeShares) AS TEXT),\n                    'typeCountChangePercent', CAST(CASE\n                        WHEN cs.prevTypeCount > 0 THEN\n                            CAST(((cs.typeCount::numeric - cs.prevTypeCount::numeric) / cs.prevTypeCount::numeric) * 100 AS DECIMAL(10,2))\n                        WHEN cs.typeCount > 0 AND cs.prevTypeCount = 0 THEN 100.00\n                        ELSE 0\n                    END AS TEXT),\n                    'typeSharesChangePercent', CAST(CASE\n                        WHEN cs.prevTypeShares > 0 THEN\n                            CAST(((cs.typeShares::numeric - cs.prevTypeShares::numeric) / cs.prevTypeShares::numeric) * 100 AS DECIMAL(10,2))\n                        WHEN cs.typeShares > 0 AND cs.prevTypeShares = 0 THEN 100.00\n                        ELSE 0\n                    END AS TEXT)\n                )\n            )\n        ELSE NULL\n    END AS \"shareholderTypes\"\n    \nFROM DataValidation dv\nLEFT JOIN CombinedStats cs ON dv.validation_status = 'VALID'\nLEFT JOIN CurrentCompanyInfo cci ON dv.latest_date = cci.\"registerDate\" AND dv.validation_status = 'VALID'\nLEFT JOIN PrevCompanyInfo pci ON dv.prev_date = pci.\"registerDate\" AND dv.validation_status = 'VALID'\nGROUP BY dv.validation_status, dv.validation_message, dv.period_count, dv.latest_date, dv.prev_date;", "options": {}}, "id": "4f8b209b-6893-4c10-831c-55a36873d786", "name": "股东类型户数和持股分布", "type": "n8n-nodes-base.postgres", "position": [-800, 860], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"content": "本接口端点：company-general-report", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2700, 380], "id": "010cecd8-1fc7-40be-a162-2f958e2987cf", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "检验是否输入参数id", "height": 80, "width": 160}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2040, 520], "id": "756e1f4b-427a-4704-a206-bf7cf97aa204", "name": "Sticky Note1"}, {"parameters": {"content": "检验是否能在数据库中找到对应组织", "height": 80, "width": 280}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1640, 320], "id": "098b4616-b411-4207-aeea-e38e1be4a303", "name": "Sticky Note2"}, {"parameters": {"content": "获得所有公司概览数据并合并为单一item方便传入AI Agent中", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-560, 460], "id": "ffbb296f-294c-4291-a8b2-9e61a87a9760", "name": "Sticky Note3"}, {"parameters": {"content": "获取公司概览数据，并传入豆包模型中进行AI报告生成", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [100, 260], "id": "96337192-0660-4473-9a51-d0cd09a8e4bc", "name": "Sticky Note4"}, {"parameters": {"content": "格式化AI报告，并且返回数据", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [500, 260], "id": "84ab0541-5f0f-48d0-8318-d20b266d03de", "name": "Sticky Note5"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1520, 400], "id": "26207cf7-1cf2-4735-83e0-b1df0de778f0", "name": "检验组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1940, 680], "id": "9c385b4c-82b1-4df0-8bcf-ae4ad2f6c6a9", "name": "格式化错误信息"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-620, 160], "id": "2c9bfed1-cf9b-489b-bbe6-28dab8296ce2", "name": "合并数据"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-480, 300], "id": "66f5ec42-8d8d-4096-aa30-af3f96373cbc", "name": "合并数据1"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-380, 560], "id": "555cae8e-6856-474e-acfc-0788ee6dea70", "name": "合并数据2"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-160, 560], "id": "412e51b0-fda3-47a2-8ac1-3e1c4b59b034", "name": "合并数据3"}, {"parameters": {"jsCode": "return [{\n  company_info: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-20, 820], "id": "7b8a2292-0172-44cd-a8b1-a7d190daa254", "name": "包装数据为单一json对象"}, {"parameters": {"httpMethod": "POST", "path": "company-general-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2340, 380], "id": "4f11e146-d53f-4c44-8dc8-ce499b6c0137", "name": "公司概览AI报告接口", "webhookId": "d3bd7471-133a-4312-a6bf-67877a6c378b"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "c2a38ba7-951e-48dd-8665-06906e7f78cc", "name": "公司概览AI报告接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [680, 360], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2680, 640], "id": "6d983c7e-dbfd-4165-a653-957bbba4db61", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"output\": AI报告实际内容\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:19:47.742Z\"\n\t}\n]", "height": 220, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, 300], "id": "fc0e4bea-2db5-4aa2-8e43-a232e0ef71e7", "name": "Sticky Note8"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indipara\":[{\"indicator\":\"ths_avg_close_int_stock\",\"indiparams\":[\"{{ $('检查数据库结果').item.json.startdate }}\",\"{{ $('检查数据库结果').item.json.enddate }}\",\"100\",\"\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, 1080], "id": "da4621f7-b823-4cfa-acda-1f28a1a06636", "name": "获取区间股价（收盘价）"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/real_time_quotation", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indicators\":\"latest\"}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-140, 1280], "id": "867a7860-87ce-4d27-b990-7fe7a3f06a6a", "name": "最新股价"}, {"parameters": {"jsCode": "// 数据库查询结果处理\nconst inputData = $input.all();\nif (!inputData || inputData.length === 0) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'NO_DATABASE_RESULT',\n      message: '数据库查询未返回任何结果',\n      field: 'database',\n      value: null,\n      timestamp: new Date().toISOString(),\n      type: 'DATABASE_ERROR'\n    }\n  };\n}\n\n// 获取查询结果\nconst dbResult = inputData[0];\nif (!dbResult || !dbResult.json || dbResult.json.length === 0) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'ORGANIZATION_NOT_FOUND',\n      message: '未找到对应的组织ID，请检查organizationcode是否正确',\n      field: 'organizationcode',\n      value: $('参数验证逻辑').item.json.organizationcode,\n      timestamp: new Date().toISOString(),\n      type: 'VALIDATION_ERROR'\n    }\n  };\n}\n\nconst result = dbResult.json;\nconst companyCode = result?.companyCode;\nconst recentDates = result?.recent_dates;\n\nif (!companyCode) {\n  return {\n    validationResult: 'error',\n    error: {\n      code: 'COMPANY_CODE_NOT_FOUND',\n      message: '未找到对应的公司代码',\n      field: 'companyCode',\n      value: null,\n      timestamp: new Date().toISOString(),\n      type: 'DATABASE_ERROR'\n    }\n  };\n}\n\n// 获取验证后的参数\nlet startDate = $('输入验证').item.json.startdate;\nlet endDate = $('输入验证').item.json.enddate;\n\n// 检查是否已经传入了时间参数\nconst hasInputDates = startDate && startDate !== \"\" && endDate && endDate !== \"\";\n\nif (hasInputDates) {\n  // 如果已经传入了时间参数，直接使用传入的参数\n  // 注意：输入验证已经验证了30天限制，这里不需要重复验证\n  return {\n    validationResult: 'success',\n    companyCode: companyCode,\n    startdate: startDate,\n    enddate: endDate,\n    timestamp: new Date().toISOString(),\n    source: 'input'\n  };\n} else {\n  // 如果没有传入时间参数，从数据库获取最新两期时间\n  if (!recentDates || !Array.isArray(recentDates) || recentDates.length < 2) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INSUFFICIENT_DATE_DATA',\n        message: '数据库中日期数据不足，无法自动获取时间范围',\n        field: 'recent_dates',\n        value: recentDates,\n        timestamp: new Date().toISOString(),\n        type: 'DATABASE_ERROR'\n      }\n    };\n  }\n  \n  const latestDate = recentDates[0];\n  const secondLatestDate = recentDates[1];\n  \n  if (!latestDate || !secondLatestDate) {\n    return {\n      validationResult: 'error',\n      error: {\n        code: 'INVALID_DATE_DATA',\n        message: '数据库中的日期数据格式无效',\n        field: 'recent_dates',\n        value: recentDates,\n        timestamp: new Date().toISOString(),\n        type: 'DATABASE_ERROR'\n      }\n    };\n  }\n  \n  // 将日期转换为YYYYMMDD格式\n  const formatDate = (dateStr) => {\n    const date = new Date(dateStr);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}/${month}/${day}`;\n  };\n  \n  startDate = formatDate(secondLatestDate);\n  endDate = formatDate(latestDate);\n  \n  \n  return {\n    validationResult: 'success',\n    companyCode: companyCode,\n    startdate: startDate,\n    enddate: endDate,\n    timestamp: new Date().toISOString(),\n    source: 'database'\n  };\n}"}, "id": "ab284e2f-248d-4c48-9903-a3ea6f6fd6a7", "name": "数据库结果处理", "type": "n8n-nodes-base.code", "position": [-2340, 1180], "typeVersion": 2}, {"parameters": {"jsCode": "const accessToken = $json.data?.access_token;\nconst expiredTimeStr = $json.data?.expired_time;\n\nif (!expiredTimeStr) {\n  throw new Error(\"expired_time 不存在，无法判断是否过期\");\n}\n\n// 将 '2025-07-10 18:42:42' 转为 ISO 格式 '2025-07-10T18:42:42'\nconst expiredAt = new Date(expiredTimeStr.replace(' ', 'T')).getTime();\nconst now = Date.now();\n\nconst isExpired = now >= expiredAt;\n\nreturn [{\n  json: {\n    access_token: accessToken,\n    expired_time: expiredTimeStr,\n    expired_timestamp: expiredAt,\n    is_expired: isExpired\n  }\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-880, 1100], "id": "287f90a9-042f-4e08-b942-2f85de6d1e7d", "name": "判断access_token是否过期"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8f0cf283-33cf-4847-a5d1-2163874c054b", "leftValue": "={{ $json.is_expired === true }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-620, 1100], "id": "c6352ba1-ced8-4f22-aed8-7fe500c11e2a", "name": "If"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/get_access_token", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"refresh_token\":\"{{ $json.refresh_token }}\"} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1160, 1100], "id": "4876f2b8-097c-4d74-a684-5fb8cb7c2f10", "name": "获取access token"}, {"parameters": {"assignments": {"assignments": [{"id": "5f906a29-c6c7-4b73-b4cc-9f8b05f9e71d", "name": "fundcode", "value": "={{ $json.fundcode }}", "type": "string"}, {"id": "70187f95-81a0-447c-aa5f-e88bb37d9aaf", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "cf972d25-2aff-4c15-af40-579db61ca064", "name": "timestamp", "value": "={{ $json.timestamp }}", "type": "string"}, {"id": "bba9fdf9-135e-473c-903a-b6c62eb42c74", "name": "refresh_token", "value": "=eyJzaWduX3RpbWUiOiIyMDI1LTA3LTIzIDE0OjExOjQ4In0=.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9C04C75508BBC8C1DE508E43E7EA749369F1E1D15B1681C6D27898F5A0A72E8D", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1600, 1100], "id": "ecd3064c-9f8a-4786-867e-63baecbaf5e1", "name": "固定refresh"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "484bfb61-dd89-49f5-a8be-de4c7f33c2b2", "name": "检查数据库结果", "type": "n8n-nodes-base.if", "position": [-2140, 1180], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $json.access_token }}\",\"ifindlang\":\"cn\"} ", "sendBody": true, "specifyBody": "json", "jsonBody": "={\"codes\":\"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\"indipara\":[{\"indicator\":\"ths_td_datesint_stock\",\"indiparams\":[\"{{ $('检查数据库结果').item.json.startdate }}\",\"{{ $('检查数据库结果').item.json.enddate }}\"]}]}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-400, 1080], "id": "6be1996c-f8b0-402d-9058-5ef6648609bf", "name": "区间交易天数"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "f53d1199-b80c-4c0b-b924-7fec0ba2cd92", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-1840, 1300], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "4342b005-7456-4437-91fa-d502aa89e8eb", "name": "average_stock_price", "value": "={{ $json.tables[0].table.ths_avg_close_int_stock[0] }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [60, 1080], "id": "c13401a7-7f9f-4207-b10f-8be3c9fda393", "name": "拿到区间均价"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 根据公司代码查询股票行业分类信息\n-- <AUTHOR>\n-- @created 2025-08-05 17:05:21\n-- @description 通过无后缀的公司代码模糊匹配ths_stock_industry_classification表中有后缀的stock_code\n-- @example 输入: companyCode=\"300723\" 输出: 匹配到\"300723.SZ\"等带后缀的股票代码\n-- @note 使用LIKE模糊匹配，支持.SZ、.SH等各种后缀格式\n\nSELECT \n  stock_code,\n  stock_name,\n  csrc_industry_new,\n  ths_industry_new,\n  sw_industry,\n  table_updated_at\nFROM ths_stock_industry_classification\nWHERE stock_code LIKE '{{ $json.companyCode }}%'\nORDER BY stock_code ASC\nLIMIT 10;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1840, 1100], "id": "6886a1e0-4ad3-4e7b-863a-4f224c3cbc95", "name": "同花顺-查找股票代码", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "同花顺数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    ci.\"companyCode\",\n    ARRAY(\n        SELECT sr.\"registerDate\"\n        FROM shareholder_registry sr\n        WHERE sr.\"organizationId\" = ci.\"organizationId\"\n        ORDER BY sr.\"registerDate\" DESC\n        LIMIT 2\n    ) AS recent_dates\nFROM company_info ci\nWHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\nLIMIT 1;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2540, 1180], "id": "06bad7b8-ac05-4d10-b7bf-ac86be3dcf38", "name": "查找开始日期和结束日期", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"assignments": {"assignments": [{"id": "7ff75585-ad49-4ffa-823a-07830f4953c0", "name": "latest_price", "value": "={{ $json.tables[0].table.latest[0] }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [60, 1280], "id": "1117acc9-ac88-484e-8fc6-3f80fc07994e", "name": "拿到最新股价"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [260, 1180], "id": "bb5978ba-6ab1-4d74-82dc-ac75eac0b548", "name": "合并区间和最新股价"}, {"parameters": {"content": "同花顺循环获取可用token", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1540, 1220], "id": "f9478fd2-7f8f-4a37-8ea9-bb797d800d34", "name": "Sticky Note6"}, {"parameters": {"content": "验证id是否可在数据库中查询到相关组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2380, 1100], "id": "3e4f0e4d-bda7-4f95-8851-96af697f5871", "name": "Sticky Note9"}, {"parameters": {"method": "POST", "url": "https://quantapi.51ifind.com/api/v1/basic_data_service", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "={\"Content-Type\":\"application/json\",\"access_token\":\"{{ $('If').item.json.access_token }}\",\"ifindlang\":\"cn\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"codes\": \"{{ $('同花顺-查找股票代码').item.json.stock_code }}\",\n    \"indipara\": [\n        {\n            \"indicator\": \"ths_close_price_stock\",\n            \"indiparams\": [\n                \"{{ $('检查数据库结果').item.json.startdate }}\",\n                \"100\",\n                \"{{ $('检查数据库结果').item.json.startdate }}\"\n            ]\n        }\n    ]\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-120, 1500], "id": "870f4fc1-914e-4b38-8929-ea4688bf51af", "name": "上一期股价"}, {"parameters": {"assignments": {"assignments": [{"id": "7ff75585-ad49-4ffa-823a-07830f4953c0", "name": "before_price", "value": "={{ $json.tables[0].table.ths_close_price_stock }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 1500], "id": "9a8dd11f-fbe7-428d-b8b7-74a1fac72535", "name": "拿上期股价"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [400, 1440], "id": "3f84df57-16e8-47c4-a818-9332cd92a662", "name": "合并区间和最新股价1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 新进股东现金账户持股分析（个人+机构分别前5名）- 英文键版\n * <AUTHOR>\n * @time 2025-01-27 16:10:00\n * @description 基于registerDate最新期和上期对比，按shareholderType区分个人和机构，分别取sharesInCashAccount前5名新进股东\n * @update 2025-01-27 16:10:00 hayden 将输出字段改为英文键名，便于前端识别和处理\n */\n\nWITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 最新期个人股东（shareholderType包含个人或知名牛散）\ncurrent_individuals AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\", \n    s.\"shareholderType\",\n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND (\n      s.\"shareholderType\" ILIKE '%个人%' OR\n      s.\"shareholderType\" ILIKE '%知名牛散%'\n    )\n),\n\n-- 最新期机构股东（除个人和知名牛散外的所有类型）\ncurrent_institutions AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderType\", \n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND s.\"shareholderType\" NOT ILIKE '%个人%'\n    AND s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n),\n\n-- 上期所有股东（用于新进判断）\nprev_all_shareholders AS (\n  SELECT DISTINCT s.\"unifiedAccountNumber\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"unifiedAccountNumber\" IS NOT NULL\n),\n\n-- 新进个人股东前5名\nnew_individuals_top5 AS (\n  SELECT \n    curr.\"securitiesAccountName\" AS shareholderName,\n    curr.\"unifiedAccountNumber\" AS unifiedAccountNumber,\n    curr.\"sharesInCashAccount\" AS sharesInCashAccount,\n    curr.\"shareholdingRatio\" AS shareholdingRatio,\n    curr.\"shareholderType\" AS shareholderType,\n    curr.\"registerDate\" AS registerDate,\n    'individual' AS investorType,\n    ROW_NUMBER() OVER (ORDER BY curr.\"sharesInCashAccount\" DESC) AS ranking\n  FROM current_individuals curr\n  LEFT JOIN prev_all_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE prev.\"unifiedAccountNumber\" IS NULL  -- 上期没有此股东，说明是新进\n),\n\n-- 新进机构股东前5名\nnew_institutions_top5 AS (\n  SELECT \n    curr.\"securitiesAccountName\" AS shareholderName,\n    curr.\"unifiedAccountNumber\" AS unifiedAccountNumber,\n    curr.\"sharesInCashAccount\" AS sharesInCashAccount,\n    curr.\"shareholdingRatio\" AS shareholdingRatio,\n    curr.\"shareholderType\" AS shareholderType,\n    curr.\"registerDate\" AS registerDate,\n    'institutional' AS investorType,\n    ROW_NUMBER() OVER (ORDER BY curr.\"sharesInCashAccount\" DESC) AS ranking\n  FROM current_institutions curr\n  LEFT JOIN prev_all_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE prev.\"unifiedAccountNumber\" IS NULL  -- 上期没有此股东，说明是新进\n),\n\n-- 取各自前5名\nfinal_individuals AS (\n  SELECT * FROM new_individuals_top5 WHERE ranking <= 5\n),\nfinal_institutions AS (\n  SELECT * FROM new_institutions_top5 WHERE ranking <= 5\n),\n\n-- 合并结果\nall_new_shareholders AS (\n  SELECT \n    shareholderName, unifiedAccountNumber, sharesInCashAccount, \n    shareholdingRatio, shareholderType, registerDate, \n    investorType, ranking\n  FROM final_individuals\n  UNION ALL\n  SELECT \n    shareholderName, unifiedAccountNumber, sharesInCashAccount,\n    shareholdingRatio, shareholderType, registerDate,\n    investorType, ranking  \n  FROM final_institutions\n)\n\n-- 最终结果（英文键名）\nSELECT \n  shareholderName,\n  unifiedAccountNumber,\n  sharesInCashAccount,\n  shareholdingRatio,\n  shareholderType,\n  registerDate,\n  ranking AS currentRanking,\n  investorType,\n  -- 统计信息\n  (SELECT COUNT(*) FROM all_new_shareholders) AS total\nFROM all_new_shareholders\nORDER BY \n  investorType ASC,  -- 先显示个人，再显示机构\n  sharesInCashAccount DESC;  -- 按现金账户持股数量降序排列", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1980, 1660], "id": "5075ebe5-5703-4fc2-b28a-576373e7faf0", "name": "新增（个人机构）-前五查询", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 增持股东现金账户持股分析（个人+机构分别前5名）- 双重排序优化版\n * <AUTHOR>\n * @time 2025-01-27 15:45:00\n * @description 基于registerDate最新期和上期对比，按shareholderType区分个人和机构，先按增持股数排序，再按sharesInCashAccount最大值排序取前5名\n * @update 2025-01-27 15:45:00 hayden 优化排序逻辑：增持股东中按增持股数排序，前5名中按现金账户持股数量最大值排序\n */\n\nWITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 最新期股东数据\ncurrent_shareholders AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\", \n    s.\"shareholderType\",\n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n),\n\n-- 上期股东数据\nprev_shareholders AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"sharesInCashAccount\" AS prev_shares_in_cash_account\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n),\n\n-- 增持股东数据（上期存在且最新期持股增加）\nincreased_shareholders AS (\n  SELECT \n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"shareholderType\",\n    curr.\"sharesInCashAccount\",\n    prev.\"prev_shares_in_cash_account\",\n    (curr.\"sharesInCashAccount\" - prev.\"prev_shares_in_cash_account\") AS shares_increase,\n    curr.\"shareholdingRatio\",\n    curr.\"registerDate\"\n  FROM current_shareholders curr\n  INNER JOIN prev_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE curr.\"sharesInCashAccount\" > prev.\"prev_shares_in_cash_account\"  -- 持股增加\n),\n\n-- 增持个人股东 - 双重排序：先按增持股数，再按现金账户持股数量\nincreased_individuals_ranked AS (\n  SELECT \n    \"securitiesAccountName\" AS name,\n    \"unifiedAccountNumber\" AS unified_account_number,\n    \"sharesInCashAccount\" AS shares_in_cash_account,\n    \"shareholdingRatio\" AS shareholding_ratio,\n    \"shareholderType\" AS shareholder_type,\n    \"registerDate\" AS register_date,\n    \"shares_increase\",\n    'individual' AS investor_type,\n    ROW_NUMBER() OVER (\n      ORDER BY \"shares_increase\" DESC, \"sharesInCashAccount\" DESC\n    ) AS ranking\n  FROM increased_shareholders\n  WHERE (\n    \"shareholderType\" ILIKE '%个人%' OR\n    \"shareholderType\" ILIKE '%知名牛散%'\n  )\n),\n\n-- 增持机构股东 - 双重排序：先按增持股数，再按现金账户持股数量\nincreased_institutions_ranked AS (\n  SELECT \n    \"securitiesAccountName\" AS name,\n    \"unifiedAccountNumber\" AS unified_account_number,\n    \"sharesInCashAccount\" AS shares_in_cash_account,\n    \"shareholdingRatio\" AS shareholding_ratio,\n    \"shareholderType\" AS shareholder_type,\n    \"registerDate\" AS register_date,\n    \"shares_increase\",\n    'institutional' AS investor_type,\n    ROW_NUMBER() OVER (\n      ORDER BY \"shares_increase\" DESC, \"sharesInCashAccount\" DESC\n    ) AS ranking\n  FROM increased_shareholders\n  WHERE \"shareholderType\" NOT ILIKE '%个人%'\n    AND \"shareholderType\" NOT ILIKE '%知名牛散%'\n),\n\n-- 取各自前5名\nfinal_individuals AS (\n  SELECT * FROM increased_individuals_ranked WHERE ranking <= 5\n),\nfinal_institutions AS (\n  SELECT * FROM increased_institutions_ranked WHERE ranking <= 5\n),\n\n-- 合并结果\nall_increased_shareholders AS (\n  SELECT \n    name, unified_account_number, shares_in_cash_account, \n    shareholding_ratio, shareholder_type, register_date, \n    shares_increase, investor_type, ranking\n  FROM final_individuals\n  UNION ALL\n  SELECT \n    name, unified_account_number, shares_in_cash_account,\n    shareholding_ratio, shareholder_type, register_date,\n    shares_increase, investor_type, ranking  \n  FROM final_institutions\n)\n\n-- 最终结果\nSELECT \n  name,\n  unified_account_number,\n  shares_in_cash_account,\n  shareholding_ratio,\n  shareholder_type,\n  register_date,\n  shares_increase,  -- 增持股数\n  ranking AS current_ranking,\n  investor_type,\n  -- 统计信息\n  (SELECT COUNT(*) FROM all_increased_shareholders) AS total\nFROM all_increased_shareholders\nORDER BY \n  investor_type ASC,  -- 先显示个人，再显示机构\n  ranking ASC;  -- 按排名升序排列（1,2,3,4,5）", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1980, 1860], "id": "d55d0903-00c2-47fb-8f8a-81f6b448a60a", "name": "增持（个人机构）-前五查询", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 减持股东现金账户持股分析（个人+机构分别前5名）- 双重排序优化版\n * <AUTHOR>\n * @time 2025-01-27 15:50:00\n * @description 基于registerDate最新期和上期对比，按shareholderType区分个人和机构，先按减持股数排序，再按sharesInCashAccount最大值排序取前5名\n * @update 2025-01-27 15:50:00 hayden 从增持改为减持股东分析：比较上期与最新期的sharesInCashAccount减少变化\n */\n\nWITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 最新期股东数据\ncurrent_shareholders AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\", \n    s.\"shareholderType\",\n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n),\n\n-- 上期股东数据\nprev_shareholders AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"sharesInCashAccount\" AS prev_shares_in_cash_account\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n),\n\n-- 减持股东数据（上期存在且最新期持股减少）\ndecreased_shareholders AS (\n  SELECT \n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"shareholderType\",\n    curr.\"sharesInCashAccount\",\n    prev.\"prev_shares_in_cash_account\",\n    (prev.\"prev_shares_in_cash_account\" - curr.\"sharesInCashAccount\") AS shares_decrease,\n    curr.\"shareholdingRatio\",\n    curr.\"registerDate\"\n  FROM current_shareholders curr\n  INNER JOIN prev_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE curr.\"sharesInCashAccount\" < prev.\"prev_shares_in_cash_account\"  -- 持股减少\n),\n\n-- 减持个人股东 - 双重排序：先按减持股数，再按现金账户持股数量\ndecreased_individuals_ranked AS (\n  SELECT \n    \"securitiesAccountName\" AS name,\n    \"unifiedAccountNumber\" AS unified_account_number,\n    \"sharesInCashAccount\" AS shares_in_cash_account,\n    \"shareholdingRatio\" AS shareholding_ratio,\n    \"shareholderType\" AS shareholder_type,\n    \"registerDate\" AS register_date,\n    \"shares_decrease\",\n    'individual' AS investor_type,\n    ROW_NUMBER() OVER (\n      ORDER BY \"shares_decrease\" DESC, \"sharesInCashAccount\" DESC\n    ) AS ranking\n  FROM decreased_shareholders\n  WHERE (\n    \"shareholderType\" ILIKE '%个人%' OR\n    \"shareholderType\" ILIKE '%知名牛散%'\n  )\n),\n\n-- 减持机构股东 - 双重排序：先按减持股数，再按现金账户持股数量\ndecreased_institutions_ranked AS (\n  SELECT \n    \"securitiesAccountName\" AS name,\n    \"unifiedAccountNumber\" AS unified_account_number,\n    \"sharesInCashAccount\" AS shares_in_cash_account,\n    \"shareholdingRatio\" AS shareholding_ratio,\n    \"shareholderType\" AS shareholder_type,\n    \"registerDate\" AS register_date,\n    \"shares_decrease\",\n    'institutional' AS investor_type,\n    ROW_NUMBER() OVER (\n      ORDER BY \"shares_decrease\" DESC, \"sharesInCashAccount\" DESC\n    ) AS ranking\n  FROM decreased_shareholders\n  WHERE \"shareholderType\" NOT ILIKE '%个人%'\n    AND \"shareholderType\" NOT ILIKE '%知名牛散%'\n),\n\n-- 取各自前5名\nfinal_individuals AS (\n  SELECT * FROM decreased_individuals_ranked WHERE ranking <= 5\n),\nfinal_institutions AS (\n  SELECT * FROM decreased_institutions_ranked WHERE ranking <= 5\n),\n\n-- 合并结果\nall_decreased_shareholders AS (\n  SELECT \n    name, unified_account_number, shares_in_cash_account, \n    shareholding_ratio, shareholder_type, register_date, \n    shares_decrease, investor_type, ranking\n  FROM final_individuals\n  UNION ALL\n  SELECT \n    name, unified_account_number, shares_in_cash_account,\n    shareholding_ratio, shareholder_type, register_date,\n    shares_decrease, investor_type, ranking  \n  FROM final_institutions\n)\n\n-- 最终结果\nSELECT \n  name,\n  unified_account_number,\n  shares_in_cash_account,\n  shareholding_ratio,\n  shareholder_type,\n  register_date,\n  shares_decrease,  -- 减持股数\n  ranking AS current_ranking,\n  investor_type,\n  -- 统计信息\n  (SELECT COUNT(*) FROM all_decreased_shareholders) AS total\nFROM all_decreased_shareholders\nORDER BY \n  investor_type ASC,  -- 先显示个人，再显示机构\n  ranking ASC;  -- 按排名升序排列（1,2,3,4,5）", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1980, 2060], "id": "65cdeb0d-82e2-4dab-9e1b-0f19082a785b", "name": "减持（个人机构）-前五查询", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 退出股东现金账户持股分析（个人+机构分别前5名）- 优化版\n * <AUTHOR>\n * @time 2025-01-27 15:55:00\n * @description 基于registerDate最新期和上期对比，按shareholderType区分个人和机构，分别取sharesInCashAccount前5名退出股东\n * @update 2025-01-27 15:55:00 hayden 从新进股东改为退出股东分析：查找上期存在但最新期不存在的股东\n */\n\nWITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 上期个人股东（shareholderType包含个人或知名牛散）\nprev_individuals AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\", \n    s.\"shareholderType\",\n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND (\n      s.\"shareholderType\" ILIKE '%个人%' OR\n      s.\"shareholderType\" ILIKE '%知名牛散%'\n    )\n),\n\n-- 上期机构股东（除个人和知名牛散外的所有类型）\nprev_institutions AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderType\", \n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND s.\"shareholderType\" NOT ILIKE '%个人%'\n    AND s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n),\n\n-- 最新期所有股东（用于退出判断）\ncurrent_all_shareholders AS (\n  SELECT DISTINCT s.\"unifiedAccountNumber\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"unifiedAccountNumber\" IS NOT NULL\n),\n\n-- 退出个人股东前5名\nexit_individuals_top5 AS (\n  SELECT \n    prev.\"securitiesAccountName\" AS name,\n    prev.\"unifiedAccountNumber\" AS unified_account_number,\n    prev.\"sharesInCashAccount\" AS shares_in_cash_account,\n    prev.\"shareholdingRatio\" AS shareholding_ratio,\n    prev.\"shareholderType\" AS shareholder_type,\n    prev.\"registerDate\" AS register_date,\n    'individual' AS investor_type,\n    ROW_NUMBER() OVER (ORDER BY prev.\"sharesInCashAccount\" DESC) AS ranking\n  FROM prev_individuals prev\n  LEFT JOIN current_all_shareholders curr \n    ON curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\n  WHERE curr.\"unifiedAccountNumber\" IS NULL  -- 最新期没有此股东，说明是退出\n),\n\n-- 退出机构股东前5名\nexit_institutions_top5 AS (\n  SELECT \n    prev.\"securitiesAccountName\" AS name,\n    prev.\"unifiedAccountNumber\" AS unified_account_number,\n    prev.\"sharesInCashAccount\" AS shares_in_cash_account,\n    prev.\"shareholdingRatio\" AS shareholding_ratio,\n    prev.\"shareholderType\" AS shareholder_type,\n    prev.\"registerDate\" AS register_date,\n    'institutional' AS investor_type,\n    ROW_NUMBER() OVER (ORDER BY prev.\"sharesInCashAccount\" DESC) AS ranking\n  FROM prev_institutions prev\n  LEFT JOIN current_all_shareholders curr \n    ON curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\n  WHERE curr.\"unifiedAccountNumber\" IS NULL  -- 最新期没有此股东，说明是退出\n),\n\n-- 取各自前5名\nfinal_individuals AS (\n  SELECT * FROM exit_individuals_top5 WHERE ranking <= 5\n),\nfinal_institutions AS (\n  SELECT * FROM exit_institutions_top5 WHERE ranking <= 5\n),\n\n-- 合并结果\nall_exit_shareholders AS (\n  SELECT \n    name, unified_account_number, shares_in_cash_account, \n    shareholding_ratio, shareholder_type, register_date, \n    investor_type, ranking\n  FROM final_individuals\n  UNION ALL\n  SELECT \n    name, unified_account_number, shares_in_cash_account,\n    shareholding_ratio, shareholder_type, register_date,\n    investor_type, ranking  \n  FROM final_institutions\n)\n\n-- 最终结果\nSELECT \n  name,\n  unified_account_number,\n  shares_in_cash_account,\n  shareholding_ratio,\n  shareholder_type,\n  register_date,\n  ranking AS current_ranking,\n  investor_type,\n  -- 统计信息\n  (SELECT COUNT(*) FROM all_exit_shareholders) AS total\nFROM all_exit_shareholders\nORDER BY \n  investor_type ASC,  -- 先显示个人，再显示机构\n  shares_in_cash_account DESC;  -- 按现金账户持股数量降序排列", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1980, 2240], "id": "ab39f96c-9907-45ce-99a4-707de391668c", "name": "退出（个人机构）-前五查询", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 不变股东现金账户持股分析（个人+机构分别前5名）- 优化版\n * <AUTHOR>\n * @time 2025-01-27 16:00:00\n * @description 基于registerDate最新期和上期对比，按shareholderType区分个人和机构，分别取sharesInCashAccount前5名不变股东\n * @update 2025-01-27 16:00:00 hayden 从退出股东改为不变股东分析：查找上期和最新期都存在且持股数量没有变化的股东\n */\n\nWITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 最新期个人股东（shareholderType包含个人或知名牛散）\ncurrent_individuals AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\", \n    s.\"shareholderType\",\n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND (\n      s.\"shareholderType\" ILIKE '%个人%' OR\n      s.\"shareholderType\" ILIKE '%知名牛散%'\n    )\n),\n\n-- 最新期机构股东（除个人和知名牛散外的所有类型）\ncurrent_institutions AS (\n  SELECT\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderType\", \n    s.\"sharesInCashAccount\",\n    s.\"shareholdingRatio\",\n    s.\"registerDate\"\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.current_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n    AND s.\"shareholderType\" IS NOT NULL\n    AND s.\"shareholderType\" NOT ILIKE '%个人%'\n    AND s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n),\n\n-- 上期所有股东（用于不变判断）\nprev_all_shareholders AS (\n  SELECT \n    s.\"unifiedAccountNumber\",\n    s.\"sharesInCashAccount\" AS prev_shares_in_cash_account\n  FROM shareholder s, date_pairs dp\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND s.\"registerDate\" = dp.prev_date\n    AND s.\"sharesInCashAccount\" IS NOT NULL\n    AND s.\"sharesInCashAccount\" > 0\n),\n\n-- 不变个人股东前5名\nunchanged_individuals_top5 AS (\n  SELECT \n    curr.\"securitiesAccountName\" AS name,\n    curr.\"unifiedAccountNumber\" AS unified_account_number,\n    curr.\"sharesInCashAccount\" AS shares_in_cash_account,\n    curr.\"shareholdingRatio\" AS shareholding_ratio,\n    curr.\"shareholderType\" AS shareholder_type,\n    curr.\"registerDate\" AS register_date,\n    'individual' AS investor_type,\n    ROW_NUMBER() OVER (ORDER BY curr.\"sharesInCashAccount\" DESC) AS ranking\n  FROM current_individuals curr\n  INNER JOIN prev_all_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE curr.\"sharesInCashAccount\" = prev.\"prev_shares_in_cash_account\"  -- 持股数量没有变化\n),\n\n-- 不变机构股东前5名\nunchanged_institutions_top5 AS (\n  SELECT \n    curr.\"securitiesAccountName\" AS name,\n    curr.\"unifiedAccountNumber\" AS unified_account_number,\n    curr.\"sharesInCashAccount\" AS shares_in_cash_account,\n    curr.\"shareholdingRatio\" AS shareholding_ratio,\n    curr.\"shareholderType\" AS shareholder_type,\n    curr.\"registerDate\" AS register_date,\n    'institutional' AS investor_type,\n    ROW_NUMBER() OVER (ORDER BY curr.\"sharesInCashAccount\" DESC) AS ranking\n  FROM current_institutions curr\n  INNER JOIN prev_all_shareholders prev \n    ON prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n  WHERE curr.\"sharesInCashAccount\" = prev.\"prev_shares_in_cash_account\"  -- 持股数量没有变化\n),\n\n-- 取各自前5名\nfinal_individuals AS (\n  SELECT * FROM unchanged_individuals_top5 WHERE ranking <= 5\n),\nfinal_institutions AS (\n  SELECT * FROM unchanged_institutions_top5 WHERE ranking <= 5\n),\n\n-- 合并结果\nall_unchanged_shareholders AS (\n  SELECT \n    name, unified_account_number, shares_in_cash_account, \n    shareholding_ratio, shareholder_type, register_date, \n    investor_type, ranking\n  FROM final_individuals\n  UNION ALL\n  SELECT \n    name, unified_account_number, shares_in_cash_account,\n    shareholding_ratio, shareholder_type, register_date,\n    investor_type, ranking  \n  FROM final_institutions\n)\n\n-- 最终结果\nSELECT \n  name,\n  unified_account_number,\n  shares_in_cash_account,\n  shareholding_ratio,\n  shareholder_type,\n  register_date,\n  ranking AS current_ranking,\n  investor_type,\n  -- 统计信息\n  (SELECT COUNT(*) FROM final_individuals) AS individual_total,\n  (SELECT COUNT(*) FROM final_institutions) AS institutional_total,\n  (SELECT COUNT(*) FROM all_unchanged_shareholders) AS total\nFROM all_unchanged_shareholders\nORDER BY \n  investor_type ASC,  -- 先显示个人，再显示机构\n  shares_in_cash_account DESC;  -- 按现金账户持股数量降序排列", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2000, 2420], "id": "d1784f38-152d-43f6-815d-046bb192fe7a", "name": "不变（个人机构）-前五查询", "alwaysOutputData": true, "credentials": {"postgres": {"id": "Y7JG7gWIh21LbPuq", "name": "项目数据库"}}}, {"parameters": {"jsCode": "/**\n * 股东行为数据整合处理（适配上游统一数据源版）\n * @description 从统一的股东行为数据获取节点获取数据并进行财务计算\n * <AUTHOR>\n * @created 2025-01-27 15:30:00\n * @updated 2025-01-27 16:39:20 hayden 添加个人和机构股东类型归类\n */\n\n// 从统一的股东行为数据获取节点获取数据\nconst shareholderBehaviorData = $(\"新进、增持、减持、不动数据获取\").first();\nconst sharePrice = $(\"合并区间和最新股价1\").all();\n\n// 解析股东行为数据\nconst rawData = shareholderBehaviorData.json.rawResponses || {};\n\n// 提取各类股东数据并转换为统一格式（分别处理个人和机构）\nconst unchangedData = [\n  ...extractShareholderData(rawData.constantIndividual || [], 'unchanged', 'individual'),\n  ...extractShareholderData(rawData.constantInstitution || [], 'unchanged', 'institution')\n];\n\nconst exitedData = [\n  ...extractShareholderData(rawData.exitIndividual || [], 'exited', 'individual'),\n  ...extractShareholderData(rawData.exitInstitution || [], 'exited', 'institution')\n];\n\nconst decreasedData = [\n  ...extractShareholderData(rawData.decreaseIndividual || [], 'decreased', 'individual'),\n  ...extractShareholderData(rawData.decreaseInstitution || [], 'decreased', 'institution')\n];\n\nconst increasedData = [\n  ...extractShareholderData(rawData.increaseIndividual || [], 'increased', 'individual'),\n  ...extractShareholderData(rawData.increaseInstitution || [], 'increased', 'institution')\n];\n\nconst newData = [\n  ...extractShareholderData(rawData.newIndividual || [], 'new', 'individual'),\n  ...extractShareholderData(rawData.newInstitution || [], 'new', 'institution')\n];\n\n/**\n * 提取股东数据并转换为统一格式\n * @param {Array} dataArray 原始数据数组\n * @param {string} behaviorType 行为类型，用于确定字段映射\n * @param {string} shareholderType 股东类型：individual（个人）或 institution（机构）\n * @returns {Array} 转换后的股东数据数组\n * @updated 2025-01-27 16:39:20 hayden 添加股东类型参数，支持个人和机构分类\n */\nfunction extractShareholderData(dataArray, behaviorType, shareholderType) {\n  const result = [];\n  \n  dataArray.forEach(response => {\n    if (response.success && response.data) {\n      // 处理不同类型的股东数据\n      const shareholders = response.data.newIndividuals || \n                          response.data.newInstitutions ||\n                          response.data.exitIndividuals ||\n                          response.data.exitInstitutions ||\n                          response.data.increaseIndividuals ||\n                          response.data.increaseInstitutions ||\n                          response.data.decreaseIndividuals ||\n                          response.data.decreaseInstitutions ||\n                          response.data.constantIndividuals ||\n                          response.data.constantInstitutions ||\n                          [];\n      \n      shareholders.forEach(shareholder => {\n        // 根据行为类型确定主要股份数量字段\n        let primarySharesField = '0';\n        \n        switch (behaviorType) {\n          case 'new':\n            // 新进股东使用 number_of_shares\n            primarySharesField = shareholder.number_of_shares || '0';\n            break;\n          case 'exited':\n            // 退出股东使用 prev_numberofshares\n            primarySharesField = shareholder.prev_numberofshares || '0';\n            break;\n          case 'increased':\n          case 'decreased':\n          case 'unchanged':\n            // 其他类型使用 current_numberofshares\n            primarySharesField = shareholder.current_numberofshares || '0';\n            break;\n          default:\n            primarySharesField = '0';\n        }\n        \n        result.push({\n          json: {\n            name: shareholder.name,\n            unified_account_number: shareholder.unified_account_number,\n            // 股东类型分类\n            shareholderType: shareholderType,\n            // 统一字段映射\n            number_of_shares: shareholder.number_of_shares,\n            current_numberofshares: shareholder.current_numberofshares,\n            prev_numberofshares: shareholder.prev_numberofshares,\n            shares_in_cash_account: primarySharesField,\n            sharesincashaccount: primarySharesField,\n            // 增持相关字段\n            increased_shares: shareholder.increased_shares,\n            increased_ratio_percent: shareholder.increased_ratio_percent,\n            // 减持相关字段  \n            decreased_shares: shareholder.decreased_shares,\n            decreased_ratio_percent: shareholder.decreased_ratio_percent,\n            // 持股比例\n            current_shareholdingratio: shareholder.current_shareholdingratio,\n            prev_shareholdingratio: shareholder.prev_shareholdingratio,\n            shareholding_ratio: shareholder.shareholding_ratio,\n            // 日期信息\n            increased_date: shareholder.increased_date,\n            decreased_date: shareholder.decreased_date,\n            register_date: shareholder.register_date,\n            exit_date: shareholder.exit_date,\n            // 排名\n            rank: shareholder.rank\n          }\n        });\n      });\n    }\n  });\n  \n  return result;\n}\n\n// 获取股价数据\nconst priceData = sharePrice[0]?.json || sharePrice[0] || {};\nconst averagePrice = Number(priceData.average_stock_price || priceData.averagePrice) || 0;\nconst latestPrice = Number(priceData.latest_price || priceData.latestPrice) || 0;\nconst beforePrice = Number(priceData.before_price || priceData.beforePrice) || 0;\n\n/**\n * 新进股东财务计算\n * @param {Object} shareholder 股东数据\n * @returns {Object} 包含财务计算的股东数据\n * @updated 2025-01-27 16:39:20 hayden 使用number_of_shares字段进行计算\n */\nfunction calculateNewShareholder(shareholder) {\n  // 新进股东使用number_of_shares字段\n  const currentShares = Number(\n    shareholder.number_of_shares ||\n    shareholder.shares_in_cash_account || \n    shareholder.sharesincashaccount ||\n    0\n  );\n  \n  \n  const estimatedCost = averagePrice * currentShares;\n  const currentMarketValue = latestPrice * currentShares;\n  const currentProfit = currentMarketValue - estimatedCost;\n  \n  return {\n    ...shareholder,\n    estimatedCost: estimatedCost.toFixed(2),\n    currentMarketValue: currentMarketValue.toFixed(2),\n    currentProfit: currentProfit.toFixed(2),\n    profitMargin: estimatedCost > 0 ? ((currentProfit / estimatedCost) * 100).toFixed(2) : '0.00'\n  };\n}\n\n/**\n * 退出股东财务计算\n * @param {Object} shareholder 股东数据\n * @returns {Object} 包含财务计算的股东数据\n * @updated 2025-01-27 16:39:20 hayden 使用prev_numberofshares字段进行计算\n */\nfunction calculateExitShareholder(shareholder) {\n  // 退出股东使用prev_numberofshares字段\n  const exitShares = Number(\n    shareholder.prev_numberofshares ||\n    shareholder.shares_in_cash_account || \n    shareholder.sharesincashaccount ||\n    0\n  );\n  \n  const exitPrice = averagePrice;\n  const holdingCost = beforePrice * exitShares;\n  const exitAmount = exitPrice * exitShares;\n  const profitLoss = exitAmount - holdingCost;\n  \n  return {\n    ...shareholder,\n    exitPrice: exitPrice.toFixed(2),\n    holdingCost: holdingCost.toFixed(2),\n    exitAmount: exitAmount.toFixed(2),\n    profitLoss: profitLoss.toFixed(2),\n    profitMargin: holdingCost > 0 ? ((profitLoss / holdingCost) * 100).toFixed(2) : '0.00'\n  };\n}\n\n/**\n * 增持股东财务计算\n * @param {Object} shareholder 股东数据\n * @returns {Object} 包含财务计算的股东数据\n * @updated 2025-01-27 16:39:20 hayden 使用current_numberofshares和increased_shares字段\n */\nfunction calculateIncreaseShareholder(shareholder) {\n  const currentShares = Number(\n    shareholder.current_numberofshares ||\n    shareholder.shares_in_cash_account || \n    shareholder.sharesincashaccount ||\n    0\n  );\n  const increasedShares = Number(\n    shareholder.increased_shares ||\n    0\n  );\n  const previousShares = currentShares - increasedShares;\n  \n  const previousCost = beforePrice * previousShares;\n  const increaseCost = averagePrice * increasedShares;\n  const totalCost = previousCost + increaseCost;\n  const currentMarketValue = latestPrice * currentShares;\n  const profitLoss = currentMarketValue - totalCost;\n  \n  return {\n    ...shareholder,\n    previousCost: previousCost.toFixed(2),\n    increaseCost: increaseCost.toFixed(2),\n    totalCost: totalCost.toFixed(2),\n    currentMarketValue: currentMarketValue.toFixed(2),\n    profitLoss: profitLoss.toFixed(2),\n    profitMargin: totalCost > 0 ? ((profitLoss / totalCost) * 100).toFixed(2) : '0.00'\n  };\n}\n\n/**\n * 减持股东财务计算\n * @param {Object} shareholder 股东数据\n * @returns {Object} 包含财务计算的股东数据\n * @updated 2025-01-27 16:39:20 hayden 使用current_numberofshares和decreased_shares字段\n */\nfunction calculateDecreaseShareholder(shareholder) {\n  const currentShares = Number(\n    shareholder.current_numberofshares ||\n    shareholder.shares_in_cash_account || \n    shareholder.sharesincashaccount ||\n    0\n  );\n  const decreasedShares = Number(\n    shareholder.decreased_shares ||\n    0\n  );\n  const previousShares = currentShares + decreasedShares;\n  \n  const holdingCost = beforePrice * previousShares;\n  const decreasePrice = averagePrice;\n  const decreaseProfit = (decreasePrice - beforePrice) * decreasedShares;\n  const currentProfit = (latestPrice - beforePrice) * currentShares;\n  const decreaseAmount = decreasePrice * decreasedShares;\n  \n  return {\n    ...shareholder,\n    holdingCost: holdingCost.toFixed(2),\n    decreasePrice: decreasePrice.toFixed(2),\n    decreaseProfit: decreaseProfit.toFixed(2),\n    currentProfit: currentProfit.toFixed(2),\n    decreaseAmount: decreaseAmount.toFixed(2),\n    totalProfit: (decreaseProfit + currentProfit).toFixed(2)\n  };\n}\n\n/**\n * 不变股东财务计算\n * @param {Object} shareholder 股东数据\n * @returns {Object} 包含财务计算的股东数据\n * @updated 2025-01-27 16:39:20 hayden 使用current_numberofshares字段\n */\nfunction calculateUnchangedShareholder(shareholder) {\n  const holdingShares = Number(\n    shareholder.current_numberofshares ||\n    shareholder.shares_in_cash_account || \n    shareholder.sharesincashaccount ||\n    0\n  );\n  \n  const profitLoss = (latestPrice - beforePrice) * holdingShares;\n  const holdingCost = beforePrice * holdingShares;\n  const currentMarketValue = latestPrice * holdingShares;\n  \n  return {\n    ...shareholder,\n    holdingCost: holdingCost.toFixed(2),\n    currentMarketValue: currentMarketValue.toFixed(2),\n    profitLoss: profitLoss.toFixed(2),\n    profitMargin: holdingCost > 0 ? ((profitLoss / holdingCost) * 100).toFixed(2) : '0.00'\n  };\n}\n\n/**\n * 数据整合函数（含财务计算）\n * @param {Array} dataArray 股东数据数组\n * @param {string} behaviorType 行为类型\n * @returns {Array} 处理后的股东数据数组\n * @updated 2025-01-27 16:39:20 hayden 保持原有逻辑不变\n */\nfunction processShareholderData(dataArray, behaviorType) {\n  return dataArray.map(item => {\n    let processedItem = { ...(item.json || item) };\n    \n    switch (behaviorType) {\n      case 'new':\n        processedItem = calculateNewShareholder(processedItem);\n        break;\n      case 'exited':\n        processedItem = calculateExitShareholder(processedItem);\n        break;\n      case 'increased':\n        processedItem = calculateIncreaseShareholder(processedItem);\n        break;\n      case 'decreased':\n        processedItem = calculateDecreaseShareholder(processedItem);\n        break;\n      case 'unchanged':\n        processedItem = calculateUnchangedShareholder(processedItem);\n        break;\n    }\n    \n    return {\n      ...processedItem,\n      behaviorType: behaviorType,\n      behaviorCategory: getBehaviorCategory(behaviorType)\n    };\n  });\n}\n\n/**\n * 获取行为分类\n * @param {string} behaviorType 行为类型\n * @returns {string} 行为分类\n * @updated 2025-01-27 16:39:20 hayden 保持原有逻辑不变\n */\nfunction getBehaviorCategory(behaviorType) {\n  const categoryMap = {\n    'unchanged': 'stable',\n    'exited': 'negative',\n    'decreased': 'negative',\n    'increased': 'positive',\n    'new': 'positive'\n  };\n  return categoryMap[behaviorType] || 'unknown';\n}\n\n// 整合所有数据\nconst consolidatedData = [\n  ...processShareholderData(newData, 'new'),\n  ...processShareholderData(exitedData, 'exited'),\n  ...processShareholderData(increasedData, 'increased'),\n  ...processShareholderData(decreasedData, 'decreased'),\n  ...processShareholderData(unchangedData, 'unchanged')\n];\n\n\n// 添加统计信息\nconst statistics = {\n  totalRecords: consolidatedData.length,\n  // 按行为类型统计\n  newCount: newData.length,\n  exitedCount: exitedData.length,\n  increasedCount: increasedData.length,\n  decreasedCount: decreasedData.length,\n  unchangedCount: unchangedData.length,\n  positiveChanges: newData.length + increasedData.length,\n  negativeChanges: exitedData.length + decreasedData.length,\n  stableCount: unchangedData.length,\n  priceInfo: {\n    averagePrice: averagePrice,\n    latestPrice: latestPrice,\n    beforePrice: beforePrice\n  }\n};\n\n// 返回整合后的数据\nreturn [{\n  json: {\n    shareholderBehaviorData: consolidatedData,\n    statistics: statistics,\n    processedAt: new Date().toISOString()\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 1900], "id": "5e782689-81b7-4c0c-b8d0-7fa5b40156cf", "name": "整合数据"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [220, 1900], "id": "a1281e2d-da08-480b-9c0e-98d64bc9cfa1", "name": "股东行为和股价数据合并"}, {"parameters": {"content": "基础信息字段\n字段名\t中文含义\t示例值\t说明\ncompanyName\t公司名称\t\"一品红\"\t上市公司名称\ncompanyCode\t公司代码\t\"300723\"\t股票代码\nregisterDate\t本期报告日期\t\"2024-07-31\"\t当前名册的权益登记日\noldestRegisterDate\t最早报告日期\t\"2024-03-29\"\t系统中该公司最早的名册日期\n本期股东结构数据\n字段名\t中文含义\t示例值\t说明\ntotalShares\t本期总股数\t454,143,281股\t公司总股本\ntotalShareholders\t本期股东总数\t14,356户\t所有股东户数（个人+机构）\ntotalInstitutions\t本期机构总数\t2,617户\t机构投资者户数\ninstitutionShares\t本期机构持股数\t273,866,742股\t机构投资者总持股数量\nindividualShareholders\t本期个人股东数\t11,739户\t个人投资者户数（总数-机构数）\n上期股东结构数据\n字段名\t中文含义\t示例值\t说明\nprevTotalShares\t上期总股数\t454,143,281股\t上一期公司总股本\nprevTotalShareholders\t上期股东总数\t13,394户\t上一期所有股东户数\nprevTotalInstitutions\t上期机构总数\t2,090户\t上一期机构投资者户数\nprevInstitutionShares\t上期机构持股数\t286,199,270股\t上一期机构投资者总持股数量\nprevIndividualShareholders\t上期个人股东数\t11,304户\t上一期个人投资者户数\n变化数额字段\n字段名\t中文含义\t示例值\t说明\nshareholdersChange\t股东总数变化\t+962户\t本期比上期增加的股东户数\ninstitutionsChange\t机构数变化\t+527户\t本期比上期增加的机构户数\nindividualShareholdersChange\t个人股东数变化\t+435户\t本期比上期增加的个人股东户数\n变化百分比字段\n字段名\t中文含义\t示例值\t说明\ntotalSharesChangePercent\t总股数变化率\t0.00%\t总股本变化百分比（无变化）\ninstitutionSharesChangePercent\t机构持股变化率\t-4.31%\t机构持股数量变化百分比（减少）\n前100名股东统计字段\n字段名\t中文含义\t示例值\t说明\ncurrentTop100Shares\t本期前100名持股数\t370,717,212股\t当前期前100名股东总持股数\nprevTop100Shares\t上期前100名持股数\t372,921,290股\t上一期前100名股东总持股数\ntop100SharesChange\t前100名持股变化数额\t-2,204,078股\t前100名股东持股数量变化\ntop100SharesChangePercent\t前100名持股变化率\t-0.59%\t前100名股东持股变化百分比", "height": 380, "width": 580}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1220, -120], "id": "842456f1-43a4-4e51-b85d-1093867fc585", "name": "Sticky Note10"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [520, 980], "id": "b41026cb-a05d-4f0c-8751-8b2b9214093f", "name": "合并股东数据和股价"}, {"parameters": {"jsCode": "/**\n * 从webhook URL中提取主机和端口信息用于n8n内部请求\n * @param {string} webhookUrl - 完整的webhook URL\n * @returns {string} 主机和端口 (例如: localhost:5678)\n * <AUTHOR>\n * @created 2025-01-27\n * @modified 2025-01-27 - 添加调试信息和容错处理\n */\nconst extractHostPort = (webhookUrl) => {\n  \n  if (!webhookUrl) {\n\n    return '';\n  }\n  \n  try {\n    const url = new URL(webhookUrl);\n    return url.host;\n  } catch (error) {\n    // 尝试正则表达式提取\n    const match = webhookUrl.match(/https?:\\/\\/([^\\/]+)/);\n    if (match && match[1]) {\n      return match[1];\n    }\n    return '';\n  }\n};\n\nconst webhookData = $('公司概览AI报告接口').first().json;\n\nconst webhookUrl = webhookData.webhookUrl;\nconst host = extractHostPort(webhookUrl);\n\nreturn { host};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1420, 1420], "id": "1848af79-4ddd-434c-a0c1-d76497d0213c", "name": "截取地址和端口"}, {"parameters": {"jsCode": "/**\n * 同时请求所有股东行为趋势数据并整理数据结构\n * <AUTHOR>\n * @created 2025-01-27\n * @modified 2025-01-27 - 包含十个股东趋势接口并整理数据结构\n */\nconst host = $input.first().json.host;\nconst companyId = $('检验组织是否存在数据库中').first().json.organizationId;\n\n// 构建十个请求URL\nconst urls = {\n  newIndividual: `http://${host}/webhook/new-individual-shareholders-trend`,\n  newInstitution: `http://${host}/webhook/new-institution-shareholders-trend`,\n  exitIndividual: `http://${host}/webhook/exit-individual-shareholders-trend`,\n  exitInstitution: `http://${host}/webhook/exit-institution-shareholders-trend`,\n  increaseIndividual: `http://${host}/webhook/increase-individual-shareholders-trend`,\n  increaseInstitution: `http://${host}/webhook/increase-institution-shareholders-trend`,\n  decreaseIndividual: `http://${host}/webhook/decrease-individual-shareholders-trend`,\n  decreaseInstitution: `http://${host}/webhook/decrease-institution-shareholders-trend`,\n  constantIndividual: `http://${host}/webhook/constant-individual-shareholders-trend`,\n  constantInstitution: `http://${host}/webhook/constant-institution-shareholders-trend`\n};\n\n// 通用请求配置函数\nconst createRequestOptions = (url) => {\n  return {\n    url: url,\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: {\n      id: companyId\n    },\n    json: true,\n    timeout: 30000,\n    skipSslCertificateValidation: false\n  };\n};\n\n// 并行执行所有HTTP请求\nconst [\n  newIndividualResponse,\n  newInstitutionResponse,\n  exitIndividualResponse,\n  exitInstitutionResponse,\n  increaseIndividualResponse,\n  increaseInstitutionResponse,\n  decreaseIndividualResponse,\n  decreaseInstitutionResponse,\n  constantIndividualResponse,\n  constantInstitutionResponse\n] = await Promise.all([\n  this.helpers.httpRequest(createRequestOptions(urls.newIndividual)),\n  this.helpers.httpRequest(createRequestOptions(urls.newInstitution)),\n  this.helpers.httpRequest(createRequestOptions(urls.exitIndividual)),\n  this.helpers.httpRequest(createRequestOptions(urls.exitInstitution)),\n  this.helpers.httpRequest(createRequestOptions(urls.increaseIndividual)),\n  this.helpers.httpRequest(createRequestOptions(urls.increaseInstitution)),\n  this.helpers.httpRequest(createRequestOptions(urls.decreaseIndividual)),\n  this.helpers.httpRequest(createRequestOptions(urls.decreaseInstitution)),\n  this.helpers.httpRequest(createRequestOptions(urls.constantIndividual)),\n  this.helpers.httpRequest(createRequestOptions(urls.constantInstitution))\n]);\n\nreturn {\n  rawResponses: {\n    newIndividual: newIndividualResponse,\n    newInstitution: newInstitutionResponse,\n    exitIndividual: exitIndividualResponse,\n    exitInstitution: exitInstitutionResponse,\n    increaseIndividual: increaseIndividualResponse,\n    increaseInstitution: increaseInstitutionResponse,\n    decreaseIndividual: decreaseIndividualResponse,\n    decreaseInstitution: decreaseInstitutionResponse,\n    constantIndividual: constantIndividualResponse,\n    constantInstitution: constantInstitutionResponse\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1460, 1680], "id": "e6b650ee-7287-41ad-8ec6-f8052beb7207", "name": "新进、增持、减持、不动数据获取"}], "connections": {"输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "公司概览AI报告接口响应", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "获取最新一期公司完整信息及较上期变化": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "获取最新一期信用账户数据及较上期变化": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "获取股东集中度及户均持股数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 1}]]}, "设置组织ID": {"main": [[{"node": "获取最新一期信用账户数据及较上期变化", "type": "main", "index": 0}, {"node": "获取最新一期公司完整信息及较上期变化", "type": "main", "index": 0}, {"node": "获取股东集中度及户均持股数据", "type": "main", "index": 0}, {"node": "获取核心持股数据", "type": "main", "index": 0}, {"node": "股东类型户数和持股分布", "type": "main", "index": 0}, {"node": "查找开始日期和结束日期", "type": "main", "index": 0}]]}, "获取核心持股数据": {"main": [[{"node": "合并数据2", "type": "main", "index": 1}]]}, "股东类型户数和持股分布": {"main": [[{"node": "合并数据3", "type": "main", "index": 1}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}, {"node": "截取地址和端口", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应", "type": "main", "index": 0}]]}, "合并数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 0}]]}, "合并数据1": {"main": [[{"node": "合并数据2", "type": "main", "index": 0}]]}, "合并数据2": {"main": [[{"node": "合并数据3", "type": "main", "index": 0}]]}, "合并数据3": {"main": [[{"node": "包装数据为单一json对象", "type": "main", "index": 0}]]}, "包装数据为单一json对象": {"main": [[{"node": "合并股东数据和股价", "type": "main", "index": 0}]]}, "公司概览AI报告接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "获取区间股价（收盘价）": {"main": [[{"node": "拿到区间均价", "type": "main", "index": 0}]]}, "最新股价": {"main": [[{"node": "拿到最新股价", "type": "main", "index": 0}]]}, "数据库结果处理": {"main": [[{"node": "检查数据库结果", "type": "main", "index": 0}]]}, "判断access_token是否过期": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "区间交易天数", "type": "main", "index": 0}], [{"node": "固定refresh", "type": "main", "index": 0}]]}, "获取access token": {"main": [[{"node": "判断access_token是否过期", "type": "main", "index": 0}]]}, "固定refresh": {"main": [[{"node": "获取access token", "type": "main", "index": 0}]]}, "检查数据库结果": {"main": [[{"node": "同花顺-查找股票代码", "type": "main", "index": 0}], [{"node": "错误响应1", "type": "main", "index": 0}]]}, "区间交易天数": {"main": [[{"node": "最新股价", "type": "main", "index": 0}, {"node": "获取区间股价（收盘价）", "type": "main", "index": 0}, {"node": "上一期股价", "type": "main", "index": 0}]]}, "拿到区间均价": {"main": [[{"node": "合并区间和最新股价", "type": "main", "index": 0}]]}, "同花顺-查找股票代码": {"main": [[{"node": "固定refresh", "type": "main", "index": 0}]]}, "查找开始日期和结束日期": {"main": [[{"node": "数据库结果处理", "type": "main", "index": 0}]]}, "拿到最新股价": {"main": [[{"node": "合并区间和最新股价", "type": "main", "index": 1}]]}, "合并区间和最新股价": {"main": [[{"node": "合并区间和最新股价1", "type": "main", "index": 0}]]}, "上一期股价": {"main": [[{"node": "拿上期股价", "type": "main", "index": 0}]]}, "拿上期股价": {"main": [[{"node": "合并区间和最新股价1", "type": "main", "index": 1}]]}, "合并区间和最新股价1": {"main": [[{"node": "股东行为和股价数据合并", "type": "main", "index": 0}]]}, "新增（个人机构）-前五查询": {"main": [[]]}, "增持（个人机构）-前五查询": {"main": [[]]}, "减持（个人机构）-前五查询": {"main": [[]]}, "退出（个人机构）-前五查询": {"main": [[]]}, "不变（个人机构）-前五查询": {"main": [[]]}, "整合数据": {"main": [[{"node": "合并股东数据和股价", "type": "main", "index": 1}]]}, "股东行为和股价数据合并": {"main": [[{"node": "整合数据", "type": "main", "index": 0}]]}, "合并股东数据和股价": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "截取地址和端口": {"main": [[{"node": "新进、增持、减持、不动数据获取", "type": "main", "index": 0}]]}, "新进、增持、减持、不动数据获取": {"main": [[{"node": "股东行为和股价数据合并", "type": "main", "index": 1}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}