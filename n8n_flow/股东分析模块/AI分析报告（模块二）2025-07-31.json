{"nodes": [{"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  return {\n    organizationId: id.trim()\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      type: inputData.webhookUrl,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "830440a5-3b69-4076-8f12-cb35adc67465", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-1240, -80], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "ec5d0003-4024-4b4c-9b3e-f2851a2750ef", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1020, -80], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "469c8ce2-24db-4196-a235-466e09e88455", "name": "数据库错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-480, 240], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-820, -60], "id": "c8ed2e2c-3372-4852-940c-22cff3ec7191", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "b6999ad6-9d56-407d-98f7-246ba2faaddb", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1040, -140], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=角色定位：\n你是一位资深的股权数据趋势分析专家，擅长从时间序列数据中洞察核心变化、进行深度趋势解读，并以结构清晰、叙述流畅、总结精炼的方式呈现分析结果。你的任务是基于一个包含多个报告期股东数据的JSON数组，严格按照预设的分析模块框架，为公司管理层生成一份关于公司股东结构动态趋势的深度量化总结。所有总结必须直接来源于提供的数据，清晰呈现期初期末对比，并适当顾及中间数据的变化以判断整体趋势，对关键变化给予恰当的描述性阐释。\n核心输入： 一个JSON数组{{ JSON.stringify($json.trendData, null, 2) }}\n，数组中的每个对象代表一个报告期的股东数据，包含字段如 registerDate, total_shares, total_shareholders, institutional_shareholders, institutional_shares, individual_shareholders, individual_shares, credit_shareholders, credit_shares, top10_shareholding_ratio, top20_shareholding_ratio, 以及 institutional_breakdown (细分机构数组)等。\n核心输出要求：\n严格遵循框架与内部结构： 按照预设的六个一级模块标题进行总结。在每个一级模块标题下，AI应组织成多个逻辑连贯的段落，深入分析该模块的核心指标。这些段落之间不设小标题。\n趋势判断的全面性： 在判断趋势（如上升/下降/稳定/波动）时，AI应综合考虑整个分析周期内的数据点（即JSON数组中的所有项），而不仅仅是期初期末两点。期初期末数据作为趋势的起点和终点，但整体趋势描述应能反映中间过程的主要特征。\n数据精准提取与计算： 准确识别并提取每个分析模块所需的期初（JSON数组的第一个元素）和期末（JSON数组的最后一个元素）数据作为关键锚点。所有计算（如人均持股、百分比变化）必须准确。\n量化描述与趋势阐释： 所有趋势总结都必须伴随具体的期初期末数据。在描述趋势时，应力求用更丰富的词汇（如“持续攀升”、“探底回升”、“高位整理后小幅回落”等，均需有数据支撑）来刻画变化特征。\n数据融入流畅叙述： 将关键数据和分析判断自然地融入总结性段落中，使信息传递连贯，避免生硬的数据罗列。\n客观中立，聚焦事实： 严格基于数据进行总结，不进行任何主观臆断、原因推测或超出数据范围的解读。\n结构清晰，详略得当： 最终输出应具有清晰的六个一级模块结构。在核心趋势点上进行适度展开，对次要信息可简化。\n分析模块与总结指引：\n(AI需明确，以下分析中的“期初”指JSON数组的第一个元素对应的数据，“期末”指JSON数组的最后一个元素对应的数据。但趋势判断需审视整个序列。)\n1. 总览趋势分析\n* (段落1：持股趋势) \"在自 [期初registerDate] 至 [期末registerDate] 的整个分析周期内，公司总股本/流通股本的持股数量展现出 [AI基于对JSON数组中所有total_shares的观察判断趋势，如：整体保持高度稳定，各报告期数值基本一致；或呈现阶段性增长后趋于平稳；或持续温和上扬等] 的特点。具体来看，期初持股量为 [期初total_shares] 股，而至分析期末，该数值 [稳定在/增长至/小幅调整为] [期末total_shares] 股。\"\n(可选，若有显著极值或转折点) \"值得注意的是，在 [某个中间registerDate]，总持股量曾达到 [对应的total_shares] 股的阶段性峰值/谷值/转折点。\"\n* (段落2：户数趋势) \"与此同时，公司股东总户数则经历了更为动态的变化，整体呈现出 [AI基于对JSON数组中所有total_shareholders的观察判断趋势，如：持续且显著的增长态势；或先降后升的“V”型走势；或在波动中重心逐步上移等] 的演变路径。股东总户数从期初的 [期初total_shareholders] 户，逐步 [攀升/回落/波动变化] 至期末的 [期末total_shareholders] 户。\"\n(可选，若有显著极值) \"在此期间，股东总户数在 [对应registerDate] 曾触及 [对应total_shareholders] 户的最高记录，或在 [对应registerDate] 降至 [对应total_shareholders] 户的最低水平。\"\n* (段落3：综合解读与筹码趋势) \"综合上述两方面数据，尽管总持股规模 [维持稳定/小幅变动]，但股东户数的 [持续增加/动态调整] 对人均持股规模产生了直接影响。通过计算，期末人均持股量约为 [期末total_shares / 期末total_shareholders，保留合适小数位] 股，与期初的约 [期初total_shares / 期初total_shareholders，保留合适小数位] 股相比，呈现出 [明显下降/有所上升/窄幅波动] 的趋势。这一系列数据变化清晰地指向，在整个分析周期内，公司股权的整体筹码分布正朝着 [更为分散/逐步集中/先集中后分散等，根据户数和人均持股的综合趋势判断] 的方向演进。\"\n2. 信用股东趋势分析\n* (段落1：持股趋势) \"在分析周期内，信用股东群体的合计持股数量展现出 [AI基于对JSON数组中所有credit_shares的观察判断趋势] 的特点。其持股总量从期初的 [期初credit_shares] 股变动至期末的 [期末credit_shares] 股，期间 [可简述中间是否有显著的增减阶段]。\"\n* (段落2：户数趋势) \"相对应地，参与信用交易的股东户数则呈现 [AI基于对JSON数组中所有credit_shareholders的观察判断趋势] 的走势，从期初的 [期初credit_shareholders] 户调整至期末的 [期末credit_shareholders] 户。\"\n* (段落3：综合解读与影响力变化) \"尽管信用股东户数经历了 [上升/下降/波动]，但其人均持股规模从期初的约 [期初credit_shares / 期初credit_shareholders] 股变动至期末的约 [期末credit_shares / 期末credit_shareholders] 股，整体显示出信用股东群体内部筹码 [趋向集中/有所分散/先X后Y] 的特征。从市场影响力角度观察，信用股东的持股占总股本比例也从期初的 [期初credit_shares_ratio] 演变至期末的 [期末credit_shares_ratio]，反映了其在公司总股本中的权重 [在逐步提升/有所下降/保持相对稳定/呈现波动变化]。\"\n3. 个人股东趋势分析\n* (段落1：持股趋势) \"个人股东在本分析周期内合计持有的股份数量整体展现出 [AI基于对JSON数组中所有individual_shares的观察判断趋势] 的演变。具体从期初的 [期初individual_shares] 股变化至期末的 [期末individual_shares] 股，整个过程 [可简述中间波动特征，如持续增长，或先抑后扬等]。\"\n* (段落2：户数趋势) \"与此同时，个人股东的户数则表现出 [AI基于对JSON数组中所有individual_shareholders的观察判断趋势] 的特点。期初为 [期初individual_shareholders] 户，至期末该数值变为 [期末individual_shareholders] 户。\"\n* (段落3：综合解读与筹码动态) \"从户数变化来看，个人投资者的市场参与度在期内经历了 [活跃度提升/参与度下降/整体稳定但结构调整] 的过程。值得注意的是，个人股东的人均持股规模从期初的约 [期初individual_shares / 期初individual_shareholders] 股演变至期末的约 [期末individual_shares / 期末individual_shareholders] 股，这直接反映了个人投资者群体内部的筹码分布正在经历一个 [逐步集中/趋于分散/先集中后分散等] 的动态调整。\"\n4. 机构股东趋势分析\n* (段落1：持股趋势) \"在分析周期内，机构投资者的合计持股数量整体呈现 [AI基于对JSON数组中所有institutional_shares的观察判断趋势] 的特点。其持股总量从期初的 [期初institutional_shares] 股调整至期末的 [期末institutional_shares] 股，期间 [可简述持股量变化的主要阶段和特征]。\"\n* (段落2：户数趋势) \"然而，参与持股的机构户数却展现出 [AI基于对JSON数组中所有institutional_shareholders的观察判断趋势] 的动态。从期初的 [期初institutional_shareholders] 户演变至期末的 [期末institutional_shareholders] 户，显示出机构参与度的 [扩张/收缩/波动调整]。\"\n* (段落3：综合解读与策略观察) \"机构持股总量与户数变化的 [同步性/背离性/复杂性] 动态，综合揭示了机构投资者对公司的整体关注度和持股策略的演变。尽管参与机构数量 [增加/减少/波动]，但其合计持股量的 [下降/上升/调整] 表明机构整体的持股意愿可能在 [减弱/增强/出现显著分化]。进一步观察，机构股东的人均持股规模从期初的约 [期初institutional_shares / 期初institutional_shareholders] 股调整至期末的约 [期末institutional_shares / 期末institutional_shareholders] 股，这反映了机构平均持股能力的 [增强/减弱/变化，以及可能的内部结构调整，例如大型机构的进出或持仓调整]。\"\n5. 细分机构股东趋势分析\n* (段落1：期末主导力量) \"在最新的机构股东构成中（期末数据），从持股规模维度看，[期末institutional_breakdown中shares最高的机构type] 占据了主导地位，其持股量达到 [其期末shares] 股。而在参与户数方面，[期末institutional_breakdown中shareholders最高的机构type] 则显示出最为广泛的机构参与基础，共有 [其期末shareholders] 户。\"\n* (段落2：核心类型持股趋势) \"深入分析主要细分机构类型在本报告周期的动态演变：[选择1-2种shares变化最显著的机构类型，如基金/私募/保险等] 的持股规模从期初的 [其期初shares] 股 [显著增长/大幅减少/波动变化] 至期末的 [其期末shares] 股，成为本期机构持股变动的重要驱动因素/主要流出方。与此同时，[选择另外1-2种shares或shareholders变化有特点的机构类型] 则呈现出 [描述其shares或shareholders的变化趋势和期初期末数据]。\"\n* (段落3：内部结构调整总结) \"综合各细分机构的数据变化，机构股东的内部持股结构在本分析周期内经历了一系列调整。整体来看，[总结哪些类型的机构影响力在提升，例如：以XX、YY为代表的机构类型持股占比/户数占比有所上升]，而 [总结哪些类型的机构影响力在相对下降] 的市场权重则出现一定程度的下滑，显示出机构投资者内部的偏好和资金流向正在发生结构性变化。\"\n6. 持股集中度趋势分析\n* (段落1：核心股东群体持股比例趋势) \"就股权集中度而言，在整个分析周期内，公司前十大股东的合计持股比例展现出 [AI基于对JSON数组中所有top10_shareholding_ratio的观察判断趋势，如：持续小幅下滑的态势；或在XX%至YY%区间内波动运行；或呈现稳步提升的特点]，具体从期初的 [期初top10_shareholding_ratio]% 演变至期末的 [期末top10_shareholding_ratio]%。\"\n\"类似地，前二十大股东的合计持股比例也呈现出 [AI基于对JSON数组中所有top20_shareholding_ratio的观察判断趋势，并与前十趋势对比，如：与前十大股东基本同步的变动趋势；或表现出更大幅度的集中/分散等]，其比例从期初的 [期初top20_shareholding_ratio]% 调整至期末的 [期末top20_shareholding_ratio]%。\"\n* (段落2：集中度结论与印证) \"上述两大核心股东群体的持股比例变化趋势，直接且清晰地表明公司的整体股权集中度在本分析期内正在经历一个 [逐步降低/有所提高/区间调整，或先X后Y] 的过程。这一关于股权集中度的判断，与本报告第一部分“总览趋势分析”中观察到的股东总户数 [总结其整体变化趋势，如：持续上升] 以及人均持股规模 [总结其整体变化趋势，如：逐步减少] 所反映的筹码动态形成了有力的相互印证，共同指向了当前股权结构的演变方向。\"\n通用输出要求：\n模块化与段落化输出： 严格按照上述六个一级模块标题输出，每个模块内部由多个逻辑连贯的段落组成，不使用小标题。\n数据准确与计算严谨： 所有从JSON中提取的数据和基于此的计算必须准确无误。\n趋势判断的全面性与审慎性： AI在判断趋势时，需综合考虑整个数据序列，对于“波动”、“稳定”等判断应基于对数据的整体观察。\n语言专业流畅，避免生硬： 保持语言的专业性和客观性，行文力求自然流畅，将数据和分析有机结合。\n聚焦数据事实，避免臆断： 严禁任何形式的“为什么”分析或市场原因解读，所有结论均需从“数据告诉我们什么”出发。", "options": {}}, "id": "a8fedae1-23ce-47c7-94c2-8ab08c8f922d", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [660, -140], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "doubao-1-5-pro-32k-250115", "mode": "id"}, "options": {}}, "id": "13a41c7f-36e0-4950-83fe-daab63eb09bb", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [740, 80], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "tQJUT9Vow398edYp", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "8ee2219b-9876-4010-8db6-09e84d64bd2d", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-840, -260], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取所有时间范围内公司基础数据（直接从company_info获取，含比例）\nSELECT \n  \"registerDate\",\n  \"companyName\",\n  \"companyCode\",\n  \"totalShares\"::numeric as total_shares,\n  \"totalShareholders\" as total_shareholders,\n  \"totalInstitutions\" as institutional_shareholders, \n  \"institutionShares\"::numeric as institutional_shares,\n  ROUND((\"institutionShares\"::numeric / NULLIF(\"totalShares\"::numeric,0)) * 100, 2) as institutional_shares_ratio,\n  (\"totalShareholders\" - \"totalInstitutions\") as individual_shareholders,\n  (\"totalShares\"::numeric - \"institutionShares\"::numeric) as individual_shares,\n  ROUND(((\"totalShares\"::numeric - \"institutionShares\"::numeric) / NULLIF(\"totalShares\"::numeric,0)) * 100, 2) as individual_shares_ratio\nFROM company_info \nWHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\nORDER BY \"registerDate\" ASC", "options": {}}, "id": "c3c8e5b8-1450-4a99-a33b-a91c4ad89893", "name": "获取公司基础数据", "type": "n8n-nodes-base.postgres", "position": [-80, -380], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取所有时间范围内信用账户数据（含比例）\nWITH date_ranges AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ntotal_shares_map AS (\n  SELECT \"registerDate\", \"totalShares\"::numeric as total_shares\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ncredit_agg AS (\n  SELECT \n    s.\"registerDate\",\n    COUNT(DISTINCT CASE WHEN s.\"marginAccount\" IS NOT NULL AND s.\"marginAccount\" != '' THEN s.\"shareholderId\" END) as credit_shareholders,\n    SUM(COALESCE(s.\"sharesInMarginAccount\", 0))::numeric as credit_shares\n  FROM shareholder s\n  INNER JOIN date_ranges dr ON s.\"registerDate\" = dr.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY s.\"registerDate\"\n)\nSELECT \n  \n  c.\"registerDate\",\n  c.credit_shareholders,\n  c.credit_shares,\n  ROUND((c.credit_shares / NULLIF(t.total_shares,0)) * 100, 2) as credit_shares_ratio\nFROM credit_agg c\nLEFT JOIN total_shares_map t ON c.\"registerDate\" = t.\"registerDate\"\nORDER BY c.\"registerDate\" ASC", "options": {}}, "id": "e2429fcb-e05e-4124-8ddd-f5ebfdab3697", "name": "获取信用账户数据", "type": "n8n-nodes-base.postgres", "position": [-80, -180], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取指定时间范围内股东集中度数据（前十大和前二十大，含数量和比例）\nWITH date_ranges AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ntotal_shares_map AS (\n  SELECT \"registerDate\", \"totalShares\"::numeric as total_shares\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nshareholder_rankings AS (\n  SELECT \n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (PARTITION BY s.\"registerDate\" ORDER BY s.\"numberOfShares\"::numeric DESC) as ranking\n  FROM shareholder s\n  INNER JOIN date_ranges dr ON s.\"registerDate\" = dr.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nconcentration_stats AS (\n  SELECT \n    sr.\"registerDate\",\n    SUM(CASE WHEN ranking <= 10 THEN sr.\"numberOfShares\" ELSE 0 END)::numeric as top10_shareholding_amount,\n    ROUND(SUM(CASE WHEN ranking <= 10 THEN sr.\"shareholdingRatio\" ELSE 0 END)::numeric, 2) as top10_shareholding_ratio,\n    SUM(CASE WHEN ranking <= 20 THEN sr.\"numberOfShares\" ELSE 0 END)::numeric as top20_shareholding_amount,\n    ROUND(SUM(CASE WHEN ranking <= 20 THEN sr.\"shareholdingRatio\" ELSE 0 END)::numeric, 2) as top20_shareholding_ratio\n  FROM shareholder_rankings sr\n  GROUP BY sr.\"registerDate\"\n)\nSELECT \n  c.\"registerDate\",\n  c.top10_shareholding_amount,\n  c.top20_shareholding_amount,\n  ROUND((c.top10_shareholding_amount / NULLIF(t.total_shares,0)) * 100, 2) as top10_shareholding_amount_ratio,\n  ROUND((c.top20_shareholding_amount / NULLIF(t.total_shares,0)) * 100, 2) as top20_shareholding_amount_ratio\nFROM concentration_stats c\nLEFT JOIN total_shares_map t ON c.\"registerDate\" = t.\"registerDate\"\nORDER BY c.\"registerDate\" ASC", "options": {}}, "id": "ef418660-9262-4465-9a02-1961ff8cf412", "name": "获取股东集中度数据", "type": "n8n-nodes-base.postgres", "position": [-80, 20], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "aa156512-106b-4337-a733-5b0185139327", "name": "startDate", "value": "={{ $json.startDate }}", "type": "string"}, {"id": "7f9dc7a1-8d3e-44a5-84b2-ebe8edcde5bc", "name": "endDate", "value": "={{ $json.endDate }}", "type": "string"}]}, "options": {}}, "id": "86474ac8-dd1e-45d1-bddb-5726118252d1", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [-300, -60], "typeVersion": 3.4}, {"parameters": {"content": "本接口端点：shareholder-trend-report", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1860, -80], "id": "a190728f-6b47-4291-bf24-bc8d440522a9", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "检验是否输入参数id", "height": 80, "width": 160}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1200, 60], "id": "690055a1-64b3-4535-bb94-e5bd7e8a9eff", "name": "Sticky Note1"}, {"parameters": {"content": "检验是否能在数据库中找到对应组织", "height": 80, "width": 280}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-800, -140], "id": "431c20fa-12ba-409e-803d-dabf84bc30d2", "name": "Sticky Note2"}, {"parameters": {"content": "获得所有公司趋势数据并合并为单一item方便传入AI Agent中", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [240, 40], "id": "769e9a3a-ab5c-4fc7-a897-a654dd1ddaeb", "name": "Sticky Note3"}, {"parameters": {"content": "获取公司概览数据，并传入豆包模型中进行AI报告生成", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [660, -260], "id": "2326fcab-5a88-42c0-a08a-77c82ffbbbcc", "name": "Sticky Note4"}, {"parameters": {"content": "格式化AI报告，并且返回数据", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1120, -260], "id": "efbe5cec-66dc-40c3-815f-a92ac4bdd014", "name": "Sticky Note5"}, {"parameters": {"content": "数据库查找不到对应id时返回错误信息", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-720, 380], "id": "e85a9e24-6469-4520-a48a-1acd1ac12ef4", "name": "Sticky Note6"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-640, -60], "id": "94ac2344-25d2-4c36-a05c-3da06e2e8dad", "name": "检验组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-700, 220], "id": "cd9a6553-72bd-47db-90bb-89a9a3055cf1", "name": "格式化错误信息"}, {"parameters": {"mode": "combine", "fieldsToMatchString": "registerDate", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [80, -180], "id": "c31efcea-da4c-411d-8c8d-292851c61628", "name": "合并数据", "alwaysOutputData": true}, {"parameters": {"mode": "combine", "fieldsToMatchString": "registerDate", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [260, -120], "id": "7a156431-c4ac-4501-a92d-afa3dafb0988", "name": "合并数据1", "alwaysOutputData": true}, {"parameters": {"jsCode": "return [{\n  trendData: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, -120], "id": "9a2d8f44-0059-481e-8bf4-d5217cd2f91b", "name": "包装数据为单一json对象"}, {"parameters": {"httpMethod": "POST", "path": "shareholder-trend-report", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1480, -80], "id": "079ba5f7-70a0-4c6c-9dbe-bdaee1b87361", "name": "股东趋势AI报告接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "c351f46d-5fb1-4e61-91a7-ec5d06936c8c", "name": "股东趋势AI报告接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1240, -140], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 260], "id": "76a0accc-947e-42a6-83ae-3b1e84182ce0", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"output\": AI报告实际内容\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:19:47.742Z\"\n\t}\n]", "height": 220, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1460, -200], "id": "899fd302-0e63-4a32-b834-237fb0ecbf46", "name": "Sticky Note8"}], "connections": {"输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "股东趋势AI报告接口响应", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "获取公司基础数据": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "获取信用账户数据": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "获取股东集中度数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 1}]]}, "设置组织ID": {"main": [[{"node": "获取公司基础数据", "type": "main", "index": 0}, {"node": "获取信用账户数据", "type": "main", "index": 0}, {"node": "获取股东集中度数据", "type": "main", "index": 0}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应", "type": "main", "index": 0}]]}, "合并数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 0}]]}, "合并数据1": {"main": [[{"node": "包装数据为单一json对象", "type": "main", "index": 0}]]}, "包装数据为单一json对象": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "股东趋势AI报告接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}