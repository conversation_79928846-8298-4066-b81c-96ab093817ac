{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "a57ad511-d986-452c-90fa-13f73db248bc", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1380, -20], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "b6506b1d-02e4-4841-8445-feeb89054a3f", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1400, 240], "typeVersion": 3.4}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const page = inputData?.query?.page || inputData?.body?.page || inputData?.page || 1;\n  const pageSize = inputData?.query?.pageSize || inputData?.body?.pageSize || inputData?.pageSize || 5;\n  const order_base = inputData?.query?.order_base || inputData?.body?.order_base || inputData?.order_base || 'current_numberOfShares';\n  const order = inputData?.query?.order || inputData?.body?.order || inputData?.order || 'Desc';\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 page\n  const parsedPage = parseInt(page, 10);\n  if (isNaN(parsedPage) || parsedPage <= 0) {\n    throw new Error('INVALID_PAGE');\n  }\n\n  // 验证 pageSize\n  const parsedPageSize = parseInt(pageSize, 10);\n  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {\n    throw new Error('INVALID_PAGE_SIZE');\n  }\n\n  return {\n    organizationId: id.trim(),\n    page: parsedPage,\n    pageSize: parsedPageSize,\n    order: order,\n    order_base:order_base\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId, page, pageSize, order,order_base } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      page,\n      pageSize,\n      type: inputData.webhookUrl,\n      order:order,\n      order_base:order_base,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_PAGE': '无效的page参数：必须是大于0的整数。',\n    'INVALID_PAGE_SIZE': '无效的pageSize参数：必须是大于0的整数。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "89e9eaf0-ba52-4382-9162-6541d0d4fbe2", "name": "输入验证1", "type": "n8n-nodes-base.code", "position": [-480, 260], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "bde09b8b-d0b5-46f2-b4ac-7e6c6f7836a7", "name": "检查验证结果1", "type": "n8n-nodes-base.if", "position": [-300, 260], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "7cd49652-c26c-4d4f-a30a-58c0261d2292", "name": "page", "value": "={{ $json.page }}", "type": "string"}, {"id": "c4065c11-a83d-460e-b614-5b0bf60a75de", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "f3f357a6-4c7a-4a01-b988-075fb7aad662", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "62efbef2-3a9f-4e7b-91fc-d3bea08409f4", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "id": "b6aa3bb7-64fe-4751-9d07-24d33b4ebe9f", "name": "设置组织ID1", "type": "n8n-nodes-base.set", "position": [480, 220], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "39a41a8c-8642-4ee4-b60c-1b752628ece1", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-140, -20], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "8d35cd60-0a5e-48a2-8b7d-fb29dd847d1e", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [640, 460], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-60, 160], "id": "3a40965c-0811-4890-a723-8e52d695585c", "name": "检验输入id1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pair AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 所有自然人股东\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\",\n    s.\"shareholderType\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n      s.\"shareholderType\" ILIKE '%个人%' OR\n      s.\"shareholderType\" ILIKE '%知名牛散%'\n    )\n),\n\n-- 每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 当前期前20名\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 所有期完整数据（用于对比）\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n),\n\n-- 当前期在前20，且持股未发生变化\ncase_equal AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    prev.\"numberOfShares\" AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    prev.\"registerDate\" AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pair dp\n  JOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\n  JOIN prev_all prev ON prev.\"registerDate\" = dp.prev_date\n                     AND prev.\"shareholderId\" = curr.\"shareholderId\"\n  WHERE curr.\"numberOfShares\" = prev.\"numberOfShares\"\n)\n\n-- 输出结果\nSELECT\n  \"securitiesAccountName\" AS name,\n  \"unifiedAccountNumber\" AS unified_account_number,\n  curr_numberOfShares AS current_numberOfShares,\n  curr_shareholdingRatio AS current_shareholdingratio,\n  ROW_NUMBER() OVER (ORDER BY curr_numberOfShares DESC) AS rank,\n  COUNT(*) OVER() AS total\nFROM case_equal\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [940, 80], "id": "7cd676d6-f7fb-4aac-b40d-eef0608f376d", "name": "获取不动个人股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json.organizationId }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pair AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\nshareholder_with_info AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND (\n      s.\"shareholderType\" NOT ILIKE '%个人%' AND\n      s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n    )\n),\njoined_periods AS (\n  SELECT\n    dp.current_date,\n    dp.prev_date,\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_shares,\n    curr.\"shareholdingRatio\" AS curr_ratio,\n    prev.\"numberOfShares\" AS prev_shares\n  FROM date_pair dp\n  JOIN shareholder_with_info curr ON curr.\"registerDate\" = dp.current_date\n  JOIN shareholder_with_info prev \n    ON prev.\"registerDate\" = dp.prev_date \n    AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n),\nunchanged_institutions AS (\n  SELECT\n    \"securitiesAccountName\" AS \"name\",\n    \"unifiedAccountNumber\" AS \"unified_account_number\",\n    curr_shares AS \"current_numberofshares\",\n    curr_ratio AS \"current_shareholdingratio\",\n    ROW_NUMBER() OVER (ORDER BY curr_shares DESC) AS rank,\n    COUNT(*) OVER() AS total\n  FROM joined_periods\n  WHERE curr_shares = prev_shares\n)\nSELECT * FROM unchanged_institutions\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [940, 300], "id": "666f2396-43b7-4bfa-9f0d-6b0efbcb95c1", "name": "获取不动机构股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "此工作流包含两个后端接口，分别为：\n\n个人不动股东：constant-individual-shareholders-trend\n\n机构不动股东：constant-institution-shareholders-trend", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1220, 280], "id": "2c99c977-b297-4901-bbb6-2759f25e7fad", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "输入参数验证，id必填\npage，pagesize，order，order_base选填，这四个函数用于分页查询和排序查询", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-500, 420], "id": "69e69f39-683a-4fd2-a8b5-d12535320d97", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入id是否能查询到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, 140], "id": "103345d5-74e0-4d15-ace0-71248a46fb50", "name": "Sticky Note2"}, {"parameters": {"content": "根据输入的url判断查询个人还是机构股东", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [620, 120], "id": "3aaf4a48-945b-4a73-9366-aa8e86b9b3ee", "name": "Sticky Note3"}, {"parameters": {"content": "查询对应类型的不动股东\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, 140], "id": "d57aac35-95fd-4993-b4dd-582d7c223bcf", "name": "Sticky Note4"}, {"parameters": {"content": "格式化查询数据并返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1780, 120], "id": "5ff95935-488e-4fdd-aeb9-630816310d8f", "name": "Sticky Note5"}, {"parameters": {"httpMethod": "POST", "path": "constant-individual-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, 200], "id": "8d5f8a60-5e5a-4d65-9e01-07f8db13bd4b", "name": "获取个人不动股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "09af8040-aa5d-4f5d-8217-ab8b71012a7f", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "37175b76-60c6-47a8-81b0-0410624183c2", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-60, 320], "id": "0c6d48ed-a030-4871-9bc4-c5991661e0b7", "name": "设置分页参数"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [120, 240], "id": "9b3dbc6b-e75f-411b-8d99-6a8be85e921f", "name": "合并数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [280, 240], "id": "0199efaa-6e47-4bcc-93e8-587b0ad74309", "name": "检验组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [480, 460], "id": "4733b9da-6ceb-4638-88e6-108e35701918", "name": "格式化错误信息"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2b1a11d5-5194-4264-9099-d7c2978cf286", "leftValue": "={{ $json.type }}", "rightValue": "individual", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [700, 220], "id": "f9e369db-6069-4dba-9f51-6678ec4583d8", "name": "根据url判断进入哪个工作流"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  constantIndividuals: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, -20], "id": "e0c000ab-16a5-4a57-9b90-67225ee8296c", "name": "计算数据总量"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  constantInstitutions: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1140, 260], "id": "7863fd4d-98ec-4642-90f8-c79755bc6c5f", "name": "计算数据总量1"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "87818a9e-3de1-40af-a5be-b0aef2d22fcd", "name": "个人不动股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1620, -20], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "549fe4b5-5f46-491e-862f-716c5e70ff8b", "name": "机构不动股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1640, 240], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "constant-institution-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, 360], "id": "6a9692c5-0ebd-4380-964c-1cfc9eb925ac", "name": "获取机构股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1200, 520], "id": "f1f08a56-6b5f-4597-acf8-701c9e3ec90d", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"constantIndividuals\": [\n\t\t\t\t{\n\t\t\t\t\t\"name\": \"吴美容\",\n\t\t\t\t\t\"unified_account_number\": \"************\",\n\t\t\t\t\t\"current_numberofshares\": \"********.00\",\n\t\t\t\t\t\"current_shareholdingratio\": \"5.61\",\n\t\t\t\t\t\"rank\": \"1\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"total\": 1\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:15:04.384Z\"\n\t}\n]", "height": 380, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2100, 40], "id": "a8c0e096-2e19-404a-9a4f-d449055161b2", "name": "Sticky Note6"}], "connections": {"格式化成功响应": {"main": [[{"node": "个人不动股东接口响应", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "机构不动股东接口响应", "type": "main", "index": 0}]]}, "输入验证1": {"main": [[{"node": "检查验证结果1", "type": "main", "index": 0}]]}, "检查验证结果1": {"main": [[{"node": "错误响应1", "type": "main", "index": 0}], [{"node": "检验输入id1", "type": "main", "index": 0}, {"node": "设置分页参数", "type": "main", "index": 0}]]}, "设置组织ID1": {"main": [[{"node": "根据url判断进入哪个工作流", "type": "main", "index": 0}]]}, "检验输入id1": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "获取不动个人股东": {"main": [[{"node": "计算数据总量", "type": "main", "index": 0}]]}, "获取不动机构股东": {"main": [[{"node": "计算数据总量1", "type": "main", "index": 0}]]}, "获取个人不动股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}, "设置分页参数": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID1", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "根据url判断进入哪个工作流": {"main": [[{"node": "获取不动个人股东", "type": "main", "index": 0}], [{"node": "获取不动机构股东", "type": "main", "index": 0}]]}, "计算数据总量": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "计算数据总量1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "获取机构股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}