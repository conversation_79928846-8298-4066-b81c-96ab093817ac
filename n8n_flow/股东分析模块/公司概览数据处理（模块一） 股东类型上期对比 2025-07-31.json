{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取最新两期公司完整信息（包括机构持股数）并附加最早日期\nWITH latest_periods AS (\n  SELECT \n    \"companyName\",\n    \"companyCode\", \n    \"totalShares\",\n    \"totalShareholders\",\n    \"totalInstitutions\",\n    \"institutionShares\",\n    \"registerDate\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ncurrent_prev AS (\n  SELECT \n    MAX(CASE WHEN rn = 1 THEN \"companyName\" END) as \"currentCompanyName\",\n    MAX(CASE WHEN rn = 1 THEN \"companyCode\" END) as \"currentCompanyCode\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShares\" END) as \"currentTotalShares\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShareholders\" END) as \"currentTotalShareholders\",\n    MAX(CASE WHEN rn = 1 THEN \"totalInstitutions\" END) as \"currentTotalInstitutions\",\n    MAX(CASE WHEN rn = 1 THEN \"institutionShares\" END) as \"currentInstitutionShares\",\n    MAX(CASE WHEN rn = 1 THEN \"registerDate\" END) as \"currentRegisterDate\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShares\" END) as \"prevTotalShares\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShareholders\" END) as \"prevTotalShareholders\", \n    MAX(CASE WHEN rn = 2 THEN \"totalInstitutions\" END) as \"prevTotalInstitutions\",\n    MAX(CASE WHEN rn = 2 THEN \"institutionShares\" END) as \"prevInstitutionShares\"\n  FROM latest_periods\n  WHERE rn <= 2\n),\noldest_period AS (\n  SELECT MIN(\"registerDate\") AS \"oldestRegisterDate\"\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n)\nSELECT \n  cp.\"currentCompanyName\" as \"companyName\",\n  cp.\"currentCompanyCode\" as \"companyCode\",\n  cp.\"currentRegisterDate\" as \"registerDate\",\n  o.\"oldestRegisterDate\",\n  cp.\"currentTotalShares\" as \"totalShares\",\n  cp.\"currentTotalShareholders\" as \"totalShareholders\",\n  cp.\"currentTotalInstitutions\" as \"totalInstitutions\",\n  cp.\"currentInstitutionShares\" as \"institutionShares\",\n  (cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\") as \"individualShareholders\",\n  cp.\"prevTotalShares\",\n  cp.\"prevTotalShareholders\",\n  cp.\"prevTotalInstitutions\",\n  cp.\"prevInstitutionShares\",\n  (cp.\"prevTotalShareholders\" - cp.\"prevTotalInstitutions\") as \"prevIndividualShareholders\",\n  -- 计算数量变化\n  (cp.\"currentTotalShareholders\" - COALESCE(cp.\"prevTotalShareholders\", cp.\"currentTotalShareholders\")) as \"shareholdersChange\",\n  (cp.\"currentTotalInstitutions\" - COALESCE(cp.\"prevTotalInstitutions\", cp.\"currentTotalInstitutions\")) as \"institutionsChange\",\n  ((cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\") - \n   COALESCE((cp.\"prevTotalShareholders\" - cp.\"prevTotalInstitutions\"), \n            (cp.\"currentTotalShareholders\" - cp.\"currentTotalInstitutions\"))) as \"individualShareholdersChange\",\n  -- 计算百分比变化\n  CASE \n    WHEN cp.\"prevTotalShares\" IS NOT NULL AND cp.\"prevTotalShares\" > 0 \n    THEN ROUND(((cp.\"currentTotalShares\" - cp.\"prevTotalShares\") / cp.\"prevTotalShares\" * 100)::numeric, 2)\n    ELSE 0 \n  END as \"totalSharesChangePercent\",\n  CASE \n    WHEN cp.\"prevInstitutionShares\" IS NOT NULL AND cp.\"prevInstitutionShares\" > 0 \n    THEN ROUND(((cp.\"currentInstitutionShares\" - cp.\"prevInstitutionShares\") / cp.\"prevInstitutionShares\" * 100)::numeric, 2)\n    ELSE 0 \n  END as \"institutionSharesChangePercent\"\nFROM current_prev cp, oldest_period o;", "options": {}}, "id": "461ad015-0755-48b9-9a28-1f16551c0195", "name": "获取最新一期公司完整信息及较上期变化", "type": "n8n-nodes-base.postgres", "position": [-460, -960], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取最新两期信用数据\nWITH latest_dates AS (\n  SELECT \n    \"registerDate\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n),\ncurrent_period AS (\n  SELECT \n    COUNT(DISTINCT CASE WHEN \"marginAccount\" IS NOT NULL AND \"marginAccount\" != '' THEN \"shareholderId\" END) as \"currentCreditAccountCount\",\n    SUM(COALESCE(\"sharesInMarginAccount\", 0)) as \"currentTotalMarginShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = (SELECT \"registerDate\" FROM latest_dates WHERE rn = 1)\n),\nprev_period AS (\n  SELECT \n    COUNT(DISTINCT CASE WHEN \"marginAccount\" IS NOT NULL AND \"marginAccount\" != '' THEN \"shareholderId\" END) as \"prevCreditAccountCount\",\n    SUM(COALESCE(\"sharesInMarginAccount\", 0)) as \"prevTotalMarginShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = (SELECT \"registerDate\" FROM latest_dates WHERE rn = 2)\n)\nSELECT \n  c.\"currentCreditAccountCount\" as \"creditAccountCount\",\n  c.\"currentTotalMarginShares\" as \"totalMarginShares\",\n  COALESCE(p.\"prevCreditAccountCount\", c.\"currentCreditAccountCount\") as \"prevCreditAccountCount\",\n  COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\") as \"prevTotalMarginShares\",\n  -- 计算变化\n  (c.\"currentCreditAccountCount\" - COALESCE(p.\"prevCreditAccountCount\", c.\"currentCreditAccountCount\")) as \"creditAccountCountChange\",\n  CASE \n    WHEN COALESCE(p.\"prevTotalMarginShares\", 0) > 0 \n    THEN ROUND(((c.\"currentTotalMarginShares\" - COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\")) / COALESCE(p.\"prevTotalMarginShares\", c.\"currentTotalMarginShares\") * 100)::numeric, 2)\n    ELSE 0 \n  END as \"totalMarginSharesChangePercent\"\nFROM current_period c\nCROSS JOIN prev_period p", "options": {}}, "id": "b64c8981-e396-46f1-ae29-84feafb2bf1b", "name": "获取最新一期信用账户数据及较上期变化", "type": "n8n-nodes-base.postgres", "position": [-460, -760], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取前十大、前二十大股东持股比例及户均持股数据（修复版）\nWITH latest_periods AS (\n  SELECT \n    \"registerDate\",\n    \"totalShares\",\n    \"totalShareholders\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) as rn\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\n-- 获取最新两期的基础数据\ncompany_data AS (\n  SELECT \n    MAX(CASE WHEN rn = 1 THEN \"registerDate\" END) as \"currentRegisterDate\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShares\" END) as \"currentTotalShares\",\n    MAX(CASE WHEN rn = 1 THEN \"totalShareholders\" END) as \"currentTotalShareholders\",\n    MAX(CASE WHEN rn = 2 THEN \"registerDate\" END) as \"prevRegisterDate\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShares\" END) as \"prevTotalShares\",\n    MAX(CASE WHEN rn = 2 THEN \"totalShareholders\" END) as \"prevTotalShareholders\"\n  FROM latest_periods\n  WHERE rn <= 2\n),\n-- 最新期股东持股比例数据\ncurrent_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as ranking\n  FROM shareholder s, company_data cd\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = cd.\"currentRegisterDate\"\n),\n-- 上一期股东持股比例数据\nprev_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\" DESC) as ranking\n  FROM shareholder s, company_data cd\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = cd.\"prevRegisterDate\"\n),\n-- 最新期集中度统计\ncurrent_stats AS (\n  SELECT \n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 10 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"currentTop10Ratio\",\n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 20 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"currentTop20Ratio\"\n  FROM current_period_data\n),\n-- 上一期集中度统计\nprev_stats AS (\n  SELECT \n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 10 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"prevTop10Ratio\",\n    ROUND(COALESCE(SUM(CASE WHEN ranking <= 20 THEN \"shareholdingRatio\" END), 0)::numeric, 2) as \"prevTop20Ratio\"\n  FROM prev_period_data\n)\nSELECT \n  -- 最新期数据\n  cs.\"currentTop10Ratio\" as \"top10ShareholdingRatio\",\n  cs.\"currentTop20Ratio\" as \"top20ShareholdingRatio\", \n  -- 使用company_info表直接计算户均持股数\n  -- ROUND((cd.\"currentTotalShares\" / cd.\"currentTotalShareholders\")::numeric, 2) as \"avgSharesPerHolder\",\n  ROUND((cd.\"currentTotalShares\" / NULLIF(cd.\"currentTotalShareholders\", 0))::numeric, 2) as \"avgSharesPerHolder\",\n\n  \n  \n  -- 上一期数据\n  COALESCE(ps.\"prevTop10Ratio\", cs.\"currentTop10Ratio\") as \"prevTop10ShareholdingRatio\",\n  COALESCE(ps.\"prevTop20Ratio\", cs.\"currentTop20Ratio\") as \"prevTop20ShareholdingRatio\",\n  -- 上一期户均持股数\n  -- ROUND((COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\"))::numeric, 2) as \"prevAvgSharesPerHolder\",\n  ROUND((\n  COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / \n  NULLIF(COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\"), 0)\n)::numeric, 2) as \"prevAvgSharesPerHolder\",\n  \n  -- 计算变化\n  ROUND((cs.\"currentTop10Ratio\" - COALESCE(ps.\"prevTop10Ratio\", cs.\"currentTop10Ratio\"))::numeric, 2) as \"top10RatioChange\",\n  ROUND((cs.\"currentTop20Ratio\" - COALESCE(ps.\"prevTop20Ratio\", cs.\"currentTop20Ratio\"))::numeric, 2) as \"top20RatioChange\",\n  -- 户均持股数变化\n  -- ROUND(((cd.\"currentTotalShares\" / cd.\"currentTotalShareholders\") - \n  --        (COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\")))::numeric, 2) as \"avgSharesPerHolderChange\"\n  ROUND((\n  (cd.\"currentTotalShares\" / NULLIF(cd.\"currentTotalShareholders\", 0)) - \n  (COALESCE(cd.\"prevTotalShares\", cd.\"currentTotalShares\") / NULLIF(COALESCE(cd.\"prevTotalShareholders\", cd.\"currentTotalShareholders\"), 0))\n)::numeric, 2) as \"avgSharesPerHolderChange\"\n  \nFROM company_data cd\nCROSS JOIN current_stats cs\nCROSS JOIN prev_stats ps", "options": {}}, "id": "b2d18a55-4927-40b1-b3ff-a7b97599060d", "name": "获取股东集中度及户均持股数据", "type": "n8n-nodes-base.postgres", "position": [-240, -585], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}]}, "options": {}}, "id": "34db3ae7-218b-405b-a959-9a1ca0537bf9", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [-680, -585], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_period AS (\n  SELECT \n    \"registerDate\",\n    \"totalShares\"\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\" DESC\n  LIMIT 1\n),\ncurrent_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) as ranking\n  FROM shareholder s, latest_period lp\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}' \n    AND s.\"registerDate\" = lp.\"registerDate\"\n)\nSELECT\n  -- 前1大\n  SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top1_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top1_shareholding_ratio,\n  -- 前10大\n  SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top10_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top10_shareholding_ratio,\n  -- 前100大\n  SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top100_shareholding_amount,\n  ROUND(\n    SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period),0) * 100, 2\n  ) AS top100_shareholding_ratio\nFROM current_period_data\n;", "options": {}}, "id": "4c914445-56ed-4f7f-a3a7-7063381b27b7", "name": "获取核心持股数据", "type": "n8n-nodes-base.postgres", "position": [-220, -200], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// 输入验证函数 - 检查organizationId\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  \n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  \n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  \n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n  \n  return id.trim();\n};\n\ntry {\n  const inputData = $input.first().json;\n  const organizationId = validateInput(inputData);\n  \n  return [{\n    json: {\n      organizationId: organizationId,\n      type:inputData.webhookUrl,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  // 返回验证错误信息\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。'\n  };\n  return messages[code] || '输入验证失败';\n}"}, "id": "6b00ff0b-7ecd-4ac3-acf2-752f8e205cf3", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-1560, -410], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "9387b336-d8e1-4726-bef0-660c1d287f5e", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1340, -410], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "90196595-daf6-4dea-9fb5-d0f4f8df0797", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [640, -485], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "99e10f19-2cf0-4499-a055-d7bbd5bc5719", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1120, -510], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "company-info", "responseMode": "responseNode", "options": {}}, "id": "ed726123-6fd5-485b-a1e2-e53b54a03cb3", "name": "获取公司概览数据接口", "type": "n8n-nodes-base.webhook", "position": [-1780, -410], "typeVersion": 2, "webhookId": "e4004008-c91a-46f5-bd32-76c95f2bd746"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "56834f61-a378-49d5-83c4-c6c75f2985cf", "name": "获取公司概览接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [860, -485], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1120, -310], "id": "9eb108c6-ee89-44dc-9d6b-cf9c2eb058ba", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "9450b12f-7a87-49e4-8bb8-b0fef1f09ea8", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-460, -85], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "/**\n * 股东类型统计查询（包含上期对比数据及数据验证）\n * <AUTHOR>\n * @created 2025-07-17 18:41:42\n * @description 查询最新报告期各机构股东类型的户数、持股数及与上期的对比变化，包含数据验证反馈\n * @update 2025-07-17 19:10:27 hayden 新增数据验证逻辑，防止只有一期名册数据的情况\n */\nWITH DateRange AS (\n    -- 获取最新和上期报告期日期\n    SELECT\n        MAX(\"registerDate\") as latest_date,\n        -- 获取上期日期（最新日期之前的最大日期）\n        (SELECT MAX(\"registerDate\")\n         FROM \"shareholder\"\n         WHERE \"organizationId\" = '{{ $json.organizationId }}'\n         AND \"registerDate\" < (SELECT MAX(\"registerDate\") FROM \"shareholder\" WHERE \"organizationId\" = '{{ $json.organizationId }}')\n        ) as prev_date,\n        -- 统计该组织的报告期数量\n        COUNT(DISTINCT \"registerDate\") as period_count\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n),\nDataValidation AS (\n    -- 数据验证：检查是否有足够的数据进行对比分析\n    SELECT\n        dr.latest_date,\n        dr.prev_date,\n        dr.period_count,\n        CASE\n            WHEN dr.period_count < 2 THEN 'INSUFFICIENT_DATA'\n            WHEN dr.prev_date IS NULL THEN 'NO_PREVIOUS_PERIOD'\n            ELSE 'VALID'\n        END as validation_status,\n        CASE\n            WHEN dr.period_count < 2 THEN '数据不足：该组织仅有一期股东名册数据，无法进行期间对比分析。请确保至少上传两期股东名册数据。'\n            WHEN dr.prev_date IS NULL THEN '缺少上期数据：无法获取上期报告期数据，请检查数据完整性。'\n            ELSE '数据验证通过'\n        END as validation_message\n    FROM DateRange dr\n),\nCurrentTypeStats AS (\n    -- 计算最新报告期每种机构股东类型的户数和持股数\n    SELECT\n        \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS typeCount,\n        -- 确保正确转换为数值类型后再求和\n        SUM(CAST(\"numberOfShares\" AS DECIMAL(17,2))) AS typeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    -- 使用模糊匹配排除个人股东类型和知名牛散\n    AND \"shareholderType\" NOT LIKE '%个人%'\n    AND \"shareholderType\" != '知名牛散'\n    -- 只查询最新日期的数据\n    AND \"registerDate\" = (SELECT latest_date FROM DateRange)\n    GROUP BY \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n    -- 只保留有股东的类型（typeCount > 0）\n    HAVING COUNT(DISTINCT \"shareholderId\") > 0\n),\nPrevTypeStats AS (\n    -- 计算上期报告期每种机构股东类型的户数和持股数\n    SELECT\n        \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS prevTypeCount,\n        SUM(CAST(\"numberOfShares\" AS DECIMAL(17,2))) AS prevTypeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    AND \"shareholderType\" NOT LIKE '%个人%'\n    AND \"shareholderType\" != '知名牛散'\n    AND \"registerDate\" = (SELECT prev_date FROM DateRange WHERE prev_date IS NOT NULL)\n    GROUP BY \"registerDate\",\n        CASE\n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n    HAVING COUNT(DISTINCT \"shareholderId\") > 0\n),\nCurrentCompanyInfo AS (\n    -- 获取最新报告期的CompanyInfo数据作为分母\n    SELECT\n        sr.\"registerDate\",\n        ci.\"totalInstitutions\",\n        ci.\"institutionShares\"\n    FROM \"company_info\" ci\n    JOIN \"shareholder_registry\" sr ON ci.\"registryId\" = sr.id\n    WHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\n    -- 只查询最新日期的数据\n    AND sr.\"registerDate\" = (SELECT latest_date FROM DateRange)\n),\nPrevCompanyInfo AS (\n    -- 获取上期报告期的CompanyInfo数据作为分母\n    SELECT\n        sr.\"registerDate\",\n        ci.\"totalInstitutions\" as \"prevTotalInstitutions\",\n        ci.\"institutionShares\" as \"prevInstitutionShares\"\n    FROM \"company_info\" ci\n    JOIN \"shareholder_registry\" sr ON ci.\"registryId\" = sr.id\n    WHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\n    AND sr.\"registerDate\" = (SELECT prev_date FROM DateRange WHERE prev_date IS NOT NULL)\n),\nCombinedStats AS (\n    -- 合并当期和上期数据\n    SELECT\n        COALESCE(cts.shareholderType, pts.shareholderType) AS shareholderType,\n        COALESCE(cts.typeCount, 0) AS typeCount,\n        COALESCE(cts.typeShares, 0) AS typeShares,\n        COALESCE(pts.prevTypeCount, 0) AS prevTypeCount,\n        COALESCE(pts.prevTypeShares, 0) AS prevTypeShares\n    FROM CurrentTypeStats cts\n    FULL OUTER JOIN PrevTypeStats pts ON cts.shareholderType = pts.shareholderType\n    -- 只保留当期有数据的类型\n    WHERE cts.shareholderType IS NOT NULL\n)\n\n-- 构建最终结果\nSELECT\n    -- 数据验证信息\n    dv.validation_status as \"validationStatus\",\n    dv.validation_message as \"validationMessage\",\n    dv.period_count as \"periodCount\",\n    \n    -- 使用TO_CHAR将日期转换为ISO格式字符串\n    CASE\n        WHEN dv.validation_status = 'VALID' THEN\n            TO_CHAR(dv.latest_date AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"')\n        ELSE NULL\n    END AS \"registerDate\",\n    \n    CASE\n        WHEN dv.validation_status = 'VALID' AND dv.prev_date IS NOT NULL THEN\n            TO_CHAR(dv.prev_date AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"')\n        ELSE NULL\n    END AS \"prevRegisterDate\",\n    \n    -- 只有验证通过才返回股东类型数据\n    CASE\n        WHEN dv.validation_status = 'VALID' THEN\n            json_agg(\n                json_build_object(\n                    'shareholderType', cs.shareholderType,\n                    'typeCount', CAST(cs.typeCount AS TEXT),\n                    'typeShares', CAST(cs.typeShares AS TEXT),\n                    'typePercentage', CAST(CASE\n                        WHEN cci.\"totalInstitutions\" > 0 THEN\n                            CAST((cs.typeCount::numeric / cci.\"totalInstitutions\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    'sharesPercentage', CAST(CASE\n                        WHEN cci.\"institutionShares\" > 0 THEN\n                            CAST((cs.typeShares::numeric / cci.\"institutionShares\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    -- 上期数据\n                    'prevTypeCount', CAST(cs.prevTypeCount AS TEXT),\n                    'prevTypeShares', CAST(cs.prevTypeShares AS TEXT),\n                    'prevTypePercentage', CAST(CASE\n                        WHEN pci.\"prevTotalInstitutions\" > 0 THEN\n                            CAST((cs.prevTypeCount::numeric / pci.\"prevTotalInstitutions\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    'prevSharesPercentage', CAST(CASE\n                        WHEN pci.\"prevInstitutionShares\" > 0 THEN\n                            CAST((cs.prevTypeShares::numeric / pci.\"prevInstitutionShares\"::numeric) * 100 AS DECIMAL(10,2))\n                        ELSE 0\n                    END AS TEXT),\n                    -- 变化数据\n                    'typeCountChange', CAST((cs.typeCount - cs.prevTypeCount) AS TEXT),\n                    'typeSharesChange', CAST((cs.typeShares - cs.prevTypeShares) AS TEXT),\n                    'typeCountChangePercent', CAST(CASE\n                        WHEN cs.prevTypeCount > 0 THEN\n                            CAST(((cs.typeCount::numeric - cs.prevTypeCount::numeric) / cs.prevTypeCount::numeric) * 100 AS DECIMAL(10,2))\n                        WHEN cs.typeCount > 0 AND cs.prevTypeCount = 0 THEN 100.00\n                        ELSE 0\n                    END AS TEXT),\n                    'typeSharesChangePercent', CAST(CASE\n                        WHEN cs.prevTypeShares > 0 THEN\n                            CAST(((cs.typeShares::numeric - cs.prevTypeShares::numeric) / cs.prevTypeShares::numeric) * 100 AS DECIMAL(10,2))\n                        WHEN cs.typeShares > 0 AND cs.prevTypeShares = 0 THEN 100.00\n                        ELSE 0\n                    END AS TEXT)\n                )\n            )\n        ELSE NULL\n    END AS \"shareholderTypes\"\n    \nFROM DataValidation dv\nLEFT JOIN CombinedStats cs ON dv.validation_status = 'VALID'\nLEFT JOIN CurrentCompanyInfo cci ON dv.latest_date = cci.\"registerDate\" AND dv.validation_status = 'VALID'\nLEFT JOIN PrevCompanyInfo pci ON dv.prev_date = pci.\"registerDate\" AND dv.validation_status = 'VALID'\nGROUP BY dv.validation_status, dv.validation_message, dv.period_count, dv.latest_date, dv.prev_date;", "options": {}}, "id": "e37250e1-e078-477e-80e6-38d67115cbbd", "name": "股东类型户数和持股分布", "type": "n8n-nodes-base.postgres", "position": [120, -220], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_periods AS (\n  SELECT \n    \"registerDate\",\n    \"totalShares\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) AS rn\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nlatest_period AS (\n  SELECT \"registerDate\", \"totalShares\"\n  FROM latest_periods\n  WHERE rn = 1\n),\nprev_period AS (\n  SELECT \"registerDate\", \"totalShares\"\n  FROM latest_periods\n  WHERE rn = 2\n),\ncurrent_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) AS ranking\n  FROM shareholder s, latest_period lp\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"registerDate\" = lp.\"registerDate\"\n),\nprev_period_data AS (\n  SELECT \n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) AS ranking\n  FROM shareholder s, prev_period pp\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"registerDate\" = pp.\"registerDate\"\n)\n\nSELECT\n  -- 当前期\n  SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top1_shareholding_amount,\n  ROUND(SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2) AS top1_shareholding_ratio,\n  SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top10_shareholding_amount,\n  ROUND(SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2) AS top10_shareholding_ratio,\n  SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END)::numeric AS top100_shareholding_amount,\n  ROUND(SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n    / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2) AS top100_shareholding_ratio,\n\n  -- 上一期\n  ROUND((\n    SELECT SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n    FROM prev_period_data\n  ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2) AS prev_top1_shareholding_ratio,\n\n  ROUND((\n    SELECT SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n    FROM prev_period_data\n  ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2) AS prev_top10_shareholding_ratio,\n\n  ROUND((\n    SELECT SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n    FROM prev_period_data\n  ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2) AS prev_top100_shareholding_ratio,\n\n  -- 差值变化（当前期 - 上一期）\n  ROUND(\n    ROUND(SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n      / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2)\n    -\n    ROUND((\n      SELECT SUM(CASE WHEN ranking <= 1 THEN \"numberOfShares\" ELSE 0 END) \n      FROM prev_period_data\n    ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2)\n  , 2) AS top1_ratio_change,\n\n  ROUND(\n    ROUND(SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n      / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2)\n    -\n    ROUND((\n      SELECT SUM(CASE WHEN ranking <= 10 THEN \"numberOfShares\" ELSE 0 END) \n      FROM prev_period_data\n    ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2)\n  , 2) AS top10_ratio_change,\n\n  ROUND(\n    ROUND(SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n      / NULLIF((SELECT \"totalShares\" FROM latest_period), 0) * 100, 2)\n    -\n    ROUND((\n      SELECT SUM(CASE WHEN ranking <= 100 THEN \"numberOfShares\" ELSE 0 END) \n      FROM prev_period_data\n    ) / NULLIF((SELECT \"totalShares\" FROM prev_period), 0) * 100, 2)\n  , 2) AS top100_ratio_change\n\nFROM current_period_data;\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-100, -440], "id": "fcad5a9d-629f-406f-b48c-8167c22f1066", "name": "新核心持股", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "验证输入参数，组织id必填", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1540, -520], "id": "3ac33cca-b677-4370-9136-ad51ffa26b5b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "验证id是否可在数据库中查询到相关组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1080, -120], "id": "240c986f-5ebd-46f9-a54e-f74a4edac238", "name": "Sticky Note1"}, {"parameters": {"content": "本接口端点：company-info", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2100, -440], "id": "939b19d2-2a67-49d4-b84b-01d496090138", "name": "Sticky Note6"}, {"parameters": {"content": "设置组织id参数，并在数据库中查询对应数据，包括最新一期公司概览数据及上期对比变化数据", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [140, -720], "id": "d5d6c158-d4a6-4b22-a698-a3187e876d39", "name": "Sticky Note2"}, {"parameters": {"content": "合并数据，格式化后返回，返回数据示例如下：\n[\n  {\n    \"success\": true,\n    \"data\": {\n      \"companyName\": \"智微智能\",\n      \"companyCode\": \"001339\",\n      \"registerDate\": \"2024-12-31T00:00:00.000Z\",\n      \"oldestRegisterDate\": \"2024-09-30T00:00:00.000Z\",\n      \"totalShares\": \"*********.00\",\n      \"totalShareholders\": 33556,\n      \"totalInstitutions\": 981,\n      \"institutionShares\": \"8956478.00\",\n      \"individualShareholders\": 32575,\n      \"prevTotalShares\": \"*********.00\",\n      \"prevTotalShareholders\": 40472,\n      \"prevTotalInstitutions\": 1279,\n      \"prevInstitutionShares\": \"********.00\",\n      \"prevIndividualShareholders\": 39193,\n      \"shareholdersChange\": -6916,\n      \"institutionsChange\": -298,\n      \"individualShareholdersChange\": -6618,\n      \"totalSharesChangePercent\": \"0.12\",\n      \"institutionSharesChangePercent\": \"-23.02\",\n      \"creditAccountCount\": \"34\",\n      \"totalMarginShares\": \"2482267.00\",\n      \"prevCreditAccountCount\": \"38\",\n      \"prevTotalMarginShares\": \"3787810.00\",\n      \"creditAccountCountChange\": \"-4\",\n      \"totalMarginSharesChangePercent\": \"-34.47\",\n      \"top10ShareholdingRatio\": \"72.76\",\n      \"top20ShareholdingRatio\": \"73.86\",\n      \"avgSharesPerHolder\": \"7483.90\",\n      \"prevTop10ShareholdingRatio\": \"72.78\",\n      \"prevTop20ShareholdingRatio\": \"73.80\",\n      \"prevAvgSharesPerHolder\": \"6197.85\",\n      \"top10RatioChange\": \"-0.02\",\n      \"top20RatioChange\": \"0.06\",\n      \"avgSharesPerHolderChange\": \"1286.05\",\n      \"top1_shareholding_amount\": \"********.00\",\n      \"top1_shareholding_ratio\": \"39.74\",\n      \"top10_shareholding_amount\": \"*********.00\",\n      \"top10_shareholding_ratio\": \"72.77\",\n      \"top100_shareholding_amount\": \"*********.00\",\n      \"top100_shareholding_ratio\": \"76.86\",\n      \"prevRegisterDate\": \"2024-09-30T00:00:00.000Z\",\n      \"shareholderTypes\": [\n        {\n          \"shareholderType\": \"公募基金\",\n          \"typeCount\": \"1\",\n          \"typeShares\": \"51900.00\",\n          \"typePercentage\": \"0.10\",\n          \"sharesPercentage\": \"0.58\",\n          \"prevTypeCount\": \"0\",\n          \"prevTypeShares\": \"0\",\n          \"prevTypePercentage\": \"0.00\",\n          \"prevSharesPercentage\": \"0.00\",\n          \"typeCountChange\": \"1\",\n          \"typeSharesChange\": \"51900.00\",\n          \"typeCountChangePercent\": \"100.00\",\n          \"typeSharesChangePercent\": \"100.00\"\n        }\n      ]\n    },\n    \"timestamp\": \"2025-07-17T10:45:53.607Z\"\n  }\n]", "height": 1320, "width": 560}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, -1160], "id": "60b1c521-867f-4a09-94d1-119d3f629396", "name": "Sticky Note3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-900, -310], "id": "7b19e03e-ee04-44fb-bab4-06f8a41dab68", "name": "检验组织是否存在数据中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-680, -85], "id": "17132fe8-1d52-45d0-8795-7aa36c735b64", "name": "格式化错误信息"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-240, -860], "id": "ed61af3b-3953-4be5-b361-2b614b480058", "name": "合并数据"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-20, -685], "id": "22947cd3-3c0f-407a-9493-8ebf1be3ba0d", "name": "合并数据1"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [200, -580], "id": "6c4c7762-b931-4289-b406-d8e655f2c0ae", "name": "合并数据2"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [420, -485], "id": "9f4b8ae7-b060-4fe0-8e8c-32b446958734", "name": "合并数据3"}, {"parameters": {"content": "记录时间：\n2025-07-31\n\n相关人员：\n<PERSON>"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2140, -260], "id": "cf753207-a2cc-42f6-8e6d-ea694fb2d6fd", "name": "Sticky Note4"}], "connections": {"获取最新一期公司完整信息及较上期变化": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "获取最新一期信用账户数据及较上期变化": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "获取股东集中度及户均持股数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 1}]]}, "设置组织ID": {"main": [[{"node": "获取最新一期信用账户数据及较上期变化", "type": "main", "index": 0}, {"node": "获取最新一期公司完整信息及较上期变化", "type": "main", "index": 0}, {"node": "获取股东集中度及户均持股数据", "type": "main", "index": 0}, {"node": "股东类型户数和持股分布", "type": "main", "index": 0}, {"node": "新核心持股", "type": "main", "index": 0}]]}, "获取核心持股数据": {"main": [[]]}, "输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "获取公司概览接口响应", "type": "main", "index": 0}]]}, "获取公司概览数据接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "检验组织是否存在数据中", "type": "main", "index": 0}]]}, "股东类型户数和持股分布": {"main": [[{"node": "合并数据3", "type": "main", "index": 1}]]}, "新核心持股": {"main": [[{"node": "合并数据2", "type": "main", "index": 1}]]}, "检验组织是否存在数据中": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "合并数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 0}]]}, "合并数据1": {"main": [[{"node": "合并数据2", "type": "main", "index": 0}]]}, "合并数据2": {"main": [[{"node": "合并数据3", "type": "main", "index": 0}]]}, "合并数据3": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}