{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 所有自然人股东\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  s.\"shareholderType\" ILIKE '%个人%' OR\n  s.\"shareholderType\" ILIKE '%知名牛散%'\n)\n),\n\n-- 为每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 上期前20\nprev_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 所有期完整数据（用于JOIN）\ncurr_all AS (\n  SELECT * FROM ranked_shareholders\n),\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n),\n\n-- 情形1: 上期在前20 + 本期自然人股东持股减少\ncase1 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    prev.\"numberOfShares\" AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    prev.\"registerDate\" AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\n  JOIN curr_all curr ON curr.\"registerDate\" = dp.current_date\n                      AND curr.\"shareholderId\" = prev.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND curr.\"numberOfShares\" < prev.\"numberOfShares\"\n),\n\n-- 情形2: 上期不在前20，本期新进入前20，自然人且持股减少\ncase2 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    COALESCE(prev.\"numberOfShares\", 0) AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    COALESCE(prev.\"shareholdingRatio\", 0) AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    dp.prev_date AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\n  LEFT JOIN prev_all prev \n         ON prev.\"registerDate\" = dp.prev_date \n        AND prev.\"shareholderId\" = curr.\"shareholderId\"\n  LEFT JOIN prev_top20 prev_ranked \n         ON prev_ranked.\"registerDate\" = dp.prev_date \n        AND prev_ranked.\"shareholderId\" = curr.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND prev_ranked.\"shareholderId\" IS NULL -- 上期不在前20\n    AND curr.\"numberOfShares\" < COALESCE(prev.\"numberOfShares\", 0)\n)\n\n-- 汇总输出\nSELECT\n  \"securitiesAccountName\" AS name,\n  \"unifiedAccountNumber\" AS unified_account_number,\n  curr_numberOfShares AS current_numberOfShares,\n  (prev_numberOfShares - curr_numberOfShares) AS decreased_shares,\n  CASE \n    WHEN prev_numberOfShares > 0 \n    THEN ROUND((prev_numberOfShares - curr_numberOfShares) * 1.0 / prev_numberOfShares * 100, 2)\n    ELSE NULL\n  END AS decreased_ratio_percent,\n  curr_shareholdingRatio AS current_shareholdingratio,\n  curr_registerDate AS decreased_date,\n  ROW_NUMBER() OVER (ORDER BY curr_numberOfShares DESC) AS rank,\n  COUNT(*) OVER() AS total\nFROM (\n  SELECT * FROM case1\n  UNION ALL\n  SELECT * FROM case2\n) t\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [920, 460], "id": "f27ceba2-c149-4061-9c59-404bd13ac92f", "name": "获取减持个人股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "f71c917f-1e8c-4311-82af-ab01cdd40150", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1360, 360], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\nshareholder_with_info AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND (\n  s.\"shareholderType\" NOT ILIKE '%个人%' AND\n  s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n)\n),\njoined_periods AS (\n  SELECT\n    dp.current_date,\n    dp.prev_date,\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_shares,\n    curr.\"shareholdingRatio\" AS curr_ratio,\n    prev.\"numberOfShares\" AS prev_shares\n  FROM date_pairs dp\n  JOIN shareholder_with_info curr ON curr.\"registerDate\" = dp.current_date\n  JOIN shareholder_with_info prev \n    ON prev.\"registerDate\" = dp.prev_date \n    AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n),\ndecreased_institutions AS (\n  SELECT\n    \"securitiesAccountName\" AS \"name\",\n    \"unifiedAccountNumber\" AS \"unified_account_number\",\n    ROUND(prev_shares - curr_shares, 2) AS \"decreased_shares\",\n    ROUND(\n      CASE WHEN prev_shares > 0 \n        THEN (prev_shares - curr_shares) / prev_shares * 100\n        ELSE NULL\n      END, 2\n    ) AS \"decreased_ratio_percent\",\n    curr_shares AS \"current_numberofshares\",\n    curr_ratio AS \"current_shareholdingratio\",\n    TO_CHAR(joined_periods.current_date, 'YYYY-MM-DD') AS \"decreased_date\",\n  ROW_NUMBER() OVER (ORDER BY curr_shares DESC) AS rank,\n  COUNT(*) OVER() AS total\n  FROM joined_periods\n  WHERE curr_shares < prev_shares\n)\nSELECT * FROM decreased_institutions\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [920, 680], "id": "2dbe567d-dc00-4af6-9b09-92d1cc6acc52", "name": "获取减持机构股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "fe34e20e-8dc0-4ddc-9812-ea69b4b3df9a", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1380, 620], "typeVersion": 3.4}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const page = inputData?.query?.page || inputData?.body?.page || inputData?.page || 1;\n  const pageSize = inputData?.query?.pageSize || inputData?.body?.pageSize || inputData?.pageSize || 5;\n  const order_base = inputData?.query?.order_base || inputData?.body?.order_base || inputData?.order_base || 'current_numberOfShares';\n  const order = inputData?.query?.order || inputData?.body?.order || inputData?.order || 'Desc';\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 page\n  const parsedPage = parseInt(page, 10);\n  if (isNaN(parsedPage) || parsedPage <= 0) {\n    throw new Error('INVALID_PAGE');\n  }\n\n  // 验证 pageSize\n  const parsedPageSize = parseInt(pageSize, 10);\n  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {\n    throw new Error('INVALID_PAGE_SIZE');\n  }\n\n  return {\n    organizationId: id.trim(),\n    page: parsedPage,\n    pageSize: parsedPageSize,\n    order: order,\n    order_base:order_base\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId, page, pageSize, order,order_base } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      page,\n      pageSize,\n      type: inputData.webhookUrl,\n      order:order,\n      order_base:order_base,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_PAGE': '无效的page参数：必须是大于0的整数。',\n    'INVALID_PAGE_SIZE': '无效的pageSize参数：必须是大于0的整数。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "4b883a2c-7257-4af1-83e9-b0b63f538f18", "name": "输入验证1", "type": "n8n-nodes-base.code", "position": [-500, 640], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "c58edccc-54e3-479e-b230-5af3ef945a21", "name": "检查验证结果1", "type": "n8n-nodes-base.if", "position": [-320, 640], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "7cd49652-c26c-4d4f-a30a-58c0261d2292", "name": "page", "value": "={{ $json.page }}", "type": "string"}, {"id": "c4065c11-a83d-460e-b614-5b0bf60a75de", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "f3f357a6-4c7a-4a01-b988-075fb7aad662", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "62efbef2-3a9f-4e7b-91fc-d3bea08409f4", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "id": "e3c68ae7-da07-46f3-880a-015228cfe991", "name": "设置组织ID1", "type": "n8n-nodes-base.set", "position": [460, 600], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "1a83910b-9009-48bc-9173-75a406255143", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-160, 360], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "3bb62c0a-c94e-4728-aead-f3af7b33ef6a", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [620, 840], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-80, 540], "id": "bf99d788-4501-4ed1-a0f5-de4df26e90e9", "name": "检验输入id1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "此工作流包含两个后端接口，分别为：\n\n个人减持股东：decrease-individual-shareholders-trend\n\n机构减持股东：decrease-institution-shareholders-trend", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1200, 640], "id": "5b52606c-8e27-472b-a0dd-89c8eddbf133", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "输入参数验证，id必填\npage，pagesize，order，order_base选填，这四个函数用于分页查询和排序查询", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-480, 780], "id": "236bce4b-edcb-4797-833b-22be28d2bc2b", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入id是否能查询到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [140, 500], "id": "462af6aa-9719-4f62-838a-b21d893321bf", "name": "Sticky Note2"}, {"parameters": {"content": "根据输入的url判断查询个人还是机构股东", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [640, 480], "id": "3e8f20bc-7af2-403d-9d59-eb60c104d74f", "name": "Sticky Note3"}, {"parameters": {"content": "查询对应类型的减持股东\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, 520], "id": "c91ca211-1e46-4c9d-b8de-671a8f70734b", "name": "Sticky Note4"}, {"parameters": {"content": "格式化查询数据并返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1540, 520], "id": "d5e7b8be-44cb-4818-8549-8899a52b8edd", "name": "Sticky Note5"}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "09af8040-aa5d-4f5d-8217-ab8b71012a7f", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "37175b76-60c6-47a8-81b0-0410624183c2", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 700], "id": "b9f5effe-a631-403f-8c73-d7258a66b5d2", "name": "设置分页参数"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [100, 620], "id": "481ca8ef-f700-4e7b-8ea1-3bffe2e29a2b", "name": "合并数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [260, 620], "id": "f5d8ae37-cfa2-4330-8e88-fc729118a61f", "name": "检验组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 840], "id": "cdb96387-6ca8-4723-bc80-4e7da44e0fc1", "name": "格式化错误信息"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2b1a11d5-5194-4264-9099-d7c2978cf286", "leftValue": "={{ $json.type }}", "rightValue": "individual", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [680, 600], "id": "3880b4f4-a1cb-42d7-9beb-a1a97c666550", "name": "根据url判断进入哪个类型工作流"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  decreaseIndividuals: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, 360], "id": "3132c2c5-ce1c-483d-9add-9c643c3c42a7", "name": "计算数据总量"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  decreaseInstitutions: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 640], "id": "ece242db-0f4d-4520-8270-aec9452146f1", "name": "计算数据总量1"}, {"parameters": {"httpMethod": "POST", "path": "decrease-individual-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-740, 580], "id": "33ca6b5f-2b0b-4121-84da-16a21a47941c", "name": "获取减持个人股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "decrease-institution-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-740, 740], "id": "e6f4618a-c8e3-4148-8e57-68e5c9683aa9", "name": "获取减持机构股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "2ec67bb0-8403-4903-a5b2-7dee357d5d1a", "name": "减持个人股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1600, 360], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "3861f3dd-1b0a-43b1-bc29-b6ad4ef713b8", "name": "减持机构股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1620, 620], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-840, 920], "id": "465c947b-34d0-4a06-bf8e-a8f0c7b6ca54", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"decreaseIndividuals\": [\n\t\t\t\t{\n\t\t\t\t\t\"name\": \"陈义勇\",\n\t\t\t\t\t\"unified_account_number\": \"************\",\n\t\t\t\t\t\"current_numberofshares\": \"920713.00\",\n\t\t\t\t\t\"decreased_shares\": \"150000.00\",\n\t\t\t\t\t\"decreased_ratio_percent\": \"14.01\",\n\t\t\t\t\t\"current_shareholdingratio\": \"0.20\",\n\t\t\t\t\t\"decreased_date\": \"2024-07-31T00:00:00.000Z\",\n\t\t\t\t\t\"rank\": \"1\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"total\": 1\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:30:40.563Z\"\n\t}\n]", "height": 460, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1900, 460], "id": "e340ed7b-fcfc-4add-ae63-dd2486000df6", "name": "Sticky Note8"}], "connections": {"获取减持个人股东": {"main": [[{"node": "计算数据总量", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "减持个人股东接口响应", "type": "main", "index": 0}]]}, "获取减持机构股东": {"main": [[{"node": "计算数据总量1", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "减持机构股东接口响应", "type": "main", "index": 0}]]}, "输入验证1": {"main": [[{"node": "检查验证结果1", "type": "main", "index": 0}]]}, "检查验证结果1": {"main": [[{"node": "错误响应1", "type": "main", "index": 0}], [{"node": "检验输入id1", "type": "main", "index": 0}, {"node": "设置分页参数", "type": "main", "index": 0}]]}, "设置组织ID1": {"main": [[{"node": "根据url判断进入哪个类型工作流", "type": "main", "index": 0}]]}, "检验输入id1": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "设置分页参数": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID1", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "根据url判断进入哪个类型工作流": {"main": [[{"node": "获取减持个人股东", "type": "main", "index": 0}], [{"node": "获取减持机构股东", "type": "main", "index": 0}]]}, "计算数据总量": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "计算数据总量1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "获取减持个人股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}, "获取减持机构股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}