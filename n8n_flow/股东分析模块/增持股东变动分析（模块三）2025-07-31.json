{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 所有自然人股东\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  s.\"shareholderType\" ILIKE '%个人%' OR\n  s.\"shareholderType\" ILIKE '%知名牛散%'\n)\n),\n\n-- 为每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 上期前20\nprev_top20 AS (\n  SELECT * FROM ranked_shareholders\n  WHERE ranking <= 20\n),\n\n-- 所有期完整数据（用于JOIN）\ncurr_all AS (\n  SELECT * FROM ranked_shareholders\n),\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n),\n\n-- 情形1: 上期在前20 + 本期自然人股东持股增加\ncase1 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    prev.\"numberOfShares\" AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    prev.\"registerDate\" AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\n  JOIN curr_all curr ON curr.\"registerDate\" = dp.current_date\n                      AND curr.\"shareholderId\" = prev.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND curr.\"numberOfShares\" > prev.\"numberOfShares\"\n),\n\n-- 情形2: 上期不在前20，本期新进入前20，自然人且持股增加\ncase2 AS (\n  SELECT\n    dp.current_date,\n    curr.\"shareholderId\",\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_numberOfShares,\n    COALESCE(prev.\"numberOfShares\", 0) AS prev_numberOfShares,\n    curr.\"shareholdingRatio\" AS curr_shareholdingRatio,\n    COALESCE(prev.\"shareholdingRatio\", 0) AS prev_shareholdingRatio,\n    curr.\"registerDate\" AS curr_registerDate,\n    dp.prev_date AS prev_registerDate,\n    curr.\"shareholderCategory\"\n  FROM date_pairs dp\n  JOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\n  LEFT JOIN prev_all prev \n         ON prev.\"registerDate\" = dp.prev_date \n        AND prev.\"shareholderId\" = curr.\"shareholderId\"\n  LEFT JOIN prev_top20 prev_ranked \n         ON prev_ranked.\"registerDate\" = dp.prev_date \n        AND prev_ranked.\"shareholderId\" = curr.\"shareholderId\"\n  WHERE dp.prev_date IS NOT NULL\n    AND prev_ranked.\"shareholderId\" IS NULL -- 上期不在前20\n    AND curr.\"numberOfShares\" > COALESCE(prev.\"numberOfShares\", 0)\n)\n\n-- 汇总输出\nSELECT\n  \"securitiesAccountName\" AS name,\n  \"unifiedAccountNumber\" AS unified_account_number,\n  curr_numberOfShares AS current_numberOfShares,\n  (curr_numberOfShares - prev_numberOfShares) AS increased_shares,\n  CASE \n    WHEN prev_numberOfShares > 0 \n    THEN ROUND((curr_numberOfShares - prev_numberOfShares) * 1.0 / prev_numberOfShares * 100, 2)\n    ELSE NULL\n  END AS increased_ratio_percent,\n  curr_shareholdingRatio AS current_shareholdingRatio,\n  curr_registerDate AS increased_date,\n  ROW_NUMBER() OVER (ORDER BY curr_numberOfShares DESC) AS rank,\n  COUNT(*) OVER() AS total\nFROM (\n  SELECT * FROM case1\n  UNION ALL\n  SELECT * FROM case2\n) t\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "e34ad5f1-dfe9-45d6-8081-296b18123796", "name": "获取增持个人股东", "type": "n8n-nodes-base.postgres", "position": [640, -20], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\nshareholder_with_info AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json.organizationId }}'\n    AND (\n  s.\"shareholderType\" NOT ILIKE '%个人%' AND\n  s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n)\n),\njoined_periods AS (\n  SELECT\n    dp.current_date,\n    dp.prev_date,\n    curr.\"unifiedAccountNumber\",\n    curr.\"securitiesAccountName\",\n    curr.\"numberOfShares\" AS curr_shares,\n    curr.\"shareholdingRatio\" AS curr_ratio,\n    prev.\"numberOfShares\" AS prev_shares\n  FROM date_pairs dp\n  JOIN shareholder_with_info curr ON curr.\"registerDate\" = dp.current_date\n  JOIN shareholder_with_info prev \n    ON prev.\"registerDate\" = dp.prev_date \n    AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\n),\nincreased_institutions AS (\n  SELECT\n    \"securitiesAccountName\" AS \"name\",\n    \"unifiedAccountNumber\" AS unified_account_number,\n    ROUND(curr_shares - prev_shares, 2) AS \"increased_shares\",\n    ROUND(\n      CASE WHEN prev_shares > 0 \n        THEN (curr_shares - prev_shares) / prev_shares * 100\n        ELSE NULL\n      END, 2\n    ) AS \"increased_ratio_percent\",\n    curr_shares AS \"current_numberofshares\",\n    curr_ratio AS current_shareholdingRatio,\n    TO_CHAR(joined_periods.current_date, 'YYYY-MM-DD') AS \"increased_date\",\n    ROW_NUMBER() OVER (ORDER BY curr_shares DESC) AS rank,\n    COUNT(*) OVER() AS total\n  FROM joined_periods\n  WHERE curr_shares > prev_shares\n)\nSELECT * FROM increased_institutions\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};", "options": {}}, "id": "ad603c66-c9eb-4f54-b7dc-5f1678295198", "name": "获取增持机构股东", "type": "n8n-nodes-base.postgres", "position": [640, 180], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const page = inputData?.query?.page || inputData?.body?.page || inputData?.page || 1;\n  const pageSize = inputData?.query?.pageSize || inputData?.body?.pageSize || inputData?.pageSize || 5;\n  const order_base = inputData?.query?.order_base || inputData?.body?.order_base || inputData?.order_base || 'current_numberOfShares';\n  const order = inputData?.query?.order || inputData?.body?.order || inputData?.order || 'Desc';\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 page\n  const parsedPage = parseInt(page, 10);\n  if (isNaN(parsedPage) || parsedPage <= 0) {\n    throw new Error('INVALID_PAGE');\n  }\n\n  // 验证 pageSize\n  const parsedPageSize = parseInt(pageSize, 10);\n  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {\n    throw new Error('INVALID_PAGE_SIZE');\n  }\n\n  return {\n    organizationId: id.trim(),\n    page: parsedPage,\n    pageSize: parsedPageSize,\n    order: order,\n    order_base:order_base\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId, page, pageSize, order,order_base } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      page,\n      pageSize,\n      type: inputData.webhookUrl,\n      order:order,\n      order_base:order_base,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_PAGE': '无效的page参数：必须是大于0的整数。',\n    'INVALID_PAGE_SIZE': '无效的pageSize参数：必须是大于0的整数。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "7c2f44de-ee58-404c-b359-b7912aacff2a", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-720, 240], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "3861a21b-97d9-46e3-8284-3582b7c5cdd3", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-540, 220], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "7cd49652-c26c-4d4f-a30a-58c0261d2292", "name": "page", "value": "={{ $json.page }}", "type": "string"}, {"id": "c4065c11-a83d-460e-b614-5b0bf60a75de", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "f3f357a6-4c7a-4a01-b988-075fb7aad662", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "62efbef2-3a9f-4e7b-91fc-d3bea08409f4", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "id": "8d4ebf14-ced2-4c38-abe0-29f704cd4b1e", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [240, 180], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "d47f72ff-a98d-43c5-8a7e-597eb633cfa9", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1080, -60], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "efdd2a3e-6c22-4b5d-9355-30e30c7cd603", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-380, -60], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "82f90d08-9271-42b7-982f-f48d9f568213", "name": "数据库错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [400, 420], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-300, 120], "id": "87ec22ca-ecb4-4c04-a3be-5f8eb9e2ae3f", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "8c5de717-803c-4dc6-90eb-60229fde93a7", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1100, 200], "typeVersion": 3.4}, {"parameters": {"content": "此工作流包含两个后端接口，分别为：\n\n个人增持股东：increase-individual-shareholders-trend\n\n机构增持股东：increase-institution-shareholders-trend", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1440, 240], "id": "1ee4d850-db8a-4919-a216-b37bae12f85a", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "输入参数验证，id必填\npage，pagesize，order，order_base选填，这四个函数用于分页查询和排序查询", "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-720, 380], "id": "598495d5-5784-48d9-8228-6a67015b6741", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入id是否能查询到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-100, 100], "id": "98e60ffb-0c4e-494e-a44b-eee07f0f2c0e", "name": "Sticky Note2"}, {"parameters": {"content": "根据输入的url判断查询个人还是机构股东", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [300, 60], "id": "62930b9d-ad79-4009-999c-d5996b808561", "name": "Sticky Note3"}, {"parameters": {"content": "查询对应类型的增持股东\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [780, 120], "id": "69e57e8b-3711-4742-9f51-9080fad8b06f", "name": "Sticky Note4"}, {"parameters": {"content": "格式化查询数据并返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1180, 100], "id": "a1449ba4-7bee-40f9-88aa-dae8574f4d06", "name": "Sticky Note5"}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "09af8040-aa5d-4f5d-8217-ab8b71012a7f", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "37175b76-60c6-47a8-81b0-0410624183c2", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-300, 280], "id": "ef25380e-240d-4e5b-9dcd-729b78872d04", "name": "设置分页参数"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-120, 200], "id": "34a50cfa-cac1-4e92-b605-35b84a772192", "name": "合并数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [40, 200], "id": "82128458-25e0-464f-99a6-6cfa5b6f73ca", "name": "判断组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [220, 420], "id": "3ba2be89-b03e-4999-bdc1-583973c42eb2", "name": "格式化错误信息"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2b1a11d5-5194-4264-9099-d7c2978cf286", "leftValue": "={{ $json.type }}", "rightValue": "individual", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [460, 180], "id": "9946e6e9-340d-4586-a0f5-97606cb7eb73", "name": "根据url判断进入工作流类型"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  increaseIndividuals: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [820, -60], "id": "de262647-93fc-430e-b87a-26b77f8d1eb8", "name": "计算数据总量"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  increaseInstitutions: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [840, 220], "id": "eb5e8de4-dbb7-45f7-a155-afb44269f8d3", "name": "计算数据总量1"}, {"parameters": {"httpMethod": "POST", "path": "increase-individual-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-980, 160], "id": "aa96c5b9-4cff-4c1c-a0f4-b08729d031cc", "name": "获取增持个人股东接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "increase-institution-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-980, 320], "id": "9861ac14-6ded-4792-998d-6b09b181d2e1", "name": "获取增持机构股东接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "394fc06e-09fc-4c1c-956d-03fa485e07e8", "name": "增持个人股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1320, -60], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "a93d97c6-e492-4922-9778-029e506fd191", "name": "增持机构股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1340, 200], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1160, 500], "id": "a21484be-9e20-47d2-b461-ab3613814de6", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"increaseIndividuals\": [\n\t\t\t\t{\n\t\t\t\t\t\"name\": \"陈丹娜\",\n\t\t\t\t\t\"unified_account_number\": \"************\",\n\t\t\t\t\t\"current_numberofshares\": \"9082800.00\",\n\t\t\t\t\t\"increased_shares\": \"9082800.00\",\n\t\t\t\t\t\"increased_ratio_percent\": null,\n\t\t\t\t\t\"current_shareholdingratio\": \"2.00\",\n\t\t\t\t\t\"increased_date\": \"2024-07-31T00:00:00.000Z\",\n\t\t\t\t\t\"rank\": \"1\"\n\t\t\t\t}\n\t\t\t\"total\": 1\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:31:43.227Z\"\n\t}\n]", "height": 460, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1580, 40], "id": "57b717f1-25d3-4382-bc34-75aba7f81f36", "name": "Sticky Note8"}], "connections": {"获取增持个人股东": {"main": [[{"node": "计算数据总量", "type": "main", "index": 0}]]}, "获取增持机构股东": {"main": [[{"node": "计算数据总量1", "type": "main", "index": 0}]]}, "输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}, {"node": "设置分页参数", "type": "main", "index": 0}]]}, "设置组织ID": {"main": [[{"node": "根据url判断进入工作流类型", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "增持个人股东接口响应", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "增持机构股东接口响应", "type": "main", "index": 0}]]}, "设置分页参数": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "判断组织是否存在数据库中", "type": "main", "index": 0}]]}, "判断组织是否存在数据库中": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应", "type": "main", "index": 0}]]}, "根据url判断进入工作流类型": {"main": [[{"node": "获取增持个人股东", "type": "main", "index": 0}], [{"node": "获取增持机构股东", "type": "main", "index": 0}]]}, "计算数据总量": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "计算数据总量1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "获取增持个人股东接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "获取增持机构股东接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}