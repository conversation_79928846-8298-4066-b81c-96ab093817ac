{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 自然人股东全集\nshareholder_base AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"shareholderCategory\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"organizationId\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  s.\"shareholderType\" ILIKE '%个人%' OR\n  s.\"shareholderType\" ILIKE '%知名牛散%'\n)\n),\n\n-- 每期自然人股东排名\nranked_shareholders AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder_base\n),\n\n-- 本期前20\ncurr_top20 AS (\n  SELECT * FROM ranked_shareholders WHERE ranking <= 20\n),\n\n-- 上一期所有自然人股东\nprev_all AS (\n  SELECT * FROM ranked_shareholders\n)\n\n-- 最终筛选：本期进前20，且上期为0或无记录\nSELECT\n  curr.\"securitiesAccountName\" AS name,\n  curr.\"unifiedAccountNumber\" AS \"unified_account_number\",\n  curr.\"numberOfShares\" AS \"number_of_shares\",\n  curr.\"shareholdingRatio\" AS \"shareholding_ratio\",\n  curr.\"registerDate\" AS \"register_date\",\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN curr_top20 curr ON curr.\"registerDate\" = dp.current_date\nLEFT JOIN prev_all prev \n       ON prev.\"registerDate\" = dp.prev_date\n      AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (prev.\"unifiedAccountNumber\" IS NULL OR prev.\"numberOfShares\" = 0)\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "36ad72a1-800a-4e05-b374-df4bb8fb787b", "name": "获取新进个人股东", "type": "n8n-nodes-base.postgres", "position": [780, 560], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 当前期所有机构股东\ncurr_institution AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"securitiesAccountName\",\n    s.\"numberOfShares\",\n    s.\"shareholdingRatio\",\n    s.\"shareholderCategory\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  s.\"shareholderType\" NOT ILIKE '%个人%' AND\n  s.\"shareholderType\" NOT ILIKE '%知名牛散%'\n)\n),\n\n-- 上一期所有机构股东\nprev_institution AS (\n  SELECT\n    s.\"registerDate\",\n    s.\"unifiedAccountNumber\",\n    s.\"numberOfShares\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"shareholderType\" NOT ILIKE '%个人%'\n)\n\n-- 筛选新进机构\nSELECT\n  curr.\"securitiesAccountName\" AS name,\n  curr.\"unifiedAccountNumber\" AS \"unified_account_number\",\n  curr.\"numberOfShares\" AS \"number_of_shares\",\n  curr.\"shareholdingRatio\" AS \"shareholding_ratio\",\n  curr.\"registerDate\" AS \"register_date\",\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN curr_institution curr ON curr.\"registerDate\" = dp.current_date\nLEFT JOIN prev_institution prev \n       ON prev.\"registerDate\" = dp.prev_date \n      AND prev.\"unifiedAccountNumber\" = curr.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (prev.\"unifiedAccountNumber\" IS NULL OR prev.\"numberOfShares\" = 0)\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "5cfbb3a2-956c-414b-93be-76d70e1a83fb", "name": "获取新进机构股东", "type": "n8n-nodes-base.postgres", "position": [780, 840], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "613396ed-0346-4fdd-b252-05efd2e4c0f6", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1220, 580], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "437b1fe7-e0d0-4858-856f-6183fea1bfc1", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1240, 840], "typeVersion": 3.4}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const page = inputData?.query?.page || inputData?.body?.page || inputData?.page || 1;\n  const pageSize = inputData?.query?.pageSize || inputData?.body?.pageSize || inputData?.pageSize || 5;\n  const order_base = inputData?.query?.order_base || inputData?.body?.order_base || inputData?.order_base || 'number_of_shares';\n  const order = inputData?.query?.order || inputData?.body?.order || inputData?.order || 'Desc';\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 page\n  const parsedPage = parseInt(page, 10);\n  if (isNaN(parsedPage) || parsedPage <= 0) {\n    throw new Error('INVALID_PAGE');\n  }\n\n  // 验证 pageSize\n  const parsedPageSize = parseInt(pageSize, 10);\n  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {\n    throw new Error('INVALID_PAGE_SIZE');\n  }\n\n  return {\n    organizationId: id.trim(),\n    page: parsedPage,\n    pageSize: parsedPageSize,\n    order: order,\n    order_base:order_base\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId, page, pageSize, order,order_base } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      page,\n      pageSize,\n      type: inputData.webhookUrl,\n      order:order,\n      order_base:order_base,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_PAGE': '无效的page参数：必须是大于0的整数。',\n    'INVALID_PAGE_SIZE': '无效的pageSize参数：必须是大于0的整数。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "e1642bd2-55c6-448a-bb2c-2b2344e6d363", "name": "输入验证1", "type": "n8n-nodes-base.code", "position": [-600, 720], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "44aad280-1bb5-4d5c-a0f6-820c7fbd4a63", "name": "检查验证结果1", "type": "n8n-nodes-base.if", "position": [-420, 720], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "7cd49652-c26c-4d4f-a30a-58c0261d2292", "name": "page", "value": "={{ $json.page }}", "type": "string"}, {"id": "c4065c11-a83d-460e-b614-5b0bf60a75de", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "f3f357a6-4c7a-4a01-b988-075fb7aad662", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "62efbef2-3a9f-4e7b-91fc-d3bea08409f4", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "id": "118b4f96-1a01-46f2-a3c6-3262ead8fac6", "name": "设置组织ID1", "type": "n8n-nodes-base.set", "position": [360, 680], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "d05a1a8a-4b4f-4456-900e-213f9dd51ac7", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-260, 440], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "446e622a-847f-4ef8-bad5-a54fb28c83d1", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [520, 920], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-180, 620], "id": "fe493f6f-63c0-4a47-b908-a72e1328f6de", "name": "检验输入id1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "此工作流包含两个后端接口，分别为：\n\n个人新进股东：new-individual-shareholders-trend\n\n机构新进股东：new-institution-shareholders-trend", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1340, 720], "id": "c053e0b1-fa8e-42eb-9f5c-7c4ad3c0f95e", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "输入参数验证，id必填\npage，pagesize，order，order_base选填，这四个函数用于分页查询和排序查询", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-620, 860], "id": "520be075-86e3-423e-be25-47d5e6fbe120", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入id是否能查询到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 580], "id": "d5f00442-1be6-4fa1-8000-00c2b61eeaff", "name": "Sticky Note2"}, {"parameters": {"content": "根据输入的url判断查询个人还是机构股东", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [500, 560], "id": "4f64120c-49f3-4142-890b-90243ba13596", "name": "Sticky Note3"}, {"parameters": {"content": "查询对应类型的新进股东\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [880, 720], "id": "c017865d-45b9-4805-bde8-94e7ef1081d8", "name": "Sticky Note4"}, {"parameters": {"content": "格式化查询数据并返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1400, 720], "id": "199b72d9-14a9-4057-90f8-6860c58d0742", "name": "Sticky Note5"}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "09af8040-aa5d-4f5d-8217-ab8b71012a7f", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "37175b76-60c6-47a8-81b0-0410624183c2", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-180, 780], "id": "1405eef9-ebd7-4c66-a3f9-183ff748f805", "name": "设置分页参数"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [0, 700], "id": "b4adb6d6-b8d5-4fd0-9a83-17a939b1ec9e", "name": "合并数据"}, {"parameters": {"httpMethod": "POST", "path": "new-individual-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-860, 640], "id": "bbfa2a37-8c56-4d65-b511-022ac26ff8b1", "name": "获取新进个人股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "new-institution-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-860, 800], "id": "6496e707-3afc-4eaa-84e6-273bf5ab9d6a", "name": "获取新进机构股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [160, 700], "id": "01dcb302-ff3e-4867-912f-26f18151b0ad", "name": "检验组织是否存在数据库中"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2b1a11d5-5194-4264-9099-d7c2978cf286", "leftValue": "={{ $json.type }}", "rightValue": "individual", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [580, 680], "id": "e47eafab-fa5c-4155-bafc-b02200623475", "name": "根据url判断进入哪个工作流"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [340, 920], "id": "708303c4-9b80-4b47-b55d-3448f151b95e", "name": "格式化错误信息"}, {"parameters": {"jsCode": "\nconst total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  newIndividuals: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 560], "id": "05f9f5b9-8be7-4556-bf78-9e97d67f0ef0", "name": "计算数据总量"}, {"parameters": {"jsCode": "\n\nconst total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  newInstitutions: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [980, 840], "id": "1ff82025-1bdb-4112-a463-48152b2f43d5", "name": "计算数据总量1"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "e07e4e5f-339a-4a8b-8b67-33c15ddd1ee4", "name": "新进个人股东响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1460, 580], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "80f630dd-4a49-435e-ba54-f0fb858eb000", "name": "新进机构股东响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1480, 840], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1280, 920], "id": "9cae61dd-4bbe-4803-acf9-cd23cca9aab0", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"newInstitutions\": [\n\t\t\t\t{\n\t\t\t\t\t\"name\": \"工银瑞信添颐混合型养老金产品－中国工商银行股份有限公司\",\n\t\t\t\t\t\"unified_account_number\": \"************\",\n\t\t\t\t\t\"number_of_shares\": \"847558.00\",\n\t\t\t\t\t\"shareholding_ratio\": \"0.19\",\n\t\t\t\t\t\"register_date\": \"2024-07-31T00:00:00.000Z\"\n\t\t\t\t}\n\t\t\t\"total\": 1\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:35:01.703Z\"\n\t}\n]", "height": 460, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1720, 520], "id": "f744e634-0652-4246-86f3-8ed865a04e3f", "name": "Sticky Note8"}], "connections": {"获取新进个人股东": {"main": [[{"node": "计算数据总量", "type": "main", "index": 0}]]}, "获取新进机构股东": {"main": [[{"node": "计算数据总量1", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "新进个人股东响应", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "新进机构股东响应", "type": "main", "index": 0}]]}, "输入验证1": {"main": [[{"node": "检查验证结果1", "type": "main", "index": 0}]]}, "检查验证结果1": {"main": [[{"node": "错误响应1", "type": "main", "index": 0}], [{"node": "检验输入id1", "type": "main", "index": 0}, {"node": "设置分页参数", "type": "main", "index": 0}]]}, "设置组织ID1": {"main": [[{"node": "根据url判断进入哪个工作流", "type": "main", "index": 0}]]}, "检验输入id1": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "设置分页参数": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "获取新进个人股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}, "获取新进机构股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID1", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "根据url判断进入哪个工作流": {"main": [[{"node": "获取新进个人股东", "type": "main", "index": 0}], [{"node": "获取新进机构股东", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "计算数据总量": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "计算数据总量1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}