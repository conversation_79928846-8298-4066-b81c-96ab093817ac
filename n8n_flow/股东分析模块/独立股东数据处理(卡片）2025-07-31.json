{"nodes": [{"parameters": {"httpMethod": "POST", "path": "single-shareholder-analysis", "responseMode": "responseNode", "options": {}}, "id": "a5cc4531-232d-47de-81fa-af883424166a", "name": "独立股东分析接口", "type": "n8n-nodes-base.webhook", "position": [-1820, 480], "typeVersion": 2, "webhookId": "shareholder-analysis-webhook"}, {"parameters": {"jsCode": "// 输入验证函数 - 检查organizationId和account参数\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const account = inputData?.query?.account || inputData?.body?.account || inputData?.account;\n  \n  // 验证organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  \n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  \n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n  \n  // 验证account参数\n  if (!account) {\n    throw new Error('MISSING_ACCOUNT');\n  }\n  \n  if (typeof account !== 'string' || account.trim() === '') {\n    throw new Error('INVALID_ACCOUNT');\n  }\n  \n  if (account.length < 1 || account.length > 100) {\n    throw new Error('ACCOUNT_LENGTH_INVALID');\n  }\n  \n  return {\n    organizationId: id.trim(),\n    account: account.trim()\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const validatedData = validateInput(inputData);\n  \n  return [{\n    json: {\n      organizationId: validatedData.organizationId,\n      account: validatedData.account,\n      type: inputData.webhookUrl,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  // 返回验证错误信息\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'MISSING_ACCOUNT': '缺少必需参数：account。请在查询参数、请求体或URL路径中提供account参数。',\n    'INVALID_ACCOUNT': '无效的account：参数必须是非空字符串。',\n    'ACCOUNT_LENGTH_INVALID': '无效的account长度：参数长度必须在1-100个字符之间。'\n  };\n  return messages[code] || '输入验证失败';\n}"}, "id": "13bec621-1d3c-49a2-a243-1d5db5c7f85b", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-1600, 480], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "0022409c-bde0-46d7-a1a0-6090a20b6696", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-1380, 480], "typeVersion": 2}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 400}}, "id": "d6c13e12-32d8-4b64-869c-6b5b80464e92", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-1160, 380], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1160, 580], "id": "0dc01e97-9169-44eb-b899-dcd437fd84e8", "name": "验证组织ID", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "has-data", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-940, 580], "id": "6aa5ba64-da22-406b-a659-3a7394ddb98a", "name": "检查组织是否存在"}, {"parameters": {"jsCode": "return [{\n  json: {\n    error: {\n      code: 'ORGANIZATION_NOT_FOUND',\n      message: '未找到指定的组织，请检查organizationId是否正确。',\n      timestamp: new Date().toISOString(),\n      type: 'DATA_NOT_FOUND'\n    }\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-720, 800], "id": "bdce6ed4-e825-4754-9918-955fa903eeb5", "name": "组织不存在错误"}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 404}}, "id": "f78394b5-692d-4fd2-abdb-3fe54ab3072b", "name": "组织不存在响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-500, 800], "typeVersion": 1.1}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "account-assignment", "name": "account", "value": "={{ $('检查验证结果').item.json.account }}", "type": "string"}]}, "options": {}}, "id": "4e624f2c-7f1b-4a22-9a48-6f96e9aa208e", "name": "设置参数", "type": "n8n-nodes-base.set", "position": [-720, 300], "typeVersion": 3.4}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 找到股东存在的最新一期\nWITH target_latest_register_date AS (\n  SELECT \n    s.\"registerDate\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"unifiedAccountNumber\" = '{{ $json[\"account\"] }}'\n  ORDER BY s.\"registerDate\" DESC\n  LIMIT 1\n),\nlatest_period AS (\n  SELECT \n    ci.\"registerDate\",\n    ci.\"totalShares\"\n  FROM company_info ci\n  JOIN target_latest_register_date t ON ci.\"registerDate\" = t.\"registerDate\"\n  WHERE ci.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nlatest_company_period AS(\n   SELECT \n    s.\"registerDate\"\n  FROM shareholder s\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY s.\"registerDate\" DESC\n  LIMIT 1\n),\nranked_shareholders AS (\n  SELECT \n    s.\"securitiesAccountName\",\n    s.\"shareholderId\",\n    s.\"unifiedAccountNumber\",\n    s.\"contactNumber\",\n    s.\"shareholderType\",\n    s.\"shareholderCategory\",\n    s.\"contactAddress\",\n    s.\"cashAccount\",\n    s.\"sharesInCashAccount\",\n    s.\"marginAccount\",\n    s.\"sharesInMarginAccount\",\n    s.\"lockedUpShares\",\n    s.\"frozenShares\",\n    lp.\"totalShares\",\n    lp.\"registerDate\",\n    lcp.\"registerDate\" AS \"latestDate\",\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) AS ranking\n  FROM shareholder s, latest_period lp, latest_company_period lcp\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"registerDate\" = lp.\"registerDate\"\n)\n\nSELECT *\nFROM ranked_shareholders\nWHERE \"unifiedAccountNumber\" = '{{ $json[\"account\"] }}'\n\n", "options": {}}, "id": "dab51d9f-306c-4b05-9fc9-e03b7fd8a525", "name": "获取最新期股东数据及排名", "type": "n8n-nodes-base.postgres", "position": [-500, -80], "typeVersion": 2.6, "alwaysOutputData": false, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 查询2：获取股东历史持股数据\nWITH all_periods AS (\n  SELECT DISTINCT \n    \"registerDate\"\n  FROM shareholder \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  ORDER BY \"registerDate\" DESC\n),\nshareholder_history AS (\n  SELECT \n    s.\"registerDate\",\n    s.\"numberOfShares\"::numeric,\n    s.\"shareholdingRatio\",\n    ci.\"totalShares\"\n  FROM shareholder s\n  JOIN company_info ci ON s.\"organizationId\" = ci.\"organizationId\" \n    AND s.\"registerDate\" = ci.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"unifiedAccountNumber\" = '{{ $json[\"account\"] }}'\n  ORDER BY s.\"registerDate\" DESC\n)\nSELECT \n  \"registerDate\",\n  \"numberOfShares\",\n  \"shareholdingRatio\",\n  \"totalShares\"\nFROM shareholder_history\nORDER BY \"registerDate\" DESC;", "options": {}}, "id": "99feeb73-2a96-4732-969b-1bafedcc8abb", "name": "获取股东历史持股数据", "type": "n8n-nodes-base.postgres", "position": [-480, 340], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_periods AS (\n  SELECT \n    \"registerDate\",\n    ROW_NUMBER() OVER (ORDER BY \"registerDate\" DESC) AS rn\n  FROM company_info \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nperiod_dates AS (\n  SELECT \n    MAX(CASE WHEN rn = 1 THEN \"registerDate\" END) AS current_date,\n    MAX(CASE WHEN rn = 2 THEN \"registerDate\" END) AS prev_date\n  FROM latest_periods\n  WHERE rn <= 2\n),\n-- 当前期数据\ncurrent_period_data AS (\n  SELECT \n    s.\"numberOfShares\"::numeric AS current_shares,\n    s.\"shareholdingRatio\" AS current_ratio,\n    ci.\"totalShares\" AS current_total_shares\n  FROM shareholder s\n  JOIN company_info ci ON s.\"organizationId\" = ci.\"organizationId\"\n    AND s.\"registerDate\" = ci.\"registerDate\"\n  JOIN period_dates pd ON s.\"registerDate\" = pd.current_date\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND s.\"unifiedAccountNumber\" = '{{ $json[\"account\"] }}'\n),\n-- 上期所有股东排名\nprev_ranked_data AS (\n  SELECT \n    s.\"unifiedAccountNumber\",\n    s.\"numberOfShares\"::numeric AS prev_shares,\n    s.\"shareholdingRatio\" AS prev_ratio,\n    ci.\"totalShares\" AS prev_total_shares,\n    ROW_NUMBER() OVER (ORDER BY s.\"numberOfShares\"::numeric DESC) AS prev_ranking\n  FROM shareholder s\n  JOIN company_info ci ON s.\"organizationId\" = ci.\"organizationId\"\n    AND s.\"registerDate\" = ci.\"registerDate\"\n  JOIN period_dates pd ON s.\"registerDate\" = pd.prev_date\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\n-- 只取该账户的上期数据 + 排名\ntarget_prev_data AS (\n  SELECT * FROM prev_ranked_data\n  WHERE \"unifiedAccountNumber\" = '{{ $json[\"account\"] }}'\n)\n\nSELECT \n  -- 当前期数据\n  COALESCE(cpd.current_shares, 0) AS \"currentShares\",\n  COALESCE(cpd.current_ratio, 0) AS \"currentRatio\",\n\n  tpd.prev_ranking AS \"prevRanking\",\n  tpd.prev_shares AS \"prevShares\",\n\n  -- 变化\n  (COALESCE(cpd.current_shares, 0) - COALESCE(tpd.prev_shares, 0)) AS \"sharesChange\",\n  ROUND((COALESCE(cpd.current_ratio, 0) - COALESCE(tpd.prev_ratio, 0))::numeric, 4) AS \"ratioChange\"\n  \nFROM period_dates pd\nLEFT JOIN current_period_data cpd ON 1=1\nLEFT JOIN target_prev_data tpd ON 1=1;\n", "options": {}}, "id": "a526d57f-e042-4823-b37c-ce378f1a5c7b", "name": "最新两期对比分析", "type": "n8n-nodes-base.postgres", "position": [-500, 100], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-220, 140], "id": "7c851729-5c40-423d-9e73-8ac0a74038c4", "name": "合并结果"}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "e6e4e126-1ab8-4d5f-9cec-81503765d4d6", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [160, 200], "typeVersion": 3.4}, {"parameters": {"content": "合并所有数据，并格式化返回结果", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [160, 80], "id": "bac8b412-f55c-40c8-a44e-78870530aa13", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "本接口端点：single-shareholder-analysis", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2220, 480], "id": "2c0ad087-05ea-4c72-826c-808c45be0ba5", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入的参数中是否包含id和account两个参数，并检验格式是否正确", "height": 80, "width": 280}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1580, 360], "id": "47222c3c-b18a-476b-8089-928c2b2d7f8a", "name": "Sticky Note2"}, {"parameters": {"content": "在数据库中根据组织id查找，检验输入id对应的组织是否在数据库中存在", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1160, 740], "id": "9bfd9757-e94d-4e06-b6b3-07b483bd2e69", "name": "Sticky Note3"}, {"parameters": {"content": "设置一码通和组织id参数，通过三个数据库节点分别查询", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-820, 0], "id": "523b5db9-be32-4254-a4c7-9c6f0d816be6", "name": "Sticky Note4"}, {"parameters": {"jsCode": "return [{\n  trendData: $input.all().map(item => item.json)\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-280, 340], "id": "644d1ffa-ba36-405b-9287-8a4502e44fed", "name": "包装数组为单一json对象"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-40, 200], "id": "8d99a93d-8382-4169-9f39-536251dd7b07", "name": "合并数据"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "460b5a7c-5b55-4698-8ee6-c80b83228b3b", "name": "独立股东分析接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [380, 200], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2220, 660], "id": "50707cf1-e84f-4bb6-b875-10f195743f95", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"securitiesAccountName\": \"工银瑞信添颐混合型养老金产品－中国工商银行股份有限公司\",\n\t\t\t\"shareholderId\": \"99PF20140117\",\n\t\t\t\"unifiedAccountNumber\": \"************\",\n\t\t\t\"contactNumber\": \"010-********\",\n\t\t\t\"shareholderType\": \"其他机构\",\n\t\t\t\"shareholderCategory\": \"基金、理财产品等(06)\",\n\t\t\t\"contactAddress\": \"北京市西城区北京银行大厦7层\",\n\t\t\t\"cashAccount\": \"**********\",\n\t\t\t\"sharesInCashAccount\": \"847558.00\",\n\t\t\t\"marginAccount\": \"\",\n\t\t\t\"sharesInMarginAccount\": \"0.00\",\n\t\t\t\"lockedUpShares\": \"0.00\",\n\t\t\t\"frozenShares\": \"0.00\",\n\t\t\t\"totalShares\": \"*********.00\",\n\t\t\t\"registerDate\": \"2024-07-31T00:00:00.000Z\",\n\t\t\t\"latestDate\": \"2024-07-31T00:00:00.000Z\",\n\t\t\t\"ranking\": \"28\",\n\t\t\t\"currentShares\": \"847558.00\",\n\t\t\t\"currentRatio\": \"0.19\",\n\t\t\t\"prevRanking\": null,\n\t\t\t\"prevShares\": null,\n\t\t\t\"sharesChange\": \"847558.00\",\n\t\t\t\"ratioChange\": \"0.1900\",\n\t\t\t\"trendData\": [\n\t\t\t\t{\n\t\t\t\t\t\"registerDate\": \"2024-07-31T00:00:00.000Z\",\n\t\t\t\t\t\"numberOfShares\": \"847558.00\",\n\t\t\t\t\t\"shareholdingRatio\": \"0.19\",\n\t\t\t\t\t\"totalShares\": \"*********.00\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:16:36.958Z\"\n\t}\n]", "height": 780, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [660, -80], "id": "********-0915-4f82-a8b2-6907996b316c", "name": "Sticky Note5"}], "connections": {"独立股东分析接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "验证组织ID", "type": "main", "index": 0}]]}, "验证组织ID": {"main": [[{"node": "检查组织是否存在", "type": "main", "index": 0}]]}, "检查组织是否存在": {"main": [[{"node": "设置参数", "type": "main", "index": 0}], [{"node": "组织不存在错误", "type": "main", "index": 0}]]}, "组织不存在错误": {"main": [[{"node": "组织不存在响应", "type": "main", "index": 0}]]}, "设置参数": {"main": [[{"node": "获取最新期股东数据及排名", "type": "main", "index": 0}, {"node": "获取股东历史持股数据", "type": "main", "index": 0}, {"node": "最新两期对比分析", "type": "main", "index": 0}]]}, "获取最新期股东数据及排名": {"main": [[{"node": "合并结果", "type": "main", "index": 0}]]}, "获取股东历史持股数据": {"main": [[{"node": "包装数组为单一json对象", "type": "main", "index": 0}]]}, "最新两期对比分析": {"main": [[{"node": "合并结果", "type": "main", "index": 1}]]}, "合并结果": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "独立股东分析接口响应", "type": "main", "index": 0}]]}, "包装数组为单一json对象": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}