{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取所有时间范围内公司基础数据（直接从company_info获取，含比例）\nSELECT \n  \"registerDate\",\n  \"companyName\",\n  \"companyCode\",\n  \"totalShares\"::numeric as total_shares,\n  \"totalShareholders\" as total_shareholders,\n  \"totalInstitutions\" as institutional_shareholders, \n  \"institutionShares\"::numeric as institutional_shares,\n  ROUND((\"institutionShares\"::numeric / NULLIF(\"totalShares\"::numeric,0)) * 100, 2) as institutional_shares_ratio,\n  (\"totalShareholders\" - \"totalInstitutions\") as individual_shareholders,\n  (\"totalShares\"::numeric - \"institutionShares\"::numeric) as individual_shares,\n  ROUND(((\"totalShares\"::numeric - \"institutionShares\"::numeric) / NULLIF(\"totalShares\"::numeric,0)) * 100, 2) as individual_shares_ratio\nFROM company_info \nWHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\nORDER BY \"registerDate\" ASC", "options": {}}, "id": "d0dca1a1-0bdf-4ec8-b0f1-9ffb0cd22060", "name": "获取公司基础数据", "type": "n8n-nodes-base.postgres", "position": [560, -320], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取所有时间范围内信用账户数据（含比例）\nWITH date_ranges AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ntotal_shares_map AS (\n  SELECT \"registerDate\", \"totalShares\"::numeric as total_shares\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ncredit_agg AS (\n  SELECT \n    s.\"registerDate\",\n    COUNT(DISTINCT CASE WHEN s.\"marginAccount\" IS NOT NULL AND s.\"marginAccount\" != '' THEN s.\"shareholderId\" END) as credit_shareholders,\n    SUM(COALESCE(s.\"sharesInMarginAccount\", 0))::numeric as credit_shares\n  FROM shareholder s\n  INNER JOIN date_ranges dr ON s.\"registerDate\" = dr.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY s.\"registerDate\"\n)\nSELECT \n  \n  c.\"registerDate\",\n  c.credit_shareholders,\n  c.credit_shares,\n  ROUND((c.credit_shares / NULLIF(t.total_shares,0)) * 100, 2) as credit_shares_ratio\nFROM credit_agg c\nLEFT JOIN total_shares_map t ON c.\"registerDate\" = t.\"registerDate\"\nORDER BY c.\"registerDate\" ASC", "options": {}}, "id": "d74f16a5-c2bd-4b19-8787-0b1c45f3bfb5", "name": "获取信用账户数据", "type": "n8n-nodes-base.postgres", "position": [560, -120], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- 获取指定时间范围内股东集中度数据（前十大和前二十大，含数量和比例）\nWITH date_ranges AS (\n  SELECT DISTINCT \"registerDate\"\n  FROM shareholder_registry \n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\ntotal_shares_map AS (\n  SELECT \"registerDate\", \"totalShares\"::numeric as total_shares\n  FROM company_info\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nshareholder_rankings AS (\n  SELECT \n    s.\"registerDate\",\n    s.\"shareholderId\",\n    s.\"numberOfShares\"::numeric,\n    s.\"shareholdingRatio\",\n    ROW_NUMBER() OVER (PARTITION BY s.\"registerDate\" ORDER BY s.\"numberOfShares\"::numeric DESC) as ranking\n  FROM shareholder s\n  INNER JOIN date_ranges dr ON s.\"registerDate\" = dr.\"registerDate\"\n  WHERE s.\"organizationId\" = '{{ $json[\"organizationId\"] }}'\n),\nconcentration_stats AS (\n  SELECT \n    sr.\"registerDate\",\n    SUM(CASE WHEN ranking <= 10 THEN sr.\"numberOfShares\" ELSE 0 END)::numeric as top10_shareholding_amount,\n    ROUND(SUM(CASE WHEN ranking <= 10 THEN sr.\"shareholdingRatio\" ELSE 0 END)::numeric, 2) as top10_shareholding_ratio,\n    SUM(CASE WHEN ranking <= 20 THEN sr.\"numberOfShares\" ELSE 0 END)::numeric as top20_shareholding_amount,\n    ROUND(SUM(CASE WHEN ranking <= 20 THEN sr.\"shareholdingRatio\" ELSE 0 END)::numeric, 2) as top20_shareholding_ratio\n  FROM shareholder_rankings sr\n  GROUP BY sr.\"registerDate\"\n)\nSELECT \n  c.\"registerDate\",\n  c.top10_shareholding_amount,\n  c.top20_shareholding_amount,\n  ROUND((c.top10_shareholding_amount / NULLIF(t.total_shares,0)) * 100, 2) as top10_shareholding_amount_ratio,\n  ROUND((c.top20_shareholding_amount / NULLIF(t.total_shares,0)) * 100, 2) as top20_shareholding_amount_ratio\nFROM concentration_stats c\nLEFT JOIN total_shares_map t ON c.\"registerDate\" = t.\"registerDate\"\nORDER BY c.\"registerDate\" ASC", "options": {}}, "id": "97ce2cff-a4f7-4b75-a5e4-6d4de357a46d", "name": "获取股东集中度数据", "type": "n8n-nodes-base.postgres", "position": [560, 80], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"jsCode": "// 输入验证函数 - 检查organizationId、startDate、endDate\nconst validateInput = (inputData) => {\n  // 校验 organizationId\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n  // 校验 startDate\n  // const startDate = inputData?.body?.startDate\n  // if (!startDate) {\n  //   throw new Error('MISSING_START_DATE');\n  // }\n  // if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(startDate)) {\n  //   throw new Error('INVALID_START_DATE_FORMAT');\n  // }\n  // 校验 endDate\n  // const endDate = inputData?.body?.endDate;\n  // if (!endDate) {\n  //   throw new Error('MISSING_END_DATE');\n  // }\n  // if (!/^\\d{4}-\\d{2}-\\d{2}$/.test(endDate)) {\n  //   throw new Error('INVALID_END_DATE_FORMAT');\n  // }\n  // 校验 startDate < endDate\n  // if (new Date(startDate) >= new Date(endDate)) {\n  //   throw new Error('START_DATE_NOT_BEFORE_END_DATE');\n  // }\n  return {\n    organizationId: id.trim(),\n    // startDate,\n    // endDate\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId } = validateInput(inputData);\n  return [{\n    json: {\n      organizationId,\n      // startDate,\n      // endDate,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'MISSING_START_DATE': '缺少必需参数：startDate。',\n    'INVALID_START_DATE_FORMAT': 'startDate 格式无效，必须为YYYY-MM-DD。',\n    'MISSING_END_DATE': '缺少必需参数：endDate。',\n    'INVALID_END_DATE_FORMAT': 'endDate 格式无效，必须为YYYY-MM-DD。',\n    'START_DATE_NOT_BEFORE_END_DATE': '开始时间必须早于结束时间。'\n  };\n  return messages[code] || '输入验证失败';\n} "}, "id": "6a7fcfdd-f7fd-4331-8a93-b7eff76c0f2a", "name": "输入验证", "type": "n8n-nodes-base.code", "position": [-700, 140], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "e1c2dad0-7b9c-4043-94cc-fe11bbebe387", "name": "检查验证结果", "type": "n8n-nodes-base.if", "position": [-480, 140], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "aa156512-106b-4337-a733-5b0185139327", "name": "startDate", "value": "={{ $json.startDate }}", "type": "string"}, {"id": "7f9dc7a1-8d3e-44a5-84b2-ebe8edcde5bc", "name": "endDate", "value": "={{ $json.endDate }}", "type": "string"}]}, "options": {}}, "id": "6ac8b75a-d1d4-4c99-b0ab-5ac24a1453f5", "name": "设置组织ID", "type": "n8n-nodes-base.set", "position": [220, 0], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "a311ff21-4305-47dc-b5be-d2d97d794ef2", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1500, 60], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "c9fa57d8-4f4d-4958-a6a7-a0cc8dcaab25", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "position": [-80, -160], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "6269f44d-372f-4f0b-a5d8-6ca1270f5831", "name": "获取结构趋势接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1660, 60], "typeVersion": 1.1}, {"parameters": {"httpMethod": "POST", "path": "shareholders-trend-analysis", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-940, 140], "id": "b1a3e846-5b0d-4708-93d6-7ed577f81f33", "name": "获取股东结构趋势接口", "webhookId": "709562a9-264b-4e6e-8257-8781fd36487d"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-180, 240], "id": "65009e8c-2ac0-43cd-87d7-225a4e4c084b", "name": "检验输入id", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "68441aa8-147a-43b1-a01e-f83aa2adedcd", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [400, 460], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH AllDates AS (\n    -- 获取所有报告期日期\n    SELECT DISTINCT \"registerDate\"\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    ORDER BY \"registerDate\"\n),\nAllTypes AS (\n    -- 获取所有机构股东类型（排除个人股东）\n    SELECT DISTINCT \n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    -- 排除个人股东类型\n    AND \"shareholderType\" NOT IN ('境内个人', '境外个人')\n),\nTypeStats AS (\n    -- 计算每个报告期每种机构股东类型的户数和持股数\n    SELECT \n        \"registerDate\",\n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS typeCount,\n        SUM(\"numberOfShares\") AS typeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    -- 排除个人股东类型\n    AND \"shareholderType\" NOT IN ('境内个人', '境外个人')\n    GROUP BY \"registerDate\", \n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n),\nCompanyInfoData AS (\n    -- 获取每个报告期的CompanyInfo数据作为分母\n    SELECT \n        sr.\"registerDate\",\n        ci.\"totalInstitutions\",\n        ci.\"institutionShares\"\n    FROM \"company_info\" ci\n    JOIN \"shareholder_registry\" sr ON ci.\"registryId\" = sr.id\n    WHERE ci.\"organizationId\" = '{{ $json.organizationId }}'\n)\n\n-- 构建最终结果\nSELECT \n    -- 使用TO_CHAR将日期转换为ISO格式字符串\n    TO_CHAR(d.\"registerDate\" AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"') AS \"registerDate\",\n    json_agg(\n        json_build_object(\n            'type', COALESCE(ts.shareholderType, t.shareholderType),\n            'count', COALESCE(ts.typeCount, 0),\n            'shares', COALESCE(ts.typeShares, 0),\n            'countPercentage', CASE \n                WHEN cid.\"totalInstitutions\" > 0 THEN \n                    CAST((COALESCE(ts.typeCount, 0)::numeric / cid.\"totalInstitutions\"::numeric) * 100 AS DECIMAL(10,2))\n                ELSE 0\n            END,\n            'sharesPercentage', CASE \n                WHEN cid.\"institutionShares\" > 0 THEN \n                    CAST((COALESCE(ts.typeShares, 0)::numeric / cid.\"institutionShares\"::numeric) * 100 AS DECIMAL(10,2))\n                ELSE 0\n            END\n        )\n    ) AS \"typeData\"\nFROM AllDates d\nCROSS JOIN AllTypes t\nLEFT JOIN TypeStats ts ON d.\"registerDate\" = ts.\"registerDate\" \n                      AND t.shareholderType = ts.shareholderType\nLEFT JOIN CompanyInfoData cid ON d.\"registerDate\" = cid.\"registerDate\"\nGROUP BY d.\"registerDate\"\nORDER BY d.\"registerDate\";", "options": {}}, "id": "e9c506d3-1c53-4886-8cda-49701f8e84bf", "name": "细分机构股东", "type": "n8n-nodes-base.postgres", "position": [560, 300], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH AllDates AS (\n    -- 获取所有报告期日期\n    SELECT DISTINCT \"registerDate\"\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    ORDER BY \"registerDate\"\n),\nAllTypes AS (\n    -- 获取所有股东类型\n    SELECT DISTINCT \n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n),\nTypeStats AS (\n    -- 计算每个报告期每种股东类型的户数和持股数\n    SELECT \n        \"registerDate\",\n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END AS shareholderType,\n        COUNT(DISTINCT \"shareholderId\") AS typeCount,\n        SUM(\"numberOfShares\") AS typeShares\n    FROM \"shareholder\"\n    WHERE \"organizationId\" = '{{ $json.organizationId }}'\n    GROUP BY \"registerDate\", \n        CASE \n            WHEN \"shareholderType\" IS NULL THEN '其他机构'\n            ELSE \"shareholderType\"\n        END\n)\n\n-- 构建最终结果\nSELECT \n    -- 使用TO_CHAR将日期转换为ISO格式字符串\n    TO_CHAR(d.\"registerDate\" AT TIME ZONE 'UTC', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"') AS \"registerDate\",\n    json_agg(\n        json_build_object(\n            'type', COALESCE(ts.shareholderType, t.shareholderType),\n            'count', COALESCE(ts.typeCount, 0),\n            'shares', COALESCE(ts.typeShares, 0)\n        )\n    ) AS \"typeData\"\nFROM AllDates d\nCROSS JOIN AllTypes t\nLEFT JOIN TypeStats ts ON d.\"registerDate\" = ts.\"registerDate\" \n                      AND t.shareholderType = ts.shareholderType\nGROUP BY d.\"registerDate\"\nORDER BY d.\"registerDate\";", "options": {}}, "id": "37eba3c6-4530-4560-a734-a00b317f7040", "name": "细分机构股东旧版-2025-06-30 17:05:56", "type": "n8n-nodes-base.postgres", "position": [1060, 680], "typeVersion": 2.6, "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "验证输入参数，组织id，开始日期，结束日期必填", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-640, 40], "id": "08d42075-317f-448a-b9e6-3f9168efb058", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "验证id是否可在数据库中查询到相关组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-200, 120], "id": "cef8bac7-e9d9-44f6-8473-9732913774b9", "name": "Sticky Note1"}, {"parameters": {"content": "本接口端点：shareholders-trend-analysis\n\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1300, 160], "id": "cdd43456-f0b3-4a47-a6e0-1a90510b34df", "name": "Sticky Note6"}, {"parameters": {"content": "设置参数，并在数据库中查询对应股东趋势数据，包括股东集中度，股东细分类型数据等", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [480, -440], "id": "4f20bcd2-c53e-430f-847e-20778a394167", "name": "Sticky Note2"}, {"parameters": {"content": "合并数据，格式化后返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1240, -60], "id": "686692aa-4454-4af1-baad-6220e2784cc5", "name": "Sticky Note3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [0, 240], "id": "deaf2507-5e57-4dec-8bdb-d2f829eb7b5d", "name": "判断组织是否在数据库存在"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [160, 460], "id": "7bb45f56-94e3-4d7c-8391-fdc655aa919d", "name": "格式化错误信息"}, {"parameters": {"mode": "combine", "fieldsToMatchString": "registerDate", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [760, -200], "id": "d941332f-4c79-441d-9c90-711a12e0cb5a", "name": "合并数据", "alwaysOutputData": true}, {"parameters": {"mode": "combine", "fieldsToMatchString": "registerDate", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [920, -20], "id": "83dd92a3-f7a8-4edb-91a0-0491ac5084d5", "name": "合并数据1", "alwaysOutputData": true}, {"parameters": {"mode": "combine", "fieldsToMatchString": "registerDate", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1140, 60], "id": "e1007707-7026-4bd0-9e86-21cb7dd6b8c7", "name": "合并数据2", "alwaysOutputData": true}, {"parameters": {"jsCode": "const list = $input.all().map(item => item.json)[0]?.registerDate ? $input.all().map(item => item.json) : []\nreturn [{\n  trendData: list\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1320, 60], "id": "fdf215b3-b69e-4589-b9d8-d9acc3258b5c", "name": "包装所有数据为单一json对象"}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：<PERSON>\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1300, 280], "id": "31c350f4-78e2-42e8-84c5-1442344ea415", "name": "Sticky Note7"}, {"parameters": {"content": "响应参数示例：\n [\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"trendData\": [\n\t\t\t\t{\n\t\t\t\t\t\"registerDate\": \"2024-06-28T00:00:00.000Z\",\n\t\t\t\t\t\"companyName\": \"一品红\",\n\t\t\t\t\t\"companyCode\": \"300723\",\n\t\t\t\t\t\"total_shares\": \"*********.00\",\n\t\t\t\t\t\"total_shareholders\": 13394,\n\t\t\t\t\t\"institutional_shareholders\": 2090,\n\t\t\t\t\t\"institutional_shares\": \"*********.00\",\n\t\t\t\t\t\"institutional_shares_ratio\": \"63.02\",\n\t\t\t\t\t\"individual_shareholders\": 11304,\n\t\t\t\t\t\"individual_shares\": \"*********.00\",\n\t\t\t\t\t\"individual_shares_ratio\": \"36.98\",\n\t\t\t\t\t\"credit_shareholders\": \"196\",\n\t\t\t\t\t\"credit_shares\": \"16453118.00\",\n\t\t\t\t\t\"credit_shares_ratio\": \"3.62\",\n\t\t\t\t\t\"top10_shareholding_amount\": \"*********.00\",\n\t\t\t\t\t\"top20_shareholding_amount\": \"*********.00\",\n\t\t\t\t\t\"top10_shareholding_amount_ratio\": \"70.27\",\n\t\t\t\t\t\"top20_shareholding_amount_ratio\": \"74.24\",\n\t\t\t\t\t\"typeData\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"QFII&RQFII\",\n\t\t\t\t\t\t\t\"count\": 1,\n\t\t\t\t\t\t\t\"shares\": 323500,\n\t\t\t\t\t\t\t\"countPercentage\": 0.05,\n\t\t\t\t\t\t\t\"sharesPercentage\": 0.11\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"保险\",\n\t\t\t\t\t\t\t\"count\": 2,\n\t\t\t\t\t\t\t\"shares\": 2762700,\n\t\t\t\t\t\t\t\"countPercentage\": 0.1,\n\t\t\t\t\t\t\t\"sharesPercentage\": 0.97\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"其他机构\",\n\t\t\t\t\t\t\t\"count\": 6,\n\t\t\t\t\t\t\t\"shares\": 2935980,\n\t\t\t\t\t\t\t\"countPercentage\": 0.29,\n\t\t\t\t\t\t\t\"sharesPercentage\": 1.03\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"基本养老保险基金\",\n\t\t\t\t\t\t\t\"count\": 2,\n\t\t\t\t\t\t\t\"shares\": 1184050,\n\t\t\t\t\t\t\t\"countPercentage\": 0.1,\n\t\t\t\t\t\t\t\"sharesPercentage\": 0.41\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"境内机构\",\n\t\t\t\t\t\t\t\"count\": 5,\n\t\t\t\t\t\t\t\"shares\": 189078441,\n\t\t\t\t\t\t\t\"countPercentage\": 0.24,\n\t\t\t\t\t\t\t\"sharesPercentage\": 66.07\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"境外机构\",\n\t\t\t\t\t\t\t\"count\": 2,\n\t\t\t\t\t\t\t\"shares\": 3000750,\n\t\t\t\t\t\t\t\"countPercentage\": 0.1,\n\t\t\t\t\t\t\t\"sharesPercentage\": 1.05\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"年金\",\n\t\t\t\t\t\t\t\"count\": 49,\n\t\t\t\t\t\t\t\"shares\": 15574229,\n\t\t\t\t\t\t\t\"countPercentage\": 2.34,\n\t\t\t\t\t\t\t\"sharesPercentage\": 5.44\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"证券\",\n\t\t\t\t\t\t\t\"count\": 1,\n\t\t\t\t\t\t\t\"shares\": 205304,\n\t\t\t\t\t\t\t\"countPercentage\": 0.05,\n\t\t\t\t\t\t\t\"sharesPercentage\": 0.07\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"证券投资基金\",\n\t\t\t\t\t\t\t\"count\": 31,\n\t\t\t\t\t\t\t\"shares\": 22193827,\n\t\t\t\t\t\t\t\"countPercentage\": 1.48,\n\t\t\t\t\t\t\t\"sharesPercentage\": 7.75\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"资管公司\",\n\t\t\t\t\t\t\t\"count\": 9,\n\t\t\t\t\t\t\t\"shares\": 41568381,\n\t\t\t\t\t\t\t\"countPercentage\": 0.43,\n\t\t\t\t\t\t\t\"sharesPercentage\": 14.52\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n]", "height": 1960, "width": 960}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1880, -760], "id": "ba2df1e2-f37d-4060-93da-371b1ab0d3dd", "name": "Sticky Note4"}], "connections": {"获取公司基础数据": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "获取信用账户数据": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "获取股东集中度数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 1}]]}, "输入验证": {"main": [[{"node": "检查验证结果", "type": "main", "index": 0}]]}, "检查验证结果": {"main": [[{"node": "错误响应", "type": "main", "index": 0}], [{"node": "检验输入id", "type": "main", "index": 0}]]}, "设置组织ID": {"main": [[{"node": "获取公司基础数据", "type": "main", "index": 0}, {"node": "获取信用账户数据", "type": "main", "index": 0}, {"node": "获取股东集中度数据", "type": "main", "index": 0}, {"node": "细分机构股东", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "获取结构趋势接口响应", "type": "main", "index": 0}]]}, "获取股东结构趋势接口": {"main": [[{"node": "输入验证", "type": "main", "index": 0}]]}, "检验输入id": {"main": [[{"node": "判断组织是否在数据库存在", "type": "main", "index": 0}]]}, "细分机构股东": {"main": [[{"node": "合并数据2", "type": "main", "index": 1}]]}, "判断组织是否在数据库存在": {"main": [[{"node": "设置组织ID", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "合并数据": {"main": [[{"node": "合并数据1", "type": "main", "index": 0}]]}, "合并数据1": {"main": [[{"node": "合并数据2", "type": "main", "index": 0}]]}, "合并数据2": {"main": [[{"node": "包装所有数据为单一json对象", "type": "main", "index": 0}]]}, "包装所有数据为单一json对象": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}