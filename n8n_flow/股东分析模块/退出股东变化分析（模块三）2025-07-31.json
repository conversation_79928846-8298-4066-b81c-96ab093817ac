{"nodes": [{"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 上期自然人股东（带前20排名）\nranked_prev AS (\n  SELECT *,\n    ROW_NUMBER() OVER (PARTITION BY \"registerDate\" ORDER BY \"numberOfShares\" DESC) AS ranking\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderType\" ILIKE '%个人%'\n),\n\nprev_top20 AS (\n  SELECT *\n  FROM ranked_prev\n  WHERE ranking <= 20\n),\n\n-- 当前期所有自然人（无排名限制）\ncurr_all AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  \"shareholderType\" ILIKE '%个人%' OR\n  \"shareholderType\" ILIKE '%知名牛散%'\n)\n)\n\n-- 筛选退出的自然人股东（前期在前20，本期无记录或持股为0）\nSELECT\n  prev.\"securitiesAccountName\" AS name,\n  prev.\"unifiedAccountNumber\" AS \"unified_account_number\",\n  TO_CHAR(dp.current_date, 'YYYY-MM-DD') AS exit_date,\n  prev.\"numberOfShares\" AS prev_numberOfShares,\n  prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN prev_top20 prev ON prev.\"registerDate\" = dp.prev_date\nLEFT JOIN curr_all curr \n       ON curr.\"registerDate\" = dp.current_date\n      AND curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (curr.\"unifiedAccountNumber\" IS NULL OR curr.\"numberOfShares\" = 0)\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [740, 740], "id": "2cb6ef2d-05ae-4831-af86-16acbfe196ee", "name": "获取退出个人股东", "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH latest_two_dates AS (\n  SELECT \"registerDate\"\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n  GROUP BY \"registerDate\"\n  ORDER BY \"registerDate\" DESC\n  LIMIT 2\n),\ndate_pairs AS (\n  SELECT \n    MAX(\"registerDate\") AS current_date,\n    MIN(\"registerDate\") AS prev_date\n  FROM latest_two_dates\n),\n\n-- 上一期机构股东（过滤掉持股为 0 的）\nprev_institution AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND \"shareholderCategory\" NOT ILIKE '%自然人%'\n    AND \"numberOfShares\" > 0\n),\n\n-- 当前期机构股东（用于判断是否退出）\ncurr_institution AS (\n  SELECT *\n  FROM shareholder\n  WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}'\n    AND (\n  \"shareholderType\" NOT ILIKE '%个人%' AND\n  \"shareholderType\" NOT ILIKE '%知名牛散%'\n)\n)\n\n-- 找出退出的机构股东\nSELECT\n  prev.\"securitiesAccountName\" AS name,\n  prev.\"unifiedAccountNumber\" AS \"unified_account_number\",\n  TO_CHAR(dp.current_date, 'YYYY-MM-DD') AS exit_date,\n  prev.\"numberOfShares\" AS prev_numberOfShares,\n  prev.\"shareholdingRatio\" AS prev_shareholdingRatio,\n  COUNT(*) OVER() AS total\nFROM date_pairs dp\nJOIN prev_institution prev ON prev.\"registerDate\" = dp.prev_date\nLEFT JOIN curr_institution curr \n       ON curr.\"registerDate\" = dp.current_date\n      AND curr.\"unifiedAccountNumber\" = prev.\"unifiedAccountNumber\"\nWHERE dp.prev_date IS NOT NULL\n  AND (curr.\"unifiedAccountNumber\" IS NULL OR curr.\"numberOfShares\" = 0)\nORDER BY {{ $json.order_base }} {{ $json.order }}\nLIMIT {{ $json[\"pageSize\"] || 5 }}\nOFFSET {{ (($json[\"page\"] || 1) - 1) * ($json[\"pageSize\"] || 5) }};\n", "options": {}}, "id": "5ab99c73-265d-473b-837e-ecbd988f8d7e", "name": "获取退出机构股东", "type": "n8n-nodes-base.postgres", "position": [740, 960], "typeVersion": 2.6, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "2c025585-fc3b-4da7-bd5a-cdeaffcbceb1", "name": "格式化成功响应", "type": "n8n-nodes-base.set", "position": [1160, 720], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "success-response", "name": "success", "value": true, "type": "boolean"}, {"id": "data-response", "name": "data", "value": "={{ $json }}", "type": "object"}, {"id": "timestamp-response", "name": "timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}]}, "options": {}}, "id": "b78500dc-daa1-4273-bfda-54c06a841ffa", "name": "格式化成功响应1", "type": "n8n-nodes-base.set", "position": [1160, 960], "typeVersion": 3.4}, {"parameters": {"jsCode": "// 输入验证函数 - 检查 organizationId、page、pageSize\nconst validateInput = (inputData) => {\n  const id = inputData?.query?.id || inputData?.body?.id || inputData?.id;\n  const page = inputData?.query?.page || inputData?.body?.page || inputData?.page || 1;\n  const pageSize = inputData?.query?.pageSize || inputData?.body?.pageSize || inputData?.pageSize || 5;\n  const order_base = inputData?.query?.order_base || inputData?.body?.order_base || inputData?.order_base || 'prev_numberOfShares';\n  const order = inputData?.query?.order || inputData?.body?.order || inputData?.order || 'Desc';\n\n  // 验证 organizationId\n  if (!id) {\n    throw new Error('MISSING_ORGANIZATION_ID');\n  }\n  if (typeof id !== 'string' || id.trim() === '') {\n    throw new Error('INVALID_ORGANIZATION_ID');\n  }\n  if (id.length < 5 || id.length > 50) {\n    throw new Error('ORGANIZATION_ID_LENGTH_INVALID');\n  }\n\n  // 验证 page\n  const parsedPage = parseInt(page, 10);\n  if (isNaN(parsedPage) || parsedPage <= 0) {\n    throw new Error('INVALID_PAGE');\n  }\n\n  // 验证 pageSize\n  const parsedPageSize = parseInt(pageSize, 10);\n  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {\n    throw new Error('INVALID_PAGE_SIZE');\n  }\n\n  return {\n    organizationId: id.trim(),\n    page: parsedPage,\n    pageSize: parsedPageSize,\n    order: order,\n    order_base:order_base\n  };\n};\n\ntry {\n  const inputData = $input.first().json;\n  const { organizationId, page, pageSize, order,order_base } = validateInput(inputData);\n\n  return [{\n    json: {\n      organizationId,\n      page,\n      pageSize,\n      type: inputData.webhookUrl,\n      order:order,\n      order_base:order_base,\n      validation: {\n        success: true,\n        timestamp: new Date().toISOString()\n      }\n    }\n  }];\n  \n} catch (error) {\n  return [{\n    json: {\n      error: {\n        code: error.message,\n        message: getErrorMessage(error.message),\n        timestamp: new Date().toISOString(),\n        type: 'VALIDATION_ERROR'\n      }\n    }\n  }];\n}\n\nfunction getErrorMessage(code) {\n  const messages = {\n    'MISSING_ORGANIZATION_ID': '缺少必需参数：organizationId。请在查询参数、请求体或URL路径中提供id参数。',\n    'INVALID_ORGANIZATION_ID': '无效的organizationId：参数必须是非空字符串。',\n    'ORGANIZATION_ID_LENGTH_INVALID': '无效的organizationId长度：参数长度必须在5-50个字符之间。',\n    'INVALID_PAGE': '无效的page参数：必须是大于0的整数。',\n    'INVALID_PAGE_SIZE': '无效的pageSize参数：必须是大于0的整数。'\n  };\n  return messages[code] || '输入验证失败';\n}\n"}, "id": "656c5cf5-c69e-4355-bf25-5df7f824d910", "name": "输入验证1", "type": "n8n-nodes-base.code", "position": [-620, 920], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "has-error", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "6c5f9650-fcc9-4b9e-954e-31d0f2196945", "name": "检查验证结果1", "type": "n8n-nodes-base.if", "position": [-440, 920], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "org-id-assignment", "name": "organizationId", "value": "={{ $json.organizationId }}", "type": "string"}, {"id": "7cd49652-c26c-4d4f-a30a-58c0261d2292", "name": "page", "value": "={{ $json.page }}", "type": "string"}, {"id": "c4065c11-a83d-460e-b614-5b0bf60a75de", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "string"}, {"id": "90979f76-bab3-4081-a188-fa2967a2cdc2", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "f3f357a6-4c7a-4a01-b988-075fb7aad662", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "62efbef2-3a9f-4e7b-91fc-d3bea08409f4", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "id": "8ada0e68-c61e-4220-b0ce-1d8abedc40b0", "name": "设置组织ID1", "type": "n8n-nodes-base.set", "position": [340, 880], "typeVersion": 3.4}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "7e3353e5-fade-4f8e-bbc5-6d3bdeb25007", "name": "错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [-280, 640], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.error }}", "options": {"responseCode": 200}}, "id": "37849d64-85e0-44c3-a971-034f80908ef4", "name": "数据库错误响应1", "type": "n8n-nodes-base.respondToWebhook", "position": [500, 1120], "typeVersion": 1.1}, {"parameters": {"operation": "execute<PERSON>uery", "query": "  SELECT * FROM company_info WHERE \"organizationId\" = '{{ $json[\"organizationId\"] }}' LIMIT 1", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-200, 820], "id": "6c4a19a2-c404-4dc3-a99d-da6f5efe5d5f", "name": "检验输入id1", "alwaysOutputData": true, "credentials": {"postgres": {"id": "FBA9FG8QfBxSMbXU", "name": "Postgres account"}}}, {"parameters": {"content": "此工作流包含两个后端接口，分别为：\n\n个人退出股东： exit-individual-shareholders-trend\n机构退出股东：exit-institution-shareholders-trend", "height": 120, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1360, 920], "id": "9251d526-05af-49d7-907b-b59d2bf9474b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "输入参数验证，id必填\npage，pagesize，order，order_base选填，这四个函数用于分页查询和排序查询", "height": 80, "width": 300}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-640, 1060], "id": "3438d969-bfcc-46f8-9b8b-ce57a8087b64", "name": "Sticky Note1"}, {"parameters": {"content": "检验输入id是否能查询到对应组织", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-20, 780], "id": "c78681bf-231e-47dd-9edc-04628d85b1d1", "name": "Sticky Note2"}, {"parameters": {"content": "根据输入的url判断查询个人还是机构股东", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [480, 760], "id": "9afc2569-cd45-49d9-9eca-dba2a00abdb1", "name": "Sticky Note3"}, {"parameters": {"content": "查询对应类型的退出股东\n", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 860], "id": "ac1574df-b202-4fe1-bf34-8a3783c080f3", "name": "Sticky Note4"}, {"parameters": {"content": "格式化查询数据并返回", "height": 80}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1640, 760], "id": "b521a64d-b754-452a-87ca-31c89a8de984", "name": "Sticky Note5"}, {"parameters": {"assignments": {"assignments": [{"id": "b80c5093-a312-4904-af32-ccb7ebef78ba", "name": "page", "value": "={{ $json.page }}", "type": "number"}, {"id": "09d4d484-6a99-473d-934a-d0a65b8906d1", "name": "pageSize", "value": "={{ $json.pageSize }}", "type": "number"}, {"id": "ede7944f-4580-44e6-90fc-2f22f6ce0d6d", "name": "type", "value": "={{ $json.type }}", "type": "string"}, {"id": "09af8040-aa5d-4f5d-8217-ab8b71012a7f", "name": "order", "value": "={{ $json.order }}", "type": "string"}, {"id": "37175b76-60c6-47a8-81b0-0410624183c2", "name": "order_base", "value": "={{ $json.order_base }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-200, 980], "id": "34f538fc-72ff-454e-aa23-cac9d5ac6829", "name": "设置分页参数"}, {"parameters": {"mode": "combine", "combineBy": "combineAll", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-20, 900], "id": "5d1059e1-b9d1-4c4d-8cd6-5fd0e699d27a", "name": "合并数据"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6efa1a8-6b60-498f-8284-b8682b026745", "leftValue": "={{ $json.id }}", "rightValue": 0, "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [140, 900], "id": "ecd967ad-3885-41ef-be0b-db55a558c533", "name": "检验组织是否存在数据库中"}, {"parameters": {"jsCode": "// 数据库错误处理和包装 - 趋势数据\ntry {\n  return [{\n      json: {\n        error: {\n          code: 'NO_TREND_DATA_FOUND',\n          message: '未找到指定组织的数据，请检查organizationId是否正确。',\n          timestamp: new Date().toISOString(),\n          type: 'DATA_NOT_FOUND'\n        }\n      }\n  }];\n}catch (error) {\n  // 处理意外错误\n  return [{\n    json: {\n      error: {\n        code: 'INTERNAL_ERROR',\n        message: '服务内部错误，请稍后重试。',\n        timestamp: new Date().toISOString(),\n        type: 'INTERNAL_ERROR',\n        details: error.message\n      }\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, 1120], "id": "f696e193-3d39-440b-a622-c1585530bec4", "name": "格式化错误信息"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2b1a11d5-5194-4264-9099-d7c2978cf286", "leftValue": "={{ $json.type }}", "rightValue": "individual", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [560, 880], "id": "b8243f10-ec9a-4816-94e2-943c7f60614e", "name": "根据url判断进入哪个类型工作流"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  exitIndividuals: list,\n  total\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 760], "id": "1c8ab628-d517-4327-a79d-cdbdf23528f6", "name": "计算数据总量"}, {"parameters": {"jsCode": "const total = Number($input.first().json.total) ?  Number($input.first().json.total) : 0\n\nconst list = $input.all().map(item => {\n  delete item.json.total\n  return {\n    ...item.json\n  }\n})\nreturn [{\n  exitInstitutions: list,\n  total\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [920, 980], "id": "b2e2055a-1421-41eb-813d-90df4c7049ea", "name": "计算数据总量1"}, {"parameters": {"httpMethod": "POST", "path": "exit-individual-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-860, 820], "id": "97c2f93b-44f8-4d66-8cbb-eec7a7b3e72d", "name": "获取退出个人股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"httpMethod": "POST", "path": "exit-institution-shareholders-trend", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-860, 980], "id": "aa59f2cd-9676-4c1c-9b1e-32f4454b72b8", "name": "获取退出机构股东数据接口", "webhookId": "2d2f6a68-2f9b-46e6-967b-985fad389b9a"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "c1d8be22-6dc8-41da-b3c4-53848252fa44", "name": "获取退出个人股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1380, 700], "typeVersion": 1.1}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "id": "b1c851ab-28f9-49bd-8cfb-163e9a885fa8", "name": "获取退出机构股东接口响应", "type": "n8n-nodes-base.respondToWebhook", "position": [1400, 960], "typeVersion": 1.1}, {"parameters": {"content": "记录日期：2025-07-31\n相关人员：Andy\n", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1360, 1160], "id": "5d80be3c-41b1-4593-bd87-ed9f51d2d198", "name": "Sticky Note7"}, {"parameters": {"content": "返回数据示例：\n[\n\t{\n\t\t\"success\": true,\n\t\t\"data\": {\n\t\t\t\"exitInstitutions\": [\n\t\t\t\t{\n\t\t\t\t\t\"name\": \"中国建设银行股份有限公司－农银汇理医疗保健主题股票型证券投资基金\",\n\t\t\t\t\t\"unified_account_number\": \"************\",\n\t\t\t\t\t\"exit_date\": \"2024-07-31\",\n\t\t\t\t\t\"prev_numberofshares\": \"1679807.00\",\n\t\t\t\t\t\"prev_shareholdingratio\": \"0.37\"\n\t\t\t\t},\n\t\t\t\"total\": 1\n\t\t},\n\t\t\"timestamp\": \"2025-07-31T08:32:52.540Z\"\n\t}\n]", "height": 460, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1640, 880], "id": "404f8a92-5152-4c27-b2c6-4504b5bf1598", "name": "Sticky Note8"}], "connections": {"获取退出个人股东": {"main": [[{"node": "计算数据总量", "type": "main", "index": 0}]]}, "获取退出机构股东": {"main": [[{"node": "计算数据总量1", "type": "main", "index": 0}]]}, "格式化成功响应": {"main": [[{"node": "获取退出个人股东接口响应", "type": "main", "index": 0}]]}, "格式化成功响应1": {"main": [[{"node": "获取退出机构股东接口响应", "type": "main", "index": 0}]]}, "输入验证1": {"main": [[{"node": "检查验证结果1", "type": "main", "index": 0}]]}, "检查验证结果1": {"main": [[{"node": "错误响应1", "type": "main", "index": 0}], [{"node": "检验输入id1", "type": "main", "index": 0}, {"node": "设置分页参数", "type": "main", "index": 0}]]}, "设置组织ID1": {"main": [[{"node": "根据url判断进入哪个类型工作流", "type": "main", "index": 0}]]}, "检验输入id1": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "设置分页参数": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "检验组织是否存在数据库中", "type": "main", "index": 0}]]}, "检验组织是否存在数据库中": {"main": [[{"node": "设置组织ID1", "type": "main", "index": 0}], [{"node": "格式化错误信息", "type": "main", "index": 0}]]}, "格式化错误信息": {"main": [[{"node": "数据库错误响应1", "type": "main", "index": 0}]]}, "根据url判断进入哪个类型工作流": {"main": [[{"node": "获取退出个人股东", "type": "main", "index": 0}], [{"node": "获取退出机构股东", "type": "main", "index": 0}]]}, "计算数据总量": {"main": [[{"node": "格式化成功响应", "type": "main", "index": 0}]]}, "计算数据总量1": {"main": [[{"node": "格式化成功响应1", "type": "main", "index": 0}]]}, "获取退出个人股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}, "获取退出机构股东数据接口": {"main": [[{"node": "输入验证1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "79f3109ea615603d6dfd41133a4057126dca2567ef13ac97c3033d81ffe1d632"}}