# 会议管理模块 API 设计文档

## 概述

本文档为会议管理模块（meet）提供简洁的 API 设计，专注于核心会议CRUD功能：创建会议、查询会议列表、查询会议详情、更新会议、删除会议。严格遵循项目现有架构模式，与investor-management模块保持一致的设计风格。

## 设计原则

1. **架构一致性**：严格遵循项目现有API架构模式（参考investor-management模块）
2. **中间件统一**：使用authMiddleware + shareholderCryptoMiddleware
3. **简洁性**：只包含5个核心CRUD操作
4. **数据验证**：使用Zod进行统一数据验证
5. **错误处理**：遵循项目统一错误处理模式

## API 架构设计

### 目录结构

遵循项目现有架构模式，参考investor-management模块：

```
packages/api/src/routes/meet/
├── router.ts               # 主路由文件，聚合所有子路由
├── types.ts               # TypeScript类型定义
├── lib/                   # 工具库目录
│   └── validators.ts      # Zod验证器
├── create.ts              # 创建会议路由
├── list.ts                # 查询会议列表路由
├── detail.ts              # 查询会议详情路由
├── update.ts              # 更新会议路由
└── delete.ts              # 删除会议路由
```

### 中间件应用

所有API路由统一应用项目标准中间件：

```typescript
// 每个路由文件都应用以下中间件
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";

export const meetingCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    // 路由处理逻辑
  }
);
```

## API 接口设计

### 1. 创建会议

**接口地址**：`POST /api/meet/create`

**功能描述**：创建新会议

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| title | string | 是 | 会议标题 |
| description | string | 否 | 会议描述 |
| meetingType | string | 否 | 会议类型（BOARD/SHAREHOLDER/GENERAL等） |
| scheduledStartTime | string | 是 | 计划开始时间（ISO 8601格式） |
| scheduledEndTime | string | 是 | 计划结束时间（ISO 8601格式） |
| location | string | 否 | 会议地点 |
| locationUrl | string | 否 | 线上会议链接 |
| maxParticipants | number | 否 | 最大参会人数 |
| isPrivate | boolean | 否 | 是否私密会议（默认false） |

**请求示例**：
```json
{
  "organizationId": "org_123456",
  "title": "2024年第一次董事会",
  "description": "讨论公司年度发展规划",
  "meetingType": "BOARD",
  "scheduledStartTime": "2024-06-15T09:00:00Z",
  "scheduledEndTime": "2024-06-15T12:00:00Z",
  "location": "公司会议室A",
  "maxParticipants": 15,
  "isPrivate": false
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "meet_789012"
  }
}
```

### 2. 查询会议列表

**接口地址**：`POST /api/meet/list`

**功能描述**：获取会议列表，支持分页、过滤和排序

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| page | number | 否 | 页码（默认1） |
| limit | number | 否 | 每页数量（默认20，最大100） |
| status | string | 否 | 会议状态过滤（SCHEDULED/ONGOING/COMPLETED/CANCELLED） |
| meetingType | string | 否 | 会议类型过滤 |
| search | string | 否 | 搜索关键词（按标题搜索） |
| startDate | string | 否 | 开始时间范围 |
| endDate | string | 否 | 结束时间范围 |
| sortBy | string | 否 | 排序字段（默认scheduledStartTime） |
| sortOrder | string | 否 | 排序方向（asc/desc，默认desc） |

**请求示例**：
```json
{
  "organizationId": "org_123456",
  "page": 1,
  "limit": 20,
  "status": "SCHEDULED",
  "meetingType": "BOARD",
  "search": "董事会",
  "sortBy": "scheduledStartTime",
  "sortOrder": "desc"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "meetings": [
      {
        "id": "meet_789012",
        "title": "2024年第一次董事会",
        "description": "讨论公司年度发展规划",
        "status": "SCHEDULED",
        "meetingType": "BOARD",
        "scheduledStartTime": "2024-06-15T09:00:00Z",
        "scheduledEndTime": "2024-06-15T12:00:00Z",
        "location": "公司会议室A",
        "locationUrl": null,
        "maxParticipants": 15,
        "isPrivate": false,
        "createdBy": "user_123",
        "createdAt": "2024-06-01T10:00:00Z",
        "updatedAt": "2024-06-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 3. 查询会议详情

**接口地址**：`POST /api/meet/detail`

**功能描述**：获取会议详细信息

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| meetingId | string | 是 | 会议ID |

**请求示例**：
```json
{
  "organizationId": "org_123456",
  "meetingId": "meet_789012"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "meet_789012",
    "title": "2024年第一次董事会",
    "description": "讨论公司年度发展规划",
    "status": "SCHEDULED",
    "meetingType": "BOARD",
    "scheduledStartTime": "2024-06-15T09:00:00Z",
    "scheduledEndTime": "2024-06-15T12:00:00Z",
    "actualStartTime": null,
    "actualEndTime": null,
    "location": "公司会议室A",
    "locationUrl": null,
    "maxParticipants": 15,
    "isPrivate": false,
    "password": null,
    "createdBy": {
      "id": "user_123",
      "name": "张三",
      "email": "<EMAIL>"
    },
    "organization": {
      "id": "org_123456",
      "name": "示例公司"
    },
    "createdAt": "2024-06-01T10:00:00Z",
    "updatedAt": "2024-06-01T10:00:00Z"
  }
}
```

### 4. 更新会议

**接口地址**：`POST /api/meet/update`

**功能描述**：更新会议信息

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| meetingId | string | 是 | 会议ID |
| title | string | 否 | 会议标题 |
| description | string | 否 | 会议描述 |
| status | string | 否 | 会议状态 |
| meetingType | string | 否 | 会议类型 |
| scheduledStartTime | string | 否 | 计划开始时间 |
| scheduledEndTime | string | 否 | 计划结束时间 |
| location | string | 否 | 会议地点 |
| locationUrl | string | 否 | 线上会议链接 |
| maxParticipants | number | 否 | 最大参会人数 |
| isPrivate | boolean | 否 | 是否私密会议 |

**请求示例**：
```json
{
  "organizationId": "org_123456",
  "meetingId": "meet_789012",
  "title": "2024年第一次董事会（更新）",
  "scheduledStartTime": "2024-06-15T10:00:00Z",
  "status": "ONGOING"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "meet_789012"
  }
}
```

### 5. 删除会议

**接口地址**：`POST /api/meet/delete`

**功能描述**：删除会议

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| meetingId | string | 是 | 会议ID |

**请求示例**：
```json
{
  "organizationId": "org_123456",
  "meetingId": "meet_789012"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 数据验证设计

### Zod验证器定义

遵循项目现有验证器模式，参考investor-management模块：

```typescript
// packages/api/src/routes/meet/lib/validators.ts
/**
 * 会议管理模块验证器
 *
 * <AUTHOR>
 * @date 2025-01-12 15:30:00
 * @updated 2025-01-12 15:30:00 hayden 根据会议管理API设计创建验证器
 * @description 会议管理模块的所有Zod验证器定义，包括会议CRUD相关验证
 */
import { z } from "zod";

// 会议状态枚举
const MeetingStatusSchema = z.enum(["SCHEDULED", "ONGOING", "COMPLETED", "CANCELLED"], {
  errorMap: () => ({ message: "会议状态必须是 SCHEDULED、ONGOING、COMPLETED、CANCELLED 之一" })
});

// 会议类型枚举
const MeetingTypeSchema = z.enum(["BOARD", "SHAREHOLDER", "GENERAL", "COMMITTEE", "EXECUTIVE", "EMERGENCY"], {
  errorMap: () => ({ message: "会议类型必须是 BOARD、SHAREHOLDER、GENERAL、COMMITTEE、EXECUTIVE、EMERGENCY 之一" })
});

// 创建会议验证
export const CreateMeetingSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  title: z.string({ required_error: "缺少必需参数title，请提供会议标题" }).min(1, "会议标题不能为空").max(255, "会议标题不能超过255个字符"),
  description: z.string().optional(),
  meetingType: MeetingTypeSchema.optional(),
  scheduledStartTime: z.string({ required_error: "缺少必需参数scheduledStartTime，请提供计划开始时间" }).datetime("开始时间格式无效，请使用ISO 8601格式"),
  scheduledEndTime: z.string({ required_error: "缺少必需参数scheduledEndTime，请提供计划结束时间" }).datetime("结束时间格式无效，请使用ISO 8601格式"),
  location: z.string().max(255, "地点不能超过255个字符").optional(),
  locationUrl: z.string().url("会议链接格式无效").optional(),
  maxParticipants: z.number().int().min(0, "最大参会人数不能为负数").optional(),
  isPrivate: z.boolean().default(false),
}).refine(data => {
  // 验证结束时间不能早于开始时间
  if (data.scheduledEndTime && data.scheduledStartTime) {
    return new Date(data.scheduledEndTime) >= new Date(data.scheduledStartTime);
  }
  return true;
}, {
  message: "结束时间不能早于开始时间",
  path: ["scheduledEndTime"]
});

// 查询会议列表验证
export const ListMeetingSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(20),
  status: MeetingStatusSchema.optional(),
  meetingType: MeetingTypeSchema.optional(),
  search: z.string().optional(),
  startDate: z.string().datetime("开始日期格式无效").optional(),
  endDate: z.string().datetime("结束日期格式无效").optional(),
  sortBy: z.string().optional().default("scheduledStartTime"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

// 查询会议详情验证
export const DetailMeetingSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  meetingId: z.string({ required_error: "缺少必需参数meetingId，请提供会议ID" }).min(1, "会议ID不能为空"),
});

// 更新会议验证
export const UpdateMeetingSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  meetingId: z.string({ required_error: "缺少必需参数meetingId，请提供会议ID" }).min(1, "会议ID不能为空"),
  title: z.string().min(1, "会议标题不能为空").max(255, "会议标题不能超过255个字符").optional(),
  description: z.string().optional(),
  status: MeetingStatusSchema.optional(),
  meetingType: MeetingTypeSchema.optional(),
  scheduledStartTime: z.string().datetime("开始时间格式无效").optional(),
  scheduledEndTime: z.string().datetime("结束时间格式无效").optional(),
  location: z.string().max(255, "地点不能超过255个字符").optional(),
  locationUrl: z.string().url("会议链接格式无效").optional(),
  maxParticipants: z.number().int().min(0, "最大参会人数不能为负数").optional(),
  isPrivate: z.boolean().optional(),
}).refine(data => {
  // 验证结束时间不能早于开始时间
  if (data.scheduledEndTime && data.scheduledStartTime) {
    return new Date(data.scheduledEndTime) >= new Date(data.scheduledStartTime);
  }
  return true;
}, {
  message: "结束时间不能早于开始时间",
  path: ["scheduledEndTime"]
});

// 删除会议验证
export const DeleteMeetingSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  meetingId: z.string({ required_error: "缺少必需参数meetingId，请提供会议ID" }).min(1, "会议ID不能为空"),
});
```

## 错误处理设计

### 错误响应格式

遵循项目统一错误响应格式：

```typescript
interface ErrorResponse {
  code: number;
  message: string;
  data: null;
}
```

### 常见错误码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 400 | BAD_REQUEST | 请求参数错误 |
| 401 | UNAUTHORIZED | 未认证 |
| 403 | FORBIDDEN | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 422 | VALIDATION_ERROR | 数据验证失败 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

### 错误处理模式

参考现有API路由的错误处理模式：

```typescript
// 在每个路由中统一处理错误
try {
  const requestData = c.get("requestData");

  // 参数验证
  const validationResult = CreateMeetingSchema.safeParse(requestData);
  if (!validationResult.success) {
    const errorMessage = validationResult.error.errors.map(err => err.message).join(", ");
    throw new HTTPException(400, { message: errorMessage });
  }

  // 业务逻辑处理
  // ...

  // 设置成功响应
  c.set("response", {
    code: 200,
    message: "操作成功",
    data: result
  });
} catch (error) {
  // 错误处理
  if (error instanceof HTTPException) {
    c.set("response", {
      code: error.status,
      message: error.message,
      data: null
    });
  } else {
    console.error("会议管理API错误:", error);
    c.set("response", {
      code: 500,
      message: "服务器内部错误",
      data: null
    });
  }
}
```

## 数据库操作设计

### 基础查询模式

遵循项目现有数据库操作模式：

```typescript
// 使用 @repo/database 的 db 实例
import { db } from "@repo/database";

// 创建会议
const createMeeting = async (data: CreateMeetingData) => {
  return await db.meet.create({
    data: {
      ...data,
      createdBy: userId,
      organizationId: organizationId,
      status: "SCHEDULED"
    }
  });
};

// 查询会议列表
const getMeetingsList = async (organizationId: string, filters: any) => {
  const whereClause = {
    organizationId,
    ...(filters.status && { status: filters.status }),
    ...(filters.meetingType && { meetingType: filters.meetingType }),
    ...(filters.search && {
      title: {
        contains: filters.search,
        mode: 'insensitive'
      }
    })
  };

  const [meetings, total] = await Promise.all([
    db.meet.findMany({
      where: whereClause,
      orderBy: {
        [filters.sortBy]: filters.sortOrder
      },
      skip: (filters.page - 1) * filters.limit,
      take: filters.limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    }),
    db.meet.count({ where: whereClause })
  ]);

  return { meetings, total };
};

// 查询会议详情
const getMeetingDetail = async (meetingId: string, organizationId: string) => {
  return await db.meet.findFirst({
    where: {
      id: meetingId,
      organizationId
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      organization: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });
};
```

## 路由实现示例

### 主路由文件

```typescript
// packages/api/src/routes/meet/router.ts
/**
 * 会议管理主路由
 * 聚合所有会议管理相关的子路由
 *
 * <AUTHOR>
 * @date 2025-01-12 15:30:00
 * @updated 2025-01-12 15:30:00 hayden 根据会议管理API设计创建主路由
 * @description 会议管理模块主路由，包括会议CRUD功能
 */
import { Hono } from "hono";
import { meetingCreateRouter } from "./create";
import { meetingListRouter } from "./list";
import { meetingDetailRouter } from "./detail";
import { meetingUpdateRouter } from "./update";
import { meetingDeleteRouter } from "./delete";

/**
 * 会议管理路由器
 */
export const meetRouter = new Hono()
  .basePath("/meet")
  .route("/", meetingCreateRouter)
  .route("/", meetingListRouter)
  .route("/", meetingDetailRouter)
  .route("/", meetingUpdateRouter)
  .route("/", meetingDeleteRouter);
```

### 类型定义文件

```typescript
// packages/api/src/routes/meet/types.ts
/**
 * 会议管理模块类型定义
 *
 * <AUTHOR>
 * @date 2025-01-12 15:30:00
 * @updated 2025-01-12 15:30:00 hayden 根据会议管理API设计创建类型定义
 * @description 会议管理模块的所有TypeScript类型定义，包括请求和响应类型
 */
import type { z } from "zod";
import type {
  CreateMeetingSchema,
  ListMeetingSchema,
  DetailMeetingSchema,
  UpdateMeetingSchema,
  DeleteMeetingSchema,
} from "./lib/validators";

// 请求类型
export type CreateMeetingRequest = z.infer<typeof CreateMeetingSchema>;
export type ListMeetingRequest = z.infer<typeof ListMeetingSchema>;
export type DetailMeetingRequest = z.infer<typeof DetailMeetingSchema>;
export type UpdateMeetingRequest = z.infer<typeof UpdateMeetingSchema>;
export type DeleteMeetingRequest = z.infer<typeof DeleteMeetingSchema>;

// 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface MeetingListResponse {
  meetings: Array<{
    id: string;
    title: string;
    description?: string;
    status: string;
    meetingType?: string;
    scheduledStartTime: string;
    scheduledEndTime: string;
    location?: string;
    locationUrl?: string;
    maxParticipants?: number;
    isPrivate: boolean;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

```
*本文档为会议管理模块提供完整的数据库设计，支持核心会议功能并预留了未来扩展空间。*