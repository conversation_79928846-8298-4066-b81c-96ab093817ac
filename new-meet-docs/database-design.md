# 会议管理模块数据库设计

## 概述

本文档为会议管理模块（meet）提供专门的数据库设计，专注于核心会议功能：会议管理页面、会议列表展示、会议详情页面展示。基于现有的 Prisma schema 进行设计，与现有数据库结构保持一致。

## 设计原则

1. **一致性**：基于现有的 Prisma schema 和数据库结构
2. **简洁性**：仅包含核心会议功能所需的数据结构
3. **独立性**：使用新的 `meet` 相关数据表，与现有 `meeting` 模块分离
4. **可扩展性**：为未来功能预留扩展空间

## 数据库模型设计

### 1. Meet 表 - 会议核心表

基于现有的 Prisma schema，会议表需要遵循现有的命名约定和字段模式，专注于核心会议数据的存储：

```prisma
// 会议基本信息表 - 新增到现有 schema.prisma 中
model Meet {
  id                 String        @id @default(cuid()) // 会议ID，主键，自动生成
  title              String        // 会议标题（必填）
  description        String?      // 会议描述（可选）
  status             MeetStatus   @default(SCHEDULED) // 会议状态，默认为已安排
  meetingType        MeetType?    // 会议类型（可选）
  scheduledStartTime DateTime      // 计划开始时间（必填）
  scheduledEndTime   DateTime      // 计划结束时间（必填）
  actualStartTime    DateTime?    // 实际开始时间（可选）
  actualEndTime      DateTime?    // 实际结束时间（可选）
  location           String?      // 会议地点（可选）
  locationType       LocationType? // 会议地点类型（可选）
  locationUrl        String?      // 线上会议链接（可选）
  maxParticipants    Int?         // 最大参会人数（可选）
  isPrivate          Boolean      @default(false) // 是否私密会议，默认为false
  password           String?      // 会议密码（可选）
  createdBy          String       // 创建人ID，关联 User 表
  user               User         @relation(fields: [createdBy], references: [id], onDelete: Cascade) // 关联用户，级联删除
  organizationId     String       // 组织ID，关联 Organization 表
  organization       Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  createdAt         DateTime      @default(now()) // 创建时间，默认为当前时间
  updatedAt         DateTime      @updatedAt // 更新时间，自动更新


  @@index([status]) // 状态索引
  @@index([createdBy]) // 创建者索引
  @@index([organizationId]) // 组织ID索引
  @@index([scheduledStartTime]) // 计划开始时间索引
  @@index([meetingType]) // 会议类型索引
  @@index([createdAt]) // 创建时间索引
  @@map("meet") // 映射到数据库表"meet"
}
```

### 2. 枚举类型定义

需要在 schema.prisma 中添加以下枚举类型：

```prisma
// 会议状态枚举
enum MeetStatus {
  SCHEDULED  // 已安排
  ONGOING    // 进行中
  COMPLETED  // 已完成
  CANCELLED  // 已取消
}

// 会议类型枚举
enum MeetType {
  BOARD        // 董事会会议
  SHAREHOLDER  // 股东大会
  GENERAL      // 普通会议
  COMMITTEE     // 委员会会议
  EXECUTIVE    // 执行会议
  EMERGENCY    // 紧急会议
}
```

## 数据关系图

```
┌─────────────┐       ┌─────────────────────┐
│  User       │       │   Organization      │
│─────────────│       │─────────────────────│
│ id (PK)     │◄──────┤ id (PK)             │
│ ...         │       │ ...                 │
└─────────────┘       └─────────────────────┘
       ▲                        ▲
       │                        │
       └────────────────────────┼─────────────────────────┘
                                │
                    ┌────────────┼─────────────┐
                    │           │             │
        ┌───────────┴──────┐ ┌───┴──────────┐ ┌─┴──────────────┐
        │     Meet        │ │ MeetMetadata   │                    │
        │─────────────────│ │───────────────│                    │
        │ id (PK)         │ │ id (PK)       │                    │
        │ title           │ │ meetId (FK)   │                    │
        │ description     │ │ key           │                    │
        │ status          │ │ value         │                    │
        │ ...             │ │ ...           │                    │
        └─────────────────┘                 │
                           └──────────────────┘
```

## 数据验证规则

### 必填字段
- `meet.title`: 非空
- `meet.status`: 非空，默认值 'SCHEDULED'
- `meet.createdBy`: 非空
- `meet.organizationId`: 非空
- `meet.scheduledStartTime`: 非空
- `meet.scheduledEndTime`: 非空

### 时间验证
- `meet.scheduledEndTime` 必须 >= `meet.scheduledStartTime`
- `meet.actualEndTime` 必须 >= `meet.actualStartTime`

### 数字验证
- `meet.maxParticipants` 必须 >= 0

### 业务规则
1. **会议状态流转**
   - `SCHEDULED` → `ONGOING` → `COMPLETED`
   - `SCHEDULED` → `CANCELLED`
   - 不允许从 `CANCELLED` 状态转回其他状态

2. **权限控制**
   - 只有会议创建者可以删除会议
   - 管理员可以查看组织内所有会议
   - 普通用户只能查看自己参与的会议

## 性能优化

### 索引策略
1. **查询优化索引**
   - 按状态查询：`idx_meet_status`
   - 按创建者查询：`idx_meet_created_by`
   - 按组织查询：`idx_meet_organization_id`
   - 按时间查询：`idx_meet_scheduled_start_time`
   - 按类型查询：`idx_meet_meeting_type`

2. **复合索引**
   - 按组织+状态：`idx_meet_organization_status`
   - 按创建者+时间：`idx_meet_created_by_created_at`

### 分页策略

```sql
-- 默认分页查询
SELECT m.* 
FROM meet m 
WHERE m.organizationId = ? 
ORDER BY m.scheduledStartTime DESC
LIMIT 20 OFFSET 0;

-- 性能优化分页（使用游标分页）
SELECT m.*
FROM meet m
WHERE m.organizationId = ?
  AND m.scheduledStartTime < ?  -- 游标值
ORDER BY m.scheduledStartTime DESC
LIMIT 20;
```

## 扩展性设计

### 未来功能预留

1. **录音和转写功能**
   - 预留 `meetRecording` 和 `meetTranscript` 表
   - 预留音频/视频文件存储路径字段
   - 预留转写文本和说话人识别字段

2. **实时会议功能**
   - 预留会议房间号和服务器地址字段
   - 预留实时聊天和屏幕共享字段

3. **会议分析功能**
   - 预留会议统计数据字段
   - 预留参会时长和参与度字段

## 安全考虑

1. **数据隔离**
   - 通过 `organizationId` 确保组织间数据隔离

2. **访问控制**
   - 所有查询需要验证用户权限
   - 敏感操作需要身份验证

3. **数据完整性**
   - 使用外键约束确保数据一致性
   - 使用事务确保数据操作的原子性

## 监控和维护

### 数据统计

```sql
-- 会议状态统计
SELECT status, COUNT(*) as count
FROM meet
WHERE organizationId = ? 
GROUP BY status;

-- 会议数量统计
SELECT 
  DATE_TRUNC('month', scheduledStartTime) as month,
  COUNT(*) as count
FROM meet
WHERE organizationId = ? 
GROUP BY month
ORDER BY month;
```

### 定期维护

1. **数据归档**
   - 定期将已完成的历史会议数据归档

2. **性能监控**
   - 监控查询性能和索引使用情况
   - 定期更新统计信息

3. **数据备份**
   - 定期备份 meet 相关数据
   - 确保数据安全和可恢复性

---

*本文档为会议管理模块提供完整的数据库设计，支持核心会议功能并预留了未来扩展空间。*