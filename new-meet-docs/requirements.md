# 会议管理模块 (meet) 需求文档

## 1. 概述

本需求文档描述了全新的会议管理模块(meet)的功能需求。该模块将提供一个现代化的会议创建、管理和查看界面，采用单列卡片布局和覆盖式详情页面，提供良好的用户体验。

## 1.1 技术实现要求

**数据层**:
- 需要新增会议相关数据表
- 提供完整的RESTful API接口
- 实现数据持久化和CRUD操作

**UI层**:
- 使用shadcn/ui组件库构建界面
- 使用Radix UI作为底层组件库
- 遵循项目现有的颜色规范和设计规范
- 禁止全新设计组件，必须使用现有组件组合

**API层**:
- 新建会议相关的API路由
- 实现数据验证和错误处理
- 遵循项目的API设计模式

## 2. 功能需求

### 2.1 会议管理页面
**用户故事**: 作为系统用户，我希望进入会议管理页面，能够看到会议主题和会议列表，以便我可以方便地查看和管理我的会议。

**验收标准**:
- **Given** 我已登录系统
- **When** 我点击会议按钮
- **Then** 我应该进入会议管理页面
- **And** 页面顶部应该显示会议主题
- **And** 页面左上角应该有筛选按钮
- **And** 页面右上角应该有预定会议按钮

### 2.2 会议列表展示
**用户故事**: 作为会议用户，我希望以卡片形式查看所有会议，按开始时间倒序排列，以便我能快速找到最新的会议。

**验收标准**:
- **Given** 我在会议管理页面
- **When** 查看会议列表区域
- **Then** 会议应该以单列卡片方式呈现
- **And** 每个会议卡片应显示：会议主题、会议时间、会议创建者姓名
- **And** 会议应该按开始时间倒序排列（最新的在最上方）
- **And** 点击会议卡片应该打开会议详情页面

### 2.3 会议详情页面
**用户故事**: 作为会议参与者，我希望查看会议的详细信息，包括基本信息和功能区域，以便我了解会议的全部内容。

**验收标准**:
- **Given** 我点击了一个会议卡片
- **When** 会议详情页面打开
- **Then** 页面应该以覆盖方式显示在当前页面上
- **And** 页面头部应该包含返回按钮（左侧）和删除按钮（右侧...菜单中）
- **And** 基本信息区应该显示：会议主题、会议时间、创建人员头像和名称
- **And** 基本信息区的会议主题和时间应该可编辑
- **And** 基本信息区下方应该有两个tab：会议信息和会议人员

### 2.4 会议信息Tab
**用户故事**: 作为会议组织者，我希望填写和管理会议的详细信息，包括类型、内容、地点等，以便 participants 了解会议安排。

**验收标准**:
- **Given** 我在会议详情页面的会议信息tab
- **When** 查看会议信息表单
- **Then** 应该显示以下字段：
  - 会议类型（下拉菜单）：业绩说明会、策略会、一对一交流会、一对多交流会、股东大会、闭门会议、产品发布会、重大事项披露会议、可转债路演会议、反路演会议、常规会议
  - 会议内容（文本区域，可选）
  - 会议地点（文本输入，可选）
  - 会议链接（文本输入，可选）
  - 接待人姓名（文本输入，可选）
  - 接待人联系方式（文本输入，可选）
  - 会议备注（文本区域，可选）
  - 会议号（自动生成，只读）
- **And** 所有可选字段默认为空
- **And** 会议号应该自动生成且不可编辑

### 2.5 会议人员Tab
**用户故事**: 作为会议组织者，我希望管理参会人员信息，以便了解谁将参加会议。

**验收标准**:
- **Given** 我在会议详情页面的会议人员tab
- **When** 查看会议人员tab
- **Then** 应该显示"开发中"文字
- **And** 该tab暂时不提供功能

### 2.6 会议预定功能
**用户故事**: 作为系统用户，我希望快速创建新会议，系统自动预填基本信息，节省我的时间。

**验收标准**:
- **Given** 我在会议管理页面
- **When** 我点击预定会议按钮
- **Then** 应该打开会议详情页面
- **And** 系统应该自动预填以下信息：
  - 会议主题：{用户名}预定的会议
  - 会议类型：常规会议
  - 会议开始时间：当前时间未来最近的30分钟或整点
  - 会议结束时间：开始时间后30分钟
  - 创建人员：当前用户信息（头像和姓名）
  - 会议号：自动生成
- **And** 系统应该显示通知提示"已创建预定会议"
- **And** 用户可以修改预填的信息
- **And** 修改后应该自动保存

## 3. 非功能需求

### 3.1 性能需求
- 会议列表加载时间不应超过2秒
- 会议详情页面打开动画应该流畅
- 支持至少100个会议的同时显示

### 3.2 用户体验需求
- 界面应该符合项目的颜色规范和设计规范
- 所有交互元素都应该有适当的视觉反馈
- 编辑状态应该有明确的视觉指示
- 错误和成功操作应该有相应的通知提示

### 3.3 技术需求
- 使用shadcn/ui和Radix UI组件库
- 遵循项目的TypeScript和React编码规范
- 使用现有的状态管理方案（Jotai + React Query）
- 遵循项目的数据库设计模式

### 3.4 数据需求
- 需要设计会议相关的数据表结构
- 需要提供完整的CRUD API接口
- 需要实现会议数据的持久化存储

## 4. 边界条件和特殊情况

### 4.1 时间处理
- 会议时间应该支持时区选择
- 应该验证会议时间的有效性（结束时间必须晚于开始时间）
- 过去时间的会议应该显示不同的状态标识

### 4.2 数据验证
- 会议主题不能为空
- 会议时间必须选择
- 会议类型必须选择
- 会议号必须唯一

### 4.3 错误处理
- 网络请求失败应该有友好的错误提示
- 数据验证失败应该显示具体的错误信息
- 系统异常应该有相应的错误处理机制

## 5. 成功标准

- 用户能够成功创建、查看和管理会议
- 界面响应迅速，用户体验良好
- 所有功能都能正常工作，没有明显的bug
- 代码质量符合项目标准
- 功能测试通过率达到95%以上

## 6. ASCII 原型图

### 6.1 会议管理页面布局
```
┌─────────────────────────────────────────────────────────────┐
│                        会议管理页面                          │
├─────────────────────────────────────────────────────────────┤
│                                                         │
│  [筛选按钮]                      会议管理页面         [预定会议] │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │             会议列表区域                             │ │
│  │                                                     │ │
│  │   ┌─────────────────┐  ┌─────────────────┐        │ │
│  │   │   会议卡片1     │  │   会议卡片2     │        │ │
│  │   │  主题:季度会议  │  │  主题:策略会    │        │ │
│  │   │  时间:2024-01-15│  │  时间:2024-01-10│        │ │
│  │   │  创建者:张三    │  │  创建者:李四    │        │ │
│  │   └─────────────────┘  └─────────────────┘        │ │
│  │                                                     │ │
│  │   ┌─────────────────┐  ┌─────────────────┐        │ │
│  │   │   会议卡片3     │  │   会议卡片4     │        │ │
│  │   │  主题:产品发布会│  │  主题:股东大会   │        │ │
│  │   │  时间:2024-01-08│  │  时间:2024-01-05│        │ │
│  │   │  创建者:王五    │  │  创建者:赵六    │        │ │
│  │   └─────────────────┘  └─────────────────┘        │ │
│  │           ... (更多会议卡片单列布局)                │ │
│  │                                                     │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                         │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 会议详情页面覆盖效果
```
┌─────────────────────────────────────────────────────────────┐
│                         会议管理页面                          │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                     会议详情页面                         │ │
│  │                                                         │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │                    头部区域                           │ │ │
│  │ │ [←]  会议详情                [•••]  [🗑️]             │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │                    基本信息区                       │ │ │
│  │ │ 可编辑:  季度会议                                  │ │ │
│  │ │ 可编辑:  2024-01-15 10:00-11:00 (UTC+8)            │ │ │
│  │ │ 自动获取:  👤 张三                               │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │                    功能区域                           │ │ │
│  │ │  [会议信息]  [会议人员]                             │ │ │
│  │ │                                                     │ │ │
│  │ │ ┌─────────────────────────────────────────────────┐ │ │
│  │ │ │                会议信息内容                       │ │ │
│  │ │ │ 会议类型: [常规会议▼]                           │ │ │
│  │ │ │ 会议内容: [可选文本区域]                        │ │ │
│  │ │ │ 会议地点: [可选输入框]                          │ │ │
│  │ │ │ 会议链接: [可选输入框]                          │ │ │
│  │ │ │ 接待人姓名: [可选输入框]                        │ │ │
│  │ │ │ 接待人联系方式: [可选输入框]                    │ │ │
│  │ │ │ 会议备注: [可选文本区域]                        │ │ │
│  │ │ │ 会议号: MT20240115001 (自动生成)                │ │ │
│  │ │ └─────────────────────────────────────────────────┘ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 6.3 会议卡片设计
```
┌─────────────────────────────────────────────────────────┐
│                       会议卡片                            │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  📅 季度会议                                            │
│                                                         │
│  时间: 2024-01-15 10:00-11:00                          │
│  创建者: 张三                                          │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 6.4 Tab切换设计
```
┌─────────────────────────────────────────────────────────┐
│                       Tab区域                           │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  [会议信息] ────────────── [会议人员]                  │
│  ████                                                    │
│                                                         │
│  会议信息内容区域...                                     │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

