import type { Context, MiddlewareHandler, Next } from "hono";
import { HTTPException } from "hono/http-exception";
import axios, { type AxiosResponse, type AxiosError } from "axios";
import { logger } from "@repo/logs";

/**
 * n8n代理中间件配置接口
 */
export interface N8nProxyMeetingConfig {
  baseUrl: string;      // n8n服务器基础URL
  timeout?: number;     // 请求超时时间（毫秒），默认30秒
}

/**
 * 代理响应接口
 */
export interface ProxyMeetingResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 扩展Hono的Variables类型
declare module 'hono' {
  interface ContextVariableMap {
    proxyMeetingResponse: ProxyMeetingResponse;
  }
}

/**
 * 创建n8n代理中间件
 * 简化版本，跳过加密解密，直接转发请求到内网n8n服务
 */
export function createN8nProxyMeetingMiddleware(config: N8nProxyMeetingConfig): MiddlewareHandler {
  const { baseUrl, timeout = 30000 } = config;

  return async (c: Context, next: Next) => {
    try {
      // 获取请求路径，移除 /api/n8n_meeting_proxy 前缀
      const originalPath = c.req.path;
      const targetPath = originalPath.replace("/api/n8n_meeting_proxy", "");
      
      // 构建目标URL
      const targetUrl = `${baseUrl}${targetPath}`;
      
      // 获取请求方法
      const method = c.req.method;
      
      // 获取查询参数
      const url = new URL(c.req.url);
      const queryParams = url.searchParams.toString();
      const finalUrl = queryParams ? `${targetUrl}?${queryParams}` : targetUrl;

      // 获取请求头（过滤掉一些不需要转发的头）
      const headers: Record<string, string> = {};
      c.req.raw.headers.forEach((value, key) => {
        // 过滤掉一些不应该转发的头
        const lowerKey = key.toLowerCase();
        if (!['host', 'connection', 'content-length', 'transfer-encoding'].includes(lowerKey)) {
          headers[key] = value;
        }
      });

      // 获取请求体
      let requestBody: any = undefined;
      if (['POST', 'PUT', 'PATCH'].includes(method)) {
        try {
          const contentType = c.req.header('content-type') || '';
          if (contentType.includes('application/json')) {
            requestBody = await c.req.json();
          } else if (contentType.includes('application/x-www-form-urlencoded')) {
            requestBody = await c.req.text();
          } else if (contentType.includes('multipart/form-data')) {
            requestBody = await c.req.formData();
          } else {
            requestBody = await c.req.text();
          }
        } catch (error) {
          logger.warn("Failed to parse request body", { error, method, targetUrl });
        }
      }

      logger.info("N8N Proxy Request", {
        method,
        originalPath,
        targetPath,
        targetUrl: finalUrl,
        hasBody: !!requestBody
      });

      // 发送代理请求
      let response: AxiosResponse;
      try {
        response = await axios({
          method: method.toLowerCase() as any,
          url: finalUrl,
          data: requestBody,
          headers,
          timeout,
          validateStatus: () => true, // 接受所有状态码
        });
      } catch (error) {
        const axiosError = error as AxiosError;
        
        // 处理网络错误和超时
        if (axiosError.code === 'ECONNABORTED' || axiosError.code === 'ETIMEDOUT') {
          logger.error("N8N Proxy Timeout", { 
            method, 
            targetUrl: finalUrl, 
            timeout,
            error: axiosError.message 
          });
          
          c.set("proxyMeetingResponse", {
            code: 504,
            message: "请求超时",
            data: null
          });
          
          await next();
          return;
        }

        // 处理连接错误
        if (axiosError.code === 'ECONNREFUSED' || axiosError.code === 'ENOTFOUND') {
          logger.error("N8N Proxy Connection Error", { 
            method, 
            targetUrl: finalUrl,
            error: axiosError.message 
          });
          
          c.set("proxyMeetingResponse", {
            code: 502,
            message: "无法连接到n8n服务",
            data: null
          });
          
          await next();
          return;
        }

        // 处理其他错误
        logger.error("N8N Proxy Request Error", { 
          method, 
          targetUrl: finalUrl,
          error: axiosError.message,
          code: axiosError.code
        });
        
        c.set("proxyMeetingResponse", {
          code: 500,
          message: "代理请求失败",
          data: null
        });
        
        await next();
        return;
      }

      // 记录响应日志
      logger.info("N8N Proxy Response", {
        method,
        targetUrl: finalUrl,
        status: response.status,
        statusText: response.statusText,
        responseSize: JSON.stringify(response.data).length
      });

      // 设置代理响应
      c.set("proxyMeetingResponse", {
        code: response.status,
        message: response.status >= 200 && response.status < 300 ? "操作成功" : response.statusText || "请求失败",
        data: response.data
      });

      await next();
      
    } catch (error) {
      logger.error("N8N Proxy Middleware Error", { 
        error: error instanceof Error ? error.message : String(error),
        path: c.req.path,
        method: c.req.method
      });
      
      c.set("proxyMeetingResponse", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      
      await next();
    }
  };
}

/**
 * 创建简单的n8n代理处理器
 * 直接返回代理结果，不进行加密处理
 */
export function createSimpleN8nMeetingProxy(baseUrl: string, timeout?: number) {
  const proxyMiddleware = createN8nProxyMeetingMiddleware({ baseUrl, timeout });
  
  return async (c: Context) => {
    // 应用代理中间件
    await proxyMiddleware(c, async () => {});
    
    // 获取代理响应
    const proxyMeetingResponse = c.get("proxyMeetingResponse");

    if (!proxyMeetingResponse) {
      return c.json({
        code: 500,
        message: "代理响应未设置",
        data: null
      }, 500);
    }

    // 直接返回代理结果
    return c.json(proxyMeetingResponse, proxyMeetingResponse.code >= 100 && proxyMeetingResponse.code < 600 ? proxyMeetingResponse.code as any : 500);
  };
}
