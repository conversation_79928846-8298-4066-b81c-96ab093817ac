import { Hono } from "hono";
import { proxy } from "hono/proxy";
import type { Context } from "hono";
import { shareholderCryptoMiddleware } from "./shareholder-crypto";
import { authMiddleware } from "./auth";

/**
 * n8n代理中间件配置接口
 * 
 * <AUTHOR>
 * @date 2025-06-15 01:00:22
 * @description 定义n8n代理服务的基础配置参数
 */
interface N8nProxyConfig {
  /** n8n服务器基础URL地址 */
  baseUrl: string;
  /** 请求超时时间(毫秒)，默认30秒 */
  timeout?: number;
}

/**
 * 创建n8n代理中间件
 * 
 * <AUTHOR>
 * @date 2025-06-15 01:00:22
 * @description 创建一个完整的n8n代理中间件，包含认证、加解密和代理转发功能
 * @param config n8n代理配置对象
 * @returns 返回配置好的Hono应用实例
 * 
 * @security
 * - 需要用户登录认证
 * - 请求和响应数据自动加解密
 * - 时间戳验证防重放攻击
 * 
 * @example
 * ```typescript
 * const proxyApp = createN8nProxyMiddleware({
 *   baseUrl: "http://*************:5678/webhook/v1",
 *   timeout: 30000
 * });
 * ```
 */
export function createN8nProxyMiddleware(config: N8nProxyConfig): Hono {
  const app = new Hono();
  
  // 设置默认超时时间
  const timeout = config.timeout || 60000; // 30秒默认超时
  
  // 应用认证中间件 - 验证用户身份
  app.use("*", authMiddleware);
  
  // 应用加解密中间件 - 处理请求解密和响应加密
  app.use("*", shareholderCryptoMiddleware());
  
  /**
   * 代理n8n请求到n8n服务器
   *
   * <AUTHOR>
   * @date 2025-06-15 01:00:22
   * @description 处理n8n代理路径下的所有HTTP方法请求，支持GET、POST、PUT、DELETE等
   * 只处理通过 /n8n_proxy 路由进来的请求，不影响其他API
   */
  app.all("/*", async (c: Context) => {
    try {
      // 获取解密后的请求数据（由加密中间件处理）
      const requestData = c.get("requestData");
      
      // 构建目标URL - 需要移除/api/n8n_proxy前缀，只保留实际的webhook路径
      // 修改记录 2025-06-15 14:48:28 hayden: 修复路径构建逻辑，移除API前缀
      // 原代码保留作为注释: const originalPath = c.req.path;
      const originalPath = c.req.path;
      // 移除 /api/n8n_proxy 前缀，只保留实际的webhook路径
      const webhookPath = originalPath.replace(/^\/api\/n8n_proxy/, '') || '/';
      const targetUrl = `${config.baseUrl}${webhookPath}`;
      
      // 获取查询参数
      const searchParams = c.req.url.split('?')[1];
      const finalUrl = searchParams ? `${targetUrl}?${searchParams}` : targetUrl;
      
      // 准备请求头
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        "User-Agent": "n8n-proxy-middleware/1.0.0",
      };
      
      // 如果有认证用户信息，可以添加到请求头
      const user = c.get("user");
      if (user?.id) {
        headers["X-Proxy-User-Id"] = user.id;
      }
      
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      try {
			// 使用Hono proxy转发请求
			// 修改记录 2025-06-15 14:46:44 hayden: 修复body参数类型，使用JSON字符串格式
			const response = await proxy(finalUrl, {
				method: c.req.method as any,
				body: requestData ? JSON.stringify(requestData) : undefined,
				// JSON.stringify({
				//   "companyCode": "600000",
				//   "analysisStartDate": "2024-01-01",
				//   "analysisEndDate": "2024-06-30",
				//   "shareholderCategory": "全部",
				//   "topN": 10
				// }),
				headers,
				signal: controller.signal,
			});

			// 清除超时定时器
			clearTimeout(timeoutId);

			// 解析响应数据
			let responseData: any = null;
			const contentType = response.headers.get("content-type");

			if (contentType?.includes("application/json")) {
				try {
					responseData = await response.json();
				} catch (parseError) {
					// JSON解析失败，使用文本内容
					responseData = await response.text();
				}
			} else {
				// 非JSON响应，获取文本内容
				responseData = await response.text();
			}

			// 设置响应数据供加密中间件处理
			c.set("response", {
				code: response.ok ? 200 : response.status,
				message: response.ok
					? "n8n代理请求成功"
					: `n8n代理请求失败: ${responseData.message}`,
				data: responseData,
			});
		} catch (proxyError: any) {
        // 清除超时定时器
        clearTimeout(timeoutId);
        
        // 判断错误类型
        const isTimeout = proxyError.name === "AbortError" || 
                         proxyError.name === "TimeoutError" ||
                         proxyError.message?.includes("timeout");
        
        const isNetworkError = proxyError.name === "TypeError" ||
                              proxyError.message?.includes("fetch");
        
        // 设置错误响应
        if (isTimeout) {
          c.set("response", {
            code: 504,
            message: "n8n服务请求超时，请稍后重试",
            data: null
          });
        } else if (isNetworkError) {
          c.set("response", {
            code: 502,
            message: "无法连接到n8n服务器，请检查网络连接",
            data: null
          });
        } else {
          c.set("response", {
            code: 500,
            message: `n8n代理服务内部错误: ${proxyError.message}`,
            data: null
          });
        }
      }
      
    } catch (error: any) {
      // 处理整体请求错误
      console.error("n8n代理中间件错误:", error);
      
      c.set("response", {
        code: 500,
        message: "代理服务器内部错误1",
        data: null
      });
    }
  });
  
  return app;
}

/**
 * 创建简化的n8n代理中间件
 * 
 * <AUTHOR>
 * @date 2025-06-15 01:00:22
 * @description 创建一个简化版本的n8n代理，使用默认配置
 * @param baseUrl n8n服务器基础URL
 * @returns 返回配置好的Hono应用实例
 * 
 * @example
 * ```typescript
 * const simpleProxy = createSimpleN8nProxy("http://localhost:5678/webhook/v1");
 * ```
 */
export function createSimpleN8nProxy(baseUrl: string): Hono {
  return createN8nProxyMiddleware({
    baseUrl,
    timeout: 60000 * 2
  });
}
