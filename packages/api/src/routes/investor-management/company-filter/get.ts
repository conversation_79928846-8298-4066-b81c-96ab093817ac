/**
 * 获取公司筛选配置路由
 *
 * <AUTHOR>
 * @date 2025-07-09 20:53:57
 * @updated 2025-07-09 20:53:57 hayden 根据投资人管理模块需求创建获取公司筛选配置接口
 * @updated 2025-07-10 09:29:29 hayden 修复shareholderCryptoMiddleware数据获取问题，使用c.get("requestData")替代直接解析请求
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { GetCompanyFilterSchema } from "../lib/validators";

export const companyFilterGetRouter = new Hono().post(
	"/get",
	authMiddleware,
	shareholderCryptoMiddleware(),
	async (c) => {
		try {
			// 获取经过shareholderCryptoMiddleware解密后的请求数据
			const requestData = c.get("requestData");

			// 参数验证
			const validationResult = GetCompanyFilterSchema.safeParse(requestData);
			if (!validationResult.success) {
				throw new HTTPException(400, { message: "请求参数无效" });
			}

			const { organizationId } = validationResult.data;

			// 查询公司筛选配置数据
			const companyFilter = await db.companyFilter.findUnique({
				where: { organizationId },
			});

			// 如果没有找到配置，返回空结果
			if (!companyFilter) {
				c.set("response", {
					code: 200,
					message: "未找到公司筛选配置",
					data: {
						companyFilter: null,
					},
				});
				return;
			}

			// 格式化公司筛选配置数据
			const formattedCompanyFilter = {
				id: companyFilter.id,
				organizationId: companyFilter.organizationId,
				companyCode: companyFilter.companyCode,
				benchmarkCompanyCodes: companyFilter.benchmarkCompanyCodes,
				modifiedAt: companyFilter.modifiedAt.toISOString(),
			};

			// 设置响应数据，由shareholderCryptoMiddleware处理加密
			c.set("response", {
				code: 200,
				message: "获取公司筛选配置成功",
				data: {
					companyFilter: formattedCompanyFilter,
				},
			});
			return;

		} catch (error) {
			if (error instanceof HTTPException) {
				c.set("response", {
					code: error.status,
					message: error.message,
					data: null,
				});
				return;
			}

			console.error("投资人管理API错误:", error);
			c.set("response", {
				code: 500,
				message: "服务器内部错误",
				data: null,
			});
			return;
		}
	}
);
