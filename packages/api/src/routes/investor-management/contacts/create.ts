/**
 * 创建投资人联系人路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人创建接口
 * @updated 2025-07-31 11:04:58 hayden 新增基金代码和一码通ID字段支持，实现股东名册集成功能
 * @description 创建投资人联系人信息，支持基金投资者和个人投资者分类管理，支持股东名册自动创建联系人
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { CreateInvestorContactSchema } from "../lib/validators";
import { queryFundCodeByShareholderName } from "../lib/fund-utils";

export const contactsCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateInvestorContactSchema.safeParse(requestData);
      if (!validationResult.success) {
        // 返回更详细的错误信息
        const errorMessage = validationResult.error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new HTTPException(400, { message: errorMessage || "请求参数无效" });
      }

      const {
        organizationId, name, phoneNumber, email, address, remarks,
        fundCode, unifiedAccountId // 新增字段
      } = validationResult.data;

      // 股东名册创建联系人的自动化处理
      let finalFundCode = fundCode;

      // 判断是否需要进行股东名册查询：
      // 1. 提供了一码通ID但没有提供基金代码
      // 2. 或者明确要求进行股东名册查询
      if (unifiedAccountId && !fundCode) {
        try {
          // 1. 根据一码通账号查询股东名称
          const shareholder = await db.shareholder.findFirst({
            where: {
              unifiedAccountNumber: unifiedAccountId,
              organizationId
            },
            select: { securitiesAccountName: true }
          });

          if (shareholder?.securitiesAccountName) {
            // 2. 根据股东名称查询基金代码
            const queriedFundCode = await queryFundCodeByShareholderName(
              shareholder.securitiesAccountName
            );

            if (queriedFundCode) {
              finalFundCode = queriedFundCode;
            }
          }
        } catch (error) {
          // 记录错误但不阻止创建流程
          console.warn('股东名册基金查询失败:', error);
        }
      }

      // 创建联系人记录
      const newContact = await db.investorContact.create({
        data: {
          organizationId,
          name,
          phoneNumber: phoneNumber || null,
          email: email || null,
          address: address || null,
          remarks: remarks || null,
          fundCode: finalFundCode || null, // 可能是自动查询的基金代码
          unifiedAccountId: unifiedAccountId || null,
          createdBy: user.id,
        }
      });

      c.set("response", {
        code: 200,
        message: "联系人创建成功",
        data: {
          contactId: newContact.contactId
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
