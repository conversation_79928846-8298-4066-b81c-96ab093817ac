/**
 * 查询投资人联系人列表路由
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建联系人列表查询接口
 * @updated 2025-07-09 19:06:43 hayden 添加手机号码和邮箱的搜索功能，支持模糊搜索
 * @updated 2025-07-31 11:04:58 hayden 新增基金代码和一码通ID查询支持，实现投资者类型筛选功能
 * @updated 2025-08-01 14:30:48 hayden 新增queryAllContacts参数支持查询组织下所有联系人，无需提供基金代码或一码通ID
 * @description 查询投资人联系人列表，支持基金代码和一码通ID精确查询，支持投资者类型识别和转换状态显示，支持查询组织下所有联系人
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { ListInvestorContactsSchema } from "../lib/validators";

export const contactsListRouter = new Hono().post(
  "/list",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");

      // 参数验证
      const validationResult = ListInvestorContactsSchema.safeParse(requestData);
      if (!validationResult.success) {
        // 返回更详细的错误信息
        const errorMessage = validationResult.error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new HTTPException(400, { message: errorMessage || "请求参数无效" });
      }

      const {
        organizationId, name, phoneNumber, email,
        fundCode, unifiedAccountId, // 主要查询参数：必须提供其中一个（除非queryAllContacts为true）
        queryAllContacts, // 新增：是否查询组织下所有联系人
        page, limit
      } = validationResult.data;

      // 构建查询条件
      const where = {
        organizationId,
        // 如果不是查询所有联系人，则添加标识符查询条件
        ...(!queryAllContacts && fundCode ? { fundCode: fundCode } : {}),
        ...(!queryAllContacts && unifiedAccountId ? { unifiedAccountId: unifiedAccountId } : {}),
        // 可选的辅助查询条件（适用于所有查询模式）
        ...(name ? { name: { contains: name } } : {}),
        ...(phoneNumber ? { phoneNumber: { contains: phoneNumber } } : {}),
        ...(email ? { email: { contains: email } } : {}),
      };

      // 查询数据
      const [total, contacts] = await Promise.all([
        db.investorContact.count({ where }),
        db.investorContact.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        })
      ]);

      c.set("response", {
        code: 200,
        message: "查询成功",
        data: {
          contacts: contacts.map(contact => ({
            contactId: contact.contactId,
            name: contact.name,
            phoneNumber: contact.phoneNumber,
            email: contact.email,
            address: contact.address,
            remarks: contact.remarks,
            fundCode: contact.fundCode, // 新增返回字段
            unifiedAccountId: contact.unifiedAccountId, // 新增返回字段
            isConverted: !!(contact.fundCode && contact.unifiedAccountId), // 新增：是否为转换投资者
            createdAt: contact.createdAt.toISOString(),
            updatedAt: contact.updatedAt ? contact.updatedAt.toISOString() : contact.createdAt.toISOString(),
            createdBy: contact.createdBy,
            updatedBy: contact.updatedBy || null,
          })),
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
