/**
 * 基金查询工具函数
 *
 * <AUTHOR>
 * @created 2025-07-31 11:04:58
 * @updated 2025-07-31 17:20:05 hayden 优化基金代码优先级规则，从包含A/a改为尾部携带A/a
 * @description 提供股东名称到基金代码的查询功能，支持托管人模糊匹配和基金代码优先级规则
 */
import { db } from "@repo/database";

/**
 * 根据股东名称查询基金代码
 * @param shareholderName 股东名称（证券账户名称）
 * @returns 基金代码或null
 * <AUTHOR>
 * @created 2025-07-31 11:04:58
 * @updated 2025-07-31 17:20:05 hayden 优化优先级规则，从包含A/a改为尾部携带A/a
 * @description 根据股东名称查询FundManagementCustody表，匹配基金全称并验证托管人，返回基金代码
 * @example
 * 输入: "中国银行股份有限公司－易方达医疗保健行业混合型证券投资基金"
 * 输出: "110023.OF" (优先选择尾部携带A的基金代码)
 */
export const queryFundCodeByShareholderName = async (
  shareholderName: string
): Promise<string | null> => {
  try {
    // 解析股东名称，提取托管人和基金全称
    // 格式：托管人－基金全称
    const parts = shareholderName.split('－');
    if (parts.length < 2) {
      return null; // 不是基金格式的股东名称
    }

    const custodianFullName = parts[0].trim(); // 托管人全称，如"中国银行股份有限公司"
    const fundFullName = parts[1].trim(); // 基金全称，如"易方达医疗保健行业混合型证券投资基金"

    // 查询基金管理和托管信息表
    // 1. 基金全称精确匹配
    // 2. 托管人模糊匹配（因为数据库中只有简称，如"中国银行"）
    const fundRecords = await db.fundManagementCustody.findMany({
      where: {
        fundFullName: {
          equals: fundFullName
        },
        custodian: {
          // 托管人模糊匹配：数据库中的简称应该包含在股东名称的托管人部分中
          // 例如：股东名称中的"中国银行股份有限公司"应该包含数据库中的"中国银行"
          not: null
        }
      },
      select: {
        fundCode: true,
        fundName: true,
        custodian: true
      }
    });

    if (fundRecords.length === 0) {
      return null;
    }

    // 进一步验证托管人匹配
    const matchedFunds = fundRecords.filter(fund => {
      if (!fund.custodian) {
        return false;
      }
      // 检查股东名称中的托管人部分是否包含数据库中的托管人简称
      return custodianFullName.includes(fund.custodian);
    });

    if (matchedFunds.length === 0) {
      return null;
    }

    // 优先级规则：尾部携带A或a的基金名称的代码优先
    // 修改记录：2025-07-31 17:20:05 hayden 优化优先级规则，从包含A/a改为尾部携带A/a
    // 原代码：fund.fundName.includes('A') || fund.fundName.includes('a')
    const priorityFund = matchedFunds.find(fund =>
      fund.fundName.endsWith('A') || fund.fundName.endsWith('a')
    );

    return priorityFund ? priorityFund.fundCode : matchedFunds[0].fundCode;
  } catch (error) {
    console.error('查询基金代码失败:', error);
    return null;
  }
};

/**
 * 验证一码通账号是否存在于股东表中
 * @param unifiedAccountId 一码通账号
 * @param organizationId 组织ID
 * @returns 股东信息或null
 * <AUTHOR>
 * @created 2025-07-31 11:04:58
 * @updated 2025-07-31 11:04:58 hayden 创建一码通账号验证函数
 * @description 验证一码通账号的有效性，返回对应的股东信息
 */
export const validateUnifiedAccount = async (
  unifiedAccountId: string,
  organizationId: string
): Promise<{ securitiesAccountName: string } | null> => {
  try {
    const shareholder = await db.shareholder.findFirst({
      where: {
        unifiedAccountNumber: unifiedAccountId,
        organizationId
      },
      select: {
        securitiesAccountName: true
      }
    });

    return shareholder;
  } catch (error) {
    console.error('验证一码通账号失败:', error);
    return null;
  }
};

/**
 * 按投资者标识查询联系人（主要查询方式）
 * @param organizationId 组织ID
 * @param fundCode 基金代码（可选）
 * @param unifiedAccountId 一码通账号（可选）
 * @returns 联系人列表
 * <AUTHOR>
 * @created 2025-07-31 11:04:58
 * @updated 2025-07-31 11:04:58 hayden 创建投资者标识查询函数
 * @description 通过基金代码或一码通账号查询联系人，这是查询联系人的主要方式，必须提供其中一个标识
 */
export const getContactsByIdentifier = async (
  organizationId: string,
  fundCode?: string,
  unifiedAccountId?: string
) => {
  // 验证至少提供一个查询标识
  if (!fundCode && !unifiedAccountId) {
    throw new Error('必须提供基金代码或一码通账号其中一个查询条件');
  }

  const where = {
    organizationId,
    // 基金代码查询（精确匹配）
    ...(fundCode ? { fundCode: fundCode } : {}),
    // 一码通账号查询（精确匹配）
    ...(unifiedAccountId ? { unifiedAccountId: unifiedAccountId } : {}),
  };

  return await db.investorContact.findMany({
    where,
    orderBy: { createdAt: "desc" }
  });
};
