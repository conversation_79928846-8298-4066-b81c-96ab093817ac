/**
 * 投资人管理模块验证器
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建验证器
 * @updated 2025-07-09 20:29:13 hayden 添加companyFilterId可选参数，支持收藏标签关联公司筛选配置
 * @updated 2025-07-09 20:33:23 hayden 修改companyFilterId为必需参数，确保所有标签都关联公司筛选配置
 * @updated 2025-07-11 09:39:13 hayden 添加公司代码格式验证：6位数字+点+大小写字母，支持多个代码逗号分隔
 * @updated 2025-07-31 11:04:58 hayden 新增基金代码和一码通ID字段验证，支持投资者类型识别和股东名册集成
 * @updated 2025-08-01 11:57:51 hayden 修改联系人创建和更新验证器，将手机号设置为必填字段
 * @updated 2025-08-01 14:30:48 hayden 修改ListInvestorContactsSchema验证器，新增queryAllContacts参数支持查询组织下所有联系人
 * @description 投资人管理模块的所有Zod验证器定义，包括公司筛选配置、投资人标签和联系人相关验证
 */
import { z } from "zod";

/**
 * 公司代码格式验证正则表达式
 * 格式：6位数字 + 点 + 大小写字母（如：000009.SZ, 000002.sz）
 * <AUTHOR>
 * @date 2025-07-11 09:39:13
 */
const COMPANY_CODE_REGEX = /^\d{6}\.[A-Za-z]+$/;

/**
 * 验证单个公司代码格式
 * @param code 公司代码字符串
 * @returns 是否符合格式要求
 * <AUTHOR>
 * @date 2025-07-11 09:39:13
 */
const validateCompanyCode = (code: string): boolean => {
  return COMPANY_CODE_REGEX.test(code.trim());
};

/**
 * 验证多个公司代码格式（逗号分隔）
 * @param codes 多个公司代码字符串，用逗号分隔
 * @returns 是否所有代码都符合格式要求
 * <AUTHOR>
 * @date 2025-07-11 09:39:13
 */
const validateMultipleCompanyCodes = (codes: string): boolean => {
  if (!codes || codes.trim() === "") {
    return true; // 空字符串视为有效（可选字段）
  }

  const codeArray = codes.split(",").map(code => code.trim()).filter(code => code !== "");
  if (codeArray.length === 0) {
    return true; // 分割后为空数组视为有效
  }

  return codeArray.every(code => validateCompanyCode(code));
};

/**
 * 验证投资者标识字段的业务逻辑
 * @param fundCode 基金代码
 * @param unifiedAccountId 一码通ID
 * @returns 验证结果
 * <AUTHOR>
 * @created 2025-07-31 11:04:58
 * @updated 2025-07-31 11:04:58 hayden 创建投资者标识验证函数，支持个人转基金的场景
 * @description 支持个人转基金的场景，允许同时保留一码通ID和基金代码，至少需要提供其中一个标识
 */
const validateInvestorIdentifier = (fundCode?: string, unifiedAccountId?: string): boolean => {
  // 至少有一个标识字段
  if (!fundCode && !unifiedAccountId) {
    return false;
  }
  
  return true;
};

// 公司筛选配置相关验证器
export const CreateCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  companyCode: z
    .string()
    .optional(),
  benchmarkCompanyCodes: z
    .string()
    .optional()
    .refine((value) => {
      if (!value) {
        return true; // 可选字段，undefined 或空字符串都有效
      }
      return validateMultipleCompanyCodes(value);
    }, {
      message: "对标公司代码格式不正确，应为6位数字+点+字母，多个代码用逗号分隔（如：000002.SZ,000006.SZ）"
    }),
});

export const GetCompanyFilterSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人标签相关验证器（基于标签系统的收藏功能）
export const CreateInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().min(1, "投资人代码不能为空"),
  tagName: z.string().min(1, "标签名称不能为空"),
  tagCategory: z.enum(["system", "user"], {
    errorMap: () => ({ message: "标签分类必须是 system、user" })
  }),
  tagMetadata: z.record(z.any()).optional(),
  companyFilterId: z.string().min(1, "公司筛选配置ID不能为空"), // 2025-07-09 20:33:23 hayden 修改为必需参数，收藏标签必须携带companyFilterId
});

// 2025-07-15 11:20:39 hayden 修改删除投资人标签验证器，从使用id改为使用基金代码
export const DeleteInvestorTagSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().min(1, "基金代码不能为空"),
});

export const ListInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
  investorCode: z.string().optional(),
  tagName: z.string().optional(),
  tagCategory: z.enum(["system", "user"]).optional(),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});

export const SyncInvestorTagsSchema = z.object({
  organizationId: z.string().min(1, "组织ID不能为空"),
});

// 投资人联系人相关验证器
export const CreateInvestorContactSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  name: z.string({ required_error: "缺少必需参数name，请提供联系人姓名" }).min(1, "联系人姓名不能为空"),
  phoneNumber: z.string({ required_error: "缺少必需参数phoneNumber，请提供手机号" }).min(1, "手机号不能为空"), // 2025-08-01 11:57:51 hayden 修改为必填字段
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(),
  unifiedAccountId: z.string().min(1, "一码通ID不能为空").optional(),
}).refine((data) => validateInvestorIdentifier(data.fundCode, data.unifiedAccountId), {
  message: "基金代码和一码通ID必须至少提供其中一个"
});

export const UpdateInvestorContactSchema = z.object({
  contactId: z.string({ required_error: "缺少必需参数contactId，请提供要更新的联系人ID" }).min(1, "联系人ID不能为空"),
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  name: z.string().min(1, "联系人姓名不能为空").optional(),
  phoneNumber: z.string().min(1, "手机号不能为空").optional(), // 2025-08-01 11:57:51 hayden 修改为必填验证，更新时如果提供则不能为空
  email: z.string().email("邮箱格式不正确").optional().or(z.literal("")),
  address: z.string().optional(),
  remarks: z.string().max(300, "备注信息不能超过300字符").optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(),
  unifiedAccountId: z.string().min(1, "一码通ID不能为空").optional(),
}).refine((data) => {
  // 更新时允许不提供标识字段（保持原有值）
  if (data.fundCode !== undefined || data.unifiedAccountId !== undefined) {
    return validateInvestorIdentifier(data.fundCode, data.unifiedAccountId);
  }
  return true;
}, {
  message: "基金代码和一码通ID必须至少提供其中一个"
});

export const DeleteInvestorContactSchema = z.object({
  contactId: z.string({ required_error: "缺少必需参数contactId，请提供要删除的联系人ID" }).min(1, "联系人ID不能为空"),
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
});

export const ListInvestorContactsSchema = z.object({
  organizationId: z.string({ required_error: "缺少必需参数organizationId，请提供组织ID" }).min(1, "组织ID不能为空"),
  name: z.string().optional(),
  phoneNumber: z.string().optional(),
  email: z.string().optional(),
  fundCode: z.string().min(1, "基金代码不能为空").optional(), // 新增：按基金代码查询
  unifiedAccountId: z.string().min(1, "一码通账号不能为空").optional(), // 新增：按一码通账号查询
  queryAllContacts: z.boolean().optional().default(false), // 新增：是否查询组织下所有联系人
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
}).refine((data) => {
  // 如果设置了查询所有联系人，则不需要提供基金代码或一码通账号
  if (data.queryAllContacts) {
    return true;
  }
  // 否则必须提供基金代码或一码通账号其中一个
  return !!(data.fundCode || data.unifiedAccountId);
}, {
  message: "必须提供基金代码或一码通账号其中一个查询条件"
});
