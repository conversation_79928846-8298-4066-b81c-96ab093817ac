/**
 * 创建投资人标签路由（收藏功能基于标签系统实现）
 *
 * <AUTHOR>
 * @date 2025-07-09 17:22:38
 * @updated 2025-07-09 17:22:38 hayden 根据投资人管理API实施方案创建标签创建接口
 * @updated 2025-07-09 20:29:13 hayden 支持收藏标签关联companyFilterId，优化标签创建逻辑
 * @updated 2025-07-09 20:33:23 hayden 修改为必须携带companyFilterId，添加验证逻辑确保数据完整性
 * @description 基于标签系统实现的收藏功能，所有标签必须关联公司筛选配置，确保数据一致性和完整性
 */
import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { CreateInvestorTagSchema } from "../lib/validators";

export const tagsCreateRouter = new Hono().post(
  "/create",
  authMiddleware,
  shareholderCryptoMiddleware(),
  async (c) => {
    try {
      const requestData = c.get("requestData");
      const user = c.get("user");

      // 参数验证
      const validationResult = CreateInvestorTagSchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { message: "请求参数无效" });
      }

      const { organizationId, investorCode, tagName, tagCategory, tagMetadata, companyFilterId } = validationResult.data;

      // 检查是否已存在相同标签
      const existingTag = await db.investorTag.findFirst({
        where: {
          organizationId,
          investorCode,
          tagName
        }
      });

      if (existingTag) {
        // 如果已存在，返回已标记信息
        c.set("response", {
          code: 200,
          message: "该投资人已有此标签",
          data: {
            id: existingTag.id
          }
        });
        return;
      }

      // 2025-07-09 20:33:23 hayden 简化逻辑：companyFilterId现在是必需参数，直接使用
      // 验证 companyFilterId 是否存在于数据库中
      const companyFilter = await db.companyFilter.findUnique({
        where: { id: companyFilterId }
      });

      if (!companyFilter) {
        throw new HTTPException(400, { message: "指定的公司筛选配置不存在" });
      }

      // 验证 companyFilterId 是否属于当前组织
      if (companyFilter.organizationId !== organizationId) {
        throw new HTTPException(400, { message: "公司筛选配置不属于当前组织" });
      }

      const newTag = await db.investorTag.create({
        data: {
          organizationId,
          investorCode,
          tagName,
          tagCategory,
          tagMetadata: tagMetadata || {},
          companyFilterId,
        }
      });

      c.set("response", {
        code: 200,
        message: "标签创建成功",
        data: {
          id: newTag.id
        }
      });
      return;

    } catch (error) {
      if (error instanceof HTTPException) {
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      console.error("投资人管理API错误:", error);
      c.set("response", {
        code: 500,
        message: "服务器内部错误",
        data: null
      });
      return;
    }
  }
);
