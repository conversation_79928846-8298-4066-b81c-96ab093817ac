import { operatorId, meetingApiClient } from "./config";
import {db} from "@repo/database";

// 定义类型
export interface InviteActivateResponse {
  success: boolean;
  data: {
    invite_activate_list: {
      userid: string;
      invite_activate_url: string;
    }[];
  };
}

// 获取用户激活链接
export const getInviteActivateLinks = {
  method: "POST",
  path: "/meetings/invite-activate",
  handler: async (useridList: string[]) => {
    try {
      // console.log('获取用户激活链接参数:', { useridList });
      
      const response = await meetingApiClient.post("/v1/users/invite-activate", {
        userid_list: useridList,
        operator_id: operatorId,
        operator_id_type: "1"
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.log("Error getting invite activate links:", error);
      throw error;
    }
  },
};

export const getShareholders = {
  method: "GET",
  path: "/meetings/invite-shareholders/:orgId",
  handler: async (organizationId: string, params?: {
    page?: number;
    pageSize?: number;
    searchTerm?: string;
  }) => {
    try {
      // 设置默认分页参数
      const page = params?.page || 1;
      const pageSize = params?.pageSize || 20;
      const searchTerm = params?.searchTerm;

      // 构建搜索条件（用于原生SQL查询）

      // 使用原生SQL查询实现去重和分页
      // 首先创建一个CTE来获取去重后的股东数据（按名字+联系方式去重，保留最新一期）
      const shareholdersQuery = `
        WITH unique_shareholders AS (
          SELECT DISTINCT ON (s."securitiesAccountName", s."contactNumber")
            s."id",
            s."securitiesAccountName",
            s."contactNumber",
            s."registerDate"
          FROM "shareholder" s
          WHERE s."organizationId" = $1
            AND s."contactNumber" IS NOT NULL
            ${searchTerm ? `AND (
              s."securitiesAccountName" ILIKE $4 OR
              s."contactNumber" ILIKE $4
            )` : ''}
          ORDER BY s."securitiesAccountName", s."contactNumber", s."registerDate" DESC
        )
        SELECT * FROM unique_shareholders
        ORDER BY "registerDate" DESC, "securitiesAccountName" ASC, "contactNumber" ASC, "id" ASC
        LIMIT $2 OFFSET $3
      `;

      // 计算总数的查询
      const countQuery = `
        WITH unique_shareholders AS (
          SELECT DISTINCT ON (s."securitiesAccountName", s."contactNumber")
            s."id"
          FROM "shareholder" s
          WHERE s."organizationId" = $1
            AND s."contactNumber" IS NOT NULL
            ${searchTerm ? `AND (
              s."securitiesAccountName" ILIKE $2 OR
              s."contactNumber" ILIKE $2
            )` : ''}
          ORDER BY s."securitiesAccountName", s."contactNumber", s."registerDate" DESC
        )
        SELECT COUNT(*) as total FROM unique_shareholders
      `;

      // 准备查询参数
      const offset = (page - 1) * pageSize;
      const searchPattern = searchTerm ? `%${searchTerm}%` : null;

      // 执行查询
      const [shareholders, totalResult] = await Promise.all([
        searchTerm
          ? db.$queryRawUnsafe(shareholdersQuery, organizationId, pageSize, offset, searchPattern)
          : db.$queryRawUnsafe(shareholdersQuery, organizationId, pageSize, offset),
        searchTerm
          ? db.$queryRawUnsafe(countQuery, organizationId, searchPattern)
          : db.$queryRawUnsafe(countQuery, organizationId)
      ]);

      const total = Number((totalResult as any[])[0]?.total || 0);
      const totalPages = Math.ceil(total / pageSize);

      // 构建分页信息
      const pagination = {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      };

      return {
        data: shareholders,
        pagination
      };
    } catch (error: any) {
      console.error("Error getting shareholders for invite:", error);
      throw error;
    }
  }
};
