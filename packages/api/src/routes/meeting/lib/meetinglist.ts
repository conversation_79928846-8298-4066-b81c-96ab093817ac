
import { operatorId, meetingApiClient } from "./config";

/* 修改块开始: 支持传入用户ID获取会议列表
 * 修改范围: getUpcomingMeetingList handler函数
 * 对应需求: 使用真实用户ID而非固定operatorId查询会议
 * 恢复方法: 删除userId参数，恢复使用固定operatorId
 */
export const getUpcomingMeetingList = {
	method: "GET",
	path: "/meetings/upcoming",
	handler: async (userId?: string) => {
		// 使用传入的用户ID，如果没有则使用默认的operatorId
		const targetUserId = userId || operatorId;
		
		try {
			// 初始化变量
			let allMeetings: any[] = [];
			let pos = 0;
			let cursory = 0;
			let remaining = 1; // 初始值设为非0，以便进入循环
			
			// 循环获取所有会议，直到没有更多会议
			while (remaining > 0) {
				const response = await meetingApiClient.get("/v1/meetings", {
					params: {
						userid: targetUserId,
						instanceid: "1",
						pos: pos,
						cursory: cursory,
						// 不使用is_show_all_sub_meetings参数
					},
				});
				
				const data = response.data;
				
				// 将本次获取的会议添加到总列表中
				if (data.meeting_info_list && data.meeting_info_list.length > 0) {
					allMeetings = [...allMeetings, ...data.meeting_info_list];
				}
				
				// 更新分页参数
				remaining = data.remaining;
				pos = data.next_pos;
				cursory = data.next_cursory;
				
				// 安全检查：如果API没有返回新的会议但remaining仍然大于0，防止无限循环
				if (data.meeting_info_list?.length === 0 && remaining > 0) {
					console.warn("API返回remaining > 0但没有新的会议数据，中断循环");
					break;
				}
			}
			
			// 构造与原API响应格式相同的返回数据，但包含所有会议
			return {
				meeting_number: allMeetings.length,
				meeting_info_list: allMeetings,
				next_pos: 0,
				remaining: 0,
				next_cursory: 0
			};
		} catch (error: any) {
			console.log("Error fetching upcoming meetings:", error);
			throw error;
		}
	},
};

export const getHistoryMeetingList = {
	method: "GET",
	path: "/meetings/history",
	handler: async (pageSize = 20, page = 1, userId?: string, startTime?: string, endTime?: string) => {
		const targetUserId = userId || operatorId;

		try {
			// 初始化变量
			let allMeetings: any[] = [];
			let currentPage = page;
			let totalPage = 1; // 初始值设为1，确保至少执行一次循环
			
			// 构建基本查询参数
			const queryParams: any = {
                page_size: pageSize,
                ...(startTime && { start_time: startTime }),
                ...(endTime && { end_time: endTime })
            };
			
			// 循环获取所有页的会议，直到没有更多页
			while (currentPage <= totalPage) {
				// 更新当前页码
				queryParams.page = currentPage;
				
				const response = await meetingApiClient.get(
					`/v1/history/meetings/${targetUserId}`,
					{ params: queryParams }
				);
				
				const data = response.data;
				
				// 更新总页数
				totalPage = data.total_page || 0;
				
				// 将本次获取的会议添加到总列表中
				if (data.meeting_info_list && data.meeting_info_list.length > 0) {
					allMeetings = [...allMeetings, ...data.meeting_info_list];
				}
				
				// 增加页码，准备获取下一页
				currentPage++;
				
				// 安全检查：如果API没有返回新的会议但还有更多页，防止无限循环
				if (data.meeting_info_list?.length === 0 && currentPage <= totalPage) {
					console.warn("API返回空会议列表但还有更多页，中断循环");
					break;
				}
			}
			
			// 构造与原API响应格式相同的返回数据，但包含所有会议
			return {
				current_page: 1,
				current_size: allMeetings.length,
				total_count: allMeetings.length,
				total_page: 1,
				meeting_info_list: allMeetings
			};
		} catch (error: any) {
			console.log("Error fetching history meetings:", error);
			throw error;
		}
	},
};
