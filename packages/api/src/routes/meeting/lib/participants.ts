import { operatorId, meetingApiClient } from "./config";
import * as XLSX from "xlsx";
import axios from "axios";

/**
 * 发起导出参会成员列表任务
 * @param meetingId 会议ID
 * @returns 导出任务ID
 */
async function exportParticipantsList(meetingId: string,startTime?: string) {
  try {
    //真实会议开始时间再往前推12小时，确保获取到会议录制文件
    startTime = Math.floor(Number(startTime)-12*60*60).toString();
    //真实会议开始时间往后推15天，确保获取到会议录制文件
    const endTime = Math.floor(Number(startTime) + (15 * 24 * 60 * 60)).toString();
    const response = await meetingApiClient.post("/v1/meetings/export-participants-list", {
      meeting_id: meetingId,
      operator_id: operatorId,
      operator_id_type: 1,
      start_time: startTime,
      end_time: endTime
    });

    return response.data;
  } catch (error: any) {
    console.error("Error exporting participants list:", error);
    throw error;
  }
}

/**
 * 获取导出参会成员列表的结果
 * @param jobId 导出任务ID
 * @returns 导出文件的URL
 */
async function getExportParticipantsResult(jobId: string) {
  try {
    const response = await meetingApiClient.get(`/v1/export/${jobId}`, {
      params: {
        operator_id: operatorId,
        operator_id_type: 1
      }
    });

    return response.data;
  } catch (error: any) {
    console.error("Error getting export result:", error);
    throw error;
  }
}

export const exportParticipants = {
  method: "POST",
  path: "/meetings/export-participants/:meetingId",
  handler: async (meetingId: string,startTime?: string) => {
    try {
      // 发起导出任务
      const exportResult = await exportParticipantsList(meetingId,startTime);
      
      if (!exportResult?.job_id) {
        throw new Error("导出任务创建失败");
      }

      // 等待一段时间后获取结果
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 获取导出结果
      const result = await getExportParticipantsResult(exportResult.job_id);
      const fileBuffer = await axios
							.get(result.url, { responseType: "arraybuffer" })
							.then((res) => res.data);
						const workbook = XLSX.read(fileBuffer, {
							type: "buffer",
						});
						const firstSheetName = workbook.SheetNames[0];
						const sheetData = XLSX.utils.sheet_to_json(
							workbook.Sheets[firstSheetName],
						);

      return {
        success: true,
        data: result,
        sheetData
      };
    } catch (error: any) {
      console.error("Export participants error:", error);
      return {
        success: false,
        error: error.message || "导出参会成员失败"
      };
    }
  }
};
