// recordDetail.ts

import { operatorId, meetingApiClient } from "./config";
import axios from "axios";

async function getMeetingRecordsID(
	meetingId: string,
	userId?: string,
	startTime?: string,
) {
	//真实会议开始时间再往前推12小时，确保获取到会议录制文件
	startTime = Math.floor(Number(startTime)-12*60*60).toString();
	//真实会议开始时间往后推15天，确保获取到会议录制文件
	const endTime = Math.floor(Number(startTime) + (15 * 24 * 60 * 60)).toString();
	const queryRecordType = "1";

	try {
		const response = await meetingApiClient.get("/v1/records", {
			params: {
				operator_id: userId,
				operator_id_type: "1",
				meeting_id: meetingId,
				start_time: startTime,
				end_time: endTime,
				query_record_type: queryRecordType,
			},
		});

		return response.data;
	} catch (error: any) {
		console.log("Error fetching meeting records:", error);
		throw error;
	}
}

export const getSingleMeetingRecordDetail = {
	method: "GET",
	path: "/meetings/records/detail/:meetingId",
	handler: async (meetingId: string, recordFileId?: string, userId?: string, startTime?: string) => {
		try {
			let fileId = recordFileId;
			
			if (!fileId) {
				const meetingRecords = await getMeetingRecordsID(meetingId, userId, startTime);
				if (!meetingRecords?.record_meetings?.[0]?.record_files?.[0]) {
					throw new Error("未找到会议录制记录");
				}

				fileId = meetingRecords.record_meetings[0].record_files[0].record_file_id;
			}

			const response = await meetingApiClient.get(`/v1/addresses/${fileId}`, {
				params: { 
					userid: userId || operatorId 
				},
			});

			const view_address = response.data.view_address;

			const txtSummary = response.data.meeting_summary.find(
				(item: any) => item.file_type === "txt",
			);
			const url = txtSummary.download_address;
			const txtResponse = await axios.get(url, { responseType: "text" });
			const txtContent = txtResponse.data;

			const docxSummary = response.data.meeting_summary.find(
				(item: any) => item.file_type === "docx",
			);

			const docx_download_address = docxSummary.download_address;

			const aiSummary = response.data.ai_minutes.find(
				(item: any) => item.file_type === "txt",
			);
			const aiSummaryUrl = aiSummary.download_address;
			const aiSummaryResponse = await axios.get(aiSummaryUrl, { responseType: "text" });
			const aiSummaryContent = aiSummaryResponse.data;
			// console.log("转写地址TXT",txtSummary);
			// console.log("转写内容",txtContent);
			return {
				docx_download_address,
				view_address,
				txtContent,
				aiSummaryContent
			};
		} catch (error: any) {
			// console.error("Error fetching record detail:", error);
		}
	},
};

/* 修改块开始: 新增会议签到列表获取功能
 * 修改范围: 新增getMeetingSignInList函数
 * 对应需求: 获取会议签到列表，支持真实用户ID，处理权限错误
 * 恢复方法: 删除此整个函数定义
 */
/**
 * 获取会议签到列表API
 */
export const getMeetingSignInList = {
	method: "GET",
	path: "/meetings/sign-in/list/:meetingId",
	handler: async (meetingId: string, userId?: string) => {
		try {
			// 使用传入的用户ID，如果没有则使用默认的operatorId
			const targetUserId = userId || operatorId;
			
			const response = await meetingApiClient.get("/v1/meetings/sign-in/list", {
				params: {
					meeting_id: meetingId,
					operator_id_type: "1",
					operator_id: targetUserId,
					instanceid: "1"
				},
			});

			// console.log("Meeting sign-in list fetched:", response.data);
			return response.data;
		} catch (error: any) {
			console.log("Error fetching meeting sign-in list:", error);
			
			// 增强错误处理，提供更详细的错误信息
			if (error.response) {
				const errorData = error.response.data;
				const status = error.response.status;
				
				// 处理权限相关错误
				if (status === 403 || status === 401) {
					const errorMessage = errorData?.error_info?.message || 
									   errorData?.message || 
									   "您没有权限获取此会议的签到列表，只有会议创建者可以查看";
					throw new Error(errorMessage);
				}
				
				// 处理会议不存在错误
				if (status === 404) {
					throw new Error("会议不存在或已被删除");
				}
				
				// 其他API错误
				const errorMessage = errorData?.error_info?.message || 
								   errorData?.message || 
								   `获取签到列表失败 (状态码: ${status})`;
				throw new Error(errorMessage);
			}
			
			if (error.request) {
				// 请求发送失败
				throw new Error("网络连接失败，请检查网络连接后重试");
			}
			
			// 其他错误
			throw new Error(error.message || "获取会议签到列表时发生未知错误");
		}
	},
};
