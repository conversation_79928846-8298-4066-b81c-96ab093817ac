import { Hono } from "hono";
import { createSimpleN8nProxy } from "../../middleware/n8n-proxy";

/**
 * n8n代理路由器
 *
 * <AUTHOR>
 * @date 2025-06-15 01:00:22
 * @description 配置n8n代理服务的路由，支持多个n8n服务器环境
 * @modified 2025-06-18 10:33:50 - 添加明确的路由类型定义以修复 TypeScript 类型推断
 *
 * @routes
 * - /* : 代理所有请求到配置的n8n服务器
 *
 * @security
 * - 需要用户认证
 * - 请求和响应自动加解密
 * - 时间戳验证防重放攻击
 *
 * @environment
 * - N8N_BASE_URL: n8n服务器基础URL (必需)
 * - N8N_BASE_URL1: n8n服务器URL版本1 (可选)
 * - N8N_BASE_URL2: n8n服务器URL版本2 (可选)
 * - N8N_BASE_URL3: n8n服务器URL版本3 (可选)
 */
const router = new Hono().basePath("/n8n_proxy");

/**
 * 获取n8n服务器基础URL
 * 
 * <AUTHOR>
 * @date 2025-06-15 01:00:22
 * @description 从环境变量中获取n8n服务器URL，支持多个版本配置
 * @returns n8n服务器基础URL
 */
function getN8nBaseUrl(): string {
  // 优先使用主要的N8N_BASE_URL
  const primaryUrl = process.env.N8N_BASE_URL;
  if (primaryUrl) {
    return primaryUrl;
  }
  
  // 回退到版本化的URL配置
  const fallbackUrls = [
    process.env.N8N_BASE_URL1,
  ].filter(Boolean);
  
  if (fallbackUrls.length > 0) {
    // 使用第一个可用的URL
    return fallbackUrls[0] as string;
  }
  
  // 默认本地开发环境URL
  const defaultUrl = "http://localhost:5678/webhook/v1";
  console.warn(`未配置N8N_BASE_URL环境变量，使用默认URL: ${defaultUrl}`);
  return defaultUrl;
}

/**
 * 注册n8n代理中间件
 *
 * <AUTHOR>
 * @date 2025-06-15 14:46:44
 * @description 将n8n代理中间件注册到所有路径，处理所有HTTP方法
 * @修改记录 2025-06-15 14:46:44 hayden: 修复路由挂载方式，使用正确的Hono语法
 *
 * @example
 * 请求路径映射:
 * - /api/n8n_proxy/company-overview → {N8N_BASE_URL}/company-overview
 * - /api/n8n_proxy/test-webhook → {N8N_BASE_URL}/test-webhook
 * - /api/n8n_proxy/webhook/data → {N8N_BASE_URL}/webhook/data
 */
const n8nBaseUrl = getN8nBaseUrl();

// 创建n8n代理中间件实例
const n8nProxy = createSimpleN8nProxy(n8nBaseUrl);

// 添加明确的路由定义以支持 TypeScript 类型推断
// 修改记录 2025-06-18 10:33:50 hayden: 添加通用路由处理器以修复类型错误
/**
 * 通用n8n代理路由处理器
 *
 * <AUTHOR>
 * @date 2025-06-18 10:33:50
 * @description 为所有可能的端点路径添加明确的路由定义，支持 TypeScript 类型推断
 * @param path 动态路径参数，支持任意端点名称
 * @returns 返回代理响应数据
 */
router.post("/:path{.*}", async (c) => {
	// 将请求转发给 n8n 代理中间件处理
	return n8nProxy.fetch(c.req.raw, c.env);
});

// 将代理中间件的所有路由挂载到当前路由器
// 修复：使用正确的Hono语法挂载子应用
// 原代码保留作为注释: router.route("/", n8nProxy);
router.route("/", n8nProxy);

export { router as n8nProxyRouter };
