import { db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { nanoid } from "nanoid";
import { z } from "zod";

export const organizationsRouter = new Hono().basePath("/organizations").get(
	"/generate-slug",
	validator(
		"query",
		z.object({
			name: z.string(),
		}),
	),
	describeRoute({
		summary: "Generate a slug for an organization",
		tags: ["Organizations"],
	}),
	async (c) => {
		const { name } = c.req.valid("query");

		/**
		 * 组织 slug 生成逻辑
		 * 修改说明：
		 * 1. 将 const 改为 let，因为需要在 slug 为空时重新赋值
		 * 2. 增加空值检查，处理纯中文输入的情况
		 * 3. 使用 nanoid(8) 生成 8 位随机字符串作为备选方案
		 * 4. 确保生成的随机字符串为小写，保持与 slugify 输出一致
		 *
		 * 修改时间: 2025-07-22
		 * 修改人: LLM-ad
		 * 修改原因: 修复纯中文组织名称生成空 slug 的问题，并确保所有 slug 都是小写
		 * 影响范围: 仅影响纯中文输入的情况，其他情况保持不变
		 */
		// let baseSlug = slugify(name, {
		// 	lowercase: true,
		// });

		// // 当 slugify 返回空字符串时（如纯中文输入），使用小写随机字符串
		// if (!baseSlug) {
		// 	baseSlug = nanoid(8).toLowerCase();
		// }

		// 生成8位随机字符串作为基础slug
		const baseSlug = nanoid(8).toLowerCase();

		let slug = baseSlug;
		let hasAvailableSlug = false;

		for (let i = 0; i < 3; i++) {
			const existing = await db.organization.findUnique({
				where: {
					slug,
				},
			});

			if (!existing) {
				hasAvailableSlug = true;
				break;
			}

			slug = `${baseSlug}-${nanoid(5)}`;
		}

		if (!hasAvailableSlug) {
			return c.json(
				{
					error: "No available slug found",
				},
				400,
			);
		}

		return c.json({
			slug,
		});
	},
);
