import { Hono } from "hono";
import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import { BatchUploadRulesSchema } from "./lib/validators";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";

/**
 * 股东分类规则API路由
 *
 * <AUTHOR>
 * @time 2025-06-25 11:49:56
 * @description 提供股东分类规则的批量上传和查询功能
 *
 * 功能包括：
 * 1. 批量上传股东分类规则
 * 2. 查询所有股东分类规则
 *
 * 修改记录：
 * - 2025-06-25 11:49:56: 添加登录验证和加密解密中间件
 */

/**
 * 批量上传股东分类规则接口
 *
 * 路径：/classification/rules/batch-upload
 * 方法：POST
 * 功能：接收JSON格式股东分类规则数组，批量插入到数据库中
 *
 * 请求参数：
 * - rules: ShareholderClassificationRuleInput[] 股东分类规则数组
 *
 * 响应数据：
 * - totalInserted: number 插入的规则数量
 * - insertedIds: string[] 插入的规则ID数组
 * - updatedAt: string 最新更新时间
 *
 * <AUTHOR>
 * @time 2025-06-25 11:49:56
 * @description 修改记录：
 * - 2025-06-25 11:49:56: 添加加密解密支持，从中间件获取解密后的数据
 * - 2025-06-25 15:30:00: 移除加密要求，直接从请求体获取数据
 */
const batchUploadHandler = async (c: any) => {
	try {
		// 直接从请求体获取数据（不使用加密中间件）
		const requestData = await c.req.json();

		// 验证请求参数格式
		const validationResult = BatchUploadRulesSchema.safeParse(requestData);
		if (!validationResult.success) {

			return c.json({
				code: 400,
				message: "请求参数验证失败",
				data: {
					errors: validationResult.error.errors.map((err) => ({
						field: err.path.join("."),
						message: err.message,
					})),
				},
			}, 400);
		}

		const { rules } = validationResult.data;

		// 使用事务执行批量操作：先清空现有规则，再插入新规则
		const result = await db.$transaction(async (tx) => {
			// 清空现有规则
			await tx.shareholderClassificationRule.deleteMany({});

			// 批量插入新规则
			const insertedRules =
				await tx.shareholderClassificationRule.createMany({
					data: rules.map((rule) => ({
						priority: rule.priority,
						type: rule.type,
						rule: rule.rule,
						matchField: rule.matchField, // 添加matchField字段支持 - 2025-06-26 14:18:31 hayden
					})),
					skipDuplicates: false, // 不跳过重复项，确保数据一致性
				});

			// 获取插入的规则ID（由于createMany不返回ID，需要重新查询）
			const allRules = await tx.shareholderClassificationRule.findMany({
				orderBy: { priority: "asc" },
				select: { id: true, updatedAt: true },
			});

			return {
				totalInserted: insertedRules.count,
				insertedIds: allRules.map((rule) => rule.id),
				lastUpdatedAt:
					allRules.length > 0 ? allRules[0].updatedAt : new Date(),
			};
		});

		// 直接返回 JSON 响应（不使用加密中间件）
		return c.json({
			code: 200,
			message: "规则批量上传成功",
			data: {
				totalInserted: result.totalInserted,
				insertedIds: result.insertedIds,
				updatedAt: result.lastUpdatedAt.toISOString(),
			},
		});
	} catch (error) {

		if (error instanceof HTTPException) {
			throw error;
		}

		return c.json({
			code: 500,
			message: "服务器内部错误",
			data: {
				error: error instanceof Error ? error.message : "未知错误",
			},
		}, 500);
	}
};

/**
 * 查询所有股东分类规则接口（分组返回）
 *
 * 路径：/classification/rules/list
 * 方法：POST
 * 功能：查询所有股东分类规则，按priority、type、matchField分组返回
 *
 * 请求参数：
 * - content: string 加密的请求内容
 * - sign: string 请求签名
 *
 * 解密后的业务数据：空对象 {}
 *
 * 响应数据：
 * - rules: ShareholderClassificationRuleGroup[] 分组后的规则列表
 * - total: number 分组总数
 * - lastUpdatedAt: string 最新更新时间
 *
 * <AUTHOR>
 * @time 2025-06-26 14:18:31
 * @description 修改记录：
 * - 2025-06-25 11:49:56: 添加加密解密支持
 * - 2025-06-26 14:18:31: 实现按priority、type、matchField分组返回逻辑
 */
const listRulesHandler = async (c: any) => {
	try {
		// 查询所有规则，按优先级排序，包含matchField字段
		const rawRules = await db.shareholderClassificationRule.findMany({
			orderBy: [{ priority: "asc" }, { type: "asc" }],
			select: {
				priority: true,
				type: true,
				rule: true,
				matchField: true,
				updatedAt: true,
			},
		});

		// 获取最新更新时间
		const lastUpdatedRule =
			await db.shareholderClassificationRule.findFirst({
				orderBy: { updatedAt: "desc" },
				select: { updatedAt: true },
			});

		// 按 priority + type + matchField 分组
		const groupedRulesMap = rawRules.reduce((acc, rule) => {
			const key = `${rule.priority}-${rule.type}-${rule.matchField}`;
			if (!acc[key]) {
				acc[key] = {
					priority: rule.priority,
					type: rule.type,
					matchField: rule.matchField,
					rules: []
				};
			}
			acc[key].rules.push(rule.rule);
			return acc;
		}, {} as Record<string, { priority: number; type: string; matchField: string; rules: string[] }>);

		// 转换为数组并按优先级排序
		const groupedRules = Object.values(groupedRulesMap).sort((a, b) => a.priority - b.priority);

		// 设置响应数据，由加密中间件处理加密
		c.set("response", {
			code: 200,
			message: "查询成功",
			data: {
				rules: groupedRules,
				total: groupedRules.length,
				lastUpdatedAt:
					lastUpdatedRule?.updatedAt.toISOString() ||
					new Date().toISOString(),
			},
		});
	} catch (error) {

		throw new HTTPException(500, {
			message: "服务器内部错误",
			cause: error instanceof Error ? error.message : "未知错误",
		});
	}
};


/**
 * 股东分类规则路由配置
 *
 * <AUTHOR>
 * @time 2025-06-26 14:18:31
 * @description 修改记录：
 * - 2025-06-25 11:49:56: 添加登录验证和加密解密中间件
 * - 2025-06-26 14:18:31: 添加更新时间检查接口
 */
export const classificationRulesRouter = new Hono()
	.post(
		"/rules/batch-upload",
		batchUploadHandler
	)
	.post(
		"/rules/list",
		authMiddleware, // 验证用户身份和权限
		shareholderCryptoMiddleware(), // 处理请求和响应的加解密
		listRulesHandler
	)
