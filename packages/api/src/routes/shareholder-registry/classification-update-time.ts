import { Hono } from "hono";
import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";

/**
 * 股东分类规则更新时间检查API路由
 *
 * <AUTHOR>
 * @time 2025-06-25 11:49:56
 * @description 提供股东分类规则的更新时间检查功能，用于前端缓存判断
 *
 * 功能：
 * 1. 获取股东分类规则的最新更新时间
 * 2. 返回规则总数
 *
 * 修改记录：
 * - 2025-06-25 11:49:56: 添加登录验证和加密解密中间件
 */

/**
 * 更新时间检查接口
 *
 * 路径：/classification/rules/check-update-time
 * 方法：POST
 * 功能：获取股东分类规则的最新更新时间，用于前端缓存判断
 *
 * 请求参数：
 * - content: string 加密的请求内容
 * - sign: string 请求签名
 *
 * 解密后的业务数据：空对象 {}
 *
 * 响应数据：
 * - lastUpdatedAt: string 最新的updatedAt时间
 * - totalRules: number 规则总数
 *
 * <AUTHOR>
 * @time 2025-06-25 11:49:56
 * @description 修改记录：添加加密解密支持
 */
const checkUpdateTimeHandler = async (c: any) => {
	try {
		// 获取规则总数
		const totalRules = await db.shareholderClassificationRule.count();

		// 获取最新更新时间
		const lastUpdatedRule =
			await db.shareholderClassificationRule.findFirst({
				orderBy: { updatedAt: "desc" },
				select: { updatedAt: true },
			});

		// 如果没有规则，返回当前时间
		const lastUpdatedAt = lastUpdatedRule?.updatedAt || new Date();

		// 设置响应数据，由加密中间件处理加密
		c.set("response", {
			code: 200,
			message: "获取更新时间成功",
			data: {
				lastUpdatedAt: lastUpdatedAt.toISOString(),
				totalRules: totalRules,
			},
		});
	} catch (error) {
		console.error("获取股东分类规则更新时间失败:", error);

		throw new HTTPException(500, {
			message: "服务器内部错误",
			cause: error instanceof Error ? error.message : "未知错误",
		});
	}
};

/**
 * 股东分类规则更新时间检查路由配置
 *
 * <AUTHOR>
 * @time 2025-06-25 11:49:56
 * @description 修改记录：添加登录验证和加密解密中间件
 */
export const classificationUpdateTimeRouter = new Hono().post(
	"/rules/check-update-time",
	authMiddleware,
	shareholderCryptoMiddleware(),
	checkUpdateTimeHandler
);
