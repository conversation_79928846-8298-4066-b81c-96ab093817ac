import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type { UploadRegistryRequest } from "../types";
import { 
  handleCompanyBinding, 
  handleRegistryRecord, 
  updateRecordCount
} from "../lib/registry-handlers";
import {
  verifyRegistryTypeByFields,
  validateShareholderFields,
  mapShareholderFields
} from "../lib/field-mapping";
import { processShareholderUpdateFields, processShareholdingAmountMerge } from "../lib/utils";

/**
 * 处理深市01/05名册合并
 * 
 * 功能：
 * 1. 验证组织存在性和名册类型合法性
 * 2. 验证上传的名册字段是否符合01或05类型特征
 * 3. 检查01/05名册合并情况
 * 4. 创建或更新名册记录
 * 5. 批量处理股东数据，合并01/05特有字段
 * 6. 更新实际股东数量
 * 
 * @param data 请求数据
 * @param userId 用户ID
 * @returns 处理后的名册记录
 */
export async function merge0105Handler(
  data: UploadRegistryRequest,
  userId: string
) {
  // 动态事务超时计算
  const totalRecords = data.shareholders.length;
  const dynamicTimeout = Math.max(30000, totalRecords * 30);
  
  // 确定当前上传的名册类型
  const isType01 = data.fileName.toLowerCase().includes("dqmc01");
  const isType05 = data.fileName.toLowerCase().includes("dqmc05");
  
  // 如果无法确定类型，抛出错误
  if (!isType01 && !isType05) {
    throw new HTTPException(400, { 
      message: "无法确定名册类型，请检查文件名是否包含DQMC01/DQMC05标识"
    });
  }
  
  // 确定当前上传的名册类型字符串
  const currentType = isType01 ? "01" : "05";
  
  // 验证上传的股东数据字段是否符合当前类型特征
  const fieldValidation = validateShareholderFields(data.shareholders, currentType);
  if (!fieldValidation.isValid) {
    throw new HTTPException(400, { 
      message: `上传的股东数据缺少${currentType}类型名册必要字段: ${fieldValidation.missingFields.join(", ")}`
    });
  }
  
  // 验证上传的名册类型是否与字段特征匹配
  const typeVerification = verifyRegistryTypeByFields(data.shareholders, currentType);
  if (!typeVerification.isMatch) {
    throw new HTTPException(400, { 
      message: typeVerification.message
    });
  }
  
  // 使用事务处理所有数据库操作
  return db.$transaction(async (tx) => {
    // 1. 验证组织是否存在
    const organization = await tx.organization.findUnique({
      where: { id: data.organizationId },
      select: { id: true, metadata: true },
    });

    // 检查组织是否存在
    if (!organization) {
      throw new HTTPException(404, { message: "组织不存在" });
    }

    // 2. 处理组织元数据
    const metadata = organization.metadata
      ? JSON.parse(organization.metadata)
      : {};

    // 3. 处理组织与公司的绑定关系
    await handleCompanyBinding(
      tx,
      data.organizationId,
      metadata,
      data.companyCode,
      data.companyInfo.companyName || ""
    );

    // 4. 查询是否存在同一公司同一期数的名册记录
    const existingRegistry = await tx.shareholderRegistry.findFirst({
      where: {
        organizationId: data.organizationId,
        companyCode: data.companyCode,
        registerDate: new Date(data.registerDate),
      },
      select: { id: true, fileName: true },
    });

    // 5. 处理名册记录
    const { registry, isNewRegistry } = await handleRegistryRecord(
      tx,
      data,
      existingRegistry,
      userId
    );

    /**
     * 6. 股东数据批量处理
     * 
     * 先对原始数据进行字段映射，确保使用数据库字段名进行后续处理
     */
    
    // 对所有股东数据进行字段映射
    const mappedShareholders = data.shareholders.map((shareholder) => {
      const mapped = mapShareholderFields(shareholder, currentType);

      return mapped;
    });
    
    // 准备查询条件 - 提取所有股东ID和一码通账号用于批量查询
    const shareholderIds = mappedShareholders
      .map((s) => s.shareholderId)
      .filter((id): id is string => id !== undefined && id !== null)
      .map(id => String(id));
    
    const unifiedAccountNumbers = mappedShareholders
      .map((s) => s.unifiedAccountNumber)
      .filter((num): num is string => num !== undefined && num !== null)
      .map(num => String(num));

    // 构建查询条件 - 与upload copy.ts保持一致
    const existingShareholders = await tx.shareholder.findMany({
      where: {
        registryId: registry.id,
        organizationId: data.organizationId,
        registerDate: new Date(data.registerDate),
        OR: [
          { shareholderId: { in: shareholderIds } },
          {
            AND: [
              { unifiedAccountNumber: { in: unifiedAccountNumbers } },
              { shareholderId: { in: shareholderIds } }
            ]
          }
        ]
      }
    });

    // 构建高效查找映射 - 使用与upload copy.ts相同的复合键逻辑
    const shareholderMap = new Map();
    for (const shareholder of existingShareholders) {
      // 使用"一码通账号_股东ID"作为复合键
      const lookupKey = `${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`;
      shareholderMap.set(lookupKey, shareholder);
    }

    // 准备批量创建和更新的数据集合
    const shareholdersToCreate: Array<Record<string, unknown>> = [];
    const shareholdersToUpdate: Array<Record<string, unknown>> = [];

    // 股东记录处理循环 - 使用映射后的数据
    for (const mappedData of mappedShareholders) {
      // 确保关键字段存在
      if (!mappedData.shareholderId || !mappedData.unifiedAccountNumber) {
        console.warn(`跳过缺少关键字段的股东记录: ${JSON.stringify(mappedData)}`);
        continue;
      }

      // 确保ID和账号非空
      const shareholderId = String(mappedData.shareholderId);
      const unifiedAccountNumber = String(mappedData.unifiedAccountNumber);

      // 使用复合键查找现有记录
      const lookupKey = `${unifiedAccountNumber}_${shareholderId}`;
      const existingShareholder = shareholderMap.get(lookupKey);

      if (existingShareholder) {
        // 2025年06月24日 18:48:19：问题已解决，移除调试日志

        // 更新逻辑 - 使用与upload copy.ts相同的更新处理函数
        const updateData: Record<string, unknown> = {
          id: existingShareholder.id,
          shareholderId: existingShareholder.shareholderId,
        };

        // 1. 处理一般字段更新 - 按照"先到先得"原则
        const hasFieldUpdates = processShareholderUpdateFields(
          updateData,
          existingShareholder,
          mappedData,
          currentType
        );

        // 2025年06月24日 18:48:19：问题已解决，移除调试日志

        // 2. 处理持股数量字段更新 - 按照"先到先得"原则
        const shareAmountUpdates = processShareholdingAmountMerge(
          existingShareholder,
          mappedData
        );

        // 合并持股数量更新到更新数据中
        Object.assign(updateData, shareAmountUpdates);

        // 如果有任何字段需要更新，添加到更新列表
        if (hasFieldUpdates || Object.keys(shareAmountUpdates).length > 0) {
          shareholdersToUpdate.push(updateData);
        }
      } else {
        // 创建逻辑 - 使用映射后的字段
        // 添加必要的关联字段和默认值
        shareholdersToCreate.push({
          ...mappedData,
          registryId: registry.id,
          organizationId: data.organizationId,
          registerDate: new Date(data.registerDate),
          // 设置默认值，确保数值字段不为null
          numberOfShares: mappedData.numberOfShares || "0",
          lockedUpShares: mappedData.lockedUpShares || "0",
          shareholdingRatio: mappedData.shareholdingRatio || "0",
          frozenShares: mappedData.frozenShares || "0"
        });
      }
    }

    // 批量创建股东记录
    if (shareholdersToCreate.length > 0) {
      // 根据数据量动态确定批处理大小
      let batchSize: number;
      const dataSize = shareholdersToCreate.length;

      // 动态批处理大小策略
      if (dataSize < 500) {
        batchSize = 200;
      } else if (dataSize < 1000) {
        batchSize = 400;
      } else {
        batchSize = 600;
      }
      
      // 分批执行创建操作
      for (let i = 0; i < shareholdersToCreate.length; i += batchSize) {
        const batch = shareholdersToCreate.slice(i, i + batchSize);
        await tx.shareholder.createMany({
          data: batch as any[],
          skipDuplicates: true,
        });
      }
    }

    // 批量更新股东记录
    if (shareholdersToUpdate.length > 0) {
      // 根据数据量动态确定并发批次大小
      let concurrencyLimit: number;
      const updateSize = shareholdersToUpdate.length;

      // 动态并发限制策略
      if (updateSize < 100) {
        concurrencyLimit = 20;
      } else if (updateSize < 500) {
        concurrencyLimit = 15;
      } else {
        concurrencyLimit = 10;
      }

      // 分批并行处理更新
      for (let i = 0; i < shareholdersToUpdate.length; i += concurrencyLimit) {
        const updateBatch = shareholdersToUpdate.slice(i, i + concurrencyLimit);

        // 并行执行当前批次的更新操作
        await Promise.all(
          updateBatch.map(async (updateData) => {
            const { id, shareholderId, ...rawData } = updateData;

            // 2025年06月24日 18:48:19：问题已解决，移除调试日志

            // 2025年06月24日 18:25:03：修复字段覆盖问题 - 过滤掉空值字段，避免覆盖现有数据
            const data: Record<string, unknown> = {};
            for (const [key, value] of Object.entries(rawData)) {
              // 只有当值不为null、undefined、空字符串时才包含在更新数据中
              if (value !== null && value !== undefined && String(value).trim() !== '') {
                data[key] = value;
              } 
            }


            // 2025年06月24日 18:45:34：关键测试 - 检查数据库更新前后的05字段状态
            // 先查询更新前的状态
            const beforeUpdate = await tx.shareholder.findUnique({
              where: {
                shareholderId_id: {
                  shareholderId: shareholderId as string,
                  id: id as string,
                },
              },
              select: {
                marginCollateralAccountName: true,
                marginCollateralAccountNumber: true,
                natureOfShares: true,
              },
            });


            // 2025年06月24日 18:41:23：关键调试 - 检查数据库更新前后的05字段状态
            const result = await tx.shareholder.update({
              where: {
                shareholderId_id: {
                  shareholderId: shareholderId as string,
                  id: id as string,
                },
              },
              data,
            });

            // 检查更新后的状态
            if (beforeUpdate && (beforeUpdate.marginCollateralAccountName || beforeUpdate.marginCollateralAccountNumber || beforeUpdate.natureOfShares)) {
              const afterUpdate = await tx.shareholder.findUnique({
                where: {
                  shareholderId_id: {
                    shareholderId: shareholderId as string,
                    id: id as string,
                  },
                },
                select: {
                  marginCollateralAccountName: true,
                  marginCollateralAccountNumber: true,
                  natureOfShares: true,
                },
              });
            }

            return result;
          })
        );
      }
    }

    // 7. 更新名册记录数
    if (!isNewRegistry) {
      // 查询当前实际的股东记录数量
      const actualShareholderCount = await tx.shareholder.count({
        where: {
          registryId: registry.id,
          organizationId: data.organizationId,
          registerDate: new Date(data.registerDate),
        },
      });

      // 更新名册记录数为实际合并后的股东数量
      await tx.shareholderRegistry.update({
        where: { id: registry.id },
        data: { recordCount: actualShareholderCount },
      });

      // 2025-06-19 16:35:37 hayden 修改：修复01/05名册合并时marginAccounts、marginShares未更新的bug
      // 无论是01名册还是05名册，都需要更新公司信息以确保marginAccounts、marginShares字段正确合并
      // 01名册：更新基础公司信息字段
      // 05名册：更新信用股东统计字段（marginAccounts、marginShares）
      if (currentType === "01" || currentType === "05") {

        // 2025-06-17 18:56:46 hayden 修改：先查询现有公司信息，采用"先到先得"原则保留05名册的信用股东统计字段
        const existingCompanyInfo = await tx.companyInfo.findFirst({
          where: {
            registryId: registry.id,
            organizationId: data.organizationId,
          },
          select: {
            id: true,
            marginAccounts: true,
            marginShares: true,
            companyName: true,
            totalShareholders: true,
            totalShares: true,
            totalInstitutions: true,
            largeSharesCount: true,
            institutionShares: true,
            largeShareholdersCount: true,
          },
        });

        // 准备更新数据，采用"先到先得"原则，只更新为空的字段
        const updateData: Record<string, unknown> = {};

        // 2025-07-02 11:07:59: hayden 修改 - 公司名称更新逻辑移到外层，无论01还是05名册都可以更新公司名称
        // 公司名称 - 如果现有名称为空且新数据有值
        if ((!existingCompanyInfo?.companyName || existingCompanyInfo.companyName === "") && data.companyInfo.companyName) {
          updateData.companyName = data.companyInfo.companyName;
        }

        // 01名册：更新基础公司信息字段
        if (currentType === "01") {

          // 总户数 - 如果现有总户数为0且新数据有值
          if ((existingCompanyInfo?.totalShareholders === null || existingCompanyInfo?.totalShareholders === 0) &&
              data.companyInfo.totalShareholders > 0) {
            updateData.totalShareholders = data.companyInfo.totalShareholders;
          }

          // 总股本 - 如果现有总股本为0且新数据有值
          if ((existingCompanyInfo?.totalShares === null || existingCompanyInfo?.totalShares?.equals(0)) &&
              data.companyInfo.totalShares) {
            updateData.totalShares = data.companyInfo.totalShares;
          }

          // 机构总数 - 如果现有机构总数为0且新数据有值
          if ((existingCompanyInfo?.totalInstitutions === null || existingCompanyInfo?.totalInstitutions === 0) &&
              data.companyInfo.totalInstitutions > 0) {
            updateData.totalInstitutions = data.companyInfo.totalInstitutions;
          }

          // 大股东持股数 - 如果现有大股东持股数为0且新数据有值
          if ((existingCompanyInfo?.largeSharesCount === null || existingCompanyInfo?.largeSharesCount?.equals(0)) &&
              data.companyInfo.largeSharesCount) {
            updateData.largeSharesCount = data.companyInfo.largeSharesCount;
          }

          // 机构持股数 - 如果现有机构持股数为0且新数据有值
          if ((existingCompanyInfo?.institutionShares === null || existingCompanyInfo?.institutionShares?.equals(0)) &&
              data.companyInfo.institutionShares) {
            updateData.institutionShares = data.companyInfo.institutionShares;
          }

          // 大股东数量 - 如果现有大股东数量为0且新数据有值
          if ((existingCompanyInfo?.largeShareholdersCount === null || existingCompanyInfo?.largeShareholdersCount === 0) &&
              data.companyInfo.largeShareholdersCount > 0) {
            updateData.largeShareholdersCount = data.companyInfo.largeShareholdersCount;
          }
        }

        // 信用股东统计字段处理 - 对01和05名册都生效
        // 如果现有公司信息中有信用股东统计字段且不为空，则保留这些字段
        // 如果当前名册也有这些字段且现有字段为空，则使用当前名册的数据
        if (existingCompanyInfo) {
          // 信用总户数 - 保留现有值，除非现有值为空且当前名册有值
          if ((existingCompanyInfo.marginAccounts === null || existingCompanyInfo.marginAccounts === 0) &&
              data.companyInfo.marginAccounts !== undefined && data.companyInfo.marginAccounts > 0) {
            updateData.marginAccounts = data.companyInfo.marginAccounts;
          } 

          // 信用总持股 - 保留现有值，除非现有值为空且当前名册有值
          if ((existingCompanyInfo.marginShares === null || existingCompanyInfo.marginShares?.equals(0)) &&
              data.companyInfo.marginShares) {
            updateData.marginShares = data.companyInfo.marginShares;
          } 
        } else {
          // 如果没有现有公司信息，直接使用当前名册的信用股东统计字段（如果有的话）
          if (data.companyInfo.marginAccounts !== undefined) {
            updateData.marginAccounts = data.companyInfo.marginAccounts;
          }
          if (data.companyInfo.marginShares !== undefined) {
            updateData.marginShares = data.companyInfo.marginShares;
          }
        }

        // 只有当有字段需要更新时才执行更新操作
        if (Object.keys(updateData).length > 0) {
          await tx.companyInfo.updateMany({
            where: {
              registryId: registry.id,
              organizationId: data.organizationId,
            },
            data: updateData,
          });

        } 
      }
    } else {

      // 如果是新名册，直接使用updateRecordCount函数
      await updateRecordCount(tx, registry, data);
    }

    return registry;
  }, {
    timeout: dynamicTimeout,
    isolationLevel: "ReadCommitted",
  });
} 