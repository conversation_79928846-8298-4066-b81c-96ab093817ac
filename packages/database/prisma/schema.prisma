// 数据源配置
datasource db {
  provider = "postgresql" // 使用PostgreSQL数据库
  url      = env("DATABASE_URL") // 从环境变量获取数据库连接URL
}

// Prisma客户端生成器配置
generator client {
  provider = "prisma-client-js" // 生成Prisma客户端代码
}

// Zod类型生成器配置
generator zod {
  provider         = "zod-prisma-types" // 使用zod-prisma-types生成Zod验证模式
  output           = "../src/zod" // 输出目录
  createInputTypes = false // 不生成输入类型
  addIncludeType   = false // 不添加include类型
  addSelectType    = false // 不添加select类型
}

// JSON类型生成器配置
generator json {
  provider = "prisma-json-types-generator" // 生成JSON类型定义
}

// 用户模型
model User {
  id                 String       @id // 用户ID,主键
  name               String // 用户名称
  email              String // 电子邮件
  emailVerified      Boolean // 邮箱是否验证
  image              String? // 用户头像URL,可选
  createdAt          DateTime // 创建时间
  updatedAt          DateTime // 更新时间
  username           String? // 用户名,可选
  role               String? // 用户角色,可选
  banned             Boolean? // 是否被封禁
  banReason          String? // 封禁原因
  banExpires         DateTime? // 封禁到期时间
  onboardingComplete Boolean      @default(false) // 是否完成引导,默认false
  paymentsCustomerId String? // 支付系统客户ID
  locale             String? // 用户语言偏好
  sessions           Session[] // 关联的会话
  accounts           Account[] // 关联的账户
  passkeys           Passkey[] // 关联的密钥
  invitations        Invitation[] // 关联的邀请
  purchases          Purchase[] // 关联的购买记录
  memberships        Member[] // 关联的成员身份
  aiChats            AiChat[] // 关联的AI聊天记录
  shareholderRegistries ShareholderRegistry[] // 关联的股东名册

  @@unique([email]) // 邮箱唯一索引
  @@unique([username]) // 用户名唯一索引
  @@map("user") // 映射到数据库表"user"
}

// 会话模型
model Session {
  id        String   @id // 会话ID,主键
  expiresAt DateTime // 过期时间
  ipAddress String? // IP地址
  userAgent String? // 用户代理
  userId    String // 关联的用户ID
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除

  impersonatedBy String? // 模拟登录者ID

  activeOrganizationId String? // 当前活动组织ID

  token     String // 会话令牌
  createdAt DateTime // 创建时间
  updatedAt DateTime // 更新时间

  @@unique([token]) // 令牌唯一索引
  @@map("session") // 映射到数据库表"session"
}

// 账户模型
model Account {
  id           String    @id // 账户ID,主键
  accountId    String // 外部账户ID
  providerId   String // 提供商ID
  userId       String // 关联的用户ID
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  accessToken  String? @db.Text // 访问令牌
  refreshToken String? @db.Text // 刷新令牌
  idToken      String? @db.Text // ID令牌
  expiresAt    DateTime? // 过期时间
  password     String? // 密码

  accessTokenExpiresAt  DateTime? // 访问令牌过期时间
  refreshTokenExpiresAt DateTime? // 刷新令牌过期时间
  scope                 String? // 权限范围
  createdAt             DateTime // 创建时间
  updatedAt             DateTime // 更新时间

  @@map("account") // 映射到数据库表"account"
}

// 验证模型
model Verification {
  id         String   @id // 验证ID,主键
  identifier String // 标识符
  value      String   @db.Text // 验证值
  expiresAt  DateTime // 过期时间

  createdAt DateTime? // 创建时间
  updatedAt DateTime? // 更新时间

  @@map("verification") // 映射到数据库表"verification"
}

// 密钥模型
model Passkey {
  id           String    @id // 密钥ID,主键
  name         String? // 密钥名称
  publicKey    String // 公钥
  userId       String // 关联的用户ID
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  credentialID String // 凭证ID
  counter      Int // 计数器
  deviceType   String // 设备类型
  backedUp     Boolean // 是否已备份
  transports   String? // 传输方式
  createdAt    DateTime? // 创建时间

  @@map("passkey") // 映射到数据库表"passkey"
}

// 组织模型
model Organization {
  id                 String       @id // 组织ID,主键
  name               String // 组织名称
  slug               String? // 组织别名
  logo               String? // 组织logo
  createdAt          DateTime // 创建时间
  metadata           String? // 元数据
  paymentsCustomerId String? // 支付系统客户ID
  members            Member[] // 关联的成员
  invitations        Invitation[] // 关联的邀请
  purchases          Purchase[] // 关联的购买记录
  aiChats            AiChat[] // 关联的AI聊天记录

  @@unique([slug]) // 别名唯一索引
  @@map("organization") // 映射到数据库表"organization"

  shareholderRegistries ShareholderRegistry[] // 关联的股东名册
  companyInfos         CompanyInfo[]         // 关联的公司信息
  shareholders         Shareholder[]         // 关联的股东信息

  // 投资人管理模块关联关系 - 2025-07-09 17:06:43 hayden 添加
  investorTags         InvestorTag[]         // 关联的投资人标签
  investorContacts     InvestorContact[]     // 关联的投资人联系人
  companyFilters       CompanyFilter[]       // 关联的公司筛选配置
}

// 成员模型
model Member {
  id             String       @id // 成员ID,主键
  organizationId String // 关联的组织ID
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  userId         String // 关联的用户ID
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  role           String // 角色
  createdAt      DateTime // 创建时间

  @@unique([userId, organizationId]) // 用户ID和组织ID联合唯一索引
  @@map("member") // 映射到数据库表"member"
}

// 邀请模型
model Invitation {
  id             String       @id // 邀请ID,主键
  organizationId String // 关联的组织ID
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  email          String // 邀请邮箱
  role           String? // 邀请角色
  status         String // 邀请状态
  expiresAt      DateTime // 过期时间
  inviterId      String // 邀请人ID
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade) // 关联用户,级联删除

  @@map("invitation") // 映射到数据库表"invitation"
}

// 购买类型枚举
enum PurchaseType {
  SUBSCRIPTION // 订阅
  ONE_TIME // 一次性购买
}

// 购买模型
model Purchase {
  id             String        @id @default(cuid()) // 购买ID,主键,自动生成
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  organizationId String? // 关联的组织ID
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  userId         String? // 关联的用户ID
  type           PurchaseType // 购买类型
  customerId     String // 客户ID
  subscriptionId String?       @unique // 订阅ID,唯一
  productId      String // 产品ID
  status         String? // 状态
  createdAt      DateTime      @default(now()) // 创建时间,默认当前时间
  updatedAt      DateTime      @updatedAt // 更新时间,自动更新

  @@index([subscriptionId]) // 订阅ID索引
}

// AI聊天模型
model AiChat {
  id             String        @id @default(cuid()) // 聊天ID,主键,自动生成
  organizationId String? // 关联的组织ID
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织,级联删除
  userId         String? // 关联的用户ID
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade) // 关联用户,级联删除
  title          String? // 聊天标题
  /// [AIChatMessages]
  messages       Json? // 聊天消息,JSON格式
  createdAt      DateTime      @default(now()) // 创建时间,默认当前时间
  updatedAt      DateTime      @updatedAt // 更新时间,自动更新
}

//2025-05-08 14:55:59 增加股东模型
// 股东名册表
model ShareholderRegistry {
  id             String         @id @default(cuid()) // 主键,自动生成
  fileName       String         // DBF文件名
  recordCount    Int            // 记录数量
  registerDate     DateTime       // 名册日期
  companyCode    String         // 公司代码
  organizationId String         // 关联的组织ID
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  userId         String         // 上传用户ID
  user           User           @relation(fields: [userId], references: [id], onDelete: SetNull) // 关联用户 指定 onDelete 删除用户时不会影响股东名册
  companyInfo    CompanyInfo[]  // 关联的公司信息
  shareholders   Shareholder[]  // 关联的股东信息
  uploadedAt     DateTime       @default(now()) // 上传时间

  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 名册日期索引
  @@unique([organizationId, companyCode, registerDate])  // 防止同一组织同一日期重复导入同一公司代码的名册
  @@map("shareholder_registry") // 映射到数据库表"shareholder_registry"
}

// 公司基本信息表
model CompanyInfo {
  id                     String              @id @default(cuid()) // 主键,自动生成
  registryId             String              @default("default_registry_id") // 关联的股东名册ID
  registry               ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId         String              @default("default_org_id") // 关联的组织ID
  organization           Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyCode            String              // 公司代码
  companyName            String              // 公司名称
  registerDate           DateTime            @default(now()) // 名册日期，默认为当前时间
  totalShareholders      Int                 @default(0) // 股东总数，默认为0
  totalInstitutions      Int                 @default(0) // 机构总数，默认为0
  largeShareholdersCount Int                 @default(0) // 大股东数量，默认为0
  largeSharesCount       Decimal             @default(0) @db.Decimal(17, 2) // 大股东持股数量，默认为0
  totalShares            Decimal             @db.Decimal(17, 2) // 总股数
  institutionShares      Decimal             @db.Decimal(17, 2) // 总机构股数

  // 新增信用股东统计字段 - 2025-06-13 12:02:02 hayden 添加
  marginAccounts         Int?                @default(0) // 信用总户数，默认为0
  marginShares           Decimal?            @default(0) @db.Decimal(17, 2) // 信用总持股，默认为0

  uploadedAt             DateTime            @default(now()) // 上传时间

  @@index([registryId]) // 名册ID索引
  @@index([companyCode]) // 公司代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([registerDate]) // 名册日期索引
  @@map("company_info") // 映射到数据库表"company_info"
}

// 股东信息表
model Shareholder {
  id                    String              @default(cuid()) // 生成的ID
  shareholderId         String              @default("default_shareholder_id") // 证件号码，作为优先主键
  registryId            String              @default("default_registry_id") // 关联的股东名册ID
  registry              ShareholderRegistry @relation(fields: [registryId], references: [id], onDelete: Cascade) // 关联股东名册，级联删除
  organizationId        String              @default("default_org_id") // 关联的组织ID
  organization          Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  unifiedAccountNumber  String              // 一码通账户号码
  securitiesAccountName String              // 证券账户名称
  shareholderCategory   String              // 持有人类别
  numberOfShares        Decimal             @db.Decimal(17, 2) // 持股数量
  lockedUpShares        Decimal             @db.Decimal(17, 2) // 限售股数量
  shareholdingRatio     Decimal             @db.Decimal(6, 2) // 持股比例
  frozenShares          Decimal             @db.Decimal(17, 2) // 冻结股数
  cashAccount           String?             // 普通证券账户
  sharesInCashAccount   Decimal?            @db.Decimal(17, 2) // 普通账户持股数量
  marginAccount         String?             // 信用证券账户
  sharesInMarginAccount Decimal?            @db.Decimal(17, 2) // 信用账户持股数量
  contactAddress        String?             // 通讯地址
  contactNumber         String?             // 电话号码
  zipCode               String?             // 邮政编码
  relatedPartyIndicator String?             // 关联关系确认标识
  clientCategory        String?             // 客户类别
  marginCollateralAccountNumber  String?    // 汇总账户号码（05名册），新增字段
  marginCollateralAccountName    String?    // 汇总账户名称（05名册），新增字段
  natureOfShares                 String?    // 股份性质（05名册），新增字段
  shareTradingCategory  String?             // 流通类型（沪市t1名册）
  rightsCategory        String?             // 权益类别（沪市t1名册）
  remarks               String?             // 备注

  // 新增股东类型字段 - 2025-06-25 11:33:27 hayden 添加
  shareholderType       String?             // 股东类型（如"知名牛散"、"境内个人"等）

  registerDate          DateTime            // 名册日期
  uploadedAt            DateTime            @default(now()) // 上传时间

  @@id([shareholderId, id]) // 设置双主键，shareholderId优先
  @@index([registryId]) // 名册ID索引
  @@index([organizationId]) // 组织ID索引
  @@index([unifiedAccountNumber]) // 一码通账户索引
  @@index([securitiesAccountName]) // 证券账户名称索引
  @@index([registerDate]) // 名册日期索引
  @@index([shareholderCategory]) // 持有人类别索引
  @@index([shareholderType]) // 股东类型索引 - 2025-06-25 11:33:27 hayden 添加
  @@index([shareholderId, registerDate]) // 组合索引：优化查询相同证件号码在不同名册期的记录
  @@map("shareholder") // 映射到数据库表"shareholder"
}

/**
 * 股东分类规则表（包含匹配字段类型）
 *
 * <AUTHOR>
 * @time 2025-06-26 14:18:31
 * @description 用于存储股东分类规则，支持基于优先级的股东自动分类
 * @update 2025-06-26 14:18:31 hayden 添加matchField字段，支持三种匹配方式
 */
model ShareholderClassificationRule {
  id         String   @id @default(cuid()) // 主键，自动生成
  priority   Int      // 优先级 1-20，1为最高优先级
  type       String   // 股东类型名称，如"知名牛散"、"境内个人"等
  rule       String   // 具体匹配规则，如"葛卫东"、"境内自然人（03）"等
  matchField String   @default("SHAREHOLDER_NAME") // 匹配字段类型：SHAREHOLDER_NAME、SHAREHOLDER_CATEGORY、SHAREHOLDER_NAME_INCLUDE
  updatedAt  DateTime @updatedAt // 更新时间，自动更新

  @@index([priority]) // 优先级索引，用于排序查询
  @@index([type]) // 类型索引，用于按类型筛选
  @@index([updatedAt]) // 更新时间索引，用于查询最新规则
  @@map("shareholder_classification_rule") // 映射到数据库表"shareholder_classification_rule"
}

/**
 * 公司筛选表 - 存储组织级别的本司和对标公司代码配置
 * <AUTHOR>
 * @created 2025-07-09 17:06:43
 * @updated 2025-07-09 17:06:43 hayden 根据投资人管理模块数据库设计方案添加公司筛选表
 * @description 组织级别的公司代码配置，用于标签系统的基础数据，与投资人标签表建立关联关系
 */
model CompanyFilter {
  id                    String       @id @default(cuid()) // 主键，自动生成
  organizationId        String       @unique // 关联的组织ID（一个组织只能有一套配置）
  organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyCode           String       // 本司公司代码
  benchmarkCompanyCodes String       // 对标公司代码（多个用逗号分隔）
  modifiedAt            DateTime     @default(now()) @updatedAt // 变更时间（创建和更新都使用此字段）

  // 关联关系：与投资人标签表建立关联，当公司筛选配置删除时，相关标签也会被级联删除
  investorTags          InvestorTag[] // 关联的投资人标签

  @@map("company_filter") // 映射到数据库表"company_filter"
}

/**
 * 投资人标签表 - 通用标签系统
 * <AUTHOR>
 * @created 2025-07-09 17:06:43
 * @updated 2025-07-09 17:06:43 hayden 根据投资人管理模块数据库设计方案添加投资人标签表
 * @description 支持系统自动标签（持仓类型）和用户操作标签（收藏）的统一管理，与公司筛选表建立关联
 */
model InvestorTag {
  id               String        @id @default(cuid()) // 主键，自动生成
  organizationId   String        // 关联的组织ID
  organization     Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  companyFilterId  String?       // 关联的公司筛选配置ID（可选，仅系统自动生成的持仓标签需要）
  companyFilter    CompanyFilter? @relation(fields: [companyFilterId], references: [id], onDelete: Cascade) // 关联公司筛选配置，级联删除
  investorCode     String        // 投资人代码（基金代码）
  tagName          String        // 标签名称（如：持有本司, 收藏等，支持动态）
  tagCategory      String        // 标签分类（system: 系统自动生成, user: 用户操作生成, custom: 自定义标签）
  tagMetadata      Json?         // 标签元数据（存储标签的额外配置信息，如颜色、图标、排序等）
  modifiedAt       DateTime      @default(now()) @updatedAt // 变更时间（创建和更新都使用此字段）

  @@index([investorCode]) // 基金代码索引
  @@index([organizationId]) // 组织ID索引
  @@index([companyFilterId]) // 公司筛选配置ID索引
  @@index([tagName]) // 标签名称索引
  @@index([tagCategory]) // 标签分类索引
  @@index([modifiedAt]) // 变更时间索引
  @@unique([organizationId, investorCode, tagName]) // 唯一约束：防重复标签
  @@map("investor_tag") // 映射到数据库表"investor_tag"
}

/**
 * 投资人联系人表 - 存储投资人联系人信息
 * <AUTHOR>
 * @created 2025-07-09 17:06:43
 * @updated 2025-07-30 17:11:10 hayden 新增基金代码和一码通ID字段支持投资人分类管理
 * @description 组织级别的投资人联系人管理，支持联系人信息的增删改查，区分基金投资者和个人投资者
 */
model InvestorContact {
  contactId         String       @id @default(cuid()) // 联系人ID，自动生成
  organizationId    String       // 关联的组织ID
  organization      Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade) // 关联组织，级联删除
  name              String       // 联系人姓名（必填）
  phoneNumber       String?      // 电话号码
  email             String?      // 邮箱地址
  address           String?      // 联系地址
  remarks           String?      // 备注信息
  fundCode          String?      // 基金代码，用于标识机构投资者（新增）
  unifiedAccountId  String?      // 一码通账户ID，用于标识个人投资者（新增）
  createdAt         DateTime     @default(now()) // 创建时间
  updatedAt         DateTime     @updatedAt // 更新时间
  createdBy         String?      // 创建人用户ID
  updatedBy         String?      // 更新人用户ID

  @@index([organizationId]) // 组织ID索引
  @@index([name]) // 姓名索引
  @@index([phoneNumber]) // 电话号码索引
  @@index([email]) // 邮箱索引
  @@index([fundCode]) // 基金代码索引（新增）
  @@index([unifiedAccountId]) // 一码通ID索引（新增）
  @@index([createdAt]) // 创建时间索引
  @@map("investor_contact") // 映射到数据库表"investor_contact"
}

/**
 * 基金基本信息表
 * 存储基金的核心基础信息，作为整个基金数据体系的主表
 * 通过基金代码与其他业务表建立关联关系
 * 
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-27 16:47:31
 * @description 基金基本信息的核心存储表，包含基金代码、名称、投资类型、成立日期、净值、费率等关键信息
 */
model FundBasicInfo {
  fundCode              String    @id @map("fund_code") /// 基金代码
  fundName              String    @map("fund_name") /// 基金名称
  investmentType        String?   @map("investment_type") /// 投资类型（二级分类）
  establishmentDate     DateTime? @map("establishment_date") @db.Date /// 基金成立日
  trackingIndexName     String?   @map("tracking_index_name") /// 跟踪指数名称
  managementCompanyName String?   @map("management_company_name") /// 基金管理人中文名称
  managerCode1st        String?   @map("manager_code_1st") /// 基金经理代码[任职状态]现任[任职年化回报排名]第1名
  managerName1st        String?   @map("manager_name_1st") /// 姓名[任职状态]现任[任职年化回报排名]第1名
  managerCode2nd        String?   @map("manager_code_2nd") /// 基金经理代码[任职状态]现任[任职年化回报排名]第2名
  managerName2nd        String?   @map("manager_name_2nd") /// 姓名[任职状态]现任[任职年化回报排名]第2名
  relatedFundCode       String?   @map("related_fund_code") /// 关联基金代码
  netAssetValue         Decimal?  @map("net_asset_value") @db.Decimal(15, 2) /// 基金资产净值(亿元)
  unitNetValue          Decimal?  @map("unit_net_value") @db.Decimal(10, 4) /// 单位净值(元)
  closingPrice          Decimal?  @map("closing_price") @db.Decimal(10, 4) /// 收盘价(元)
  managementFeeRate     Decimal?  @map("management_fee_rate") @db.Decimal(8, 4) /// 管理费率(%)
  priceChangePct        Decimal?  @map("price_change_pct") @db.Decimal(10, 4) /// 涨跌幅(%)
  custodyFeeRate        Decimal?  @map("custody_fee_rate") @db.Decimal(8, 4) /// 托管费率(%)
  tradingVolume         BigInt?   @map("trading_volume") /// 成交额(元)
  turnoverRate          Decimal?  @map("turnover_rate") @db.Decimal(10, 4) /// 换手率(%)
  premiumDiscountRate   Decimal?  @map("premium_discount_rate") @db.Decimal(10, 4) /// 相对净值折溢价率(%)
  tableUpdatedAt        DateTime  @default(now()) @map("table_updated_at") /// 表更新时间

// 保留的核心索引
@@index([fundName])                    // 基金名称查询
@@map("ths_fund_basic_info")
}

/**
 * 基金业绩回报表
 * 存储基金在不同时间周期的收益率表现数据
 * 用于分析基金的历史业绩和投资回报情况
 * 
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-27 16:47:31
 * @description 基金业绩回报数据表，包含今年以来、近期各时间段的总回报率统计
 */
model FundPerformanceReturn {
  fundCode              String    @id @map("fund_code") /// 基金代码
  fundName              String    @map("fund_name") /// 基金简称
  latestDate            DateTime? @map("latest_date") @db.Date /// 最新日期
  previousDate          DateTime? @map("previous_date") @db.Date /// 上期日期
  previousUnitNetValue  Decimal?  @map("previous_unit_net_value") @db.Decimal(10, 4) /// 上期单位净值
  ytdTotalReturn        Decimal?  @map("ytd_total_return") @db.Decimal(10, 4) /// 今年以来总回报(%)
  week1TotalReturn      Decimal?  @map("week_1_total_return") @db.Decimal(10, 4) /// 最近一周总回报(%)
  month1TotalReturn     Decimal?  @map("month_1_total_return") @db.Decimal(10, 4) /// 最近一月总回报(%)
  month3TotalReturn     Decimal?  @map("month_3_total_return") @db.Decimal(10, 4) /// 最近三月总回报(%)
  month6TotalReturn     Decimal?  @map("month_6_total_return") @db.Decimal(10, 4) /// 最近六月总回报(%)
  year1TotalReturn      Decimal?  @map("year_1_total_return") @db.Decimal(10, 4) /// 最近一年总回报(%)
  year2TotalReturn      Decimal?  @map("year_2_total_return") @db.Decimal(10, 4) /// 最近两年总回报(%)
  year3TotalReturn      Decimal?  @map("year_3_total_return") @db.Decimal(10, 4) /// 最近三年总回报(%)
  year5TotalReturn      Decimal?  @map("year_5_total_return") @db.Decimal(10, 4) /// 最近五年总回报(%)
  sinceInceptionReturn  Decimal?  @map("since_inception_return") @db.Decimal(10, 4) /// 成立以来总回报(%)
  tableUpdatedAt        DateTime  @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([fundName])           // 基金名称查询
  @@map("ths_fund_performance_return")
}

/**
 * 基金行业配置表
 * 存储基金的重仓行业分布情况和配置比例
 * 用于分析基金的行业投资偏好和风险分散情况
 * 
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-27 16:47:31
 * @description 基金行业配置数据表，包含前10大重仓行业及其占基金资产净值的比例
 */
model FundIndustryAllocation {
  fundCode         String   @id @map("fund_code") /// 证券代码
  fundName         String   @map("fund_name") /// 证券名称
  topIndustry1     String?  @map("top_industry_1") /// 重仓行业名称[排名]第1名
  topIndustry2     String?  @map("top_industry_2") /// 重仓行业名称[排名]第2名
  topIndustry3     String?  @map("top_industry_3") /// 重仓行业名称[排名]第3名
  topIndustry4     String?  @map("top_industry_4") /// 重仓行业名称[排名]第4名
  topIndustry5     String?  @map("top_industry_5") /// 重仓行业名称[排名]第5名
  topIndustry6     String?  @map("top_industry_6") /// 重仓行业名称[排名]第6名
  topIndustry7     String?  @map("top_industry_7") /// 重仓行业名称[排名]第7名
  topIndustry8     String?  @map("top_industry_8") /// 重仓行业名称[排名]第8名
  topIndustry9     String?  @map("top_industry_9") /// 重仓行业名称[排名]第9名
  topIndustry10    String?  @map("top_industry_10") /// 重仓行业名称[排名]第10名
  industryRatio1   Decimal? @map("industry_ratio_1") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第1名[单位]%
  industryRatio2   Decimal? @map("industry_ratio_2") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第2名[单位]%
  industryRatio3   Decimal? @map("industry_ratio_3") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第3名[单位]%
  industryRatio4   Decimal? @map("industry_ratio_4") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第4名[单位]%
  industryRatio5   Decimal? @map("industry_ratio_5") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第5名[单位]%
  industryRatio6   Decimal? @map("industry_ratio_6") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第6名[单位]%
  industryRatio7   Decimal? @map("industry_ratio_7") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第7名[单位]%
  industryRatio8   Decimal? @map("industry_ratio_8") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第8名[单位]%
  industryRatio9   Decimal? @map("industry_ratio_9") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第9名[单位]%
  industryRatio10  Decimal? @map("industry_ratio_10") @db.Decimal(12, 4) /// 重仓行业市值占基金资产净值比[排名]第10名[单位]%
  tableUpdatedAt   DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([fundName]) /// 证券名称
  @@map("ths_fund_industry_allocation")
}

/**
 * 基金重仓股票和债券表
 * 存储基金的重仓股票和债券持仓明细信息
 * 包含持仓代码、名称、市值、占比、数量等详细数据
 *
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-27 16:47:31
 * @description 基金重仓持仓数据表，包含前10大重仓股票和前5大重仓债券的详细持仓信息
 */
model FundTopHoldingsStock {
  fundCode            String   @id @map("fund_code") /// 证券代码
  fundName            String   @map("fund_name") /// 证券名称
  // 重仓股票代码 (1-10名)
  stockCode1          String?  @map("stock_code_1") /// 重仓证券代码[排名]第1名
  stockCode2          String?  @map("stock_code_2") /// 重仓证券代码[排名]第2名
  stockCode3          String?  @map("stock_code_3") /// 重仓证券代码[排名]第3名
  stockCode4          String?  @map("stock_code_4") /// 重仓证券代码[排名]第4名
  stockCode5          String?  @map("stock_code_5") /// 重仓证券代码[排名]第5名
  stockCode6          String?  @map("stock_code_6") /// 重仓证券代码[排名]第6名
  stockCode7          String?  @map("stock_code_7") /// 重仓证券代码[排名]第7名
  stockCode8          String?  @map("stock_code_8") /// 重仓证券代码[排名]第8名
  stockCode9          String?  @map("stock_code_9") /// 重仓证券代码[排名]第9名
  stockCode10         String?  @map("stock_code_10") /// 重仓证券代码[排名]第10名
  // 重仓股票名称 (1-10名)
  stockName1          String?  @map("stock_name_1") /// 重仓证券名称[排名]第1名
  stockName2          String?  @map("stock_name_2") /// 重仓证券名称[排名]第2名
  stockName3          String?  @map("stock_name_3") /// 重仓证券名称[排名]第3名
  stockName4          String?  @map("stock_name_4") /// 重仓证券名称[排名]第4名
  stockName5          String?  @map("stock_name_5") /// 重仓证券名称[排名]第5名
  stockName6          String?  @map("stock_name_6") /// 重仓证券名称[排名]第6名
  stockName7          String?  @map("stock_name_7") /// 重仓证券名称[排名]第7名
  stockName8          String?  @map("stock_name_8") /// 重仓证券名称[排名]第8名
  stockName9          String?  @map("stock_name_9") /// 重仓证券名称[排名]第9名
  stockName10         String?  @map("stock_name_10") /// 重仓证券名称[排名]第10名
  // 重仓股票市值 (1-10名)
  stockMarketValue1   BigInt?  @map("stock_market_value_1") /// 重仓证券持仓市值[排名]第1名[单位]元
  stockMarketValue2   BigInt?  @map("stock_market_value_2") /// 重仓证券持仓市值[排名]第2名[单位]元
  stockMarketValue3   BigInt?  @map("stock_market_value_3") /// 重仓证券持仓市值[排名]第3名[单位]元
  stockMarketValue4   BigInt?  @map("stock_market_value_4") /// 重仓证券持仓市值[排名]第4名[单位]元
  stockMarketValue5   BigInt?  @map("stock_market_value_5") /// 重仓证券持仓市值[排名]第5名[单位]元
  stockMarketValue6   BigInt?  @map("stock_market_value_6") /// 重仓证券持仓市值[排名]第6名[单位]元
  stockMarketValue7   BigInt?  @map("stock_market_value_7") /// 重仓证券持仓市值[排名]第7名[单位]元
  stockMarketValue8   BigInt?  @map("stock_market_value_8") /// 重仓证券持仓市值[排名]第8名[单位]元
  stockMarketValue9   BigInt?  @map("stock_market_value_9") /// 重仓证券持仓市值[排名]第9名[单位]元
  stockMarketValue10  BigInt?  @map("stock_market_value_10") /// 重仓证券持仓市值[排名]第10名[单位]元
  // 重仓股票占比 (1-10名)
  stockRatio1         Decimal? @map("stock_ratio_1") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第1名[单位]%
  stockRatio2         Decimal? @map("stock_ratio_2") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第2名[单位]%
  stockRatio3         Decimal? @map("stock_ratio_3") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第3名[单位]%
  stockRatio4         Decimal? @map("stock_ratio_4") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第4名[单位]%
  stockRatio5         Decimal? @map("stock_ratio_5") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第5名[单位]%
  stockRatio6         Decimal? @map("stock_ratio_6") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第6名[单位]%
  stockRatio7         Decimal? @map("stock_ratio_7") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第7名[单位]%
  stockRatio8         Decimal? @map("stock_ratio_8") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第8名[单位]%
  stockRatio9         Decimal? @map("stock_ratio_9") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第9名[单位]%
  stockRatio10        Decimal? @map("stock_ratio_10") @db.Decimal(12, 4) /// 重仓证券市值占基金资产净值比[排名]第10名[单位]%
// 重仓股票数量 (1-10名)
  stockQuantity1      BigInt?  @map("stock_quantity_1") /// 重仓证券持仓数量[排名]第1名[单位]份
  stockQuantity2      BigInt?  @map("stock_quantity_2") /// 重仓证券持仓数量[排名]第2名[单位]份
  stockQuantity3      BigInt?  @map("stock_quantity_3") /// 重仓证券持仓数量[排名]第3名[单位]份
  stockQuantity4      BigInt?  @map("stock_quantity_4") /// 重仓证券持仓数量[排名]第4名[单位]份
  stockQuantity5      BigInt?  @map("stock_quantity_5") /// 重仓证券持仓数量[排名]第5名[单位]份
  stockQuantity6      BigInt?  @map("stock_quantity_6") /// 重仓证券持仓数量[排名]第6名[单位]份
  stockQuantity7      BigInt?  @map("stock_quantity_7") /// 重仓证券持仓数量[排名]第7名[单位]份
  stockQuantity8      BigInt?  @map("stock_quantity_8") /// 重仓证券持仓数量[排名]第8名[单位]份
  stockQuantity9      BigInt?  @map("stock_quantity_9") /// 重仓证券持仓数量[排名]第9名[单位]份
  stockQuantity10     BigInt?  @map("stock_quantity_10") /// 重仓证券持仓数量[排名]第10名[单位]份
  // 重仓债券代码 (1-5名)
bondCode1           String?  @map("bond_code_1") /// 重仓债券代码[排名]第1名
  bondCode2           String?  @map("bond_code_2") /// 重仓债券代码[排名]第2名
  bondCode3           String?  @map("bond_code_3") /// 重仓债券代码[排名]第3名
  bondCode4           String?  @map("bond_code_4") /// 重仓债券代码[排名]第4名
  bondCode5           String?  @map("bond_code_5") /// 重仓债券代码[排名]第5名
  // 重仓债券名称 (1-5名)
  bondName1           String?  @map("bond_name_1") /// 重仓债券名称[排名]第1名
  bondName2           String?  @map("bond_name_2") /// 重仓债券名称[排名]第2名
  bondName3           String?  @map("bond_name_3") /// 重仓债券名称[排名]第3名
  bondName4           String?  @map("bond_name_4") /// 重仓债券名称[排名]第4名
  bondName5           String?  @map("bond_name_5") /// 重仓债券名称[排名]第5名
  // 重仓债券市值 (1-5名)
  bondMarketValue1    BigInt?  @map("bond_market_value_1") /// 重仓债券持有市值[排名]第1名[单位]元
  bondMarketValue2    BigInt?  @map("bond_market_value_2") /// 重仓债券持有市值[排名]第2名[单位]元
  bondMarketValue3    BigInt?  @map("bond_market_value_3") /// 重仓债券持有市值[排名]第3名[单位]元
  bondMarketValue4    BigInt?  @map("bond_market_value_4") /// 重仓债券持有市值[排名]第4名[单位]元
  bondMarketValue5    BigInt?  @map("bond_market_value_5") /// 重仓债券持有市值[排名]第5名[单位]元
  // 重仓债券占比 (1-5名)
  bondRatio1          Decimal? @map("bond_ratio_1") @db.Decimal(12, 4) /// 重仓债券市值占基金资产净值比[排名]第1名[单位]%
  bondRatio2          Decimal? @map("bond_ratio_2") @db.Decimal(12, 4) /// 重仓债券市值占基金资产净值比[排名]第2名[单位]%
  bondRatio3          Decimal? @map("bond_ratio_3") @db.Decimal(12, 4) /// 重仓债券市值占基金资产净值比[排名]第3名[单位]%
  bondRatio4          Decimal? @map("bond_ratio_4") @db.Decimal(12, 4) /// 重仓债券市值占基金资产净值比[排名]第4名[单位]%
  bondRatio5          Decimal? @map("bond_ratio_5") @db.Decimal(12, 4) /// 重仓债券市值占基金资产净值比[排名]第5名[单位]%
  // 重仓债券数量 (1-5名)
  bondQuantity1       BigInt?  @map("bond_quantity_1") /// 重仓债券持仓数量[排名]第1名[单位]张
  bondQuantity2       BigInt?  @map("bond_quantity_2") /// 重仓债券持仓数量[排名]第2名[单位]张
  bondQuantity3       BigInt?  @map("bond_quantity_3") /// 重仓债券持仓数量[排名]第3名[单位]张
  bondQuantity4       BigInt?  @map("bond_quantity_4") /// 重仓债券持仓数量[排名]第4名[单位]张
  bondQuantity5       BigInt?  @map("bond_quantity_5") /// 重仓债券持仓数量[排名]第5名[单位]张
  tableUpdatedAt      DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([fundName])      // 基金名称查询
  @@map("ths_fund_top_holdings_stock")
}

/**
 * 基金经理基本信息表
 * 存储基金经理的个人基本信息和从业经历
 * 包含第1名和第2名基金经理的完整信息，包括学历、任职年限、管理规模等关键信息
 *
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-30 16:47:31
 * @description 基金经理基本信息数据表，包含基金经理的个人资料、从业经历和管理业绩统计，支持双经理管理模式
 */
model FundManagerBasicInfo {
  fundCode                    String   @id @map("fund_code") /// 证券代码
  fundName                    String   @map("fund_name") /// 证券名称

  // 第1名基金经理信息
  managerName1st              String?  @map("manager_name_1st") /// 姓名[任职状态]现任[任职年化回报排名]第1名
  managerCode1st              String?  @map("manager_code_1st") /// 基金经理代码[任职状态]现任[任职年化回报排名]第1名
  gender1st                   String?  @map("gender_1st") /// 性别[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
  education1st                String?  @map("education_1st") /// 学历[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
  tenureDays1st               Int?     @map("tenure_days_1st") /// 任职天数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]天
  tenureYears1st              Decimal? @map("tenure_years_1st") @db.Decimal(8, 2) /// 自然任职年限[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]年
  historicalFundCount1st      Int?     @map("historical_fund_count_1st") /// 历任基金数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]个
  totalFundScale1st           Decimal? @map("total_fund_scale_1st") @db.Decimal(20, 2) /// 任职基金总规模[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位设置]万元
  tenureDaysDuplicate1st      Int?     @map("tenure_days_duplicate_1st") /// 任职天数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]天
  currentFundCount1st         Int?     @map("current_fund_count_1st") /// 在任基金数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
  awardCount1st               Int?     @map("award_count_1st") /// 经理获奖次数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]次
  resume1st                   String?  @map("resume_1st") @db.Text /// 简历[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否

  // 第2名基金经理信息
  managerName2nd              String?  @map("manager_name_2nd") /// 姓名[任职状态]现任[任职年化回报排名]第2名
  managerCode2nd              String?  @map("manager_code_2nd") /// 基金经理代码[任职状态]现任[任职年化回报排名]第2名
  gender2nd                   String?  @map("gender_2nd") /// 性别[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
  education2nd                String?  @map("education_2nd") /// 学历[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
  tenureDays2nd               Int?     @map("tenure_days_2nd") /// 任职天数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]天
  tenureYears2nd              Decimal? @map("tenure_years_2nd") @db.Decimal(8, 2) /// 自然任职年限[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]年
  historicalFundCount2nd      Int?     @map("historical_fund_count_2nd") /// 历任基金数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]个
  totalFundScale2nd           Decimal? @map("total_fund_scale_2nd") @db.Decimal(20, 2) /// 任职基金总规模[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位设置]万元
  tenureDaysDuplicate2nd      Int?     @map("tenure_days_duplicate_2nd") /// 任职天数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]天
  currentFundCount2nd         Int?     @map("current_fund_count_2nd") /// 在任基金数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
  awardCount2nd               Int?     @map("award_count_2nd") /// 经理获奖次数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]次
  resume2nd                   String?  @map("resume_2nd") @db.Text /// 简历[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否

  tableUpdatedAt              DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  // 保留核心查询索引
  @@index([fundName])
  @@index([managerName1st])
  @@index([managerName2nd])
  @@index([managerCode1st])
  @@index([managerCode2nd])
  @@index([totalFundScale1st])
  @@index([totalFundScale2nd])
  @@index([tableUpdatedAt])
  @@map("ths_fund_manager_basic_info")
}

/**
 * 基金经理业绩回报表
 * 存储基金经理在不同时间周期的投资业绩表现
 * 用于评估基金经理的投资管理能力和历史业绩
 *
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-01-30 16:47:31
 * @description 基金经理业绩回报数据表，包含基金经理在各时间段的投资回报率统计
 * @modification 2025-01-30 16:47:31 - 修改主键策略：将managerName和managedFundType设为复合主键，解决基金经理名称重复问题
 */
model FundManagerPerformanceReturn {
  managerName           String   @map("manager_name") /// 基金经理
  managedFundType       String   @map("managed_fund_type") /// 在管基金类型
  ytdTotalReturn        Decimal? @map("ytd_total_return") @db.Decimal(10, 4) /// 今年以来总回报(%)
  month1TotalReturn     Decimal? @map("month_1_total_return") @db.Decimal(10, 4) /// 最近一月总回报(%)
  month3TotalReturn     Decimal? @map("month_3_total_return") @db.Decimal(10, 4) /// 最近三月总回报(%)
  month6TotalReturn     Decimal? @map("month_6_total_return") @db.Decimal(10, 4) /// 最近六月总回报(%)
  year1TotalReturn      Decimal? @map("year_1_total_return") @db.Decimal(10, 4) /// 最近一年总回报(%)
  year2TotalReturn      Decimal? @map("year_2_total_return") @db.Decimal(10, 4) /// 最近两年总回报(%)
  year3TotalReturn      Decimal? @map("year_3_total_return") @db.Decimal(10, 4) /// 最近三年总回报(%)
  geometricAnnualReturn Decimal? @map("geometric_annual_return") @db.Decimal(10, 4) /// 几何任职年化回报(%)
  fundCompany           String?  @map("fund_company") /// 基金公司
  tableUpdatedAt        DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  // 复合主键：基金经理名称 + 在管基金类型
  @@id([managerName, managedFundType])

  // 保留核心查询索引
  @@index([managedFundType])
  @@index([year1TotalReturn])
  @@index([geometricAnnualReturn])
  @@index([fundCompany])
  @@index([tableUpdatedAt])
  @@map("ths_fund_manager_performance_return")
}

/**
 * 基金经理管理基金表
 * 存储基金经理与其管理基金的对应关系
 * 包含任职起始日期、投资类型等关键信息
 * 
 * <AUTHOR>
 * @created 2025-01-27
 * @description 基金经理管理基金关系表，记录基金经理的基金管理历史和当前管理状态
 */
model FundManagerManagedFunds {
  id              Int      @id @default(autoincrement()) @map("id") /// 自增主键
  managerName     String   @map("manager_name") /// 基金经理
  fundCode        String   @map("fund_code") /// 代码
  fundName        String   @map("fund_name") /// 名称
  startDate       DateTime? @map("start_date") @db.Date /// 任职起始日
  investmentType  String?  @map("investment_type") /// 投资类型
  fundCompany     String?  @map("fund_company") /// 基金公司
  tableUpdatedAt  DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  @@unique([managerName, fundCode], map: "unique_manager_fund")
  @@index([managerName])
  @@index([fundCode])
  @@map("ths_fund_manager_managed_funds")
}

/**
 * 基金公司基本信息表
 * 存储基金管理公司的基本信息和经营数据
 * 包含公司规模、基金数量、经理人数等关键指标
 *
 * <AUTHOR>
 * @created 2025-01-27 16:47:31
 * @updated 2025-07-31 13:47:31
 * @description 基金管理公司基本信息数据表，包含公司成立信息、资产规模、管理团队等核心数据
 * @modification 2025-07-31 13:47:31 - 根据ths_fund_management_company_basic_info8.xlsx文件完整解析，包含表格所有13个字段：证券代码、证券名称、基金管理人中文名称、基金管理人成立日期、基金管理人注册资本、基金管理人总经理、基金管理人资产净值合计、旗下基金数、基金经理数、基金管理人电话、基金管理人传真、基金管理人主页、基金管理人办公地址
 */
model FundManagementCompanyBasicInfo {
  fundCode             String   @id @map("fund_code") /// 证券代码
  fundName             String   @map("fund_name") /// 证券名称
  companyName          String?  @map("company_name") /// 基金管理人中文名称
  establishmentDate    DateTime? @map("establishment_date") @db.Date /// 基金管理人成立日期
  registeredCapital    String?  @map("registered_capital") /// 基金管理人注册资本
  generalManager       String?  @map("general_manager") /// 基金管理人总经理
  totalNetAssetValue   Decimal? @map("total_net_asset_value") @db.Decimal(15, 2) /// 基金管理人资产净值合计[报告期]最新一期(MRQ)[单位]亿元
  fundCount            Int?     @map("fund_count") /// 旗下基金数
  managerCount         Int?     @map("manager_count") /// 基金经理数
  companyPhone         String?  @map("company_phone") /// 基金管理人电话
  companyFax           String?  @map("company_fax") /// 基金管理人传真
  companyWebsite       String?  @map("company_website") /// 基金管理人主页
  officeAddress        String?  @map("office_address") /// 基金管理人办公地址
  tableUpdatedAt       DateTime @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([fundName])
  @@index([companyName])
  @@index([establishmentDate])
  @@index([totalNetAssetValue])
  @@index([fundCount])
  @@index([tableUpdatedAt])
  @@map("ths_fund_management_company_basic_info")
}
/**
 * 基金管理和托管信息表
 * 存储基金的管理人和托管人信息
 * 包含基金代码、名称、全称、托管人、管理人等核心信息
 *
 * <AUTHOR>
 * @created 2025-01-30 16:47:31
 * @updated 2025-01-30 16:47:31
 * @description 基金管理和托管信息数据表，记录基金的管理机构和托管机构信息，用于基金监管和风险控制
 */
model FundManagementCustody {
  fundCode              String    @id @map("fund_code") /// 证券代码
  fundName              String    @map("fund_name") /// 证券名称
  fundFullName          String?   @map("fund_full_name") /// 基金全称
  custodian             String?   @map("custodian") /// 基金托管人
  managementCompanyName String?   @map("management_company_name") /// 基金管理人中文名称
  tableUpdatedAt        DateTime  @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([fundName])                    // 基金名称查询
  @@index([custodian])                   // 托管人查询
  @@index([managementCompanyName])       // 管理人查询
  @@index([tableUpdatedAt])              // 更新时间查询
  @@map("ths_fund_management_custody")
}

/**
 * 股票行业分类信息表
 * 存储股票的行业分类信息，包含多种行业分类标准
 * 以股票代码为唯一主键，包含证监会、同花顺、申万等行业分类
 *
 * <AUTHOR>
 * @created 2025-07-30 16:47:31
 * @updated 2025-07-30 16:47:31
 * @description 股票行业分类数据表，记录股票在不同行业分类标准下的归属，用于行业分析和投资研究
 */
model StockIndustryClassification {
  stockCode             String    @id @map("stock_code") /// 股票代码
  stockName             String    @map("stock_name") /// 股票名称
  csrcIndustryNew       String?   @map("csrc_industry_new") /// 证监会行业分类(新)
  thsIndustryNew        String?   @map("ths_industry_new") /// 同花顺行业分类(新)
  swIndustry            String?   @map("sw_industry") /// 申万行业分类
  tableUpdatedAt        DateTime  @default(now()) @map("table_updated_at") /// 表更新时间

  @@index([stockName])                    // 股票名称查询
  @@index([csrcIndustryNew])              // 证监会行业分类查询
  @@index([thsIndustryNew])               // 同花顺行业分类查询
  @@index([swIndustry])                   // 申万行业分类查询
  @@index([tableUpdatedAt])               // 更新时间查询
  @@map("ths_stock_industry_classification")
}

/**
 * 证券（股票）信息表
 * @description 存储证券基本信息及行业分类，通过证券代码唯一标识
 */
model TonghuashunStockIndustryInfo {
  securityCode       String  @id @map("security_code") // 证券代码（如股票代码）
  securityName       String  @map("security_name") // 证券简称
  companyFullName    String  @map("company_full_name") // 公司全称
  industryCategory   String  @map("industry_category") // 所属新证监会行业（门类行业）
  industryBigCategory String? @map("industry_big_category") // 所属新证监会行业（大类行业）
  industryDetail     String? @map("industry_detail") // 所属新证监会行业（全部明细）
  updateTime         DateTime @map("update_time") // 表格更新时间

  // 注释掉有问题的反向关联 - 2025-01-27 jason修复
  // 原因：industryCategory字段不是唯一的，无法建立正确的关联关系
  // fundIndustryInfos  TonghuashunFundIndustryInfo[] @relation("StockIndustryFundRelation")

  // 添加索引
  @@index([securityCode]) // 证券代码索引
  @@index([securityName]) // 证券简称索引
  @@index([companyFullName]) // 公司全称索引
  @@index([industryCategory]) // 门类行业索引


  @@map("tonghuashun_stock_industry_info")
}

/**
 * 基金行业配置表
 * @description 记录基金在各行业的配置情况，通过基金代码和行业代码联合唯一标识
 * <AUTHOR>
 * @updated 2025-01-27 14:30:00 修复Decimal类型语法和关联关系语法错误
 */
model TonghuashunFundIndustryInfo {
  fundCode                String  @map("fund_code") // 基金代码
  industryCode            String  @map("industry_code") // 行业代码
  fundName                String  @map("fund_name") // 基金名称
  industryName            String  @map("industry_name") // 行业名称
  marketValue             Decimal? @map("market_value") @db.Decimal(15, 2) // 市值 (万元)
  netValueRatio           Decimal? @map("net_value_ratio") @db.Decimal(10, 4) // 占净值比 (%)
  stockInvestRatio        Decimal? @map("stock_invest_ratio") @db.Decimal(10, 4) // 占股票投资市值比 (%)
  standardAllocationRatio Decimal? @map("standard_allocation_ratio") @db.Decimal(10, 4) // 股票市场标准行业配置比例 (%)
  relativeAllocationRatio Decimal? @map("relative_allocation_ratio") @db.Decimal(10, 4) // 相对标准行业配置比例 (%)
  marketValueGrowthRate   Decimal? @map("market_value_growth_rate") @db.Decimal(10, 4) // 市值增长率 (%)
  investmentType          String? @map("investment_type") // 投资类型
  managementCompany       String? @map("management_company") // 管理公司
  updateTime              DateTime @map("update_time") // 表格更新时间

  // 注释掉有问题的关联关系 - 2025-01-27 jason修复
  // 原因：industryCategory字段不是唯一的，多个股票可能属于同一行业
  // stockIndustry TonghuashunStockIndustryInfo @relation("StockIndustryFundRelation", fields: [industryName], references: [industryCategory])

  // 联合主键
  @@id([fundCode, industryCode])

  // 添加索引
  @@index([fundCode]) // 基金代码索引
  @@index([industryCode]) // 行业代码索引
  @@index([industryName]) // 行业名称索引
  @@index([managementCompany]) // 管理公司索引
  @@index([updateTime]) // 更新时间索引

  @@map("tonghuashun_fund_industry_info")
}