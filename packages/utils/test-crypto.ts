import * as dotenv from 'dotenv';
import * as path from 'node:path';
import * as fs from 'node:fs';
import { createEncryptedRequest, decryptRequestData, verifySign, isTimestampValid, CRYPTO_CONSTANTS } from './lib/crypto';

// 加载环境变量，优先从 .env.local 加载
const rootDir = path.resolve(__dirname, '../..');
const envLocalPath = path.join(rootDir, '.env.local');
const envPath = path.join(rootDir, '.env');

// 手动加载环境变量
if (fs.existsSync(envLocalPath)) {
  console.log(`加载环境变量从 ${envLocalPath}`);
  const envConfig = dotenv.parse(fs.readFileSync(envLocalPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
} else if (fs.existsSync(envPath)) {
  console.log(`加载环境变量从 ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('未找到环境变量文件');
}

// 测试加密和解密流程
function testEncryptionFlow() {
	console.log("======= 开始测试加密解密流程 =======");

	// 输出当前环境变量信息（不显示完整内容，只显示是否存在）
	console.log("环境变量检查:");
	console.log(
		`- ${CRYPTO_CONSTANTS.ENV_API_KEY}: ${
			process.env[CRYPTO_CONSTANTS.ENV_API_KEY] ? "已设置" : "未设置"
		}`
	);
	console.log(
		`- ${CRYPTO_CONSTANTS.ENV_API_SECRET}: ${
			process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] ? "已设置" : "未设置"
		}`
	);
	console.log(
		`- ${CRYPTO_CONSTANTS.ENV_API_IV}: ${
			process.env[CRYPTO_CONSTANTS.ENV_API_IV] ? "已设置" : "未设置"
		}`
	);

	// 模拟业务数据
	const originalData = {
		contactId: "cmdr5t9om0001hv2ga0nenbao",
		organizationId: "Mc40qLLs7M1YJkI8RTZvr4aHij9Q9T36",
		// name: "中国工商银行股份有限公司",
		// phoneNumber: "***********",
		// email: "<EMAIL>",
		// address: "北京市朝阳区",
		// remarks: "",
		// unifiedAccountId: "************",
	};
	// const originalData = {
	//   organizationId: "ax5Se9MZbh6mMIMmFBpvsvsX7ntVOjGD",
	//   fileName: "DQMC01_001339_20240930.DBF",
	//   recordCount: 1,
	//   registerDate: "2024-09-30",
	//   companyCode: "001339",
	//   companyInfo: {
	//     companyName: "智微智能",
	//     totalShares: "********",
	//     totalShareholders: 1279,
	//     totalInstitutions: 1279,
	//     largeSharesCount: "1",
	//     largeShareholdersCount: 1
	//   },
	//   shareholders: [
	//     {
	//       shareholderId: "340104197202131529",
	//       unifiedAccountNumber: "************",
	//       securitiesAccountName: "袁微微",
	//       shareholderCategory: "境内自然人(03)",
	//       numberOfShares: "********",
	//       lockedUpShares: "********",
	//       shareholdingRatio: "39.79",
	//       frozenShares: "0",
	//       cashAccount: "**********",
	//       sharesInCashAccount: "********",
	//       marginAccount: "",
	//       sharesInMarginAccount: "0",
	//       contactAddress: "广东省深圳市福田区车公庙泰然九路海松大厦B座13楼",
	//       contactNumber: "***********",
	//       zipCode: "518000",
	//       relatedPartyIndicator: "Y",
	//       clientCategory: "",
	//       remarks: ""
	//     }
	//   ]
	// };

	console.log("\n原始数据:", JSON.stringify(originalData, null, 2));

	try {
		// 1. 客户端: 创建加密请求
		console.log("\n--- 第一步: 创建加密请求 ---");
		const encryptedRequest = createEncryptedRequest(originalData);
		console.log("加密后的请求:", JSON.stringify(encryptedRequest, null, 2));

		// 2. 服务端: 验证签名
		console.log("\n--- 第二步: 验证签名 ---");
		const { content, sign } = encryptedRequest;
		const isSignValid = verifySign(content, sign);
		console.log("签名是否有效:", isSignValid);

		if (!isSignValid) {
			throw new Error("签名验证失败");
		}

		// 3. 服务端: 解密内容
		console.log("\n--- 第三步: 解密内容 ---");
		// 使用泛型指定业务数据类型
		const decrypted = decryptRequestData<typeof originalData>(content);
		console.log("解密后的数据:", JSON.stringify(decrypted.data, null, 2));
		console.log("请求时间戳:", new Date(decrypted.timestamp).toISOString());

		// 4. 服务端: 验证时间戳
		console.log("\n--- 第四步: 验证时间戳 ---");
		const isTimeValid = isTimestampValid(decrypted.timestamp);
		console.log("时间戳是否有效:", isTimeValid);

		// 5. 数据比对验证
		console.log("\n--- 第五步: 数据比对验证 ---");
		const isDataEqual =
			JSON.stringify(originalData) === JSON.stringify(decrypted.data);
		console.log("数据是否一致:", isDataEqual);

		console.log(
			"\n加密解密流程测试:",
			isSignValid && isTimeValid && isDataEqual ? "成功" : "失败"
		);
	} catch (error) {
		console.error("测试过程出错:", error);
	}

	console.log("======= 测试结束 =======");
}

// 测试加密请求的过期验证
function testExpiredRequest() {
  console.log('\n\n======= 开始测试请求过期验证 =======');
  
  // 模拟一个过期的时间戳 (2分钟前)
  const expiredTimestamp = Date.now() - 2 * 60 * 1000;
  console.log('过期时间戳:', new Date(expiredTimestamp).toISOString());
  
  // 验证时间戳
  const isValid = isTimestampValid(expiredTimestamp);
  console.log('时间戳是否有效:', isValid);
  console.log('过期验证测试:', !isValid ? '成功' : '失败');
  
  console.log('======= 测试结束 =======');
}

// 执行测试
console.log('开始执行加密解密功能测试...\n');
testEncryptionFlow();
testExpiredRequest(); 