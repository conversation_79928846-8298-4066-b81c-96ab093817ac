#!/usr/bin/env node

/**
 * =============================================================================
 * 简单合并脚本：将dev分支合并到当前分支 (Node.js版本)
 * =============================================================================
 * 创建时间: 2025-01-05
 * 修改时间: 2025-07-16
 * 作者: Alson
 * 版本: v1.1 (Node.js版本)
 *
 * 📖 使用说明:
 *   pnpm fdev                    # 将dev分支合并到当前分支
 *   node scripts/merge-from-dev.js  # 直接执行脚本
 *
 * 🎯 使用场景:
 *   • 功能开发中途同步dev分支的新代码
 *   • 解决功能分支落后于dev分支的问题
 *   • 在提交PR前确保代码是最新的
 *   • 获取团队其他成员提交的最新功能
 *   • Windows/macOS/Linux 跨平台支持
 *
 * 🔄 执行流程:
 *   1. 检查工作区是否干净（必须先提交当前更改）
 *   2. 获取当前分支名（如: feature/login）
 *   3. 拉取远程dev分支最新代码
 *   4. 合并dev分支到当前分支
 *   5. 显示合并结果和提交历史
 *
 * ⚠️ 注意事项:
 *   • 确保当前分支的更改已提交
 *   • 如遇冲突需要手动解决
 *   • 合并后仍在当前分支上（不会切换分支）
 *   • 简单脚本，无备份和复杂回滚机制
 *
 * 🔥 冲突处理:
 *   如果出现合并冲突：
 *   1. 手动编辑冲突文件，解决冲突标记
 *   2. git add <文件名>
 *   3. git commit
 *   或者放弃合并: git merge --abort
 *
 * 💡 典型工作流程:
 *   git add . && git commit -m "保存进度"  # 提交当前更改
 *   pnpm fdev                             # 同步dev最新代码
 *   # 继续开发...
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 颜色定义 (Windows兼容)
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

// 日志函数
function log(level, message) {
    const colorMap = {
        info: colors.blue,
        success: colors.green,
        warning: colors.yellow,
        error: colors.red
    };
    
    const color = colorMap[level] || colors.reset;
    const prefix = `${color}[${level.toUpperCase()}]${colors.reset}`;
    console.log(`${prefix} ${message}`);
}

// 执行命令函数
function execCommand(command, options = {}) {
    try {
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        });
        return { success: true, output: result };
    } catch (error) {
        return { 
            success: false, 
            error: error.message,
            output: error.stdout || '',
            stderr: error.stderr || ''
        };
    }
}

// 检查环境依赖
function checkEnvironment() {
    log('info', '🔍 正在进行环境检查...');
    
    const missingDeps = [];
    
    // 检查git是否安装
    const gitCheck = execCommand('git --version', { silent: true });
    if (!gitCheck.success) {
        missingDeps.push('git');
    }
    
    // 检查是否在项目根目录
    if (!fs.existsSync('package.json')) {
        log('error', '未找到package.json文件，请在项目根目录运行此脚本');
        process.exit(1);
    }
    
    // 检查package.json中的脚本配置
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        if (!packageJson.scripts || !packageJson.scripts.fdev) {
            log('warning', 'package.json中未找到fdev脚本配置');
            log('info', '请确保package.json中包含: "fdev": "node scripts/merge-from-dev.js"');
        }
    } catch (error) {
        log('warning', '无法读取package.json文件');
    }
    
    // 报告缺失的依赖
    if (missingDeps.length > 0) {
        log('error', '缺少必要的依赖工具:');
        missingDeps.forEach(dep => console.log(`  ❌ ${dep}`));
        log('info', '请安装缺失的工具后重试');
        process.exit(1);
    }
    
    log('success', '✅ 环境检查通过');
    console.log();
}

// 显示帮助信息
function showHelp() {
    console.log(`
🔄 将dev分支合并到当前分支 (Node.js版本)

用法: pnpm fdev

功能: 将dev分支的最新代码同步到当前功能分支
适用: 功能开发中途同步主分支代码

特性:
    ✅ 跨平台支持 (Windows/macOS/Linux)
    ✅ 自动检查工作区状态
    ✅ 智能处理合并冲突
    ✅ 详细的操作日志
    ✅ 安全的合并流程

执行流程:
    1. 检查工作区是否干净（必须先提交当前更改）
    2. 获取当前分支名（如: feature/login）
    3. 拉取远程dev分支最新代码
    4. 合并dev分支到当前分支
    5. 显示合并结果和提交历史

注意事项:
    • 确保当前分支更改已提交
    • 合并后仍在当前分支上
    • 如遇冲突需要手动解决

冲突处理:
    如果出现合并冲突：
    1. 手动编辑冲突文件，解决冲突标记
    2. git add <文件名>
    3. git commit
    或者放弃合并: git merge --abort

典型工作流程:
    git add . && git commit -m "保存进度"  # 提交当前更改
    pnpm fdev                             # 同步dev最新代码
    # 继续开发...

使用示例:
    # 基本用法
    pnpm fdev                    # 执行合并操作
    
    # 查看帮助
    node scripts/merge-from-dev.js --help
`);
}

// 处理合并冲突
function handleMergeConflict() {
    log('error', '🔥 检测到合并冲突！');
    console.log();
    
    const statusResult = execCommand('git status --porcelain', { silent: true });
    if (statusResult.success) {
        const conflicts = statusResult.output.split('\n')
            .filter(line => line.match(/^UU|^AA|^DD/))
            .map(line => line.slice(3));
        
        if (conflicts.length > 0) {
            log('info', '冲突文件列表:');
            conflicts.forEach(file => console.log(`  📄 ${file}`));
            console.log();
        }
    }
    
    log('warning', '请按以下步骤解决冲突:');
    console.log('  1️⃣  手动编辑冲突文件，解决冲突标记');
    console.log('  2️⃣  添加已解决的文件: git add <文件名>');
    console.log('  3️⃣  完成合并: git commit');
    console.log();
    
    log('info', '或者放弃此次合并:');
    console.log('  ❌ 中止合并: git merge --abort');
    console.log();
    
    return false;
}

// 主函数
async function main() {
    try {
        // 参数处理
        const args = process.argv.slice(2);
        for (const arg of args) {
            switch (arg) {
                case '--help':
                case '-h':
                    showHelp();
                    process.exit(0);
                    break;
                default:
                    log('error', `未知参数: ${arg}`);
                    log('info', '使用 --help 查看帮助信息');
                    process.exit(1);
            }
        }

        // 执行环境检查
        checkEnvironment();

        log('info', '🔄 开始将dev分支合并到当前分支...');

        // 1. 检查是否在Git仓库中
        const gitCheck = execCommand('git rev-parse --git-dir', { silent: true });
        if (!gitCheck.success) {
            log('error', '当前目录不是Git仓库');
            process.exit(1);
        }

        // 2. 检查工作区是否干净
        const statusResult = execCommand('git status --porcelain', { silent: true });
        if (statusResult.success && statusResult.output.trim()) {
            log('error', '工作区有未提交的更改，请先提交或暂存：');
            execCommand('git status --short');
            log('warning', '提示：可以使用 \'git add .\' 和 \'git commit -m "message"\' 提交更改');
            process.exit(1);
        }

        // 3. 获取当前分支名
        const branchResult = execCommand('git branch --show-current', { silent: true });
        if (!branchResult.success) {
            log('error', '无法获取当前分支名');
            process.exit(1);
        }
        const currentBranch = branchResult.output.trim();
        log('info', `当前分支: ${currentBranch}`);

        // 4. 检查是否已经在dev分支
        if (currentBranch === 'dev') {
            log('warning', '已经在dev分支上，无需合并');
            process.exit(0);
        }

        // 5. 检查dev分支是否存在
        const devCheck = execCommand('git show-ref --verify --quiet refs/heads/dev', { silent: true });
        if (!devCheck.success) {
            log('error', 'dev分支不存在');
            process.exit(1);
        }

        // 6. 检查远程仓库连接
        log('info', '检查远程仓库连接...');
        const remoteCheck = execCommand('git ls-remote New-repo-Gitea', { silent: true });
        if (!remoteCheck.success) {
            log('error', '无法连接到远程仓库New-repo-Gitea');
            process.exit(1);
        }

        // 7. 智能检测和拉取dev分支代码
        log('info', '检测可用的dev分支...');
        let devBranch = 'dev';
        let fetchSuccess = false;

        // 检查远程是否有dev分支
        const remoteDevCheck = execCommand('git ls-remote New-repo-Gitea dev', { silent: true });
        if (remoteDevCheck.success && remoteDevCheck.output.trim()) {
            // New-repo-Gitea有dev分支，尝试拉取
            log('info', '发现远程New-repo-Gitea/dev分支，正在拉取...');
            const fetchResult = execCommand('git fetch New-repo-Gitea dev:dev', { silent: true });
            if (fetchResult.success) {
                fetchSuccess = true;
                devBranch = 'dev';
                log('success', '成功拉取New-repo-Gitea/dev到本地dev分支');
            } else {
                // 尝试直接fetch
                const altFetchResult = execCommand('git fetch New-repo-Gitea dev', { silent: true });
                if (altFetchResult.success) {
                    fetchSuccess = true;
                    devBranch = 'New-repo-Gitea/dev';
                    log('success', '成功拉取New-repo-Gitea/dev，将直接使用远程分支');
                }
            }
        } else {
            // New-repo-Gitea没有dev分支，检查是否有develop分支
            const remoteDevelopCheck = execCommand('git ls-remote New-repo-Gitea develop', { silent: true });
            if (remoteDevelopCheck.success && remoteDevelopCheck.output.trim()) {
                log('warning', '远程New-repo-Gitea没有dev分支，但发现develop分支');
                log('info', '正在拉取New-repo-Gitea/develop分支...');
                const fetchDevelopResult = execCommand('git fetch New-repo-Gitea develop', { silent: true });
                if (fetchDevelopResult.success) {
                    fetchSuccess = true;
                    devBranch = 'New-repo-Gitea/develop';
                    log('success', '成功拉取New-repo-Gitea/develop，将使用develop分支进行合并');
                }
            }
        }

        if (!fetchSuccess) {
            log('error', '无法拉取dev相关分支');
            log('warning', '可能的原因：');
            log('warning', '  - 远程仓库没有dev或develop分支');
            log('warning', '  - 本地dev分支与远程分支有冲突');
            log('warning', '  - 网络连接问题');
            log('warning', '  - 远程仓库权限问题');
            console.log();
            log('info', '可用的解决方案：');
            console.log('  1. 检查远程分支: git branch -r');
            console.log('  2. 手动指定要合并的分支: git merge New-repo-Gitea/main');
            console.log('  3. 重置本地dev分支: git branch -D dev && git checkout -b dev New-repo-Gitea/develop');
            process.exit(1);
        }

        // 8. 合并dev分支到当前分支
        log('info', `合并${devBranch}分支到当前分支 ${currentBranch}...`);

        const userInfo = execCommand('git config user.name', { silent: true });
        const userEmail = execCommand('git config user.email', { silent: true });
        const userName = userInfo.success ? userInfo.output.trim() : 'Unknown';
        const email = userEmail.success ? userEmail.output.trim() : '<EMAIL>';

        const commitMessage = `Merge ${devBranch} into ${currentBranch}

Merged by: ${userName} <${email}>
Date: ${new Date().toISOString()}
Source: ${devBranch}
Target: ${currentBranch}`;

        const mergeResult = execCommand(`git merge ${devBranch} --no-ff -m "${commitMessage}"`, { silent: true });
        if (!mergeResult.success) {
            // 检查是否是合并冲突
            const conflictCheck = execCommand('git status --porcelain', { silent: true });
            if (conflictCheck.success && conflictCheck.output.match(/^UU|^AA|^DD/m)) {
                handleMergeConflict();
                process.exit(1);
            } else {
                log('error', '合并失败，原因未知');
                process.exit(1);
            }
        }

        // 9. 成功完成
        console.log();
        log('success', `🎉 成功将dev分支合并到 ${currentBranch} 分支！`);
        console.log();
        log('info', '📊 操作摘要：');
        console.log('  ✅ 源分支: dev');
        console.log(`  ✅ 目标分支: ${currentBranch}`);
        console.log(`  ✅ 当前位置: ${currentBranch} 分支`);

        console.log();
        log('info', '📈 最近的提交记录：');
        execCommand('git log --oneline --graph -5');

        console.log();
        log('success', '✨ 合并操作完成！现在可以继续在当前分支开发');

    } catch (error) {
        log('error', `脚本执行失败: ${error.message || error}`);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        log('error', `脚本执行失败: ${error.message || error}`);
        process.exit(1);
    });
}
