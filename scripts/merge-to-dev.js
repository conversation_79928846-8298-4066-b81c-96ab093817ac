#!/usr/bin/env node

/**
 * =============================================================================
 * 安全合并脚本：将当前分支合并到dev分支 (Node.js版本)
 * =============================================================================
 * 创建时间: 2025-01-05
 * 修改时间: 2025-07-16
 * 作者: Alson
 * 版本: v2.1 (Node.js版本)
 *
 * 📖 使用说明:
 *   pnpm todev                    # 将当前分支合并到dev分支
 *   pnpm todevp                   # 预览模式（推荐先使用）
 *   pnpm todev:help              # 显示帮助信息
 *   node scripts/merge-to-dev.js  # 直接执行脚本
 *
 * 🎯 使用场景:
 *   • 功能开发完成，发布到dev分支
 *   • 需要安全保障的重要合并操作
 *   • 团队协作中的正式代码提交
 *   • Windows/macOS/Linux 跨平台支持
 *
 * ⚠️ 重要说明:
 *   • 脚本执行完成后会停留在dev分支上
 *   • 不会自动切换回原分支（需要手动切换）
 *   • 原分支保持不变，可随时切换回去
 *   • 如遇错误会自动回滚到原分支
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 全局变量
let DRY_RUN = false;
let SHOW_HELP = false;
let ORIGINAL_BRANCH = '';
let BACKUP_BRANCH = '';

// 颜色定义 (Windows兼容)
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

// 日志函数
function log(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const colorMap = {
        info: colors.blue,
        success: colors.green,
        warning: colors.yellow,
        error: colors.red,
        debug: colors.purple,
        step: colors.cyan
    };
    
    const color = colorMap[level] || colors.reset;
    const prefix = `${color}[${level.toUpperCase()}]${colors.reset}`;
    console.log(`${prefix} ${message}`);
}

// 执行命令函数
function execCommand(command, options = {}) {
    try {
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        });
        return { success: true, output: result };
    } catch (error) {
        return { 
            success: false, 
            error: error.message,
            output: error.stdout || '',
            stderr: error.stderr || ''
        };
    }
}

// 检查环境依赖
function checkEnvironment() {
    log('info', '🔍 正在进行环境检查...');
    
    const missingDeps = [];
    
    // 检查git是否安装
    const gitCheck = execCommand('git --version', { silent: true });
    if (!gitCheck.success) {
        missingDeps.push('git');
    }
    
    // 检查是否在项目根目录
    if (!fs.existsSync('package.json')) {
        log('error', '未找到package.json文件，请在项目根目录运行此脚本');
        process.exit(1);
    }
    
    // 检查package.json中的脚本配置
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        if (!packageJson.scripts || !packageJson.scripts.todev) {
            log('warning', 'package.json中未找到todev脚本配置');
        }
    } catch (error) {
        log('warning', '无法读取package.json文件');
    }
    
    // 报告缺失的依赖
    if (missingDeps.length > 0) {
        log('error', '缺少必要的依赖工具:');
        missingDeps.forEach(dep => console.log(`  ❌ ${dep}`));
        log('info', '请安装缺失的工具后重试');
        process.exit(1);
    }
    
    log('success', '✅ 环境检查通过');
    console.log();
}

// 显示帮助信息
function showHelp() {
    console.log(`
🚀 安全合并脚本 - 将当前分支合并到dev分支 (Node.js版本)

用法:
    pnpm todev [选项]
    node scripts/merge-to-dev.js [选项]

选项:
    --help, -h          显示此帮助信息
    --dry-run, -n       预览操作但不执行（安全模式）

功能特性:
    ✅ 自动检查工作区状态
    ✅ 智能处理合并冲突
    ✅ 支持快进合并和三方合并
    ✅ 自动备份当前状态
    ✅ 详细的操作日志
    ✅ 错误自动回滚
    ✅ 支持预览模式
    ✅ 跨平台支持 (Windows/macOS/Linux)

详细执行流程:
    📋 准备阶段:
        1. 检查当前目录是否为Git仓库
        2. 检查工作区是否有未提交的更改（必须干净）
        3. 获取当前分支名称（如: feature/login）
        4. 检查是否已在dev分支（如是则直接退出）
        5. 创建当前分支的安全备份

    🔄 合并阶段:
        6. 验证dev分支是否存在
        7. 检查远程仓库连接状态
        8. 切换到dev分支
        9. 拉取远程dev分支最新代码
        10. 智能合并当前分支到dev（快进或三方合并）
        11. 推送合并结果到远程origin/dev

    🎯 完成阶段:
        12. 显示操作摘要和提交历史
        13. 自动清理备份分支
        14. 提供后续操作建议

使用示例:
    📝 基本用法:
        pnpm todev                    # 执行合并操作
        pnpm todevp                  # 预览模式（推荐先使用）
        pnpm todev:help             # 显示完整帮助

    🔍 典型场景:
        # 场景1: 功能开发完成，合并到dev
        git add . && git commit -m "完成登录功能"
        pnpm todevp                 # 先预览
        pnpm todev                  # 执行合并
        git checkout feature/login  # 切换回原分支继续开发

重要注意事项:
    ⚠️  执行前必须满足:
        • 当前分支所有更改已提交（工作区干净）
        • 确保有dev分支的推送权限
        • 网络连接正常

    💡 最佳实践:
        • 合并前先运行测试确保功能正常
        • 重要功能建议先创建Pull Request审查
        • 合并后及时通知团队成员
        • 定期清理已合并的功能分支
`);
}

// 创建安全备份
function createBackup(branchName) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    BACKUP_BRANCH = `${branchName}-backup-${timestamp}`;

    if (DRY_RUN) {
        log('info', `[预览] 将创建备份分支: ${BACKUP_BRANCH}`);
        return true;
    }

    log('info', `创建安全备份分支: ${BACKUP_BRANCH}`);
    const result = execCommand(`git branch ${BACKUP_BRANCH}`, { silent: true });
    if (result.success) {
        log('success', `备份创建成功: ${BACKUP_BRANCH}`);
        return true;
    } else {
        log('warning', '备份创建失败，但继续执行');
        return false;
    }
}

// 清理备份分支
function cleanupBackup() {
    if (BACKUP_BRANCH && !DRY_RUN) {
        log('info', `清理备份分支: ${BACKUP_BRANCH}`);
        execCommand(`git branch -D ${BACKUP_BRANCH}`, { silent: true });
    }
}

// 错误处理函数
function handleError(error) {
    log('error', `脚本执行失败: ${error.message || error}`);
    
    // 检查是否有合并冲突
    const statusResult = execCommand('git status --porcelain', { silent: true });
    if (statusResult.success && statusResult.output.match(/^UU|^AA|^DD/m)) {
        log('warning', '检测到合并冲突，中止合并操作');
        execCommand('git merge --abort', { silent: true });
    }
    
    // 切换回原分支
    if (ORIGINAL_BRANCH && ORIGINAL_BRANCH !== 'dev') {
        log('info', `切换回原分支: ${ORIGINAL_BRANCH}`);
        execCommand(`git checkout ${ORIGINAL_BRANCH}`, { silent: true });
    }
    
    // 清理备份分支
    cleanupBackup();
    
    process.exit(1);
}

// 处理合并冲突
function handleMergeConflict() {
    log('error', '🔥 检测到合并冲突！');
    console.log();
    
    const statusResult = execCommand('git status --porcelain', { silent: true });
    if (statusResult.success) {
        const conflicts = statusResult.output.split('\n')
            .filter(line => line.match(/^UU|^AA|^DD/))
            .map(line => line.slice(3));
        
        if (conflicts.length > 0) {
            log('info', '冲突文件列表:');
            conflicts.forEach(file => console.log(`  📄 ${file}`));
            console.log();
        }
    }
    
    log('warning', '请按以下步骤解决冲突:');
    console.log('  1️⃣  手动编辑冲突文件，解决冲突标记 (<<<<<<<, =======, >>>>>>>)');
    console.log('  2️⃣  添加已解决的文件: git add <文件名>');
    console.log('  3️⃣  完成合并: git commit');
    console.log('  4️⃣  推送结果: git push New-repo-Gitea dev');
    console.log();
    
    log('info', '或者放弃此次合并:');
    console.log('  ❌ 中止合并: git merge --abort');
    console.log(`  🔄 切换回原分支: git checkout ${ORIGINAL_BRANCH}`);
    console.log();
    
    return false;
}

// 智能合并函数
function smartMerge(sourceBranch) {
    const targetBranch = 'dev';
    log('step', `开始智能合并: ${sourceBranch} -> ${targetBranch}`);

    if (DRY_RUN) {
        log('info', '[预览] 将执行合并操作');
        log('info', '[预览] 检查是否可以快进合并...');

        // 检查是否可以快进合并
        const ancestorCheck = execCommand(`git merge-base --is-ancestor ${targetBranch} ${sourceBranch}`, { silent: true });
        if (ancestorCheck.success) {
            log('success', '[预览] ✅ 可以执行快进合并');
        } else {
            log('warning', '[预览] ⚠️  需要执行三方合并');
        }
        return true;
    }

    // 检查是否可以快进合并
    const ancestorCheck = execCommand(`git merge-base --is-ancestor ${targetBranch} ${sourceBranch}`, { silent: true });
    if (ancestorCheck.success) {
        log('info', '✅ 执行快进合并...');
        const mergeResult = execCommand(`git merge ${sourceBranch} --ff-only`, { silent: true });
        if (mergeResult.success) {
            log('success', '快进合并成功！');
            return true;
        } else {
            log('warning', '快进合并失败，尝试三方合并');
        }
    }

    // 执行三方合并
    log('info', '🔄 执行三方合并...');
    const userInfo = execCommand('git config user.name', { silent: true });
    const userEmail = execCommand('git config user.email', { silent: true });
    const userName = userInfo.success ? userInfo.output.trim() : 'Unknown';
    const email = userEmail.success ? userEmail.output.trim() : '<EMAIL>';

    const commitMessage = `Merge branch '${sourceBranch}' into ${targetBranch}

Merged by: ${userName} <${email}>
Date: ${new Date().toISOString()}
Source: ${sourceBranch}
Target: ${targetBranch}`;

    const mergeResult = execCommand(`git merge ${sourceBranch} --no-ff -m "${commitMessage}"`, { silent: true });
    if (mergeResult.success) {
        log('success', '三方合并成功！');
        return true;
    } else {
        // 检查是否是合并冲突
        const statusResult = execCommand('git status --porcelain', { silent: true });
        if (statusResult.success && statusResult.output.match(/^UU|^AA|^DD/m)) {
            return handleMergeConflict();
        } else {
            log('error', '合并失败，原因未知');
            return false;
        }
    }
}

// 主函数
async function main() {
    try {
        // 参数处理
        const args = process.argv.slice(2);
        for (const arg of args) {
            switch (arg) {
                case '--help':
                case '-h':
                    SHOW_HELP = true;
                    break;
                case '--dry-run':
                case '-n':
                    DRY_RUN = true;
                    break;
                default:
                    log('error', `未知参数: ${arg}`);
                    log('info', '使用 --help 查看帮助信息');
                    process.exit(1);
            }
        }

        // 显示帮助信息
        if (SHOW_HELP) {
            showHelp();
            process.exit(0);
        }

        // 执行环境检查
        checkEnvironment();

        // 显示脚本信息
        if (DRY_RUN) {
            log('warning', '🔍 预览模式 - 不会执行实际操作');
        } else {
            log('info', '🚀 开始执行安全合并脚本...');
        }

        // 1. 检查是否在Git仓库中
        const gitCheck = execCommand('git rev-parse --git-dir', { silent: true });
        if (!gitCheck.success) {
            log('error', '当前目录不是Git仓库');
            process.exit(1);
        }

        // 2. 检查工作区是否干净
        const statusResult = execCommand('git status --porcelain', { silent: true });
        if (statusResult.success && statusResult.output.trim()) {
            log('error', '工作区有未提交的更改，请先提交或暂存：');
            execCommand('git status --short');
            log('warning', '提示：可以使用 \'git add .\' 和 \'git commit -m "message"\' 提交更改');
            log('warning', '或使用 \'git stash\' 暂存更改');
            process.exit(1);
        }

        // 3. 获取当前分支名
        const branchResult = execCommand('git branch --show-current', { silent: true });
        if (!branchResult.success) {
            log('error', '无法获取当前分支名');
            process.exit(1);
        }
        ORIGINAL_BRANCH = branchResult.output.trim();
        log('step', `当前分支: ${ORIGINAL_BRANCH}`);

        // 4. 检查是否已经在dev分支
        if (ORIGINAL_BRANCH === 'dev') {
            log('warning', '已经在dev分支上，无需合并');
            process.exit(0);
        }

        // 5. 创建安全备份
        createBackup(ORIGINAL_BRANCH);

        // 6. 检查dev分支是否存在
        log('step', '检查dev分支是否存在...');
        const devCheck = execCommand('git show-ref --verify --quiet refs/heads/dev', { silent: true });
        if (!devCheck.success) {
            log('error', 'dev分支不存在，请先创建dev分支');
            log('info', '提示：可以使用 \'git checkout -b dev\' 创建dev分支');
            process.exit(1);
        }

        // 7. 检查远程仓库连接
        log('step', '检查远程仓库连接...');
        if (!DRY_RUN) {
            const remoteCheck = execCommand('git ls-remote New-repo-Gitea', { silent: true });
            if (!remoteCheck.success) {
                log('error', '无法连接到远程仓库origin');
                log('info', '请检查网络连接和仓库权限');
                process.exit(1);
            }
            log('success', '远程仓库连接正常');
        } else {
            log('info', '[预览] 将检查远程仓库连接');
        }

        // 8. 切换到dev分支
        log('step', '切换到dev分支...');
        if (!DRY_RUN) {
            const checkoutResult = execCommand('git checkout dev');
            if (!checkoutResult.success) {
                throw new Error('切换到dev分支失败');
            }
        } else {
            log('info', '[预览] 将切换到dev分支');
        }

        // 9. 拉取最新的dev分支代码
        log('step', '拉取最新的dev分支代码...');
        if (!DRY_RUN) {
            const pullResult = execCommand('git pull New-repo-Gitea dev');
            if (!pullResult.success) {
                log('error', '拉取dev分支失败');
                log('warning', '可能的原因：');
                log('warning', '  - 网络连接问题');
                log('warning', '  - 远程仓库权限问题');
                log('warning', '  - 本地dev分支与远程分支冲突');
                execCommand(`git checkout ${ORIGINAL_BRANCH}`, { silent: true });
                process.exit(1);
            }
            log('success', 'dev分支代码更新完成');
        } else {
            log('info', '[预览] 将拉取最新的dev分支代码');
        }

        // 10. 执行智能合并
        if (!smartMerge(ORIGINAL_BRANCH)) {
            log('error', '合并过程中遇到问题');
            process.exit(1);
        }

        // 11. 推送到远程dev分支
        log('step', '推送合并结果到远程dev分支...');
        if (!DRY_RUN) {
            const pushResult = execCommand('git push New-repo-Gitea dev');
            if (!pushResult.success) {
                log('error', '推送失败');
                log('warning', '合并已完成，但推送失败');
                log('info', '可能的原因：');
                log('info', '  - 网络连接问题');
                log('info', '  - 远程仓库权限问题');
                log('info', '  - 远程dev分支有新的提交');
                log('warning', '请手动执行推送：');
                log('warning', '  git push New-repo-Gitea dev');
                process.exit(1);
            }
            log('success', '推送完成');
        } else {
            log('info', '[预览] 将推送合并结果到远程dev分支');
        }

        // 12. 清理和总结
        if (!DRY_RUN) {
            // 自动清理备份分支
            if (BACKUP_BRANCH) {
                cleanupBackup();
                log('success', `备份分支已自动清理: ${BACKUP_BRANCH}`);
            }

            // 成功完成
            console.log();
            log('success', `🎉 成功将分支 '${ORIGINAL_BRANCH}' 合并到 dev 分支！`);
            console.log();
            log('info', '📊 操作摘要：');
            console.log(`  ✅ 源分支: ${ORIGINAL_BRANCH}`);
            console.log('  ✅ 目标分支: dev');
            console.log('  ✅ 远程仓库: New-repo-Gitea');
            console.log('  ✅ 当前位置: dev分支');
            console.log('  ✅ 备份分支: 已自动清理');

            console.log();
            log('info', '🔄 后续操作建议：');
            console.log(`  📝 切换回原分支继续开发: git checkout ${ORIGINAL_BRANCH}`);
            console.log('  🧪 在dev分支运行完整测试');
            console.log('  📢 通知团队成员合并完成');
            console.log(`  🗑️  考虑删除已合并的功能分支: git branch -d ${ORIGINAL_BRANCH}`);
            console.log('  🔄 如需撤销合并: git reset --hard HEAD~1 (在dev分支执行)');
            console.log();
            log('warning', '⚠️  重要提醒：');
            console.log(`  • 当前在dev分支，不是原来的 ${ORIGINAL_BRANCH} 分支`);
            console.log(`  • 如需继续在原分支开发，请手动切换: git checkout ${ORIGINAL_BRANCH}`);
            console.log('  • 原分支代码未受影响，可随时切换回去');

            console.log();
            log('info', '📈 最近的提交记录：');
            execCommand('git log --oneline --graph -5');

            console.log();
            log('success', '✨ 合并操作完成！');
        } else {
            console.log();
            log('success', '🔍 预览完成！');
            console.log();
            log('info', '📋 预览摘要：');
            console.log(`  📂 源分支: ${ORIGINAL_BRANCH}`);
            console.log('  📂 目标分支: dev');
            console.log('  🌐 远程仓库: New-repo-Gitea');
            console.log('  💾 将创建备份分支');
            console.log();
            log('info', '如果预览结果正确，请执行：');
            console.log('  pnpm todev');
            console.log();
        }

    } catch (error) {
        handleError(error);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(handleError);
}
