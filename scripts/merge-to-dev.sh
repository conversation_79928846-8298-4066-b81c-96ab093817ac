#!/bin/bash

# =============================================================================
# 安全合并脚本：将当前分支合并到dev分支
# =============================================================================
# 创建时间: 2025-01-05
# 修改时间: 2025-01-05
# 作者: Alson
# 版本: v2.1
#
# 📖 使用说明:
#   chmod +x scripts/merge-to-dev.sh     # 首次使用前必须赋予执行权限
#   pnpm todev                           # 将当前分支合并到dev分支
#   pnpm todevp                          # 预览模式（推荐先使用）
#   pnpm todev:help                      # 显示帮助信息
#   ./scripts/merge-to-dev.sh            # 直接执行脚本
#
# 🔧 首次设置 (重要!):
#   在使用脚本前，必须先给脚本赋予执行权限：
#   chmod +x scripts/merge-to-dev.sh
#   
#   如果没有执行权限，会出现 "Permission denied" 错误
#
# 🎯 使用场景:
#   • 功能开发完成，发布到dev分支
#   • 需要安全保障的重要合并操作
#   • 团队协作中的正式代码提交
#
# ⚠️ 重要说明:
#   • 【重要】首次使用前必须赋予脚本执行权限
#   • 脚本执行完成后会停留在dev分支上
#   • 不会自动切换回原分支（需要手动切换）
#   • 原分支保持不变，可随时切换回去
#   • 如遇错误会自动回滚到原分支
#
# 💡 典型工作流程:
#   # 首次设置
#   chmod +x scripts/merge-to-dev.sh     # 赋予执行权限
#   
#   # 日常使用
#   git add . && git commit -m "完成功能"  # 提交当前更改
#   pnpm todevp                           # 先预览操作
#   pnpm todev                            # 执行合并
#   git checkout feature/xxx              # 切换回原分支
#
# 功能特性:
#   ✅ 自动检查工作区状态
#   ✅ 智能处理合并冲突
#   ✅ 支持快进合并和三方合并
#   ✅ 自动备份当前状态
#   ✅ 详细的操作日志
#   ✅ 错误自动回滚
#   ✅ 支持预览模式
#
# 处理场景:
#   - 简单快进合并
#   - 三方合并（无冲突）
#   - 合并冲突（提供详细指导）
#   - 网络连接问题
#   - 权限问题
#   - 分支不存在等异常情况
#
# 安全保障:
#   - 操作前自动备份
#   - 失败时自动回滚
#   - 详细的操作记录
#   - 多重安全检查
# =============================================================================

set -e  # 遇到错误立即退出

# 环境依赖检查
check_environment() {
    local missing_deps=()
    
    # 检查pnpm是否安装
    if ! command -v pnpm &> /dev/null; then
        missing_deps+=("pnpm")
    fi
    
    # 检查git是否安装
    if ! command -v git &> /dev/null; then
        missing_deps+=("git")
    fi
    
    # 检查是否在项目根目录（package.json存在且包含todev脚本）
    if [ ! -f "package.json" ]; then
        echo -e "${RED}[ERROR]${NC} 未找到package.json文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查package.json中是否包含todev脚本配置
    if ! grep -q '"todev"' package.json 2>/dev/null; then
        echo -e "${YELLOW}[WARNING]${NC} package.json中未找到todev脚本配置"
        echo -e "${BLUE}[INFO]${NC} 请确保package.json中包含: \"todev\": \"./scripts/merge-to-dev.sh\""
    fi
    
    # 检查package.json中是否包含todevp脚本配置
    if ! grep -q '"todevp"' package.json 2>/dev/null; then
        echo -e "${YELLOW}[WARNING]${NC} package.json中未找到todevp脚本配置"
        echo -e "${BLUE}[INFO]${NC} 请确保package.json中包含: \"todevp\": \"./scripts/merge-to-dev.sh --dry-run\""
    fi
    
    # 报告缺失的依赖
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}[ERROR]${NC} 缺少必要的依赖工具:"
        for dep in "${missing_deps[@]}"; do
            echo "  ❌ $dep"
        done
        echo
        echo -e "${BLUE}[INFO]${NC} 请安装缺失的工具后重试"
        exit 1
    fi
}

# 初始化检查
initialize_checks() {
    echo -e "${BLUE}[INFO]${NC} 🔍 正在进行环境检查..."
    check_environment
    echo -e "${GREEN}[SUCCESS]${NC} ✅ 环境检查通过"
    echo
}

# 全局变量
DRY_RUN=false
SHOW_HELP=false
ORIGINAL_BRANCH=""
BACKUP_BRANCH=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
🚀 安全合并脚本 - 将当前分支合并到dev分支

用法:
    pnpm todev [选项]
    ./scripts/merge-to-dev.sh [选项]

选项:
    --help, -h          显示此帮助信息
    --dry-run, -n       预览操作但不执行（安全模式）

功能特性:
    ✅ 自动检查工作区状态
    ✅ 智能处理合并冲突
    ✅ 支持快进合并和三方合并
    ✅ 自动备份当前状态
    ✅ 详细的操作日志
    ✅ 错误自动回滚
    ✅ 支持预览模式

详细执行流程:
    📋 准备阶段:
        1. 检查当前目录是否为Git仓库
        2. 检查工作区是否有未提交的更改（必须干净）
        3. 获取当前分支名称（如: feature/login）
        4. 检查是否已在dev分支（如是则直接退出）
        5. 创建当前分支的安全备份（如: feature/login-backup-20250105-1923）

    🔄 合并阶段:
        6. 验证dev分支是否存在
        7. 检查远程仓库连接状态
        8. 切换到dev分支
        9. 拉取远程dev分支最新代码
        10. 智能合并当前分支到dev（快进或三方合并）
        11. 推送合并结果到远程origin/dev

    🎯 完成阶段:
        12. 显示操作摘要和提交历史
        13. 自动清理备份分支
        14. 提供后续操作建议

    💾 备份分支处理:
        • 自动创建: <原分支名>-backup-<时间戳>
        • 合并成功: 自动删除备份分支
        • 合并失败: 回滚后自动删除备份分支
        • 备份分支用途: 仅在操作过程中提供安全保障

    ⚠️  重要说明:
        • 脚本执行完成后会停留在dev分支上
        • 不会自动切换回原分支（需要手动切换）
        • 原分支保持不变，可随时切换回去
        • 如遇错误会自动回滚到原分支

合并冲突处理:
    🔥 当发生合并冲突时:
        • 脚本会暂停执行并保持在dev分支
        • 显示所有冲突文件的详细列表
        • 提供逐步解决冲突的具体指令
        • 用户手动解决冲突后可继续推送
        • 如需放弃，可执行 git merge --abort 回到合并前状态

    🛡️ 安全保障:
        • 操作前自动创建备份分支
        • 失败时自动切换回原分支
        • 保留完整的操作历史记录
        • 提供多种恢复方案

    💾 备份分支管理:
        • 命名格式: <原分支>-backup-<YYYYMMDD-HHMMSS>
        • 创建时机: 合并操作开始前
        • 自动清理: 成功或失败后都会自动删除
        • 临时性质: 仅在操作过程中存在，不会留存

使用示例:
    📝 基本用法:
        pnpm todev                    # 执行合并操作
        pnpm todevp                  # 预览模式（推荐先使用）
        pnpm todev:help             # 显示完整帮助

    🔍 典型场景:
        # 场景1: 功能开发完成，合并到dev
        git add . && git commit -m "完成登录功能"
        pnpm todevp                 # 先预览
        pnpm todev                  # 执行合并
        git checkout feature/login  # 切换回原分支继续开发

        # 场景2: 遇到合并冲突
        pnpm todev                  # 执行合并
        # 脚本提示冲突，按指导解决冲突后:
        git add .                   # 添加解决后的文件
        git commit                  # 完成合并
        git push origin dev         # 推送结果

执行结果说明:
    ✅ 成功情况:
        • 当前分支代码已合并到dev分支
        • 远程dev分支已更新
        • 脚本停留在dev分支（需手动切换回原分支）
        • 原分支保持不变，随时可切换回去

    ❌ 失败情况:
        • 自动回滚到原分支
        • 保留备份分支以防数据丢失
        • 显示详细的错误信息和解决建议

重要注意事项:
    ⚠️  执行前必须满足:
        • 当前分支所有更改已提交（工作区干净）
        • 确保有dev分支的推送权限
        • 网络连接正常

    💡 最佳实践:
        • 合并前先运行测试确保功能正常
        • 重要功能建议先创建Pull Request审查
        • 合并后及时通知团队成员
        • 定期清理已合并的功能分支

EOF
}

# 创建安全备份
create_backup() {
    local branch_name="$1"
    BACKUP_BRANCH="${branch_name}-backup-$(date +%Y%m%d-%H%M%S)"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将创建备份分支: $BACKUP_BRANCH"
        return 0
    fi

    log_info "创建安全备份分支: $BACKUP_BRANCH"
    if git branch "$BACKUP_BRANCH"; then
        log_success "备份创建成功: $BACKUP_BRANCH"
    else
        log_warning "备份创建失败，但继续执行"
    fi
}

# 清理备份分支
cleanup_backup() {
    if [[ -n "$BACKUP_BRANCH" && "$DRY_RUN" == "false" ]]; then
        log_info "清理备份分支: $BACKUP_BRANCH"
        git branch -D "$BACKUP_BRANCH" 2>/dev/null || true
    fi
}

# 增强的错误处理函数
handle_error() {
    local exit_code=$?
    log_error "脚本执行失败 (退出码: $exit_code)，正在执行回滚..."

    # 如果在合并过程中失败，尝试中止合并
    if git status --porcelain | grep -q "^UU\|^AA\|^DD"; then
        log_warning "检测到合并冲突，中止合并操作"
        git merge --abort 2>/dev/null || true
    fi

    # 切换回原分支
    if [[ -n "$ORIGINAL_BRANCH" && "$ORIGINAL_BRANCH" != "dev" ]]; then
        log_info "切换回原分支: $ORIGINAL_BRANCH"
        git checkout "$ORIGINAL_BRANCH" 2>/dev/null || true
    fi

    # 自动清理备份分支
    if [[ -n "$BACKUP_BRANCH" && "$DRY_RUN" == "false" ]]; then
        log_info "清理备份分支: $BACKUP_BRANCH"
        git branch -D "$BACKUP_BRANCH" 2>/dev/null || true
        log_success "备份分支已自动清理"
    fi

    exit $exit_code
}

# 处理合并冲突
handle_merge_conflict() {
    log_error "🔥 检测到合并冲突！"
    echo
    log_info "冲突文件列表:"
    git status --porcelain | grep "^UU\|^AA\|^DD" | while read -r line; do
        echo "  📄 ${line:3}"
    done
    echo

    log_warning "请按以下步骤解决冲突:"
    echo "  1️⃣  手动编辑冲突文件，解决冲突标记 (<<<<<<<, =======, >>>>>>>)"
    echo "  2️⃣  添加已解决的文件: git add <文件名>"
    echo "  3️⃣  完成合并: git commit"
    echo "  4️⃣  推送结果: git push origin dev"
    echo

    log_info "或者放弃此次合并:"
    echo "  ❌ 中止合并: git merge --abort"
    echo "  🔄 切换回原分支: git checkout $ORIGINAL_BRANCH"
    echo

    log_warning "冲突解决完成后，可以手动推送:"
    echo "  git push origin dev"
    echo

    # 不自动退出，让用户手动处理
    return 1
}

# 智能合并函数
smart_merge() {
    local source_branch="$1"
    local target_branch="dev"

    log_step "开始智能合并: $source_branch -> $target_branch"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[预览] 将执行合并操作"
        log_info "[预览] 检查是否可以快进合并..."

        # 检查是否可以快进合并
        if git merge-base --is-ancestor "$target_branch" "$source_branch"; then
            log_success "[预览] ✅ 可以执行快进合并"
        else
            log_warning "[预览] ⚠️  需要执行三方合并"
        fi
        return 0
    fi

    # 检查是否可以快进合并
    if git merge-base --is-ancestor "$target_branch" "$source_branch"; then
        log_info "✅ 执行快进合并..."
        if git merge "$source_branch" --ff-only; then
            log_success "快进合并成功！"
            return 0
        else
            log_warning "快进合并失败，尝试三方合并"
        fi
    fi

    # 执行三方合并
    log_info "🔄 执行三方合并..."
    if git merge "$source_branch" --no-ff -m "Merge branch '$source_branch' into $target_branch

Merged by: $(git config user.name) <$(git config user.email)>
Date: $(date)
Source: $source_branch
Target: $target_branch"; then
        log_success "三方合并成功！"
        return 0
    else
        # 检查是否是合并冲突
        if git status --porcelain | grep -q "^UU\|^AA\|^DD"; then
            handle_merge_conflict
            return 1
        else
            log_error "合并失败，原因未知"
            return 1
        fi
    fi
}

# 设置错误陷阱
trap handle_error ERR

# 参数处理
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            SHOW_HELP=true
            shift
            ;;
        --dry-run|-n)
            DRY_RUN=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            log_info "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 显示帮助信息
if [[ "$SHOW_HELP" == "true" ]]; then
    show_help
    exit 0
fi

# 执行初始化检查
initialize_checks

# 显示脚本信息
if [[ "$DRY_RUN" == "true" ]]; then
    log_warning "🔍 预览模式 - 不会执行实际操作"
else
    log_info "🚀 开始执行安全合并脚本..."
fi

# 1. 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    log_error "当前目录不是Git仓库"
    exit 1
fi

# 2. 检查工作区是否干净
if [[ -n $(git status --porcelain) ]]; then
    log_error "工作区有未提交的更改，请先提交或暂存："
    git status --short
    log_warning "提示：可以使用 'git add .' 和 'git commit -m \"message\"' 提交更改"
    log_warning "或使用 'git stash' 暂存更改"
    exit 1
fi

# 3. 获取当前分支名
ORIGINAL_BRANCH=$(git branch --show-current)
log_step "当前分支: $ORIGINAL_BRANCH"

# 4. 检查是否已经在dev分支
if [[ "$ORIGINAL_BRANCH" == "dev" ]]; then
    log_warning "已经在dev分支上，无需合并"
    exit 0
fi

# 5. 创建安全备份
create_backup "$ORIGINAL_BRANCH"

# 6. 检查dev分支是否存在
log_step "检查dev分支是否存在..."
if ! git show-ref --verify --quiet refs/heads/dev; then
    log_error "dev分支不存在，请先创建dev分支"
    log_info "提示：可以使用 'git checkout -b dev' 创建dev分支"
    exit 1
fi

# 7. 检查远程仓库连接
log_step "检查远程仓库连接..."
if [[ "$DRY_RUN" == "false" ]]; then
    if ! git ls-remote origin > /dev/null 2>&1; then
        log_error "无法连接到远程仓库origin"
        log_info "请检查网络连接和仓库权限"
        exit 1
    fi
    log_success "远程仓库连接正常"
else
    log_info "[预览] 将检查远程仓库连接"
fi

# 8. 切换到dev分支
log_step "切换到dev分支..."
if [[ "$DRY_RUN" == "false" ]]; then
    git checkout dev
else
    log_info "[预览] 将切换到dev分支"
fi

# 9. 拉取最新的dev分支代码
log_step "拉取最新的dev分支代码..."
if [[ "$DRY_RUN" == "false" ]]; then
    if ! git pull origin dev; then
        log_error "拉取dev分支失败"
        log_warning "可能的原因："
        log_warning "  - 网络连接问题"
        log_warning "  - 远程仓库权限问题"
        log_warning "  - 本地dev分支与远程分支冲突"
        git checkout "$ORIGINAL_BRANCH"
        exit 1
    fi
    log_success "dev分支代码更新完成"
else
    log_info "[预览] 将拉取最新的dev分支代码"
fi

# 10. 执行智能合并
if ! smart_merge "$ORIGINAL_BRANCH"; then
    log_error "合并过程中遇到问题"
    # 如果是冲突，smart_merge已经处理了，这里直接退出
    exit 1
fi

# 11. 推送到远程dev分支
log_step "推送合并结果到远程dev分支..."
if [[ "$DRY_RUN" == "false" ]]; then
    if ! git push origin dev; then
        log_error "推送失败"
        log_warning "合并已完成，但推送失败"
        log_info "可能的原因："
        log_info "  - 网络连接问题"
        log_info "  - 远程仓库权限问题"
        log_info "  - 远程dev分支有新的提交"
        log_warning "请手动执行推送："
        log_warning "  git push origin dev"
        exit 1
    fi
    log_success "推送完成"
else
    log_info "[预览] 将推送合并结果到远程dev分支"
fi

# 12. 清理和总结
if [[ "$DRY_RUN" == "false" ]]; then
    # 自动清理备份分支
    if [[ -n "$BACKUP_BRANCH" ]]; then
        cleanup_backup
        log_success "备份分支已自动清理: $BACKUP_BRANCH"
    fi

    # 成功完成
    echo
    log_success "🎉 成功将分支 '$ORIGINAL_BRANCH' 合并到 dev 分支！"
    echo
    log_info "📊 操作摘要："
    echo "  ✅ 源分支: $ORIGINAL_BRANCH"
    echo "  ✅ 目标分支: dev"
    echo "  ✅ 远程仓库: origin"
    echo "  ✅ 当前位置: dev分支"
    echo "  ✅ 备份分支: 已自动清理"

    echo
    log_info "🔄 后续操作建议："
    echo "  📝 切换回原分支继续开发: git checkout $ORIGINAL_BRANCH"
    echo "  🧪 在dev分支运行完整测试"
    echo "  📢 通知团队成员合并完成"
    echo "  🗑️  考虑删除已合并的功能分支: git branch -d $ORIGINAL_BRANCH"
    echo "  🔄 如需撤销合并: git reset --hard HEAD~1 (在dev分支执行)"
    echo
    log_warning "⚠️  重要提醒："
    echo "  • 当前在dev分支，不是原来的 $ORIGINAL_BRANCH 分支"
    echo "  • 如需继续在原分支开发，请手动切换: git checkout $ORIGINAL_BRANCH"
    echo "  • 原分支代码未受影响，可随时切换回去"

    echo
    log_info "📈 最近的提交记录："
    git log --oneline --graph -5

    echo
    log_success "✨ 合并操作完成！"
else
    echo
    log_success "🔍 预览完成！"
    echo
    log_info "📋 预览摘要："
    echo "  📂 源分支: $ORIGINAL_BRANCH"
    echo "  📂 目标分支: dev"
    echo "  🌐 远程仓库: origin"
    echo "  💾 将创建备份分支"
    echo
    log_info "如果预览结果正确，请执行："
    echo "  pnpm todev"
    echo
fi
